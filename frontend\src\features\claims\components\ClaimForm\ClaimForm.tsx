import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Save, CheckCircle, AlertCircle } from 'lucide-react';
import { ClaimFormData } from '../../types';
import { validateForm, ValidationErrors, commonValidations } from '@/utils/validation/formValidation';
import { supabase } from '@/lib/supabase';
import {
  OwnerInformationSection,
  PropertyInformationSection,
  AddressInformationSection,
  ContactInformationSection
} from './ClaimFormSections';

interface ClaimFormProps {
  onBack: () => void;
  onSave?: (claimId: string) => void;
}

const initialFormData: ClaimFormData = {
  owner_name: '',
  owner_first_name: '',
  owner_last_name: '',
  owner_business_name: '',
  property_id: '',
  amount: 0,
  property_type: '',
  securities_name: '',
  cusip: '',
  shares_reported: 0,
  owner_address: '',
  owner_city: '',
  owner_state: '',
  owner_zip: '',
  holder_name: '',
  holder_address: '',
  holder_city: '',
  holder_state: '',
  holder_zip: '',
  priority: 'medium',
  state: '',
  report_date: '',
  primary_phone: '',
  primary_email: '',
  description: ''
};

const validationRules = {
  owner_name: commonValidations.required,
  amount: commonValidations.positiveNumber,
  state: commonValidations.required,
  primary_email: commonValidations.email,
  primary_phone: commonValidations.phone,
  owner_zip: commonValidations.zipCode,
  holder_zip: commonValidations.zipCode
};

export const ClaimForm: React.FC<ClaimFormProps> = ({ onBack, onSave }) => {
  const [formData, setFormData] = useState<ClaimFormData>(initialFormData);
  const [errors, setErrors] = useState<ValidationErrors>({});
  const [loading, setLoading] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');

  const handleFieldChange = (field: keyof ClaimFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear field error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const validationErrors = validateForm(formData, validationRules);
    setErrors(validationErrors);
    
    if (Object.keys(validationErrors).length > 0) {
      return;
    }

    setLoading(true);
    setSuccessMessage('');

    try {
      // Prepare claim data for database
      const claimData = {
        owner_name: formData.owner_name?.trim(),
        owner_first_name: formData.owner_first_name?.trim() || null,
        owner_last_name: formData.owner_last_name?.trim() || null,
        owner_business_name: formData.owner_business_name?.trim() || null,
        property_id: formData.property_id?.trim() || null,
        amount: formData.amount || 0,
        property_type: formData.property_type || null,
        securities_name: formData.securities_name?.trim() || null,
        cusip: formData.cusip?.trim() || null,
        shares_reported: formData.shares_reported || null,
        owner_address: formData.owner_address?.trim() || null,
        owner_city: formData.owner_city?.trim() || null,
        owner_state: formData.owner_state || null,
        owner_zip: formData.owner_zip?.trim() || null,
        holder_name: formData.holder_name?.trim() || null,
        holder_address: formData.holder_address?.trim() || null,
        holder_city: formData.holder_city?.trim() || null,
        holder_state: formData.holder_state || null,
        holder_zip: formData.holder_zip?.trim() || null,
        priority: formData.priority || 'medium',
        state: formData.state,
        report_date: formData.report_date || null,
        status: 'new',
        complexity_score: 5,
        commission_rate: 0.15,
        compliance_status: 'pending',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      const { data, error } = await supabase
        .from('claims')
        .insert([claimData])
        .select()
        .single();

      if (error) throw error;

      // Add initial activity log
      await supabase
        .from('claim_activities')
        .insert({
          claim_id: data.id,
          agent_id: 'system',
          activity_type: 'claim_created',
          title: 'Claim created',
          description: formData.description || 'New claim submitted through form'
        });

      setSuccessMessage('Claim created successfully!');
      
      if (onSave) {
        onSave(data.id);
      }

    } catch (error) {
      console.error('Error creating claim:', error);
      setErrors({ submit: 'Failed to create claim. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <h1 className="text-3xl font-bold text-gray-900">New Claim</h1>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="flex items-center gap-2 p-4 bg-green-50 border border-green-200 rounded-md text-green-800">
          <CheckCircle className="h-5 w-5" />
          {successMessage}
        </div>
      )}

      {/* Error Message */}
      {errors.submit && (
        <div className="flex items-center gap-2 p-4 bg-red-50 border border-red-200 rounded-md text-red-800">
          <AlertCircle className="h-5 w-5" />
          {errors.submit}
        </div>
      )}

      {/* Form */}
      <form onSubmit={handleSubmit} className="space-y-6">
        <OwnerInformationSection
          formData={formData}
          errors={errors}
          onChange={handleFieldChange}
        />

        <PropertyInformationSection
          formData={formData}
          errors={errors}
          onChange={handleFieldChange}
        />

        <AddressInformationSection
          formData={formData}
          errors={errors}
          onChange={handleFieldChange}
        />

        <ContactInformationSection
          formData={formData}
          errors={errors}
          onChange={handleFieldChange}
        />

        {/* Submit Button */}
        <div className="flex justify-end space-x-4 pt-6 border-t">
          <Button type="button" variant="outline" onClick={onBack}>
            Cancel
          </Button>
          <Button type="submit" disabled={loading}>
            <Save className="h-4 w-4 mr-2" />
            {loading ? 'Creating...' : 'Create Claim'}
          </Button>
        </div>
      </form>
    </div>
  );
}; 