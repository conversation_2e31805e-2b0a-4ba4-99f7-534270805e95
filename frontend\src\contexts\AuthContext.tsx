import { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import { User, AuthState, UserRole, ROLE_PERMISSIONS } from '@/types/auth'

interface SignupData {
  firstName: string
  lastName: string
  companyName: string
  phone?: string
  plan: string
  role: string
}

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>
  signup: (email: string, password: string, data: SignupData) => Promise<void>
  logout: () => void
  switchRole: (role: UserRole) => void // For demo purposes
}

const AuthContext = createContext<AuthContextType | undefined>(undefined)

// Mock users for demonstration
const MOCK_USERS: Record<string, User> = {
  '<EMAIL>': {
    id: '1',
    email: '<EMAIL>',
    name: '<PERSON>',
    role: 'junior_agent',
    phone: '******-0100',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T09:30:00Z',
    permissions: ROLE_PERMISSIONS.junior_agent,
  },
  
  '<EMAIL>': {
    id: '2',
    email: '<EMAIL>',
    name: 'John Doe',
    role: 'senior_agent',
    phone: '******-0101',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T10:15:00Z',
    permissions: ROLE_PERMISSIONS.senior_agent,
  },
  
  '<EMAIL>': {
    id: '3',
    email: '<EMAIL>',
    name: 'System Administrator',
    role: 'admin',
    phone: '******-0102',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T08:45:00Z',
    permissions: ROLE_PERMISSIONS.admin,
  },
  
  '<EMAIL>': {
    id: '4',
    email: '<EMAIL>',
    name: 'Mike Wilson',
    role: 'contractor',
    phone: '******-0103',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T07:20:00Z',
    permissions: ROLE_PERMISSIONS.contractor,
  },
  
  '<EMAIL>': {
    id: '5',
    email: '<EMAIL>',
    name: 'Sarah Johnson',
    role: 'compliance',
    phone: '******-0104',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T09:00:00Z',
    permissions: ROLE_PERMISSIONS.compliance,
  },

  '<EMAIL>': {
    id: '6',
    email: '<EMAIL>',
    name: 'David Brown',
    role: 'finance',
    phone: '******-0105',
    active: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    lastLoginAt: '2024-01-15T08:45:00Z',
    permissions: ROLE_PERMISSIONS.finance,
  },
}

export function AuthProvider({ children }: { children: ReactNode }) {
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    permissions: [],
  })

  // Check for existing session on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('arwa_user')
    if (savedUser) {
      try {
        const user = JSON.parse(savedUser) as User
        setAuthState({
          user,
          isAuthenticated: true,
          isLoading: false,
          permissions: user.permissions,
        })
      } catch (error) {
        console.error('Error parsing saved user:', error)
        localStorage.removeItem('arwa_user')
        setAuthState(prev => ({ ...prev, isLoading: false }))
      }
    } else {
      setAuthState(prev => ({ ...prev, isLoading: false }))
    }
  }, [])

  const login = async (email: string, password: string): Promise<void> => {
    setAuthState(prev => ({ ...prev, isLoading: true }))
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const user = MOCK_USERS[email.toLowerCase()]
    
    if (!user || password !== 'password') {
      setAuthState(prev => ({ ...prev, isLoading: false }))
      throw new Error('Invalid email or password')
    }

    // Update last login time
    const updatedUser = {
      ...user,
      lastLoginAt: new Date().toISOString(),
    }

    localStorage.setItem('arwa_user', JSON.stringify(updatedUser))
    
    setAuthState({
      user: updatedUser,
      isAuthenticated: true,
      isLoading: false,
      permissions: updatedUser.permissions,
    })
  }

  const signup = async (email: string, _password: string, data: SignupData): Promise<void> => {
    setAuthState(prev => ({ ...prev, isLoading: true }))
    
    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    // Check if user already exists
    if (MOCK_USERS[email.toLowerCase()]) {
      setAuthState(prev => ({ ...prev, isLoading: false }))
      throw new Error('An account with this email already exists')
    }

    // Create new user
    const newUser: User = {
      id: Date.now().toString(),
      email: email.toLowerCase(),
      name: `${data.firstName} ${data.lastName}`,
      role: (data.role as UserRole) || 'admin',
      phone: data.phone || '',
      active: true,
      companyName: data.companyName,
      plan: data.plan,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastLoginAt: new Date().toISOString(),
      permissions: ROLE_PERMISSIONS[data.role as UserRole] || ROLE_PERMISSIONS.admin,
    }

    // Add to mock users (in real app, this would be saved to database)
    MOCK_USERS[email.toLowerCase()] = newUser

    localStorage.setItem('arwa_user', JSON.stringify(newUser))
    
    setAuthState({
      user: newUser,
      isAuthenticated: true,
      isLoading: false,
      permissions: newUser.permissions,
    })
  }

  const logout = () => {
    localStorage.removeItem('arwa_user')
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      permissions: [],
    })
  }

  // For demo purposes - switch between roles
  const switchRole = (role: UserRole) => {
    if (!authState.user) return
    
    const updatedUser = {
      ...authState.user,
      role,
      permissions: ROLE_PERMISSIONS[role],
    }
    
    localStorage.setItem('arwa_user', JSON.stringify(updatedUser))
    
    setAuthState(prev => ({
      ...prev,
      user: updatedUser,
      permissions: updatedUser.permissions,
    }))
  }

  const value: AuthContextType = {
    ...authState,
    login,
    logout,
    switchRole,
    signup,
  }

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>
}

export function useAuth() {
  const context = useContext(AuthContext)
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

// Hook for checking permissions
export function usePermissions() {
  const { user, permissions } = useAuth()
  
  const hasPermission = (permission: string) => {
    return permissions.includes(permission as any)
  }
  
  const hasAllPermissions = (requiredPermissions: string[]) => {
    return requiredPermissions.every(permission => 
      permissions.includes(permission as any)
    )
  }
  
  const hasAnyPermission = (requiredPermissions: string[]) => {
    return requiredPermissions.some(permission => 
      permissions.includes(permission as any)
    )
  }
  
  return {
    user,
    permissions,
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
  }
} 