import { ReactNode, useState } from 'react'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { MainContent } from './MainContent'
import { CommandPalette } from '@/components/ui/command-palette'
import { useAuth } from '@/contexts/AuthContext'

interface AppLayoutProps {
  children: ReactNode
  currentView: string
  onViewChange: (view: string) => void
  commandPaletteOpen: boolean
  onCommandPaletteToggle: (open: boolean) => void
}

export function AppLayout({
  children,
  currentView,
  onViewChange,
  commandPaletteOpen,
  onCommandPaletteToggle
}: AppLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const { user } = useAuth()

  if (!user) {
    return <div className="min-h-screen flex items-center justify-center">{children}</div>
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        open={sidebarOpen}
        onToggle={setSidebarOpen}
        currentView={currentView}
        onViewChange={onViewChange}
      />

      {/* Main Content Area */}
      <div className={`transition-all duration-300 ${sidebarOpen ? 'lg:ml-64' : 'lg:ml-16'}`}>
        {/* Header */}
        <Header
          onSidebarToggle={() => setSidebarOpen(!sidebarOpen)}
          onCommandPaletteOpen={() => onCommandPaletteToggle(true)}
        />

        {/* Page Content */}
        <MainContent>
          {children}
        </MainContent>
      </div>

      {/* Command Palette */}
      <CommandPalette
        open={commandPaletteOpen}
        onOpenChange={onCommandPaletteToggle}
        onNavigate={onViewChange}
      />
    </div>
  )
}
