import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Palette, 
  Globe, 
  Smartphone, 
  Mail, 
  FileText, 
  BarChart, 
  Crown,
  Sparkles,
  Settings,
  Eye,
  Upload,
  Download,
  Brush,
  Layout,
  Shield,
  Zap
} from 'lucide-react';
import { 
  WhiteLabelSolution, 
  PortalCustomization,
  ReportBrandingSettings 
} from '@/types/enterprise';

interface WhiteLabelSolutionProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const WhiteLabelSolutionComponent: React.FC<WhiteLabelSolutionProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('branding');
  const [brandingConfig, setBrandingConfig] = useState<Partial<WhiteLabelSolution>>({
    branding_enabled: userPlan === 'diamond',
    custom_domain: userPlan === 'diamond' ? 'your-company.assethunter.com' : '',
    primary_color: '#2563eb',
    secondary_color: '#64748b',
    email_branding: userPlan === 'diamond',
    mobile_app_branding: userPlan === 'diamond'
  });

  const features = {
    platinum: [
      'Custom Domain Setup',
      'Basic Branding Controls',
      'Email Template Customization',
      'Report Header/Footer',
      'Custom Logo Integration',
      'Portal Theme Selection'
    ],
    diamond: [
      'Full White Label Solution',
      'Complete UI Customization',
      'Mobile App Branding',
      'Advanced Portal Controls',
      'Custom Document Templates',
      'Comprehensive Report Branding',
      'Multi-Brand Management',
      'Advanced Feature Controls'
    ]
  };

  const mockBrandingPreview = {
    company_name: 'YourCompany Asset Recovery',
    logo_url: '/api/placeholder/200/60',
    primary_color: brandingConfig.primary_color,
    secondary_color: brandingConfig.secondary_color,
    custom_domain: brandingConfig.custom_domain,
    theme: 'professional'
  };

  const renderBrandingControls = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5" />
              Color Scheme
            </CardTitle>
            <CardDescription>
              Customize your brand colors across the platform
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Primary Color</label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={brandingConfig.primary_color}
                  onChange={(e) => setBrandingConfig({...brandingConfig, primary_color: e.target.value})}
                  className="w-12 h-12 rounded border"
                  disabled={!brandingConfig.branding_enabled}
                />
                <span className="text-sm text-gray-600">{brandingConfig.primary_color}</span>
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Secondary Color</label>
              <div className="flex items-center gap-3">
                <input
                  type="color"
                  value={brandingConfig.secondary_color}
                  onChange={(e) => setBrandingConfig({...brandingConfig, secondary_color: e.target.value})}
                  className="w-12 h-12 rounded border"
                  disabled={!brandingConfig.branding_enabled}
                />
                <span className="text-sm text-gray-600">{brandingConfig.secondary_color}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Domain Configuration
            </CardTitle>
            <CardDescription>
              Set up your custom domain and SSL
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Custom Domain</label>
              <input
                type="text"
                value={brandingConfig.custom_domain}
                onChange={(e) => setBrandingConfig({...brandingConfig, custom_domain: e.target.value})}
                placeholder="your-company.assethunter.com"
                className="w-full px-3 py-2 border rounded-md"
                disabled={!brandingConfig.branding_enabled}
              />
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">SSL Certificate</span>
              <Badge variant={brandingConfig.branding_enabled ? "default" : "secondary"}>
                {brandingConfig.branding_enabled ? "Active" : "Upgrade Required"}
              </Badge>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm">DNS Management</span>
              <Badge variant={brandingConfig.branding_enabled ? "default" : "secondary"}>
                {brandingConfig.branding_enabled ? "Configured" : "Not Available"}
              </Badge>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Live Preview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Live Preview
          </CardTitle>
          <CardDescription>
            See how your branding will appear to users
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div 
            className="border rounded-lg p-6 bg-white"
            style={{ 
              borderColor: brandingConfig.primary_color,
              backgroundColor: `${brandingConfig.primary_color}05` 
            }}
          >
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3">
                <div 
                  className="w-12 h-12 rounded-lg flex items-center justify-center text-white font-bold"
                  style={{ backgroundColor: brandingConfig.primary_color }}
                >
                  YC
                </div>
                <div>
                  <h3 className="font-semibold">{mockBrandingPreview.company_name}</h3>
                  <p className="text-sm text-gray-600">{mockBrandingPreview.custom_domain}</p>
                </div>
              </div>
              <Button 
                size="sm" 
                style={{ backgroundColor: brandingConfig.primary_color }}
                className="text-white"
              >
                Dashboard
              </Button>
            </div>
            <div className="grid grid-cols-3 gap-4">
              <div className="bg-white p-3 rounded border">
                <div className="text-2xl font-bold" style={{ color: brandingConfig.primary_color }}>1,234</div>
                <div className="text-sm text-gray-600">Active Claims</div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="text-2xl font-bold" style={{ color: brandingConfig.secondary_color }}>$85.2K</div>
                <div className="text-sm text-gray-600">Recovered</div>
              </div>
              <div className="bg-white p-3 rounded border">
                <div className="text-2xl font-bold" style={{ color: brandingConfig.primary_color }}>94%</div>
                <div className="text-sm text-gray-600">Success Rate</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderPortalCustomization = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Layout className="h-5 w-5" />
              Layout Options
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {['Standard', 'Custom', 'Branded'].map((layout) => (
                <div key={layout} className="flex items-center justify-between">
                  <span className="text-sm">{layout} Layout</span>
                  <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                    {userPlan === 'diamond' ? "Available" : "Diamond Only"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Access Controls
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Role-based Access</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Feature Toggles</span>
                <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                  {userPlan === 'diamond' ? "Active" : "Upgrade"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Custom Permissions</span>
                <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                  {userPlan === 'diamond' ? "Active" : "Upgrade"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Advanced Features
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Navigation Customization</span>
                <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                  {userPlan === 'diamond' ? "Available" : "Diamond"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Dashboard Widgets</span>
                <Badge variant="default">Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Custom Welcome Message</span>
                <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                  {userPlan === 'diamond' ? "Available" : "Diamond"}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderFeatureComparison = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Crown className="h-5 w-5 text-purple-600" />
              Platinum Features
            </CardTitle>
            <CardDescription>
              Professional branding and customization
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {features.platinum.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-blue-600" />
              Diamond Features
            </CardTitle>
            <CardDescription>
              Complete white-label solution
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {features.diamond.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Sparkles className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Upgrade to Diamond</h3>
              <p className="text-gray-600 mb-4">
                Unlock complete white-label capabilities and advanced enterprise features
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">White Label Solution</h2>
          <p className="text-gray-600">
            Complete branding and customization for your enterprise
          </p>
        </div>
        <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
          {userPlan === 'diamond' ? 'Diamond Active' : 'Platinum'}
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="branding">Branding</TabsTrigger>
          <TabsTrigger value="portal">Portal</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="branding" className="space-y-6">
          {renderBrandingControls()}
        </TabsContent>

        <TabsContent value="portal" className="space-y-6">
          {renderPortalCustomization()}
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          {renderFeatureComparison()}
        </TabsContent>
      </Tabs>
    </div>
  );
}; 