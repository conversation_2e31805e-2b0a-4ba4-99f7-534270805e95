-- Sample data for Asset Recovery Web Application
-- Run this after the main schema to populate with test data

-- Insert sample users
INSERT INTO users (email, role, name, phone) VALUES
('<EMAIL>', 'senior_agent', '<PERSON>', '******-0101'),
('<EMAIL>', 'junior_agent', '<PERSON>', '******-0102'),
('<EMAIL>', 'junior_agent', '<PERSON>', '******-0103'),
('<EMAIL>', 'compliance', '<PERSON>', '******-0104'),
('<EMAIL>', 'finance', '<PERSON>', '******-0105');

-- Insert sample state batches
INSERT INTO state_batches (state, year, file_name, imported_by, total_records, processed_records) VALUES
('CA', 2024, 'california_unclaimed_2024.csv', (SELECT id FROM users WHERE email = '<EMAIL>'), 1500, 1500),
('TX', 2024, 'texas_unclaimed_2024.csv', (SELECT id FROM users WHERE email = '<EMAIL>'), 2300, 2300),
('FL', 2024, 'florida_unclaimed_2024.csv', (SELECT id FROM users WHERE email = '<EMAIL>'), 1800, 1800);

-- Insert sample claimants
INSERT INTO claimants (type, first_name, last_name, business_name) VALUES
('individual', 'Robert', 'Anderson', NULL),
('individual', 'Maria', 'Garcia', NULL),
('individual', 'James', 'Miller', NULL),
('business', NULL, NULL, 'ABC Construction LLC'),
('individual', 'Linda', 'Davis', NULL),
('business', NULL, NULL, 'Tech Solutions Inc'),
('individual', 'Michael', 'Rodriguez', NULL),
('individual', 'Jennifer', 'Wilson', NULL),
('individual', 'William', 'Moore', NULL),
('business', NULL, NULL, 'Green Energy Corp');

-- Insert sample addresses
INSERT INTO addresses (claimant_id, line1, city, state, zip, verified) VALUES
((SELECT id FROM claimants WHERE first_name = 'Robert' AND last_name = 'Anderson'), '123 Main St', 'Los Angeles', 'CA', '90210', true),
((SELECT id FROM claimants WHERE first_name = 'Maria' AND last_name = 'Garcia'), '456 Oak Ave', 'Houston', 'TX', '77001', false),
((SELECT id FROM claimants WHERE first_name = 'James' AND last_name = 'Miller'), '789 Pine Rd', 'Miami', 'FL', '33101', true),
((SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'), '321 Business Blvd', 'Dallas', 'TX', '75201', true),
((SELECT id FROM claimants WHERE first_name = 'Linda' AND last_name = 'Davis'), '654 Elm St', 'San Francisco', 'CA', '94102', false);

-- Insert sample phone numbers
INSERT INTO phones (claimant_id, phone, type, verified) VALUES
((SELECT id FROM claimants WHERE first_name = 'Robert' AND last_name = 'Anderson'), '******-1001', 'mobile', true),
((SELECT id FROM claimants WHERE first_name = 'Maria' AND last_name = 'Garcia'), '******-1002', 'home', false),
((SELECT id FROM claimants WHERE first_name = 'James' AND last_name = 'Miller'), '******-1003', 'mobile', true),
((SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'), '******-1004', 'work', true),
((SELECT id FROM claimants WHERE first_name = 'Linda' AND last_name = 'Davis'), '******-1005', 'mobile', false);

-- Insert sample email addresses
INSERT INTO emails (claimant_id, email, verified) VALUES
((SELECT id FROM claimants WHERE first_name = 'Robert' AND last_name = 'Anderson'), '<EMAIL>', true),
((SELECT id FROM claimants WHERE first_name = 'Maria' AND last_name = 'Garcia'), '<EMAIL>', false),
((SELECT id FROM claimants WHERE first_name = 'James' AND last_name = 'Miller'), '<EMAIL>', true),
((SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'), '<EMAIL>', true),
((SELECT id FROM claimants WHERE first_name = 'Linda' AND last_name = 'Davis'), '<EMAIL>', false);

-- Insert sample claims
INSERT INTO claims (
  state_batch_id, 
  original_claim_id, 
  claim_number, 
  state, 
  amount_reported, 
  amount_net, 
  status, 
  claimant_primary_id, 
  assigned_agent_id, 
  fee_cap_pct
) VALUES
(
  (SELECT id FROM state_batches WHERE state = 'CA' LIMIT 1),
  'CA-2024-001',
  'CLM-001',
  'CA',
  15000.00,
  13500.00,
  'new',
  (SELECT id FROM claimants WHERE first_name = 'Robert' AND last_name = 'Anderson'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  10.00
),
(
  (SELECT id FROM state_batches WHERE state = 'TX' LIMIT 1),
  'TX-2024-002',
  'CLM-002',
  'TX',
  25000.00,
  21250.00,
  'contacted',
  (SELECT id FROM claimants WHERE first_name = 'Maria' AND last_name = 'Garcia'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  15.00
),
(
  (SELECT id FROM state_batches WHERE state = 'FL' LIMIT 1),
  'FL-2024-003',
  'CLM-003',
  'FL',
  8500.00,
  6800.00,
  'contract_sent',
  (SELECT id FROM claimants WHERE first_name = 'James' AND last_name = 'Miller'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  20.00
),
(
  (SELECT id FROM state_batches WHERE state = 'TX' LIMIT 1),
  'TX-2024-004',
  'CLM-004',
  'TX',
  45000.00,
  38250.00,
  'signed',
  (SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  15.00
),
(
  (SELECT id FROM state_batches WHERE state = 'CA' LIMIT 1),
  'CA-2024-005',
  'CLM-005',
  'CA',
  12000.00,
  10800.00,
  'filed',
  (SELECT id FROM claimants WHERE first_name = 'Linda' AND last_name = 'Davis'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  10.00
),
(
  (SELECT id FROM state_batches WHERE state = 'FL' LIMIT 1),
  'FL-2024-006',
  'CLM-006',
  'FL',
  32000.00,
  25600.00,
  'paid',
  (SELECT id FROM claimants WHERE business_name = 'Tech Solutions Inc'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  20.00
),
(
  (SELECT id FROM state_batches WHERE state = 'CA' LIMIT 1),
  'CA-2024-007',
  'CLM-007',
  'CA',
  7500.00,
  6750.00,
  'new',
  (SELECT id FROM claimants WHERE first_name = 'Michael' AND last_name = 'Rodriguez'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  10.00
),
(
  (SELECT id FROM state_batches WHERE state = 'TX' LIMIT 1),
  'TX-2024-008',
  'CLM-008',
  'TX',
  18000.00,
  15300.00,
  'contacted',
  (SELECT id FROM claimants WHERE first_name = 'Jennifer' AND last_name = 'Wilson'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  15.00
),
(
  (SELECT id FROM state_batches WHERE state = 'FL' LIMIT 1),
  'FL-2024-009',
  'CLM-009',
  'FL',
  55000.00,
  44000.00,
  'contract_sent',
  (SELECT id FROM claimants WHERE first_name = 'William' AND last_name = 'Moore'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  20.00
),
(
  (SELECT id FROM state_batches WHERE state = 'CA' LIMIT 1),
  'CA-2024-010',
  'CLM-010',
  'CA',
  28000.00,
  25200.00,
  'signed',
  (SELECT id FROM claimants WHERE business_name = 'Green Energy Corp'),
  (SELECT id FROM users WHERE email = '<EMAIL>'),
  10.00
);

-- Insert sample activities
INSERT INTO activities (claim_id, agent_id, type, summary, detail) VALUES
((SELECT id FROM claims WHERE claim_number = 'CLM-001'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'note', 'Initial research completed', 'Found claimant contact information through public records'),
((SELECT id FROM claims WHERE claim_number = 'CLM-002'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'call', 'First contact attempt', 'Left voicemail message explaining the unclaimed property'),
((SELECT id FROM claims WHERE claim_number = 'CLM-002'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'call', 'Successful contact', 'Spoke with claimant, explained process, sending engagement letter'),
((SELECT id FROM claims WHERE claim_number = 'CLM-003'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'email', 'Engagement letter sent', 'Sent via DocuSign for electronic signature'),
((SELECT id FROM claims WHERE claim_number = 'CLM-004'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'document_upload', 'Signed contract received', 'Contract fully executed, ready to file with state'),
((SELECT id FROM claims WHERE claim_number = 'CLM-005'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'status_change', 'Filed with state', 'Submitted claim package to California State Controller'),
((SELECT id FROM claims WHERE claim_number = 'CLM-006'), (SELECT id FROM users WHERE email = '<EMAIL>'), 'status_change', 'Payment received', 'State issued check, forwarding to claimant');

-- Insert sample lead scores
INSERT INTO lead_scores (claim_id, score, factors_json, model_version) VALUES
((SELECT id FROM claims WHERE claim_number = 'CLM-001'), 85, '{"amount_factor": 0.7, "contact_info_quality": 0.9, "state_processing_time": 0.8}', 'v1.0'),
((SELECT id FROM claims WHERE claim_number = 'CLM-002'), 72, '{"amount_factor": 0.8, "contact_info_quality": 0.6, "state_processing_time": 0.7}', 'v1.0'),
((SELECT id FROM claims WHERE claim_number = 'CLM-003'), 68, '{"amount_factor": 0.5, "contact_info_quality": 0.8, "state_processing_time": 0.9}', 'v1.0'),
((SELECT id FROM claims WHERE claim_number = 'CLM-004'), 92, '{"amount_factor": 0.9, "contact_info_quality": 0.95, "state_processing_time": 0.7}', 'v1.0'),
((SELECT id FROM claims WHERE claim_number = 'CLM-005'), 78, '{"amount_factor": 0.6, "contact_info_quality": 0.85, "state_processing_time": 0.8}', 'v1.0');

-- Insert sample documents
INSERT INTO documents (claim_id, claimant_id, doc_type, file_name, file_path, uploaded_by) VALUES
((SELECT id FROM claims WHERE claim_number = 'CLM-003'), (SELECT id FROM claimants WHERE first_name = 'James' AND last_name = 'Miller'), 'engagement_letter', 'engagement_letter_CLM-003.pdf', '/documents/engagement_letters/CLM-003.pdf', (SELECT id FROM users WHERE email = '<EMAIL>')),
((SELECT id FROM claims WHERE claim_number = 'CLM-004'), (SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'), 'engagement_letter', 'engagement_letter_CLM-004.pdf', '/documents/engagement_letters/CLM-004.pdf', (SELECT id FROM users WHERE email = '<EMAIL>')),
((SELECT id FROM claims WHERE claim_number = 'CLM-004'), (SELECT id FROM claimants WHERE business_name = 'ABC Construction LLC'), 'poa', 'power_of_attorney_CLM-004.pdf', '/documents/poa/CLM-004.pdf', (SELECT id FROM users WHERE email = '<EMAIL>')),
((SELECT id FROM claims WHERE claim_number = 'CLM-005'), (SELECT id FROM claimants WHERE first_name = 'Linda' AND last_name = 'Davis'), 'state_form', 'ca_claim_form_CLM-005.pdf', '/documents/state_forms/CLM-005.pdf', (SELECT id FROM users WHERE email = '<EMAIL>'));

-- Insert sample audit logs
INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values) VALUES
((SELECT id FROM users WHERE email = '<EMAIL>'), 'INSERT', 'claims', (SELECT id FROM claims WHERE claim_number = 'CLM-001'), '{"claim_number": "CLM-001", "status": "new"}'),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'UPDATE', 'claims', (SELECT id FROM claims WHERE claim_number = 'CLM-002'), '{"status": "contacted"}'),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'UPDATE', 'claims', (SELECT id FROM claims WHERE claim_number = 'CLM-003'), '{"status": "contract_sent"}'),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'UPDATE', 'claims', (SELECT id FROM claims WHERE claim_number = 'CLM-004'), '{"status": "signed"}'),
((SELECT id FROM users WHERE email = '<EMAIL>'), 'UPDATE', 'claims', (SELECT id FROM claims WHERE claim_number = 'CLM-005'), '{"status": "filed"}'); 