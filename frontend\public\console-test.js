/**
 * 🧪 ASSETHUNTERPRO CONSOLE FUNCTION TESTS
 * ========================================
 * 
 * Run this script in the browser console on the main app page (http://localhost:3005)
 * to test all functions within the actual React application context.
 * 
 * Usage:
 * 1. Open http://localhost:3005 in browser
 * 2. Open browser console (F12)
 * 3. Copy and paste this entire script
 * 4. Press Enter to run
 */

console.log(`
🧪 ASSETHUNTERPRO CONSOLE FUNCTION TESTS
========================================

Testing all functions within the React application context...
This will provide the most accurate results.

Timestamp: ${new Date().toISOString()}
Location: ${window.location.href}
`);

// Test state
let testResults = {
    startTime: Date.now(),
    total: 0,
    passed: 0,
    failed: 0,
    warnings: 0,
    tests: []
};

// Logging function
function logTest(name, status, details = {}) {
    testResults.total++;
    testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
    
    const test = { name, status, details, timestamp: new Date().toISOString() };
    testResults.tests.push(test);
    
    const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
    const color = status === 'pass' ? 'color: #22c55e' : status === 'fail' ? 'color: #ef4444' : 'color: #f59e0b';
    
    console.log(`%c${icon} ${name}`, color, details.info || '');
}

// Main test function
async function runConsoleTests() {
    console.log('\n🔍 PHASE 1: ENVIRONMENT TESTING');
    console.log('================================');
    
    // Environment tests
    const envTests = [
        { name: 'localStorage', test: () => {
            try {
                localStorage.setItem('test', 'ok');
                const result = localStorage.getItem('test') === 'ok';
                localStorage.removeItem('test');
                return result;
            } catch (e) { return false; }
        }},
        { name: 'sessionStorage', test: () => {
            try {
                sessionStorage.setItem('test', 'ok');
                const result = sessionStorage.getItem('test') === 'ok';
                sessionStorage.removeItem('test');
                return result;
            } catch (e) { return false; }
        }},
        { name: 'fetch API', test: () => typeof fetch === 'function' },
        { name: 'Promise support', test: () => typeof Promise === 'function' },
        { name: 'JSON support', test: () => typeof JSON === 'object' }
    ];

    envTests.forEach(test => {
        try {
            const result = test.test();
            logTest(`Environment: ${test.name}`, result ? 'pass' : 'fail', {
                info: result ? 'Working correctly' : 'Not available'
            });
        } catch (error) {
            logTest(`Environment: ${test.name}`, 'fail', {
                info: `Error: ${error.message}`
            });
        }
    });

    console.log('\n🌐 PHASE 2: SERVER TESTING');
    console.log('===========================');
    
    // Server tests
    const serverTests = [
        { name: 'Development server port', test: () => window.location.port === '3005' },
        { name: 'Development server protocol', test: () => window.location.protocol === 'http:' },
        { name: 'Development server host', test: () => window.location.hostname === 'localhost' }
    ];

    serverTests.forEach(test => {
        const result = test.test();
        logTest(`Server: ${test.name}`, result ? 'pass' : 'warning', {
            info: result ? 'Configured correctly' : 'Configuration issue'
        });
    });

    console.log('\n🔌 PHASE 3: BACKEND API TESTING');
    console.log('================================');
    
    // Backend API tests
    const apiTests = [
        { name: 'Health endpoint', url: 'http://localhost:3001/health' },
        { name: 'Claims API', url: 'http://localhost:3001/api/claims' },
        { name: 'Dashboard API', url: 'http://localhost:3001/api/dashboard/stats' }
    ];

    for (const test of apiTests) {
        try {
            const response = await fetch(test.url);
            logTest(`API: ${test.name}`, response.ok ? 'pass' : 'fail', {
                info: response.ok ? `Status: ${response.status}` : `Failed: ${response.status}`
            });
        } catch (error) {
            logTest(`API: ${test.name}`, 'fail', {
                info: `Cannot reach: ${error.message}`
            });
        }
    }

    console.log('\n🎨 PHASE 4: REACT APP TESTING');
    console.log('==============================');
    
    // React app tests
    const root = document.getElementById('root');
    if (root) {
        const hasContent = root.children.length > 0;
        const hasDataAttr = root.hasAttribute('data-reactroot');
        
        logTest('UI: React root element', 'pass', {
            info: `Found with ${root.children.length} children`
        });
        
        logTest('UI: React content loaded', hasContent ? 'pass' : 'warning', {
            info: hasContent ? `${root.children.length} child elements` : 'No content in root'
        });
    } else {
        logTest('UI: React root element', 'fail', {
            info: 'Root element not found'
        });
    }

    // CSS and styles
    const hasStyles = document.querySelectorAll('link[rel="stylesheet"], style').length > 0;
    logTest('UI: CSS styles loaded', hasStyles ? 'pass' : 'warning', {
        info: hasStyles ? 'Stylesheets detected' : 'No stylesheets found'
    });

    // Interactive elements
    const buttons = document.querySelectorAll('button');
    logTest('UI: Interactive buttons', buttons.length > 0 ? 'pass' : 'warning', {
        info: `Found ${buttons.length} buttons`
    });

    // Form inputs (context-aware)
    const inputs = document.querySelectorAll('input');
    const navigation = document.querySelector('nav, aside, [role="navigation"]');
    const userElements = document.querySelector('[class*="user"], [class*="logout"]');
    const userLoggedIn = navigation !== null || userElements !== null;

    if (inputs.length > 0) {
        logTest('UI: Form inputs', 'pass', {
            info: `Found ${inputs.length} input elements`
        });
    } else if (userLoggedIn) {
        logTest('UI: Form inputs', 'pass', {
            info: 'No inputs visible - user logged in (expected)'
        });
    } else {
        logTest('UI: Form inputs', 'warning', {
            info: 'No inputs detected and login status unclear'
        });
    }

    // Navigation elements
    const navElements = document.querySelectorAll('nav, aside, [role="navigation"]');
    const navButtons = document.querySelectorAll('button[class*="nav"], aside button');
    const totalNav = navElements.length + navButtons.length;

    logTest('UI: Navigation elements', totalNav > 0 ? 'pass' : 'warning', {
        info: totalNav > 0 ? `Found ${totalNav} navigation elements` : 'No navigation detected'
    });

    console.log('\n✅ PHASE 5: DATA VALIDATION TESTING');
    console.log('====================================');
    
    // Validation tests
    const validationTests = [
        { name: 'Email validation', test: () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test('<EMAIL>') },
        { name: 'Phone validation', test: () => /^\+?[\d\s\-\(\)]{10,}$/.test('************') },
        { name: 'SSN validation', test: () => /^\d{3}-?\d{2}-?\d{4}$/.test('***********') }
    ];

    validationTests.forEach(test => {
        try {
            const result = test.test();
            logTest(`Validation: ${test.name}`, result ? 'pass' : 'fail', {
                info: result ? 'Regex working correctly' : 'Validation failed'
            });
        } catch (error) {
            logTest(`Validation: ${test.name}`, 'fail', {
                info: `Error: ${error.message}`
            });
        }
    });

    // Generate summary
    generateConsoleSummary();
}

function generateConsoleSummary() {
    const endTime = Date.now();
    const duration = (endTime - testResults.startTime) / 1000;
    const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
    
    console.log('\n' + '='.repeat(80));
    console.log('📊 CONSOLE TEST SUMMARY');
    console.log('='.repeat(80));
    
    const status = successRate > 95 ? '🟢 EXCELLENT' : 
                  successRate > 85 ? '🟢 HEALTHY' : 
                  successRate > 70 ? '🟡 GOOD' :
                  successRate > 60 ? '🟡 NEEDS ATTENTION' : '🔴 CRITICAL';
    
    console.log(`
⏱️  Execution Time: ${duration.toFixed(2)} seconds
🧪 Total Tests: ${testResults.total}
✅ Passed: ${testResults.passed} (${successRate}%)
⚠️  Warnings: ${testResults.warnings} (${((testResults.warnings / testResults.total) * 100).toFixed(1)}%)
❌ Failed: ${testResults.failed} (${((testResults.failed / testResults.total) * 100).toFixed(1)}%)

🎯 OVERALL SYSTEM HEALTH: ${status}
`);

    // Component status
    const apiPassed = testResults.tests.filter(t => t.name.includes('API:') && t.status === 'pass').length;
    const uiPassed = testResults.tests.filter(t => t.name.includes('UI:') && t.status === 'pass').length;
    const envPassed = testResults.tests.filter(t => t.name.includes('Environment:') && t.status === 'pass').length;

    console.log(`📋 COMPONENT STATUS:
✅ Environment: ${envPassed}/5 tests passed
✅ Backend APIs: ${apiPassed}/3 tests passed  
✅ Frontend UI: ${uiPassed}/6 tests passed
✅ Data Validation: ${testResults.tests.filter(t => t.name.includes('Validation:') && t.status === 'pass').length}/3 tests passed`);

    if (testResults.failed > 0) {
        console.log('\n🚨 FAILED TESTS:');
        testResults.tests.filter(t => t.status === 'fail').forEach(test => {
            console.log(`❌ ${test.name}: ${test.details.info}`);
        });
    }

    if (testResults.warnings > 0) {
        console.log('\n⚠️  WARNINGS:');
        testResults.tests.filter(t => t.status === 'warning').forEach(test => {
            console.log(`⚠️  ${test.name}: ${test.details.info}`);
        });
    }

    console.log(`
💾 RESULTS SAVED TO CONSOLE
============================
Test results are available in the testResults variable.
Access with: console.log(testResults);

🔄 TO RE-RUN TESTS
==================
Call: runConsoleTests()

🎉 CONCLUSION
=============
${successRate > 85 ? 
  'Your AssetHunterPro application is working excellently!' : 
  'Please address the failed tests and warnings above.'}
`);

    // Save to localStorage
    localStorage.setItem('assetHunterPro_consoleTestResults', JSON.stringify({
        timestamp: new Date().toISOString(),
        summary: { successRate: parseFloat(successRate), status },
        results: testResults
    }));
}

// Make functions available globally
window.runConsoleTests = runConsoleTests;
window.testResults = testResults;

// Auto-run tests
console.log('🚀 Starting console tests in 2 seconds...');
setTimeout(() => {
    runConsoleTests();
}, 2000);
