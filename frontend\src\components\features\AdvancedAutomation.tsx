import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Zap, 
  Workflow, 
  Mail, 
  PlayCircle,
  PauseCircle,
  Settings,
  BarChart3,
  Clock,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  Users,
  Calendar,
  Filter,
  Globe,
  Cpu,
  MessageSquare,
  Send,
  Target,
  Repeat,
  Bot
} from 'lucide-react';
import type { AdvancedAutomation, WorkflowSequence, EmailCampaign } from '@/types/automation';
import { usePricingLogic } from '@/hooks/usePricingLogic';

interface AdvancedAutomationProps {
  onUpgrade?: () => void;
}

export default function AdvancedAutomation({ onUpgrade }: AdvancedAutomationProps) {
  const { currentPlan, subscription } = usePricingLogic();
  const [activeTab, setActiveTab] = useState('overview');
  const [automationData, setAutomationData] = useState<AdvancedAutomation | null>(null);

  // Mock data - would come from API in real implementation
  useEffect(() => {
    const mockAutomationData: Partial<AdvancedAutomation> = {
      performance_metrics: {
        overall_performance: {
          total_automations: 47,
          active_automations: 42,
          total_executions_today: 1847,
          success_rate_percentage: 94.7,
          time_saved_hours: 127.5,
          cost_savings_dollars: 15420,
          error_rate_percentage: 2.3
        },
        workflow_performance: [
          {
            workflow_id: 'wf_001',
            workflow_name: 'New Claim Processing',
            executions_count: 234,
            success_rate: 96.8,
            average_duration: 4.2,
            error_rate: 3.2,
            performance_score: 94.1
          },
          {
            workflow_id: 'wf_002', 
            workflow_name: 'Follow-up Sequence',
            executions_count: 789,
            success_rate: 92.3,
            average_duration: 12.7,
            error_rate: 7.7,
            performance_score: 87.6
          },
          {
            workflow_id: 'wf_003',
            workflow_name: 'Document Collection',
            executions_count: 156,
            success_rate: 98.1,
            average_duration: 2.8,
            error_rate: 1.9,
            performance_score: 96.4
          }
        ],
        email_performance: {
          total_campaigns: 23,
          emails_sent: 12847,
          overall_open_rate: 34.7,
          overall_click_rate: 8.9,
          conversion_rate: 3.2,
          unsubscribe_rate: 0.8,
          revenue_generated: 47820
        },
        trigger_performance: {
          total_triggers: 67,
          active_triggers: 61,
          trigger_executions: 2341,
          average_response_time: 0.3,
          trigger_success_rate: 97.2,
          most_active_triggers: ['Claim Status Change', 'New Contact Added', 'Document Uploaded']
        },
        roi_metrics: {
          setup_cost: 25000,
          maintenance_cost: 5000,
          time_savings_value: 180000,
          efficiency_gains: 89,
          revenue_impact: 247000,
          cost_per_execution: 0.85,
          roi_percentage: 312
        }
      }
    };

    setAutomationData(mockAutomationData as AdvancedAutomation);
  }, []);

  const hasAccess = currentPlan && ['silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasAdvancedAutomation = currentPlan && ['gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasTeamAutomation = currentPlan && ['topaz', 'ruby', 'diamond'].includes(currentPlan.id);

  if (!hasAccess) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <Zap className="h-16 w-16 mx-auto text-gray-400" />
          <h3 className="text-xl font-semibold">Advanced Automation</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Powerful workflow sequences, email automation, and intelligent triggers to streamline your asset recovery operations.
          </p>
          <div className="bg-gradient-to-r from-purple-50 to-blue-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 mb-3">
              <strong>Included in Silver and above:</strong>
            </p>
            <ul className="text-sm text-gray-600 space-y-1 text-left max-w-md mx-auto">
              <li>• Basic workflow automation</li>
              <li>• Email sequence automation</li>
              <li>• Trigger-based actions</li>
              <li>• Performance analytics</li>
              <li>• Template library</li>
            </ul>
          </div>
          <Button onClick={onUpgrade} className="bg-purple-600 hover:bg-purple-700">
            Upgrade to Access Automation
          </Button>
        </div>
      </Card>
    );
  }

  const getStatusColor = (rate: number) => {
    if (rate >= 95) return 'text-green-600';
    if (rate >= 85) return 'text-blue-600';
    if (rate >= 75) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getPerformanceColor = (score: number) => {
    if (score >= 90) return 'bg-green-500';
    if (score >= 80) return 'bg-blue-500';
    if (score >= 70) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  return (
    <div className="space-y-6">
      {/* Automation Overview Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-6 w-6 text-purple-600" />
              <CardTitle>Advanced Automation Dashboard</CardTitle>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-500">Success Rate</div>
                <div className={`text-sm font-semibold ${getStatusColor(automationData?.performance_metrics?.overall_performance?.success_rate_percentage || 0)}`}>
                  {automationData?.performance_metrics?.overall_performance?.success_rate_percentage || 0}%
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">Time Saved Today</div>
                <div className="text-sm font-semibold text-green-600">
                  {automationData?.performance_metrics?.overall_performance?.time_saved_hours || 0}h
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Workflow className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-gray-500">Active Workflows</div>
                  <div className="text-xl font-semibold">{automationData?.performance_metrics?.overall_performance?.active_automations || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <PlayCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-gray-500">Executions Today</div>
                  <div className="text-xl font-semibold">{automationData?.performance_metrics?.overall_performance?.total_executions_today || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-gray-500">Emails Sent</div>
                  <div className="text-xl font-semibold">{automationData?.performance_metrics?.email_performance?.emails_sent || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-gray-500">Cost Savings</div>
                  <div className="text-xl font-semibold text-green-600">
                    ${(automationData?.performance_metrics?.overall_performance?.cost_savings_dollars || 0).toLocaleString()}
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="workflows">Workflows</TabsTrigger>
              <TabsTrigger value="email">Email Auto</TabsTrigger>
              <TabsTrigger value="triggers">Triggers</TabsTrigger>
              <TabsTrigger value="analytics">Analytics</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Automation Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Overall Success Rate</span>
                      <span className="text-sm text-gray-600">
                        {automationData?.performance_metrics?.overall_performance?.success_rate_percentage || 0}%
                      </span>
                    </div>
                    <Progress 
                      value={automationData?.performance_metrics?.overall_performance?.success_rate_percentage || 0} 
                      className="h-2 bg-green-500"
                    />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Email Open Rate</span>
                      <span className="text-sm text-gray-600">
                        {automationData?.performance_metrics?.email_performance?.overall_open_rate || 0}%
                      </span>
                    </div>
                    <Progress 
                      value={automationData?.performance_metrics?.email_performance?.overall_open_rate || 0} 
                      className="h-2 bg-blue-500"
                    />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Trigger Response Time</span>
                      <span className="text-sm text-gray-600">
                        {automationData?.performance_metrics?.trigger_performance?.average_response_time || 0}s
                      </span>
                    </div>
                    <Progress value={95} className="h-2 bg-purple-500" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Automation Activity</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">247 follow-up emails sent</span>
                      </div>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Workflow className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">New claim workflow triggered</span>
                      </div>
                      <span className="text-xs text-gray-500">3 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Bot className="h-4 w-4 text-purple-600" />
                        <span className="text-sm">Document automation completed</span>
                      </div>
                      <span className="text-xs text-gray-500">5 hours ago</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="workflows" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Workflow Sequences</h3>
                <div className="flex gap-2">
                  {!hasAdvancedAutomation && (
                    <Badge variant="outline" className="text-orange-600">
                      Advanced workflows require Gold+ plan
                    </Badge>
                  )}
                  <Button size="sm" variant="outline">
                    <Settings className="h-4 w-4 mr-2" />
                    Manage Workflows
                  </Button>
                </div>
              </div>
              
              <div className="grid grid-cols-1 gap-4">
                {automationData?.performance_metrics?.workflow_performance?.map((workflow, index) => (
                  <Card key={index} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
                          <Workflow className="h-5 w-5 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">{workflow.workflow_name}</h4>
                          <p className="text-sm text-gray-600">{workflow.executions_count} executions</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <Badge variant={workflow.success_rate >= 95 ? 'default' : 'secondary'}>
                          {workflow.success_rate}% Success
                        </Badge>
                        <Button size="sm" variant="ghost">
                          <PlayCircle className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-gray-500">Success Rate:</span>
                        <div className={`font-medium ${getStatusColor(workflow.success_rate)}`}>
                          {workflow.success_rate}%
                        </div>
                      </div>
                      <div>
                        <span className="text-gray-500">Avg Duration:</span>
                        <div className="font-medium">{workflow.average_duration}min</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Error Rate:</span>
                        <div className="font-medium text-red-600">{workflow.error_rate}%</div>
                      </div>
                      <div>
                        <span className="text-gray-500">Performance:</span>
                        <div className="flex items-center gap-2">
                          <div className="w-16 bg-gray-200 rounded-full h-2">
                            <div 
                              className={`h-2 rounded-full ${getPerformanceColor(workflow.performance_score)}`}
                              style={{ width: `${workflow.performance_score}%` }}
                            ></div>
                          </div>
                          <span className="text-xs">{workflow.performance_score}</span>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Workflow Templates</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center gap-2 mb-2">
                        <Target className="h-5 w-5 text-green-600" />
                        <h4 className="font-medium">Lead Nurturing</h4>
                      </div>
                      <p className="text-sm text-gray-600">
                        Automated sequence for new contact follow-up and engagement
                      </p>
                      <Badge variant="outline" className="mt-2">Popular</Badge>
                    </div>
                    
                    <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center gap-2 mb-2">
                        <Repeat className="h-5 w-5 text-blue-600" />
                        <h4 className="font-medium">Claim Processing</h4>
                      </div>
                      <p className="text-sm text-gray-600">
                        End-to-end automation for new claim intake and verification
                      </p>
                      <Badge variant="outline" className="mt-2">Recommended</Badge>
                    </div>
                    
                    <div className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center gap-2 mb-2">
                        <Calendar className="h-5 w-5 text-purple-600" />
                        <h4 className="font-medium">Compliance Reminder</h4>
                      </div>
                      <p className="text-sm text-gray-600">
                        Scheduled compliance checks and deadline notifications
                      </p>
                      <Badge variant="outline" className="mt-2">Enterprise</Badge>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="email" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Email Automation</h3>
                <Button size="sm" variant="outline">
                  <Send className="h-4 w-4 mr-2" />
                  Create Campaign
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <h4 className="font-semibold">Total Campaigns</h4>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-1">
                    {automationData?.performance_metrics?.email_performance?.total_campaigns || 0}
                  </div>
                  <p className="text-sm text-gray-600">Active email automations</p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <BarChart3 className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold">Open Rate</h4>
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-1">
                    {automationData?.performance_metrics?.email_performance?.overall_open_rate || 0}%
                  </div>
                  <p className="text-sm text-gray-600">Above industry average</p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                    <h4 className="font-semibold">Revenue Generated</h4>
                  </div>
                  <div className="text-2xl font-bold text-purple-600 mb-1">
                    ${(automationData?.performance_metrics?.email_performance?.revenue_generated || 0).toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">This quarter</p>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Email Campaign Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Welcome Series</h4>
                          <p className="text-sm text-gray-600">New contact onboarding</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">42.3% Open Rate</div>
                        <div className="text-xs text-gray-500">1,247 sent</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <Repeat className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Follow-up Sequence</h4>
                          <p className="text-sm text-gray-600">Claim status updates</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">38.7% Open Rate</div>
                        <div className="text-xs text-gray-500">3,421 sent</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                          <Calendar className="h-4 w-4 text-purple-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">Monthly Newsletter</h4>
                          <p className="text-sm text-gray-600">Industry updates and tips</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">31.2% Open Rate</div>
                        <div className="text-xs text-gray-500">8,179 sent</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="triggers" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Trigger Engine</h3>
                <Button size="sm" variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Create Trigger
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Cpu className="h-5 w-5" />
                      Active Triggers
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Total Triggers</span>
                      <span className="text-sm font-medium">
                        {automationData?.performance_metrics?.trigger_performance?.total_triggers || 0}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Active</span>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        {automationData?.performance_metrics?.trigger_performance?.active_triggers || 0}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Success Rate</span>
                      <span className="text-sm font-medium text-green-600">
                        {automationData?.performance_metrics?.trigger_performance?.trigger_success_rate || 0}%
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Avg Response Time</span>
                      <span className="text-sm font-medium">
                        {automationData?.performance_metrics?.trigger_performance?.average_response_time || 0}s
                      </span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Most Active Triggers</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {automationData?.performance_metrics?.trigger_performance?.most_active_triggers?.map((trigger, index) => (
                      <div key={index} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm font-medium">{trigger}</span>
                        <Badge variant="outline">Active</Badge>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Trigger Events Today</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3 p-3 bg-green-50 rounded-lg">
                      <Clock className="h-4 w-4 text-green-600" />
                      <div className="flex-1">
                        <div className="font-medium">Claim Status Change</div>
                        <div className="text-sm text-gray-600">Triggered follow-up email sequence</div>
                      </div>
                      <span className="text-xs text-gray-500">2 min ago</span>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                      <Users className="h-4 w-4 text-blue-600" />
                      <div className="flex-1">
                        <div className="font-medium">New Contact Added</div>
                        <div className="text-sm text-gray-600">Started welcome workflow</div>
                      </div>
                      <span className="text-xs text-gray-500">15 min ago</span>
                    </div>
                    
                    <div className="flex items-center gap-3 p-3 bg-purple-50 rounded-lg">
                      <Globe className="h-4 w-4 text-purple-600" />
                      <div className="flex-1">
                        <div className="font-medium">Document Uploaded</div>
                        <div className="text-sm text-gray-600">Triggered document processing</div>
                      </div>
                      <span className="text-xs text-gray-500">32 min ago</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="analytics" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Automation Analytics</h3>
                <Button size="sm" variant="outline">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  View Detailed Report
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card className="p-4 text-center">
                  <Clock className="h-8 w-8 mx-auto text-blue-600 mb-2" />
                  <div className="text-2xl font-bold text-blue-600">
                    {automationData?.performance_metrics?.overall_performance?.time_saved_hours || 0}h
                  </div>
                  <div className="text-sm text-gray-600">Time Saved Today</div>
                </Card>
                
                <Card className="p-4 text-center">
                  <TrendingUp className="h-8 w-8 mx-auto text-green-600 mb-2" />
                  <div className="text-2xl font-bold text-green-600">
                    ${(automationData?.performance_metrics?.overall_performance?.cost_savings_dollars || 0).toLocaleString()}
                  </div>
                  <div className="text-sm text-gray-600">Cost Savings</div>
                </Card>
                
                <Card className="p-4 text-center">
                  <CheckCircle className="h-8 w-8 mx-auto text-purple-600 mb-2" />
                  <div className="text-2xl font-bold text-purple-600">
                    {automationData?.performance_metrics?.overall_performance?.success_rate_percentage || 0}%
                  </div>
                  <div className="text-sm text-gray-600">Success Rate</div>
                </Card>
                
                <Card className="p-4 text-center">
                  <AlertTriangle className="h-8 w-8 mx-auto text-orange-600 mb-2" />
                  <div className="text-2xl font-bold text-orange-600">
                    {automationData?.performance_metrics?.overall_performance?.error_rate_percentage || 0}%
                  </div>
                  <div className="text-sm text-gray-600">Error Rate</div>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">ROI Analysis</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    <div className="text-center">
                      <div className="text-3xl font-bold text-green-600">312%</div>
                      <div className="text-sm text-gray-600">Return on Investment</div>
                      <div className="text-xs text-gray-500 mt-1">vs. manual processes</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-blue-600">$47K</div>
                      <div className="text-sm text-gray-600">Monthly Savings</div>
                      <div className="text-xs text-gray-500 mt-1">operational efficiency</div>
                    </div>
                    <div className="text-center">
                      <div className="text-3xl font-bold text-purple-600">89%</div>
                      <div className="text-sm text-gray-600">Process Improvement</div>
                      <div className="text-xs text-gray-500 mt-1">accuracy increase</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upgrade Prompt for Limited Features */}
      {!hasAdvancedAutomation && (
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                  <Zap className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Unlock Advanced Automation</h3>
                  <p className="text-sm text-gray-600">
                    Upgrade to Gold plan for complex workflows, AI-powered triggers, and advanced email automation.
                  </p>
                </div>
              </div>
              <Button onClick={onUpgrade} className="bg-purple-600 hover:bg-purple-700">
                Upgrade to Gold
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 