import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  X, 
  Clock, 
  FileText, 
  User, 
  Phone, 
  Mail,
  DollarSign,
  Filter,
  ArrowRight,
  Loader2
} from 'lucide-react';
import { QuickSearchResult } from '@/types/dashboard';

interface QuickSearchBarProps {
  className?: string;
}

// Mock search data
const generateMockSearchResults = (query: string): QuickSearchResult[] => {
  if (!query || query.length < 2) return [];

  const mockData: QuickSearchResult[] = [
    {
      id: '1',
      type: 'claimant',
      title: '<PERSON>',
      subtitle: 'Insurance Settlement - $125,000',
      value: 125000,
      status: 'new',
      lastUpdate: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
      relevance: 0.95
    },
    {
      id: '2',
      type: 'claim',
      title: 'CLM-2024-001',
      subtitle: 'High-value insurance claim pending contact',
      value: 125000,
      status: 'pending',
      lastUpdate: new Date(Date.now() - 1 * 60 * 60 * 1000),
      relevance: 0.92
    },
    {
      id: '3',
      type: 'claimant',
      title: '<PERSON> <PERSON>',
      subtitle: 'Bank Account - $89,000',
      value: 89000,
      status: 'contacted',
      lastUpdate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      relevance: 0.88
    },
    {
      id: '4',
      type: 'contact',
      title: '<EMAIL>',
      subtitle: 'Verified email - Last contacted 2 days ago',
      status: 'verified',
      lastUpdate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
      relevance: 0.85
    },
    {
      id: '5',
      type: 'document',
      title: 'Settlement Agreement - Williams',
      subtitle: 'PDF document uploaded yesterday',
      status: 'recent',
      lastUpdate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
      relevance: 0.82
    },
    {
      id: '6',
      type: 'claimant',
      title: 'Emily Rodriguez',
      subtitle: 'Securities - $67,000',
      value: 67000,
      status: 'qualified',
      lastUpdate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
      relevance: 0.78
    },
    {
      id: '7',
      type: 'contact',
      title: '(*************',
      subtitle: 'Verified phone - Last attempt failed',
      status: 'failed',
      lastUpdate: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
      relevance: 0.75
    }
  ];

  // Filter results based on query
  return mockData
    .filter(item => 
      item.title.toLowerCase().includes(query.toLowerCase()) ||
      item.subtitle.toLowerCase().includes(query.toLowerCase())
    )
    .sort((a, b) => b.relevance - a.relevance)
    .slice(0, 6); // Limit to top 6 results
};

export const QuickSearchBar: React.FC<QuickSearchBarProps> = ({ className }) => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<QuickSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [recentSearches, setRecentSearches] = useState<string[]>([
    'Jessica Williams',
    'CLM-2024-001',
    'high value claims',
    'pending contacts'
  ]);

  const searchRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const resultsRef = useRef<HTMLDivElement>(null);

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (query.length >= 2) {
        setIsSearching(true);
        // Simulate API delay
        setTimeout(() => {
          const searchResults = generateMockSearchResults(query);
          setResults(searchResults);
          setIsSearching(false);
          setShowResults(true);
        }, 300);
      } else {
        setResults([]);
        setShowResults(false);
        setSelectedIndex(-1);
      }
    }, 300);

    return () => clearTimeout(timer);
  }, [query]);

  // Click outside to close
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
        setSelectedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!showResults) return;

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setSelectedIndex(prev => 
          prev < results.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setSelectedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (selectedIndex >= 0 && results[selectedIndex]) {
          handleResultSelect(results[selectedIndex]);
        }
        break;
      case 'Escape':
        setShowResults(false);
        setSelectedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };

  const handleResultSelect = (result: QuickSearchResult) => {
    // Add to recent searches if not already present
    setRecentSearches(prev => {
      const filtered = prev.filter(search => search !== result.title);
      return [result.title, ...filtered].slice(0, 4);
    });

    setQuery(result.title);
    setShowResults(false);
    setSelectedIndex(-1);
    
    // Here you would navigate to the selected result
    console.log('Selected result:', result);
  };

  const handleRecentSearchClick = (search: string) => {
    setQuery(search);
    inputRef.current?.focus();
  };

  const clearSearch = () => {
    setQuery('');
    setResults([]);
    setShowResults(false);
    setSelectedIndex(-1);
    inputRef.current?.focus();
  };

  const getResultIcon = (type: QuickSearchResult['type']) => {
    switch (type) {
      case 'claimant':
        return <User className="h-4 w-4" />;
      case 'claim':
        return <FileText className="h-4 w-4" />;
      case 'contact':
        return query.includes('@') ? <Mail className="h-4 w-4" /> : <Phone className="h-4 w-4" />;
      case 'document':
        return <FileText className="h-4 w-4" />;
      default:
        return <Search className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status?: string) => {
    switch (status) {
      case 'new':
        return 'bg-blue-100 text-blue-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'contacted':
        return 'bg-green-100 text-green-800';
      case 'verified':
        return 'bg-green-100 text-green-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'qualified':
        return 'bg-purple-100 text-purple-800';
      case 'recent':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatValue = (value?: number): string => {
    if (!value) return '';
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `$${(value / 1000).toFixed(0)}K`;
    return `$${value.toLocaleString()}`;
  };

  const formatLastUpdate = (date?: Date): string => {
    if (!date) return '';
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    return 'Just now';
  };

  return (
    <div ref={searchRef} className={`relative ${className}`}>
      <Card>
        <CardContent className="p-3">
          <div className="relative">
            {/* Search Input */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                ref={inputRef}
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                onKeyDown={handleKeyDown}
                onFocus={() => query.length >= 2 && setShowResults(true)}
                placeholder="Search claims, claimants, contacts..."
                className="w-full pl-10 pr-10 py-2 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              {query && (
                <button
                  onClick={clearSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 hover:text-gray-600"
                >
                  <X className="h-4 w-4" />
                </button>
              )}
              {isSearching && (
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                  <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
                </div>
              )}
            </div>

            {/* Recent Searches */}
            {!query && (
              <div className="mt-3">
                <div className="text-xs text-gray-500 mb-2 flex items-center gap-2">
                  <Clock className="h-3 w-3" />
                  Recent Searches
                </div>
                <div className="flex flex-wrap gap-1">
                  {recentSearches.map((search, index) => (
                    <Button
                      key={index}
                      variant="ghost"
                      size="sm"
                      onClick={() => handleRecentSearchClick(search)}
                      className="h-6 px-2 text-xs text-gray-600 hover:text-gray-900"
                    >
                      {search}
                    </Button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Search Results */}
      {showResults && (results.length > 0 || isSearching) && (
        <Card className="absolute top-full left-0 right-0 mt-1 z-50 shadow-lg border-gray-200">
          <CardContent className="p-2">
            <div ref={resultsRef} className="max-h-96 overflow-y-auto">
              {isSearching ? (
                <div className="flex items-center justify-center py-8 text-gray-500">
                  <Loader2 className="h-5 w-5 animate-spin mr-2" />
                  Searching...
                </div>
              ) : results.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Search className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No results found for "{query}"</p>
                  <p className="text-xs text-gray-400">Try different keywords or check spelling</p>
                </div>
              ) : (
                <div className="space-y-1">
                  {results.map((result, index) => (
                    <div
                      key={result.id}
                      onClick={() => handleResultSelect(result)}
                      className={`p-3 rounded-lg cursor-pointer transition-all ${
                        index === selectedIndex 
                          ? 'bg-blue-50 border border-blue-200' 
                          : 'hover:bg-gray-50 border border-transparent'
                      }`}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 min-w-0 flex-1">
                          <div className="text-gray-500 flex-shrink-0">
                            {getResultIcon(result.type)}
                          </div>
                          <div className="min-w-0 flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <h4 className="text-sm font-medium text-gray-900 truncate">
                                {result.title}
                              </h4>
                              {result.status && (
                                <Badge 
                                  variant="outline" 
                                  className={`text-xs ${getStatusColor(result.status)}`}
                                >
                                  {result.status}
                                </Badge>
                              )}
                              {result.value && (
                                <div className="flex items-center gap-1 text-xs text-green-600">
                                  <DollarSign className="h-3 w-3" />
                                  {formatValue(result.value)}
                                </div>
                              )}
                            </div>
                            <p className="text-xs text-gray-600 truncate">
                              {result.subtitle}
                            </p>
                            {result.lastUpdate && (
                              <p className="text-xs text-gray-400 mt-1">
                                {formatLastUpdate(result.lastUpdate)}
                              </p>
                            )}
                          </div>
                        </div>
                        <ArrowRight className="h-4 w-4 text-gray-400 flex-shrink-0 ml-2" />
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {results.length > 0 && (
              <div className="border-t border-gray-100 mt-2 pt-2">
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    {results.length} result{results.length !== 1 ? 's' : ''} found
                  </span>
                  <div className="flex items-center gap-2">
                    <span>↑↓ Navigate</span>
                    <span>↵ Select</span>
                    <span>⎋ Close</span>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 