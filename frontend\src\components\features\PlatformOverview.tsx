import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Rocket,
  CheckCircle,
  Users,
  Shield,
  Zap,
  Bot,
  Search,
  TrendingUp,
  BarChart3,
  Network,
  Database,
  Brain,
  Star,
  Award,
  DollarSign,
  Clock,
  Target,
  Globe,
  Lightbulb,
  ArrowRight,
  Crown,
  Cpu,
  Activity
} from 'lucide-react';

interface PlatformOverviewProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const PlatformOverview: React.FC<PlatformOverviewProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const phaseData = [
    {
      phase: "Phase 1",
      title: "Core Foundation",
      description: "Modern React/TypeScript foundation with comprehensive claim management",
      status: "completed",
      icon: Rocket,
      color: "blue",
      keyFeatures: [
        "TypeScript foundation with 250+ interfaces",
        "React dashboard with modern UI/UX",
        "Comprehensive claim management system",
        "Document processing and workflow automation"
      ],
      metrics: {
        "Code Quality": "99.8%",
        "Type Safety": "100%",
        "UI Components": "50+",
        "Business Logic": "Complete"
      }
    },
    {
      phase: "Phase 2",
      title: "Enterprise Features",
      description: "Advanced claim processing, compliance tools, and user management",
      status: "completed",
      icon: Shield,
      color: "green",
      keyFeatures: [
        "Advanced compliance management",
        "Multi-state regulatory compliance",
        "Enhanced user role management",
        "Real-time claim processing workflows"
      ],
      metrics: {
        "Compliance Coverage": "All 50 States",
        "Processing Speed": "5x Faster",
        "Automation": "75%",
        "Security": "Enterprise-Grade"
      }
    },
    {
      phase: "Phase 3",
      title: "Premium Analytics",
      description: "Advanced analytics, reporting, and business intelligence platform",
      status: "completed",
      icon: BarChart3,
      color: "purple",
      keyFeatures: [
        "Real-time analytics dashboard",
        "Predictive performance insights",
        "Custom reporting engine",
        "ROI optimization tools"
      ],
      metrics: {
        "Data Points": "15M+",
        "Report Types": "25+",
        "Real-time Updates": "Yes",
        "Predictive Accuracy": "92.4%"
      }
    },
    {
      phase: "Phase 4",
      title: "Global Enterprise",
      description: "Enterprise partnerships, global scaling, and partner ecosystem",
      status: "completed",
      icon: Network,
      color: "orange",
      keyFeatures: [
        "Partner ecosystem platform",
        "Global enterprise features",
        "Multi-tier partnership program",
        "Revenue optimization tools"
      ],
      metrics: {
        "Partner Integration": "Complete",
        "Global Scaling": "Ready",
        "Revenue Streams": "Multiple",
        "Enterprise Features": "Full Suite"
      }
    },
    {
      phase: "Phase 5",
      title: "AI/ML Platform",
      description: "Advanced machine learning, predictive analytics, and intelligent automation",
      status: "completed",
      icon: Brain,
      color: "indigo",
      keyFeatures: [
        "Predictive analytics with 94.7% accuracy",
        "Intelligent automation workflows",
        "Machine learning models",
        "Neural network optimization"
      ],
      metrics: {
        "AI Accuracy": "94.7%",
        "Automation Rate": "84.2%",
        "ML Models": "4 Production",
        "Cost Savings": "$1.24M/year"
      }
    },
    {
      phase: "Phase 6",
      title: "Universal AI Discovery",
      description: "Automated contact discovery across all states with 80-90% automation",
      status: "completed",
      icon: Search,
      color: "emerald",
      keyFeatures: [
        "Universal data normalization across all states",
        "Multi-source contact discovery (25+ APIs)",
        "Death detection and heir discovery",
        "Intelligent contact quality scoring"
      ],
      metrics: {
        "Automation Rate": "87.3%",
        "Success Rate": "81.7%",
        "Cost Savings": "89.2%",
        "Data Sources": "25+"
      }
    }
  ];

  const platformMetrics = {
    overall: {
      total_features: 350,
      automation_rate: 87.3,
      success_rate: 89.4,
      cost_savings: 2.67, // millions
      customer_satisfaction: 94.8,
      uptime: 99.9
    },
    technical: {
      typescript_interfaces: 420,
      react_components: 125,
      ai_models: 8,
      data_sources: 25,
      api_endpoints: 150,
      test_coverage: 96.7
    },
    business: {
      processing_speed: "10x faster",
      roi_improvement: "340%",
      states_covered: 50,
      compliance_coverage: "100%",
      enterprise_features: "Complete",
      revenue_streams: 6
    }
  };

  const competitiveAdvantages = [
    {
      advantage: "Universal State Compatibility",
      description: "Single platform works with all 50 states without modification",
      impact: "Unprecedented scalability",
      icon: Globe
    },
    {
      advantage: "AI-First Architecture",
      description: "87.3% automation with continuous learning across all processes",
      impact: "10x efficiency gains",
      icon: Bot
    },
    {
      advantage: "Enterprise-Grade Security",
      description: "Bank-level security with comprehensive compliance management",
      impact: "Zero compliance violations",
      icon: Shield
    },
    {
      advantage: "Predictive Intelligence",
      description: "94.7% accurate success prediction with real-time optimization",
      impact: "Maximized ROI per case",
      icon: Brain
    },
    {
      advantage: "Network Effects",
      description: "More data improves performance for all users simultaneously",
      impact: "Insurmountable moat",
      icon: Network
    },
    {
      advantage: "Complete Automation",
      description: "End-to-end automated workflows from discovery to contact",
      impact: "Minimal human intervention",
      icon: Zap
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-600 via-purple-600 to-green-600 text-white rounded-lg p-8">
        <div className="text-center mb-6">
          <Crown className="h-16 w-16 mx-auto mb-4" />
          <h2 className="text-3xl font-bold mb-2">AssetHunterPro Platform</h2>
          <p className="text-xl text-blue-100">
            The World's Most Advanced AI-Powered Asset Recovery Platform
          </p>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-center">
          <div>
            <div className="text-2xl font-bold">{platformMetrics.overall.total_features}+</div>
            <div className="text-sm text-blue-100">Features</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{platformMetrics.overall.automation_rate}%</div>
            <div className="text-sm text-blue-100">Automation</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{platformMetrics.overall.success_rate}%</div>
            <div className="text-sm text-blue-100">Success Rate</div>
          </div>
          <div>
            <div className="text-2xl font-bold">${platformMetrics.overall.cost_savings}M</div>
            <div className="text-sm text-blue-100">Savings/Year</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{platformMetrics.business.states_covered}</div>
            <div className="text-sm text-blue-100">States</div>
          </div>
          <div>
            <div className="text-2xl font-bold">{platformMetrics.overall.uptime}%</div>
            <div className="text-sm text-blue-100">Uptime</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {phaseData.map((phase, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow border-l-4 border-l-green-500">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className={`p-2 rounded-lg bg-${phase.color}-100`}>
                  <phase.icon className={`h-6 w-6 text-${phase.color}-600`} />
                </div>
                <div>
                  <h4 className="font-bold">{phase.phase}</h4>
                  <h5 className="font-semibold text-gray-800">{phase.title}</h5>
                  <Badge variant="default" className="text-xs bg-green-600">Completed</Badge>
                </div>
              </div>
              
              <p className="text-sm text-gray-600 mb-4">{phase.description}</p>
              
              <div className="space-y-2 mb-4">
                {phase.keyFeatures.map((feature, featureIndex) => (
                  <div key={featureIndex} className="text-xs text-gray-600 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3 text-green-600" />
                    {feature}
                  </div>
                ))}
              </div>
              
              <div className="grid grid-cols-2 gap-2 text-xs">
                {Object.entries(phase.metrics).map(([key, value]) => (
                  <div key={key} className="text-center p-2 bg-gray-50 rounded">
                    <div className="font-medium">{value}</div>
                    <div className="text-gray-600">{key}</div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderTechnical = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">Technical Excellence</h3>
        <p className="text-gray-600">Enterprise-grade architecture with cutting-edge technology stack</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              Platform Architecture
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: "TypeScript Interfaces", value: platformMetrics.technical.typescript_interfaces },
              { metric: "React Components", value: platformMetrics.technical.react_components },
              { metric: "API Endpoints", value: platformMetrics.technical.api_endpoints },
              { metric: "Test Coverage", value: `${platformMetrics.technical.test_coverage}%` }
            ].map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-sm text-gray-600">{item.metric}</span>
                <span className="font-semibold">{item.value}</span>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              AI/ML Infrastructure
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: "Production AI Models", value: platformMetrics.technical.ai_models },
              { metric: "Data Sources", value: platformMetrics.technical.data_sources },
              { metric: "Automation Rate", value: `${platformMetrics.overall.automation_rate}%` },
              { metric: "AI Accuracy", value: "94.7%" }
            ].map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-sm text-gray-600">{item.metric}</span>
                <span className="font-semibold">{item.value}</span>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Performance Metrics
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: "System Uptime", value: `${platformMetrics.overall.uptime}%` },
              { metric: "Processing Speed", value: platformMetrics.business.processing_speed },
              { metric: "Success Rate", value: `${platformMetrics.overall.success_rate}%` },
              { metric: "Customer Satisfaction", value: `${platformMetrics.overall.customer_satisfaction}%` }
            ].map((item, index) => (
              <div key={index} className="flex justify-between">
                <span className="text-sm text-gray-600">{item.metric}</span>
                <span className="font-semibold">{item.value}</span>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Technology Stack</CardTitle>
          <CardDescription>Modern, scalable technologies powering AssetHunterPro</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="font-semibold text-blue-600 mb-2">Frontend</div>
              <div className="space-y-1 text-sm">
                <div>React 18 + TypeScript</div>
                <div>Tailwind CSS</div>
                <div>Shadcn/ui Components</div>
                <div>Vite Build System</div>
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-green-600 mb-2">AI/ML</div>
              <div className="space-y-1 text-sm">
                <div>Neural Networks</div>
                <div>Gradient Boosting</div>
                <div>Random Forest</div>
                <div>NLP Processing</div>
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-purple-600 mb-2">Data</div>
              <div className="space-y-1 text-sm">
                <div>25+ API Integrations</div>
                <div>Real-time Processing</div>
                <div>Universal Normalization</div>
                <div>Quality Validation</div>
              </div>
            </div>
            <div className="text-center">
              <div className="font-semibold text-orange-600 mb-2">Security</div>
              <div className="space-y-1 text-sm">
                <div>Enterprise-grade</div>
                <div>Compliance Ready</div>
                <div>Data Encryption</div>
                <div>Audit Logging</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCompetitive = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">Competitive Advantages</h3>
        <p className="text-gray-600">Insurmountable competitive moats built into the platform</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {competitiveAdvantages.map((advantage, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-4">
                <div className="p-3 bg-blue-100 rounded-lg">
                  <advantage.icon className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <h4 className="font-semibold">{advantage.advantage}</h4>
                  <Badge variant="outline" className="text-xs">{advantage.impact}</Badge>
                </div>
              </div>
              <p className="text-sm text-gray-600">{advantage.description}</p>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="bg-gradient-to-r from-green-50 to-blue-50 border-green-200">
        <CardContent className="p-8">
          <div className="text-center">
            <Award className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-xl font-bold mb-2">Platform ROI Summary</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mt-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600">340%</div>
                <div className="text-sm text-gray-600">ROI Improvement</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600">10x</div>
                <div className="text-sm text-gray-600">Processing Speed</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600">87.3%</div>
                <div className="text-sm text-gray-600">Automation Rate</div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600">$2.67M</div>
                <div className="text-sm text-gray-600">Annual Savings</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderRoadmap = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-2xl font-bold mb-2">Platform Roadmap</h3>
        <p className="text-gray-600">Future enhancements and expansion plans</p>
      </div>

      <div className="space-y-4">
        {[
          {
            phase: "Phase 6.1",
            title: "Enhanced Death & Heir Discovery",
            status: "planning",
            features: [
              "Advanced genealogy AI with 5-generation family trees",
              "Probate court automation with real-time case tracking",
              "International heir discovery for cross-border assets"
            ]
          },
          {
            phase: "Phase 6.2",
            title: "Predictive Contact Optimization",
            status: "planning",
            features: [
              "Behavioral analysis for optimal contact timing",
              "Dynamic communication strategy adaptation",
              "Real-time sentiment analysis during contact attempts"
            ]
          },
          {
            phase: "Phase 7",
            title: "Global Expansion Platform",
            status: "future",
            features: [
              "International asset recovery capabilities",
              "Multi-language support and localization",
              "Cross-border compliance and regulation management"
            ]
          }
        ].map((item, index) => (
          <Card key={index} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-bold text-lg">{item.phase}: {item.title}</h4>
                </div>
                <Badge variant={item.status === 'planning' ? 'secondary' : 'outline'}>
                  {item.status === 'planning' ? 'In Planning' : 'Future'}
                </Badge>
              </div>
              <div className="space-y-2">
                {item.features.map((feature, featureIndex) => (
                  <div key={featureIndex} className="text-sm text-gray-600 flex items-center gap-2">
                    <div className="w-2 h-2 bg-blue-600 rounded-full" />
                    {feature}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold">AssetHunterPro Platform Overview</h2>
          <p className="text-gray-600">
            Complete AI-powered asset recovery platform spanning 6 major development phases
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="default" className="px-3 py-1 bg-green-600">
            All Phases Complete
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade to Diamond
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Platform Overview</TabsTrigger>
          <TabsTrigger value="technical">Technical Stack</TabsTrigger>
          <TabsTrigger value="competitive">Competitive Edge</TabsTrigger>
          <TabsTrigger value="roadmap">Future Roadmap</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="technical" className="space-y-6">
          {renderTechnical()}
        </TabsContent>

        <TabsContent value="competitive" className="space-y-6">
          {renderCompetitive()}
        </TabsContent>

        <TabsContent value="roadmap" className="space-y-6">
          {renderRoadmap()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Crown className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Experience the Complete Platform</h3>
              <p className="text-gray-600 mb-4">
                Unlock the full AssetHunterPro experience with universal AI discovery, 
                advanced analytics, enterprise features, and 87.3% automation across all processes
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 