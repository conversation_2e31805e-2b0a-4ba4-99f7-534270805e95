import React, { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle 
} from '@/components/ui/dialog';
import { 
  Search, 
  FileText, 
  Users, 
  DollarSign, 
  BarChart3, 
  Settings, 
  Plus, 
  Upload, 
  Download,
  Moon,
  Sun,
  Zap,
  Home,
  Shield,
  Activity,
  Calendar,
  Bell,
  Filter,
  Command
} from 'lucide-react';

interface CommandAction {
  id: string;
  title: string;
  description: string;
  icon: React.ReactNode;
  keywords: string[];
  shortcut?: string;
  action: () => void;
  category: 'Navigation' | 'Actions' | 'Settings' | 'Data';
}

interface CommandPaletteProps {
  isOpen: boolean;
  onClose: () => void;
  onNavigate: (view: string) => void;
  onAction: (action: string) => void;
  onToggleTheme: () => void;
}

export const CommandPalette: React.FC<CommandPaletteProps> = ({
  isOpen,
  onClose,
  onNavigate,
  onAction,
  onToggleTheme
}) => {
  const [search, setSearch] = useState('');
  const [selectedIndex, setSelectedIndex] = useState(0);

  const commands: CommandAction[] = [
    // Navigation
    {
      id: 'nav-dashboard',
      title: 'Go to Dashboard',
      description: 'Navigate to the main dashboard',
      icon: <Home className="h-4 w-4" />,
      keywords: ['dashboard', 'home', 'main'],
      shortcut: 'g d',
      action: () => onNavigate('dashboard'),
      category: 'Navigation'
    },
    {
      id: 'nav-claims',
      title: 'Go to Claims',
      description: 'View and manage claims',
      icon: <FileText className="h-4 w-4" />,
      keywords: ['claims', 'cases', 'recovery'],
      shortcut: 'g c',
      action: () => onNavigate('claims'),
      category: 'Navigation'
    },
    {
      id: 'nav-analytics',
      title: 'Go to Analytics',
      description: 'View performance analytics',
      icon: <BarChart3 className="h-4 w-4" />,
      keywords: ['analytics', 'reports', 'metrics'],
      shortcut: 'g a',
      action: () => onNavigate('analytics'),
      category: 'Navigation'
    },
    {
      id: 'nav-users',
      title: 'Go to User Management',
      description: 'Manage users and permissions',
      icon: <Users className="h-4 w-4" />,
      keywords: ['users', 'team', 'permissions'],
      shortcut: 'g u',
      action: () => onNavigate('users'),
      category: 'Navigation'
    },
    {
      id: 'nav-settings',
      title: 'Go to Settings',
      description: 'System configuration',
      icon: <Settings className="h-4 w-4" />,
      keywords: ['settings', 'config', 'preferences'],
      shortcut: 'g s',
      action: () => onNavigate('settings'),
      category: 'Navigation'
    },

    // Actions
    {
      id: 'action-new-claim',
      title: 'Create New Claim',
      description: 'Start a new asset recovery claim',
      icon: <Plus className="h-4 w-4" />,
      keywords: ['new', 'create', 'claim', 'add'],
      shortcut: 'n c',
      action: () => onAction('new-claim'),
      category: 'Actions'
    },
    {
      id: 'action-bulk-import',
      title: 'Bulk Import Claims',
      description: 'Import multiple claims from CSV',
      icon: <Upload className="h-4 w-4" />,
      keywords: ['import', 'bulk', 'csv', 'upload'],
      shortcut: 'i c',
      action: () => onAction('bulk-import'),
      category: 'Actions'
    },
    {
      id: 'action-export-data',
      title: 'Export Data',
      description: 'Export claims and analytics data',
      icon: <Download className="h-4 w-4" />,
      keywords: ['export', 'download', 'backup'],
      shortcut: 'e d',
      action: () => onAction('export-data'),
      category: 'Actions'
    },
    {
      id: 'action-notifications',
      title: 'View Notifications',
      description: 'Check alerts and updates',
      icon: <Bell className="h-4 w-4" />,
      keywords: ['notifications', 'alerts', 'updates'],
      shortcut: 'n o',
      action: () => onAction('notifications'),
      category: 'Actions'
    },

    // Settings
    {
      id: 'setting-toggle-theme',
      title: 'Toggle Dark Mode',
      description: 'Switch between light and dark themes',
      icon: <Moon className="h-4 w-4" />,
      keywords: ['theme', 'dark', 'light', 'mode'],
      shortcut: 't d',
      action: onToggleTheme,
      category: 'Settings'
    },
    {
      id: 'setting-preferences',
      title: 'User Preferences',
      description: 'Customize your experience',
      icon: <Settings className="h-4 w-4" />,
      keywords: ['preferences', 'settings', 'customize'],
      action: () => onAction('preferences'),
      category: 'Settings'
    },

    // Data Actions
    {
      id: 'data-search',
      title: 'Global Search',
      description: 'Search across all data',
      icon: <Search className="h-4 w-4" />,
      keywords: ['search', 'find', 'lookup'],
      shortcut: '/',
      action: () => onAction('global-search'),
      category: 'Data'
    },
    {
      id: 'data-filter',
      title: 'Advanced Filters',
      description: 'Apply advanced filtering options',
      icon: <Filter className="h-4 w-4" />,
      keywords: ['filter', 'advanced', 'search'],
      shortcut: 'f a',
      action: () => onAction('advanced-filters'),
      category: 'Data'
    }
  ];

  const filteredCommands = commands.filter(command => 
    command.title.toLowerCase().includes(search.toLowerCase()) ||
    command.description.toLowerCase().includes(search.toLowerCase()) ||
    command.keywords.some(keyword => keyword.toLowerCase().includes(search.toLowerCase()))
  );

  const executeCommand = useCallback((command: CommandAction) => {
    command.action();
    onClose();
    setSearch('');
    setSelectedIndex(0);
  }, [onClose]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;

      switch (e.key) {
        case 'ArrowDown':
          e.preventDefault();
          setSelectedIndex(i => (i + 1) % filteredCommands.length);
          break;
        case 'ArrowUp':
          e.preventDefault();
          setSelectedIndex(i => i === 0 ? filteredCommands.length - 1 : i - 1);
          break;
        case 'Enter':
          e.preventDefault();
          if (filteredCommands[selectedIndex]) {
            executeCommand(filteredCommands[selectedIndex]);
          }
          break;
        case 'Escape':
          onClose();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, filteredCommands, selectedIndex, executeCommand, onClose]);

  useEffect(() => {
    if (isOpen) {
      setSelectedIndex(0);
    }
  }, [search, isOpen]);

  const groupedCommands = filteredCommands.reduce((acc, command) => {
    if (!acc[command.category]) {
      acc[command.category] = [];
    }
    acc[command.category].push(command);
    return acc;
  }, {} as Record<string, CommandAction[]>);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0 overflow-hidden">
        <DialogHeader className="px-4 pt-4 pb-2">
          <DialogTitle className="flex items-center gap-2">
            <Command className="h-5 w-5" />
            Command Palette
          </DialogTitle>
        </DialogHeader>
        
        <div className="px-4 pb-2">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Type a command or search..."
              value={search}
              onChange={(e) => setSearch(e.target.value)}
              className="pl-10"
              autoFocus
            />
          </div>
        </div>

        <div className="max-h-96 overflow-y-auto">
          {Object.entries(groupedCommands).map(([category, commands]) => (
            <div key={category} className="px-4 py-2">
              <h3 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                {category}
              </h3>
              <div className="space-y-1">
                {commands.map((command, index) => {
                  const globalIndex = filteredCommands.findIndex(c => c.id === command.id);
                  const isSelected = globalIndex === selectedIndex;
                  
                  return (
                    <div
                      key={command.id}
                      className={`flex items-center justify-between p-3 rounded-lg cursor-pointer transition-colors ${
                        isSelected 
                          ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800' 
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => executeCommand(command)}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`p-1.5 rounded ${
                          isSelected 
                            ? 'bg-blue-100 dark:bg-blue-800 text-blue-600 dark:text-blue-300' 
                            : 'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
                        }`}>
                          {command.icon}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{command.title}</div>
                          <div className="text-xs text-gray-500">{command.description}</div>
                        </div>
                      </div>
                      {command.shortcut && (
                        <Badge variant="outline" className="text-xs font-mono">
                          {command.shortcut}
                        </Badge>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>

        {filteredCommands.length === 0 && search && (
          <div className="px-4 py-8 text-center text-gray-500">
            <Search className="h-8 w-8 mx-auto mb-2" />
            <p>No commands found for "{search}"</p>
            <p className="text-xs mt-1">Try searching for navigation, actions, or settings</p>
          </div>
        )}

        <div className="px-4 py-2 border-t bg-gray-50 dark:bg-gray-800 text-xs text-gray-500">
          <div className="flex items-center justify-between">
            <span>Use ↑↓ to navigate, ↵ to select, esc to close</span>
            <div className="flex items-center gap-2">
              <Badge variant="outline" className="text-xs">Ctrl</Badge>
              <span>+</span>
              <Badge variant="outline" className="text-xs">K</Badge>
              <span>to open</span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 