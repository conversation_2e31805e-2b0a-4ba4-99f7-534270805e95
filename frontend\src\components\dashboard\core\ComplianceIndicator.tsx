import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Shield, 
  CheckCircle, 
  AlertTriangle, 
  XCircle, 
  Calendar, 
  FileText, 
  Eye, 
  Lock,
  Database,
  Users,
  AlertCircle,
  ChevronDown,
  ChevronUp,
  ExternalLink
} from 'lucide-react';
import { ComplianceStatus, ComplianceItem } from '@/types/dashboard';

interface ComplianceIndicatorProps {
  className?: string;
}

// Mock compliance data
const generateMockComplianceStatus = (): ComplianceStatus => ({
  overall: 'compliant',
  dataRetention: {
    status: 'pass',
    score: 98,
    lastCheck: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    issues: [],
    recommendations: ['Consider implementing automated archival for records older than 7 years']
  },
  privacyCompliance: {
    status: 'pass',
    score: 95,
    lastCheck: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    issues: [],
    recommendations: [
      'Update privacy policy to include new data collection methods',
      'Schedule quarterly privacy training for all staff'
    ]
  },
  auditTrail: {
    status: 'warning',
    score: 87,
    lastCheck: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    issues: [
      'Missing audit logs for 2 system maintenance sessions',
      'Incomplete user access tracking for guest users'
    ],
    recommendations: [
      'Enable comprehensive logging for all system maintenance',
      'Implement guest user activity tracking',
      'Schedule monthly audit log reviews'
    ]
  },
  accessControl: {
    status: 'pass',
    score: 100,
    lastCheck: new Date(Date.now() - 45 * 60 * 1000), // 45 minutes ago
    issues: [],
    recommendations: []
  },
  lastAudit: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 7 days ago
  nextAudit: new Date(Date.now() + 23 * 24 * 60 * 60 * 1000) // 23 days from now
});

export const ComplianceIndicator: React.FC<ComplianceIndicatorProps> = ({ className }) => {
  const [complianceData] = useState<ComplianceStatus>(generateMockComplianceStatus());
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);

  const getOverallStatusColor = (status: ComplianceStatus['overall']) => {
    switch (status) {
      case 'compliant':
        return {
          bg: 'bg-green-50 border-green-200',
          text: 'text-green-800',
          icon: 'text-green-600',
          badge: 'bg-green-100 text-green-800'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 border-yellow-200',
          text: 'text-yellow-800',
          icon: 'text-yellow-600',
          badge: 'bg-yellow-100 text-yellow-800'
        };
      case 'violation':
        return {
          bg: 'bg-red-50 border-red-200',
          text: 'text-red-800',
          icon: 'text-red-600',
          badge: 'bg-red-100 text-red-800'
        };
      default:
        return {
          bg: 'bg-gray-50 border-gray-200',
          text: 'text-gray-800',
          icon: 'text-gray-600',
          badge: 'bg-gray-100 text-gray-800'
        };
    }
  };

  const getItemStatusIcon = (status: ComplianceItem['status']) => {
    switch (status) {
      case 'pass':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      case 'fail':
        return <XCircle className="h-4 w-4 text-red-600" />;
      default:
        return <AlertCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  const getItemStatusColor = (status: ComplianceItem['status']) => {
    switch (status) {
      case 'pass':
        return 'text-green-600';
      case 'warning':
        return 'text-yellow-600';
      case 'fail':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getSectionIcon = (section: string) => {
    switch (section) {
      case 'dataRetention':
        return <Database className="h-4 w-4" />;
      case 'privacyCompliance':
        return <Lock className="h-4 w-4" />;
      case 'auditTrail':
        return <FileText className="h-4 w-4" />;
      case 'accessControl':
        return <Users className="h-4 w-4" />;
      default:
        return <Shield className="h-4 w-4" />;
    }
  };

  const formatSectionName = (section: string) => {
    switch (section) {
      case 'dataRetention':
        return 'Data Retention';
      case 'privacyCompliance':
        return 'Privacy Compliance';
      case 'auditTrail':
        return 'Audit Trail';
      case 'accessControl':
        return 'Access Control';
      default:
        return section;
    }
  };

  const formatLastCheck = (date: Date): string => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return date.toLocaleDateString();
    }
  };

  const formatDate = (date: Date): string => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const calculateOverallScore = () => {
    const scores = [
      complianceData.dataRetention.score,
      complianceData.privacyCompliance.score,
      complianceData.auditTrail.score,
      complianceData.accessControl.score
    ];
    return Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length);
  };

  const getOverallStatusText = (status: ComplianceStatus['overall']) => {
    switch (status) {
      case 'compliant':
        return 'Fully Compliant';
      case 'warning':
        return 'Attention Needed';
      case 'violation':
        return 'Critical Issues';
      default:
        return 'Unknown Status';
    }
  };

  const overallScore = calculateOverallScore();
  const colors = getOverallStatusColor(complianceData.overall);
  const complianceSections = [
    { key: 'dataRetention', data: complianceData.dataRetention },
    { key: 'privacyCompliance', data: complianceData.privacyCompliance },
    { key: 'auditTrail', data: complianceData.auditTrail },
    { key: 'accessControl', data: complianceData.accessControl }
  ];

  return (
    <Card className={`${className} ${colors.bg} border`}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Shield className={`h-4 w-4 ${colors.icon}`} />
            Compliance Status
            <Badge variant="outline" className={`${colors.badge} text-xs`}>
              {getOverallStatusText(complianceData.overall)}
            </Badge>
          </CardTitle>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className={`h-7 ${colors.text} hover:bg-white/50`}
          >
            {isExpanded ? (
              <ChevronUp className="h-3 w-3" />
            ) : (
              <ChevronDown className="h-3 w-3" />
            )}
          </Button>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Overall Score */}
        <div className="text-center">
          <div className="text-3xl font-bold mb-1" style={{ color: overallScore >= 95 ? '#16a34a' : overallScore >= 85 ? '#ea580c' : '#dc2626' }}>
            {overallScore}%
          </div>
          <div className={`text-sm ${colors.text}`}>Overall Compliance Score</div>
          <Progress 
            value={overallScore} 
            className="w-full mt-2 h-2"
          />
        </div>

        {/* Compact View */}
        {!isExpanded && (
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2">
              {complianceSections.map(({ key, data }) => (
                <div
                  key={key}
                  className="flex items-center gap-2 p-2 bg-white/50 rounded-md border border-white/20"
                >
                  <div className={getItemStatusColor(data.status)}>
                    {getItemStatusIcon(data.status)}
                  </div>
                  <div className="min-w-0 flex-1">
                    <div className="text-xs font-medium truncate">
                      {formatSectionName(key)}
                    </div>
                    <div className="text-xs text-gray-600">
                      {data.score}%
                    </div>
                  </div>
                </div>
              ))}
            </div>

            <div className="text-xs text-center text-gray-600 pt-2 border-t border-white/30">
              Last audit: {formatDate(complianceData.lastAudit)} • 
              Next: {formatDate(complianceData.nextAudit)}
            </div>
          </div>
        )}

        {/* Expanded View */}
        {isExpanded && (
          <div className="space-y-4">
            {/* Detailed Sections */}
            <div className="space-y-3">
              {complianceSections.map(({ key, data }) => (
                <div key={key} className="bg-white/50 rounded-lg p-3 border border-white/30">
                  <div 
                    className="flex items-center justify-between cursor-pointer"
                    onClick={() => setSelectedSection(selectedSection === key ? null : key)}
                  >
                    <div className="flex items-center gap-3">
                      <div className={colors.icon}>
                        {getSectionIcon(key)}
                      </div>
                      <div>
                        <h4 className={`text-sm font-medium ${colors.text}`}>
                          {formatSectionName(key)}
                        </h4>
                        <div className="flex items-center gap-2 mt-1">
                          {getItemStatusIcon(data.status)}
                          <span className="text-xs text-gray-600">
                            {data.score}% • Last check: {formatLastCheck(data.lastCheck)}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Progress value={data.score} className="w-16 h-2" />
                      {selectedSection === key ? (
                        <ChevronUp className="h-4 w-4 text-gray-400" />
                      ) : (
                        <ChevronDown className="h-4 w-4 text-gray-400" />
                      )}
                    </div>
                  </div>

                  {/* Section Details */}
                  {selectedSection === key && (
                    <div className="mt-3 pt-3 border-t border-white/30 space-y-3">
                      {/* Issues */}
                      {data.issues.length > 0 && (
                        <div>
                          <h5 className="text-xs font-medium text-red-700 mb-2 flex items-center gap-1">
                            <AlertTriangle className="h-3 w-3" />
                            Issues ({data.issues.length})
                          </h5>
                          <ul className="space-y-1">
                            {data.issues.map((issue, index) => (
                              <li key={index} className="text-xs text-gray-700 pl-4 relative">
                                <span className="absolute left-0 top-1.5 w-1 h-1 bg-red-500 rounded-full"></span>
                                {issue}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Recommendations */}
                      {data.recommendations.length > 0 && (
                        <div>
                          <h5 className="text-xs font-medium text-blue-700 mb-2 flex items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            Recommendations ({data.recommendations.length})
                          </h5>
                          <ul className="space-y-1">
                            {data.recommendations.map((rec, index) => (
                              <li key={index} className="text-xs text-gray-700 pl-4 relative">
                                <span className="absolute left-0 top-1.5 w-1 h-1 bg-blue-500 rounded-full"></span>
                                {rec}
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Actions */}
                      <div className="flex gap-2 pt-2">
                        <Button size="sm" variant="outline" className="h-6 text-xs">
                          <Eye className="h-3 w-3 mr-1" />
                          Review Details
                        </Button>
                        <Button size="sm" variant="outline" className="h-6 text-xs">
                          <ExternalLink className="h-3 w-3 mr-1" />
                          Generate Report
                        </Button>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>

            {/* Audit Schedule */}
            <div className="bg-white/50 rounded-lg p-3 border border-white/30">
              <h4 className={`text-sm font-medium ${colors.text} mb-2 flex items-center gap-2`}>
                <Calendar className="h-4 w-4" />
                Audit Schedule
              </h4>
              <div className="grid grid-cols-2 gap-4 text-xs">
                <div>
                  <span className="text-gray-600">Last Audit:</span>
                  <div className="font-medium">{formatDate(complianceData.lastAudit)}</div>
                </div>
                <div>
                  <span className="text-gray-600">Next Audit:</span>
                  <div className="font-medium">{formatDate(complianceData.nextAudit)}</div>
                </div>
              </div>
              <div className="mt-3 flex gap-2">
                <Button size="sm" variant="outline" className="h-6 text-xs">
                  Schedule Review
                </Button>
                <Button size="sm" variant="outline" className="h-6 text-xs">
                  Audit History
                </Button>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="grid grid-cols-2 gap-2">
              <Button size="sm" variant="outline" className={`h-8 text-xs ${colors.text} hover:bg-white/50`}>
                <FileText className="h-3 w-3 mr-1" />
                Compliance Report
              </Button>
              <Button size="sm" variant="outline" className={`h-8 text-xs ${colors.text} hover:bg-white/50`}>
                <Shield className="h-3 w-3 mr-1" />
                Security Settings
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 