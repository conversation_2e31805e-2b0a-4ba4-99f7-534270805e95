import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { History, MessageSquare, Plus, Clock, Zap } from 'lucide-react';
import { ClaimActivity } from '../../types/claim.types';
import { formatDate } from '@/utils/formatting/dates';

interface ActivityTimelineProps {
  activities: ClaimActivity[];
  isAddingNote: boolean;
  onAddNote: (note: string) => Promise<void>;
  onAddActivity?: (title: string, description: string, activityType: string) => Promise<void>;
}

// Quick templates for common activities
const ACTIVITY_TEMPLATES = [
  {
    title: 'Initial Contact',
    description: 'Made initial contact with claimant to discuss claim',
    type: 'contact'
  },
  {
    title: 'Left Voicemail',
    description: 'Called claimant, left detailed voicemail with callback instructions and claim reference number',
    type: 'call'
  },
  {
    title: 'Documents Requested',
    description: 'Requested identification and supporting documentation via email',
    type: 'document_request'
  },
  {
    title: 'Email Sent',
    description: 'Sent follow-up email with claim details and next steps',
    type: 'email'
  },
  {
    title: 'Documents Received',
    description: 'Received and reviewed required documentation from claimant',
    type: 'document_received'
  },
  {
    title: 'Follow-up Required',
    description: 'Case requires follow-up action in 3 business days',
    type: 'follow_up'
  }
];

export const ActivityTimeline: React.FC<ActivityTimelineProps> = ({
  activities,
  isAddingNote,
  onAddNote,
  onAddActivity
}) => {
  const [newNote, setNewNote] = useState('');
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [customNote, setCustomNote] = useState('');
  const [showCustomNote, setShowCustomNote] = useState(false);

  const handleAddNote = async () => {
    if (!newNote.trim()) return;
    
    await onAddNote(newNote);
    setNewNote('');
  };

  const handleTemplateSelect = async (templateIndex: string) => {
    if (!templateIndex || !onAddActivity) return;
    
    const template = ACTIVITY_TEMPLATES[parseInt(templateIndex)];
    await onAddActivity(template.title, template.description, template.type);
    setSelectedTemplate('');
  };

  const handleCustomActivity = async () => {
    if (!customNote.trim() || !onAddActivity) return;
    
    await onAddActivity('Custom Note', customNote, 'note');
    setCustomNote('');
    setShowCustomNote(false);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-5 w-5" />
          Activity Timeline
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Quick Templates Section */}
          {onAddActivity && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-2 mb-3">
                <Zap className="h-4 w-4 text-blue-600" />
                <h4 className="font-medium text-blue-900">Quick Actions</h4>
              </div>
              
              <div className="space-y-3">
                {/* Template Selector */}
                <div className="flex gap-2">
                  <Select value={selectedTemplate} onValueChange={handleTemplateSelect}>
                    <SelectTrigger className="flex-1">
                      <SelectValue placeholder="Choose a quick action..." />
                    </SelectTrigger>
                    <SelectContent>
                      {ACTIVITY_TEMPLATES.map((template, index) => (
                        <SelectItem key={index} value={index.toString()}>
                          {template.title}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Custom Note Toggle */}
                <div className="flex gap-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setShowCustomNote(!showCustomNote)}
                    className="flex items-center gap-1"
                  >
                    <Plus className="h-3 w-3" />
                    Custom Note
                  </Button>
                </div>

                {/* Custom Note Input */}
                {showCustomNote && (
                  <div className="space-y-2">
                    <Textarea
                      placeholder="Enter custom activity note..."
                      value={customNote}
                      onChange={(e) => setCustomNote(e.target.value)}
                      className="min-h-[60px]"
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={handleCustomActivity}
                        disabled={!customNote.trim() || isAddingNote}
                      >
                        Add Activity
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => {
                          setShowCustomNote(false);
                          setCustomNote('');
                        }}
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Traditional Note Input */}
          <div className="flex gap-2">
            <Textarea
              placeholder="Add a detailed note or update..."
              value={newNote}
              onChange={(e) => setNewNote(e.target.value)}
              className="flex-1"
            />
            <Button 
              onClick={handleAddNote} 
              disabled={!newNote.trim() || isAddingNote}
              className="self-end"
            >
              <MessageSquare className="h-4 w-4 mr-2" />
              {isAddingNote ? 'Adding...' : 'Add Note'}
            </Button>
          </div>

          <Separator />

          {/* Activity List */}
          <div className="space-y-4">
            {activities.length === 0 ? (
              <p className="text-gray-500 text-center py-4">No activities recorded yet</p>
            ) : (
              activities.map((activity) => (
                <div key={activity.id} className="flex gap-3 p-3 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className={`p-2 rounded-full ${getActivityIcon(activity.activity_type)}`}>
                    <MessageSquare className="h-4 w-4" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <p className="font-medium">{activity.title}</p>
                      <div className="flex items-center gap-2 text-xs text-gray-500">
                        <Clock className="h-3 w-3" />
                        <span>{formatDate(activity.created_at)}</span>
                      </div>
                    </div>
                    {activity.description && (
                      <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                    )}
                    <p className="text-xs text-gray-500 mt-1">
                      by {activity.users ? `${activity.users.first_name} ${activity.users.last_name}`.trim() : activity.agent_id || 'System'}
                    </p>
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Helper function to get activity icon styling
const getActivityIcon = (activityType: string): string => {
  const iconMap: Record<string, string> = {
    'call': 'bg-blue-100 text-blue-600',
    'email': 'bg-green-100 text-green-600',
    'document_request': 'bg-purple-100 text-purple-600',
    'document_received': 'bg-emerald-100 text-emerald-600',
    'follow_up': 'bg-orange-100 text-orange-600',
    'contact': 'bg-cyan-100 text-cyan-600',
    'note': 'bg-gray-100 text-gray-600',
    'status_change': 'bg-yellow-100 text-yellow-600'
  };
  
  return iconMap[activityType] || 'bg-gray-100 text-gray-600';
}; 