# =============================================================================
# ASSETHUNTERPRO ENVIRONMENT CONFIGURATION
# =============================================================================
# Copy this file to .env and fill in your actual values
# Never commit .env to version control - it contains sensitive API keys

# =============================================================================
# SUPABASE DATABASE CONFIGURATION (REQUIRED)
# =============================================================================
# Get these from your Supabase project dashboard: https://app.supabase.com
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# Email service configuration (using Resend)
VITE_RESEND_API_KEY=your-resend-api-key
VITE_DEFAULT_FROM_EMAIL=<EMAIL>
VITE_DEFAULT_FROM_NAME=AssetHunterPro
VITE_ENABLE_MOCK_EMAIL=false

# =============================================================================
# FEATURE FLAGS - PRODUCTION SETTINGS
# =============================================================================
VITE_USE_MOCK_DATA=false
VITE_USE_REAL_DATABASE=true

# Discovery & AI Features
VITE_ENABLE_REAL_DISCOVERY=true
VITE_ENABLE_LOCAL_ML=false
VITE_ENABLE_PREMIUM_APIS=false

# Contact Intelligence
VITE_CONTACT_SCORING_ENABLED=true
VITE_EMAIL_VERIFICATION_ENABLED=false

# Performance Monitoring
VITE_PERFORMANCE_MONITORING=true

# =============================================================================
# EXTERNAL API KEYS (OPTIONAL)
# =============================================================================
# Google People API for contact enrichment
VITE_GOOGLE_PEOPLE_API_KEY=your-google-people-api-key

# Have I Been Pwned API for security checks
VITE_HIBP_API_KEY=your-hibp-api-key

# =============================================================================
# DOCUSIGN INTEGRATION (FOR ELECTRONIC SIGNATURES)
# =============================================================================
# Required for document signing workflows
# Get these from your DocuSign Developer Account: https://developers.docusign.com

# Your DocuSign Integration Key (found in Apps & Keys)
VITE_DOCUSIGN_INTEGRATION_KEY=your-integration-key

# Your DocuSign User ID (found in Apps & Keys)
VITE_DOCUSIGN_USER_ID=your-user-id

# Your DocuSign Account ID (found in Apps & Keys)
VITE_DOCUSIGN_ACCOUNT_ID=your-account-id

# DocuSign API Base URL (use demo for testing, www for production)
VITE_DOCUSIGN_BASE_URL=https://demo.docusign.net/restapi

# OAuth redirect URL (must match your DocuSign app configuration)
VITE_DOCUSIGN_REDIRECT_URL=http://localhost:3005/docusign/callback

# Webhook URL for receiving DocuSign events
VITE_DOCUSIGN_WEBHOOK_URL=http://localhost:3005/api/docusign/webhook

# =============================================================================
# STRIPE PAYMENT INTEGRATION (FOR BILLING & COMMISSIONS)
# =============================================================================
# Required for subscription billing and commission payments
# Get these from your Stripe Dashboard: https://dashboard.stripe.com/apikeys

# Stripe Publishable Key (safe to expose in frontend)
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-publishable-key

# Stripe Secret Key (keep this secure, server-side only)
VITE_STRIPE_SECRET_KEY=sk_test_your-secret-key

# Stripe Webhook Secret (for verifying webhook signatures)
VITE_STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# Stripe Environment (test or live)
VITE_STRIPE_ENVIRONMENT=test

# =============================================================================
# LOCAL DEVELOPMENT
# =============================================================================
# Local ML service endpoint (if using local AI features)
VITE_LOCAL_ML_ENDPOINT=http://localhost:5000

# Node environment
NODE_ENV=development

# Development server port
PORT=3000

# =============================================================================
# PRODUCTION DEPLOYMENT NOTES
# =============================================================================
# For production deployment:
# 1. Change all URLs from localhost to your production domain
# 2. Use production API keys (live mode for Stripe, production for DocuSign)
# 3. Set NODE_ENV=production
# 4. Ensure all webhook URLs are publicly accessible
# 5. Enable SSL/HTTPS for all endpoints
# 6. Set up proper CORS policies in Supabase
# 7. Configure proper authentication redirects

# =============================================================================
# SECURITY NOTES
# =============================================================================
# - Never commit .env files to version control
# - Use different API keys for development and production
# - Regularly rotate API keys and secrets
# - Monitor API usage and set up alerts for unusual activity
# - Use environment-specific webhook URLs
# - Enable MFA on all third-party service accounts
