// Stripe Payment Integration Service for AssetHunterPro
// Handles subscription billing, commission payments, and payment processing

import { FEATURE_FLAGS } from '@/config/features'

export interface StripeConfig {
  publishableKey: string
  secretKey: string
  webhookSecret: string
  environment: 'test' | 'live'
}

export interface Customer {
  id: string
  email: string
  name: string
  phone?: string
  address?: Address
  metadata?: Record<string, string>
}

export interface Address {
  line1: string
  line2?: string
  city: string
  state: string
  postal_code: string
  country: string
}

export interface PaymentMethod {
  id: string
  type: 'card' | 'bank_account' | 'ach_debit'
  card?: CardDetails
  billing_details: BillingDetails
}

export interface CardDetails {
  brand: string
  last4: string
  exp_month: number
  exp_year: number
}

export interface BillingDetails {
  name: string
  email: string
  phone?: string
  address?: Address
}

export interface Subscription {
  id: string
  customer: string
  status: 'active' | 'past_due' | 'canceled' | 'incomplete' | 'trialing'
  current_period_start: number
  current_period_end: number
  plan: SubscriptionPlan
  trial_end?: number
  cancel_at_period_end: boolean
}

export interface SubscriptionPlan {
  id: string
  name: string
  amount: number
  currency: string
  interval: 'month' | 'year'
  features: string[]
}

export interface PaymentIntent {
  id: string
  amount: number
  currency: string
  status: 'requires_payment_method' | 'requires_confirmation' | 'requires_action' | 'processing' | 'succeeded' | 'canceled'
  client_secret: string
  metadata?: Record<string, string>
}

export interface Commission {
  id: string
  agentId: string
  claimId: string
  amount: number
  percentage: number
  status: 'pending' | 'processing' | 'paid' | 'failed'
  paidAt?: string
  paymentIntentId?: string
}

export interface Invoice {
  id: string
  customer: string
  amount_due: number
  amount_paid: number
  currency: string
  status: 'draft' | 'open' | 'paid' | 'void' | 'uncollectible'
  due_date?: number
  pdf?: string
}

export class StripeService {
  private static instance: StripeService
  private config: StripeConfig | null = null
  private stripe: any = null // Stripe instance

  static getInstance(): StripeService {
    if (!StripeService.instance) {
      StripeService.instance = new StripeService()
    }
    return StripeService.instance
  }

  /**
   * Initialize Stripe service with configuration
   */
  async initialize(config: StripeConfig) {
    this.config = config
    
    if (typeof window !== 'undefined') {
      // Load Stripe.js in browser
      const { loadStripe } = await import('@stripe/stripe-js')
      this.stripe = await loadStripe(config.publishableKey)
    }
    
    console.log('💳 Stripe service initialized')
  }

  /**
   * Create a new customer
   */
  async createCustomer(customerData: Omit<Customer, 'id'>): Promise<{ data: Customer | null; error: any }> {
    try {
      const response = await this.apiRequest('/customers', {
        method: 'POST',
        body: {
          email: customerData.email,
          name: customerData.name,
          phone: customerData.phone,
          address: customerData.address,
          metadata: customerData.metadata || {}
        }
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe customer creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Create a subscription for a customer
   */
  async createSubscription(customerId: string, priceId: string, trialDays?: number): Promise<{ data: Subscription | null; error: any }> {
    try {
      const body: any = {
        customer: customerId,
        items: [{ price: priceId }],
        payment_behavior: 'default_incomplete',
        expand: ['latest_invoice.payment_intent']
      }

      if (trialDays) {
        body.trial_period_days = trialDays
      }

      const response = await this.apiRequest('/subscriptions', {
        method: 'POST',
        body
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe subscription creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Cancel a subscription
   */
  async cancelSubscription(subscriptionId: string, immediately: boolean = false): Promise<{ error: any }> {
    try {
      const endpoint = immediately
        ? `/subscriptions/${subscriptionId}`
        : `/subscriptions/${subscriptionId}`

      const body = immediately 
        ? null
        : { cancel_at_period_end: true }

      await this.apiRequest(endpoint, {
        method: immediately ? 'DELETE' : 'POST',
        body
      })

      return { error: null }
    } catch (error) {
      console.error('❌ Stripe subscription cancellation failed:', error)
      return { error }
    }
  }

  /**
   * Create payment intent for commission payment
   */
  async createCommissionPayment(agentId: string, amount: number, claimId: string): Promise<{ data: PaymentIntent | null; error: any }> {
    try {
      const response = await this.apiRequest('/payment_intents', {
        method: 'POST',
        body: {
          amount: Math.round(amount * 100), // Convert to cents
          currency: 'usd',
          automatic_payment_methods: { enabled: true },
          metadata: {
            type: 'commission',
            agent_id: agentId,
            claim_id: claimId
          }
        }
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe commission payment creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Process ACH payment for commission
   */
  async processACHPayment(agentId: string, amount: number, bankAccountId: string): Promise<{ data: PaymentIntent | null; error: any }> {
    try {
      const response = await this.apiRequest('/payment_intents', {
        method: 'POST',
        body: {
          amount: Math.round(amount * 100),
          currency: 'usd',
          payment_method: bankAccountId,
          payment_method_types: ['us_bank_account'],
          metadata: {
            type: 'ach_commission',
            agent_id: agentId
          }
        }
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe ACH payment failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get customer's payment methods
   */
  async getPaymentMethods(customerId: string): Promise<{ data: PaymentMethod[]; error: any }> {
    try {
      const response = await this.apiRequest(`/customers/${customerId}/payment_methods`, {
        method: 'GET'
      })

      return { data: response.data || [], error: null }
    } catch (error) {
      console.error('❌ Stripe payment methods fetch failed:', error)
      return { data: [], error }
    }
  }

  /**
   * Get subscription details
   */
  async getSubscription(subscriptionId: string): Promise<{ data: Subscription | null; error: any }> {
    try {
      const response = await this.apiRequest(`/subscriptions/${subscriptionId}`, {
        method: 'GET'
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe subscription fetch failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get customer invoices
   */
  async getInvoices(customerId: string, limit: number = 10): Promise<{ data: Invoice[]; error: any }> {
    try {
      const response = await this.apiRequest(`/invoices?customer=${customerId}&limit=${limit}`, {
        method: 'GET'
      })

      return { data: response.data || [], error: null }
    } catch (error) {
      console.error('❌ Stripe invoices fetch failed:', error)
      return { data: [], error }
    }
  }

  /**
   * Create setup intent for saving payment method
   */
  async createSetupIntent(customerId: string): Promise<{ data: any; error: any }> {
    try {
      const response = await this.apiRequest('/setup_intents', {
        method: 'POST',
        body: {
          customer: customerId,
          payment_method_types: ['card'],
          usage: 'off_session'
        }
      })

      return { data: response, error: null }
    } catch (error) {
      console.error('❌ Stripe setup intent creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Handle webhook events
   */
  async handleWebhook(payload: string, signature: string): Promise<{ success: boolean; event?: any }> {
    try {
      if (!this.config?.webhookSecret) {
        throw new Error('Webhook secret not configured')
      }

      // In a real implementation, you would verify the webhook signature
      // const event = stripe.webhooks.constructEvent(payload, signature, this.config.webhookSecret)
      
      const event = JSON.parse(payload) // Mock for development
      
      await this.processWebhookEvent(event)
      
      return { success: true, event }
    } catch (error) {
      console.error('❌ Stripe webhook processing failed:', error)
      return { success: false }
    }
  }

  // Private helper methods
  private async apiRequest(endpoint: string, options: { method: string; body?: any }) {
    if (!this.config) {
      throw new Error('Stripe service not configured')
    }

    const url = `https://api.stripe.com/v1${endpoint}`
    
    const response = await fetch(url, {
      method: options.method,
      headers: {
        'Authorization': `Bearer ${this.config.secretKey}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: options.body ? this.encodeFormData(options.body) : undefined
    })

    if (!response.ok) {
      const error = await response.json()
      throw new Error(error.error?.message || 'Stripe API error')
    }

    return response.json()
  }

  private encodeFormData(data: any): string {
    return Object.keys(data)
      .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(data[key])}`)
      .join('&')
  }

  private async processWebhookEvent(event: any) {
    switch (event.type) {
      case 'payment_intent.succeeded':
        await this.handlePaymentSucceeded(event.data.object)
        break
      case 'customer.subscription.updated':
        await this.handleSubscriptionUpdated(event.data.object)
        break
      case 'invoice.payment_failed':
        await this.handlePaymentFailed(event.data.object)
        break
      default:
        console.log(`Unhandled webhook event: ${event.type}`)
    }
  }

  private async handlePaymentSucceeded(paymentIntent: any) {
    console.log('💳 Payment succeeded:', paymentIntent.id)
    // Update database records, send notifications, etc.
  }

  private async handleSubscriptionUpdated(subscription: any) {
    console.log('📋 Subscription updated:', subscription.id)
    // Update subscription status in database
  }

  private async handlePaymentFailed(invoice: any) {
    console.log('❌ Payment failed:', invoice.id)
    // Handle failed payment, notify user, etc.
  }


}

export const stripeService = StripeService.getInstance()
