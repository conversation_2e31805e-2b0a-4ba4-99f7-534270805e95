// AssetHunterPro Feature Flags Configuration
// REAL DATA MODE - Mock data disabled

export const FEATURE_FLAGS = {
  // Data Sources - REAL DATA ONLY
  USE_MOCK_DATA: false, // DISABLED - Using real data only
  USE_REAL_DATABASE: true, // ENABLED - Using Supabase for all data
  
  // Discovery & APIs
  ENABLE_REAL_DISCOVERY: import.meta.env.VITE_ENABLE_REAL_DISCOVERY === 'true',
  ENABLE_LOCAL_ML: import.meta.env.VITE_ENABLE_LOCAL_ML === 'true',
  ENABLE_PREMIUM_APIS: import.meta.env.VITE_ENABLE_PREMIUM_APIS === 'true',
  
  // Contact Intelligence
  CONTACT_SCORING_ENABLED: import.meta.env.VITE_CONTACT_SCORING_ENABLED === 'true',
  EMAIL_VERIFICATION_ENABLED: import.meta.env.VITE_EMAIL_VERIFICATION_ENABLED === 'true',
  
  // External Services
  OPENCORPORATES_ENABLED: import.meta.env.VITE_OPENCORPORATES_API_KEY ? true : false,
  GOOGLE_PEOPLE_ENABLED: import.meta.env.VITE_GOOGLE_PEOPLE_API_KEY ? true : false,
  HIBP_ENABLED: import.meta.env.VITE_HIBP_API_KEY ? true : false,
  
  // Development Features
  DEBUG_MODE: import.meta.env.DEV,
  PERFORMANCE_MONITORING: import.meta.env.VITE_PERFORMANCE_MONITORING === 'true',
} as const;

export const API_CONFIG = {
  // Free API Endpoints
  OPENCORPORATES_BASE_URL: 'https://api.opencorporates.com/v0.4',
  GOOGLE_PEOPLE_BASE_URL: 'https://people.googleapis.com/v1',
  HIBP_BASE_URL: 'https://haveibeenpwned.com/api/v3',
  
  // Local ML Service
  LOCAL_ML_ENDPOINT: import.meta.env.VITE_LOCAL_ML_ENDPOINT || 'http://localhost:5000',
  
  // Rate Limiting
  RATE_LIMITS: {
    OPENCORPORATES_FREE: { requests: 500, period: 'month' },
    GOOGLE_PEOPLE_FREE: { requests: 100, period: 'day' },
    HIBP_FREE: { requests: 1500, period: 'day' },
  }
} as const;

export const DEVELOPMENT_CONFIG = {
  // Demo data refresh intervals
  MOCK_DATA_REFRESH_INTERVAL: 30000, // 30 seconds
  REAL_TIME_SIMULATION_ENABLED: true,
  
  // Logging
  ENABLE_API_LOGGING: import.meta.env.DEV,
  ENABLE_PERFORMANCE_LOGGING: import.meta.env.DEV,
  
  // Testing
  ENABLE_TEST_ENDPOINTS: import.meta.env.DEV,
} as const;

// Helper functions for feature checks
export const isFeatureEnabled = (feature: keyof typeof FEATURE_FLAGS): boolean => {
  return FEATURE_FLAGS[feature];
};

export const getApiConfig = (service: string) => {
  switch (service) {
    case 'opencorporates':
      return {
        enabled: FEATURE_FLAGS.OPENCORPORATES_ENABLED,
        baseUrl: API_CONFIG.OPENCORPORATES_BASE_URL,
        apiKey: import.meta.env.VITE_OPENCORPORATES_API_KEY,
        rateLimit: API_CONFIG.RATE_LIMITS.OPENCORPORATES_FREE
      };
    case 'google-people':
      return {
        enabled: FEATURE_FLAGS.GOOGLE_PEOPLE_ENABLED,
        baseUrl: API_CONFIG.GOOGLE_PEOPLE_BASE_URL,
        apiKey: import.meta.env.VITE_GOOGLE_PEOPLE_API_KEY,
        rateLimit: API_CONFIG.RATE_LIMITS.GOOGLE_PEOPLE_FREE
      };
    case 'hibp':
      return {
        enabled: FEATURE_FLAGS.HIBP_ENABLED,
        baseUrl: API_CONFIG.HIBP_BASE_URL,
        apiKey: import.meta.env.VITE_HIBP_API_KEY,
        rateLimit: API_CONFIG.RATE_LIMITS.HIBP_FREE
      };
    default:
      return null;
  }
};

// Development utilities
export const logFeatureFlags = () => {
  if (DEVELOPMENT_CONFIG.ENABLE_API_LOGGING) {
    console.log('🏁 AssetHunterPro Feature Flags:', FEATURE_FLAGS);
    console.log('🔧 API Configuration:', API_CONFIG);
  }
};

// Initialize logging in development
if (import.meta.env.DEV) {
  logFeatureFlags();
} 