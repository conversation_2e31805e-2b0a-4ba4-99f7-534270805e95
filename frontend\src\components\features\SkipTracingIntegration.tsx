import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Input } from '@/components/ui/input';
import { 
  Search, 
  UserCheck, 
  MapPin, 
  Phone, 
  Mail, 
  Building, 
  Shield, 
  TrendingUp, 
  Clock, 
  CheckCircle,
  AlertCircle,
  Eye,
  FileText,
  Users,
  Database,
  Settings,
  Zap,
  DollarSign,
  Target
} from 'lucide-react';
import { 
  SkipTracingIntegration, 
  ResearchProvider, 
  ContactDiscovery, 
  VerificationTools,
  SearchAnalytics 
} from '@/types/skip-tracing';

interface SkipTracingIntegrationProps {
  userPlan: 'gold' | 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

interface SearchResult {
  id: string;
  name: string;
  confidence: number;
  addresses: string[];
  phones: string[];
  email: string;
  relatives: string[];
  last_verified: string;
  data_sources: string[];
}

export const SkipTracingIntegrationComponent: React.FC<SkipTracingIntegrationProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('search');
  const [searchQuery, setSearchQuery] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [searchStats, setSearchStats] = useState({
    total_searches: 2847,
    successful_searches: 2103,
    success_rate: 73.9,
    avg_search_time: 2.3,
    providers_connected: 12,
    data_sources_active: 47,
    cost_per_search: 3.25,
    total_savings: 14280
  });

  const [researchProviders] = useState<ResearchProvider[]>([
    {
      provider_id: 'provider-001',
      provider_name: 'TLOxp',
      provider_type: 'people_search',
      data_sources: [
        {
          source_name: 'Public Records',
          source_type: 'public_records',
          data_freshness: 'Real-time',
          coverage_percentage: 95,
          update_frequency: 'Daily'
        },
        {
          source_name: 'Credit Headers',
          source_type: 'credit_headers',
          data_freshness: '30 days',
          coverage_percentage: 87,
          update_frequency: 'Monthly'
        }
      ],
      search_capabilities: [
        {
          search_type: 'name_search',
          input_requirements: [
            { field_name: 'first_name', field_type: 'string', required: true },
            { field_name: 'last_name', field_type: 'string', required: true }
          ],
          output_data_types: ['addresses', 'phone_numbers', 'relatives', 'associates'],
          search_depth: 'comprehensive',
          turnaround_time: '< 5 seconds',
          success_rate: 89.2
        }
      ],
      cost_structure: {
        pricing_model: 'per_search',
        base_cost: 2.50,
        volume_discounts: [
          { minimum_volume: 100, discount_percentage: 10, discount_type: 'percentage' },
          { minimum_volume: 500, discount_percentage: 15, discount_type: 'percentage' }
        ],
        additional_fees: [],
        billing_frequency: 'Monthly'
      },
      accuracy_rating: 94.3,
      coverage_areas: [
        {
          geographic_region: 'United States',
          coverage_percentage: 98,
          data_completeness: 92,
          update_frequency: 'Real-time',
          special_limitations: []
        }
      ],
      api_integration: {
        api_version: '2.1',
        authentication_method: 'OAuth 2.0',
        rate_limits: [
          { limit_type: 'requests_per_minute', limit_value: 60 }
        ],
        supported_formats: ['JSON', 'XML'],
        webhook_support: true,
        batch_processing: true
      },
      compliance_certifications: ['SOC 2', 'GLBA', 'FCRA']
    },
    {
      provider_id: 'provider-002',
      provider_name: 'Accurint',
      provider_type: 'background_check',
      data_sources: [
        {
          source_name: 'Court Records',
          source_type: 'court_records',
          data_freshness: 'Weekly',
          coverage_percentage: 78,
          update_frequency: 'Weekly'
        }
      ],
      search_capabilities: [
        {
          search_type: 'ssn_search',
          input_requirements: [
            { field_name: 'ssn', field_type: 'string', required: true }
          ],
          output_data_types: ['identity_verification', 'address_history', 'employment_history'],
          search_depth: 'deep',
          turnaround_time: '10-30 seconds',
          success_rate: 82.1
        }
      ],
      cost_structure: {
        pricing_model: 'tiered',
        base_cost: 4.75,
        volume_discounts: [
          { minimum_volume: 250, discount_percentage: 12, discount_type: 'percentage' }
        ],
        additional_fees: [
          { fee_name: 'Premium Search', fee_amount: 2.00, fee_trigger: 'Deep background check', fee_frequency: 'Per search' }
        ],
        billing_frequency: 'Monthly'
      },
      accuracy_rating: 91.7,
      coverage_areas: [
        {
          geographic_region: 'United States',
          coverage_percentage: 85,
          data_completeness: 88,
          update_frequency: 'Weekly',
          special_limitations: ['Some rural areas limited']
        }
      ],
      api_integration: {
        api_version: '3.0',
        authentication_method: 'API Key',
        rate_limits: [
          { limit_type: 'requests_per_hour', limit_value: 1000 }
        ],
        supported_formats: ['JSON'],
        webhook_support: false,
        batch_processing: true
      },
      compliance_certifications: ['SOC 2', 'FCRA', 'DPPA']
    }
  ]);

  const getProviderStatusColor = (accuracy: number) => {
    if (accuracy >= 90) return 'bg-green-500';
    if (accuracy >= 80) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('en-US').format(num);
  };

  const mockSearchResults: SearchResult[] = [
    {
      id: '1',
      name: 'John Michael Smith',
      confidence: 94,
      addresses: ['123 Main St, Austin, TX 78701', '456 Oak Ave, Dallas, TX 75201'],
      phones: ['(*************', '(*************'],
      email: '<EMAIL>',
      relatives: ['Jane Smith', 'Robert Smith'],
      last_verified: '2024-01-15',
      data_sources: ['TLOxp', 'Public Records']
    },
    {
      id: '2',
      name: 'John Steven Smith',
      confidence: 78,
      addresses: ['789 Pine St, Houston, TX 77001'],
      phones: ['(*************'],
      email: '<EMAIL>',
      relatives: ['Sarah Smith'],
      last_verified: '2024-01-10',
      data_sources: ['Accurint', 'Credit Headers']
    }
  ];

  const performSearch = () => {
    if (searchQuery.trim()) {
      setSearchResults(mockSearchResults);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header Section */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">Skip Tracing Integration</h1>
          <p className="text-muted-foreground mt-2">
            Professional people search and asset location with integrated verification tools
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="outline" className="px-3 py-1">
            {userPlan.charAt(0).toUpperCase() + userPlan.slice(1)} Plan
          </Badge>
          <Button variant="outline" size="sm">
            <Settings className="h-4 w-4 mr-2" />
            Configure
          </Button>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 lg:grid-cols-8 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Searches</p>
                <p className="text-2xl font-bold">{formatNumber(searchStats.total_searches)}</p>
              </div>
              <Search className="h-5 w-5 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Successful</p>
                <p className="text-2xl font-bold">{formatNumber(searchStats.successful_searches)}</p>
              </div>
              <CheckCircle className="h-5 w-5 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Success Rate</p>
                <p className="text-2xl font-bold">{searchStats.success_rate}%</p>
              </div>
              <Target className="h-5 w-5 text-purple-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Avg Time</p>
                <p className="text-2xl font-bold">{searchStats.avg_search_time}s</p>
              </div>
              <Clock className="h-5 w-5 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Providers</p>
                <p className="text-2xl font-bold">{searchStats.providers_connected}</p>
              </div>
              <Database className="h-5 w-5 text-cyan-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Data Sources</p>
                <p className="text-2xl font-bold">{searchStats.data_sources_active}</p>
              </div>
              <FileText className="h-5 w-5 text-pink-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Cost/Search</p>
                <p className="text-2xl font-bold">{formatCurrency(searchStats.cost_per_search)}</p>
              </div>
              <DollarSign className="h-5 w-5 text-yellow-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Savings</p>
                <p className="text-2xl font-bold">{formatCurrency(searchStats.total_savings)}</p>
              </div>
              <TrendingUp className="h-5 w-5 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="search">People Search</TabsTrigger>
          <TabsTrigger value="providers">Data Providers</TabsTrigger>
          <TabsTrigger value="verification">Verification Tools</TabsTrigger>
          <TabsTrigger value="analytics">Search Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="search" className="space-y-6">
          {/* Search Interface */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5" />
                Professional People Search
              </CardTitle>
              <CardDescription>
                Search across multiple data sources with real-time verification and compliance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex gap-4">
                  <Input 
                    placeholder="Enter name, phone, email, or address..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={performSearch} className="px-8">
                    <Search className="h-4 w-4 mr-2" />
                    Search
                  </Button>
                </div>

                {/* Search Results */}
                {searchResults.length > 0 && (
                  <div className="space-y-4 mt-6">
                    <h3 className="font-semibold text-lg">Search Results</h3>
                    {searchResults.map((result) => (
                      <Card key={result.id} className="p-4">
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3">
                              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-semibold">
                                {result.name.split(' ').map((n: string) => n[0]).join('')}
                              </div>
                              <div>
                                <h4 className="font-semibold">{result.name}</h4>
                                <p className="text-sm text-muted-foreground">
                                  Last verified: {result.last_verified}
                                </p>
                              </div>
                            </div>
                            <div className="flex items-center gap-2">
                              <Badge variant={result.confidence >= 90 ? 'default' : 
                                           result.confidence >= 70 ? 'secondary' : 'destructive'}>
                                {result.confidence}% match
                              </Badge>
                              <Button variant="outline" size="sm">
                                <Eye className="h-4 w-4 mr-2" />
                                View Details
                              </Button>
                            </div>
                          </div>

                          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm font-medium">
                                <MapPin className="h-4 w-4 text-blue-500" />
                                Addresses
                              </div>
                              {result.addresses.map((address, idx) => (
                                <p key={idx} className="text-sm text-muted-foreground pl-6">
                                  {address}
                                </p>
                              ))}
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm font-medium">
                                <Phone className="h-4 w-4 text-green-500" />
                                Phone Numbers
                              </div>
                              {result.phones.map((phone, idx) => (
                                <p key={idx} className="text-sm text-muted-foreground pl-6">
                                  {phone}
                                </p>
                              ))}
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm font-medium">
                                <Mail className="h-4 w-4 text-purple-500" />
                                Email
                              </div>
                              <p className="text-sm text-muted-foreground pl-6">
                                {result.email}
                              </p>
                            </div>

                            <div className="space-y-2">
                              <div className="flex items-center gap-2 text-sm font-medium">
                                <Users className="h-4 w-4 text-orange-500" />
                                Relatives
                              </div>
                              {result.relatives.map((relative, idx) => (
                                <p key={idx} className="text-sm text-muted-foreground pl-6">
                                  {relative}
                                </p>
                              ))}
                            </div>
                          </div>

                          <div className="border-t pt-3">
                            <div className="flex items-center gap-4 text-sm text-muted-foreground">
                              <span>Data Sources:</span>
                              {result.data_sources.map((source, idx) => (
                                <Badge key={idx} variant="outline" className="text-xs">
                                  {source}
                                </Badge>
                              ))}
                            </div>
                          </div>
                        </div>
                      </Card>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="providers" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
                Research Providers
              </CardTitle>
              <CardDescription>
                Connected data providers and their capabilities
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                {researchProviders.map((provider, index) => (
                  <Card key={index} className="p-4">
                    <div className="space-y-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${getProviderStatusColor(provider.accuracy_rating)}`}></div>
                          <div>
                            <h3 className="font-semibold text-lg">{provider.provider_name}</h3>
                            <p className="text-sm text-muted-foreground capitalize">
                              {provider.provider_type.replace('_', ' ')}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-4">
                          <Badge variant="default">
                            {provider.accuracy_rating}% accuracy
                          </Badge>
                          <Badge variant="outline">
                            {formatCurrency(provider.cost_structure.base_cost)} / search
                          </Badge>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <Card className="p-3">
                          <h4 className="font-medium mb-2">Data Sources</h4>
                          <div className="space-y-1">
                            {provider.data_sources.map((source, idx) => (
                              <div key={idx} className="text-sm">
                                <span className="font-medium">{source.source_name}</span>
                                <span className="text-muted-foreground ml-2">
                                  ({source.coverage_percentage}% coverage)
                                </span>
                              </div>
                            ))}
                          </div>
                        </Card>

                        <Card className="p-3">
                          <h4 className="font-medium mb-2">Search Capabilities</h4>
                          <div className="space-y-1">
                            {provider.search_capabilities.map((capability, idx) => (
                              <div key={idx} className="text-sm">
                                <span className="font-medium capitalize">
                                  {capability.search_type.replace('_', ' ')}
                                </span>
                                <span className="text-muted-foreground ml-2">
                                  ({capability.success_rate}% success)
                                </span>
                              </div>
                            ))}
                          </div>
                        </Card>

                        <Card className="p-3">
                          <h4 className="font-medium mb-2">Compliance</h4>
                          <div className="flex flex-wrap gap-1">
                            {provider.compliance_certifications.map((cert, idx) => (
                              <Badge key={idx} variant="outline" className="text-xs">
                                {cert}
                              </Badge>
                            ))}
                          </div>
                        </Card>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="verification" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Verification Tools
              </CardTitle>
              <CardDescription>
                Identity, contact, and asset verification services
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <UserCheck className="h-6 w-6 text-blue-500" />
                    <h3 className="font-semibold">Identity Verification</h3>
                  </div>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Document verification
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Biometric verification
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Knowledge-based auth
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Fraud detection
                    </li>
                  </ul>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Phone className="h-6 w-6 text-green-500" />
                    <h3 className="font-semibold">Contact Verification</h3>
                  </div>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Phone validation
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Email verification
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Address standardization
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Social media validation
                    </li>
                  </ul>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-3 mb-3">
                    <Building className="h-6 w-6 text-purple-500" />
                    <h3 className="font-semibold">Asset Verification</h3>
                  </div>
                  <ul className="space-y-2 text-sm">
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Property records
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Business ownership
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Vehicle registration
                    </li>
                    <li className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-500" />
                      Financial accounts
                    </li>
                  </ul>
                </Card>
              </div>

              {userPlan === 'gold' && (
                <div className="mt-6 p-4 border border-dashed border-muted-foreground/50 rounded-lg text-center">
                  <p className="text-muted-foreground mb-2">
                    Advanced verification tools available in Platinum and Diamond plans
                  </p>
                  <Button onClick={onUpgrade} variant="outline">
                    Upgrade Plan
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Search Analytics
              </CardTitle>
              <CardDescription>
                Performance metrics and cost analysis for your skip tracing operations
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Provider Performance</h4>
                  <div className="space-y-3">
                    {researchProviders.map((provider, idx) => (
                      <div key={idx} className="flex items-center justify-between p-2 bg-muted/30 rounded">
                        <span className="text-sm font-medium">{provider.provider_name}</span>
                        <div className="flex items-center gap-2">
                          <span className="text-sm">{provider.accuracy_rating}%</span>
                          <div className="w-16 h-2 bg-muted rounded-full">
                            <div 
                              className="h-full bg-green-500 rounded-full"
                              style={{ width: `${provider.accuracy_rating}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Cost Analysis</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Average cost per search</span>
                      <span className="font-medium">{formatCurrency(searchStats.cost_per_search)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Total monthly cost</span>
                      <span className="font-medium">{formatCurrency(searchStats.cost_per_search * 150)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Estimated annual savings</span>
                      <span className="font-medium text-green-600">{formatCurrency(searchStats.total_savings)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">ROI</span>
                      <span className="font-medium text-green-600">340%</span>
                    </div>
                  </div>
                </Card>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SkipTracingIntegrationComponent; 