-- Asset Recovery Web Application Database Schema
-- Run this in your Supabase SQL Editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Create custom types
CREATE TYPE user_role AS ENUM ('admin', 'senior_agent', 'junior_agent', 'contractor', 'compliance', 'finance');
CREATE TYPE claim_status AS ENUM ('new', 'contacted', 'contract_sent', 'signed', 'filed', 'paid', 'closed');
CREATE TYPE activity_type AS ENUM ('call', 'email', 'sms', 'document_upload', 'status_change', 'note');

-- Users table
CREATE TABLE users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  role user_role NOT NULL DEFAULT 'junior_agent',
  name TEXT NOT NULL,
  phone TEXT,
  active BOOLEAN DEFAULT true,
  tenant_id UUID, -- For multi-tenant support
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- State batches table (for CSV imports)
CREATE TABLE state_batches (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  state TEXT NOT NULL,
  year INTEGER NOT NULL,
  file_name TEXT NOT NULL,
  imported_by UUID REFERENCES users(id),
  imported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  total_records INTEGER DEFAULT 0,
  processed_records INTEGER DEFAULT 0
);

-- Claimants table
CREATE TABLE claimants (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  type TEXT DEFAULT 'individual', -- 'individual' or 'business'
  first_name TEXT,
  last_name TEXT,
  business_name TEXT,
  dob DATE,
  ssn_encrypted TEXT, -- Encrypted SSN
  do_not_contact BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Addresses table (one-to-many with claimants)
CREATE TABLE addresses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claimant_id UUID REFERENCES claimants(id) ON DELETE CASCADE,
  line1 TEXT NOT NULL,
  line2 TEXT,
  city TEXT NOT NULL,
  state TEXT NOT NULL,
  zip TEXT NOT NULL,
  country TEXT DEFAULT 'US',
  verified BOOLEAN DEFAULT false,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Phone numbers table (one-to-many with claimants)
CREATE TABLE phones (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claimant_id UUID REFERENCES claimants(id) ON DELETE CASCADE,
  phone TEXT NOT NULL,
  type TEXT DEFAULT 'mobile', -- 'mobile', 'home', 'work'
  source TEXT DEFAULT 'manual', -- 'manual', 'skip_trace', 'import'
  verified BOOLEAN DEFAULT false,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email addresses table (one-to-many with claimants)
CREATE TABLE emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claimant_id UUID REFERENCES claimants(id) ON DELETE CASCADE,
  email TEXT NOT NULL,
  verified BOOLEAN DEFAULT false,
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Claims table
CREATE TABLE claims (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  state_batch_id UUID REFERENCES state_batches(id),
  original_claim_id TEXT, -- Original ID from state system
  claim_number TEXT UNIQUE NOT NULL,
  state TEXT NOT NULL,
  amount_reported DECIMAL(12,2),
  amount_net DECIMAL(12,2),
  status claim_status NOT NULL DEFAULT 'new',
  claimant_primary_id UUID REFERENCES claimants(id),
  assigned_agent_id UUID REFERENCES users(id),
  escheat_date DATE,
  fee_cap_pct DECIMAL(5,2), -- Fee percentage cap for this state
  imported_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_status_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Activities table (audit trail)
CREATE TABLE activities (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claim_id UUID REFERENCES claims(id) ON DELETE CASCADE,
  agent_id UUID REFERENCES users(id),
  type activity_type NOT NULL,
  summary TEXT NOT NULL,
  detail TEXT,
  disposition TEXT,
  next_follow_up TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  immutable_after TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '5 minutes')
);

-- Documents table
CREATE TABLE documents (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claim_id UUID REFERENCES claims(id) ON DELETE CASCADE,
  claimant_id UUID REFERENCES claimants(id),
  doc_type TEXT NOT NULL, -- 'engagement_letter', 'poa', 'state_form', 'id_document'
  file_name TEXT NOT NULL,
  file_path TEXT NOT NULL, -- S3 path or Supabase storage path
  file_size INTEGER,
  mime_type TEXT,
  e_sign_id TEXT, -- DocuSign envelope ID
  signer_status TEXT, -- 'pending', 'signed', 'declined'
  uploaded_by UUID REFERENCES users(id),
  uploaded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Lead scores table (AI/ML scoring)
CREATE TABLE lead_scores (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claim_id UUID REFERENCES claims(id) ON DELETE CASCADE,
  score INTEGER CHECK (score >= 0 AND score <= 100),
  factors_json JSONB, -- Store scoring factors as JSON
  model_version TEXT,
  scored_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Fee configuration table (per state)
CREATE TABLE fee_configs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  state TEXT NOT NULL UNIQUE,
  max_fee_pct DECIMAL(5,2) NOT NULL,
  bond_required BOOLEAN DEFAULT false,
  bond_amount DECIMAL(12,2),
  license_required BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs table
CREATE TABLE audit_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  record_id UUID,
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_assigned_agent ON claims(assigned_agent_id);
CREATE INDEX idx_claims_state ON claims(state);
CREATE INDEX idx_claims_amount ON claims(amount_reported);
CREATE INDEX idx_activities_claim_id ON activities(claim_id);
CREATE INDEX idx_activities_created_at ON activities(created_at);
CREATE INDEX idx_documents_claim_id ON documents(claim_id);
CREATE INDEX idx_lead_scores_claim_id ON lead_scores(claim_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_claimants_name ON claimants(last_name, first_name);

-- Enable Row Level Security
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE state_batches ENABLE ROW LEVEL SECURITY;
ALTER TABLE claimants ENABLE ROW LEVEL SECURITY;
ALTER TABLE addresses ENABLE ROW LEVEL SECURITY;
ALTER TABLE phones ENABLE ROW LEVEL SECURITY;
ALTER TABLE emails ENABLE ROW LEVEL SECURITY;
ALTER TABLE claims ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE lead_scores ENABLE ROW LEVEL SECURITY;
ALTER TABLE fee_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (basic examples - customize based on your needs)
CREATE POLICY "Users can view their own data" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can view all claims" ON claims
  FOR SELECT USING (true);

CREATE POLICY "Users can update assigned claims" ON claims
  FOR UPDATE USING (assigned_agent_id = auth.uid() OR 
    EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role IN ('admin', 'senior_agent')));

CREATE POLICY "Users can view all claimants" ON claimants
  FOR SELECT USING (true);

CREATE POLICY "Users can view all activities" ON activities
  FOR SELECT USING (true);

CREATE POLICY "Users can insert activities" ON activities
  FOR INSERT WITH CHECK (agent_id = auth.uid());

CREATE POLICY "Users can view all documents" ON documents
  FOR SELECT USING (true);

CREATE POLICY "Admins can view all audit logs" ON audit_logs
  FOR SELECT USING (EXISTS (SELECT 1 FROM users WHERE id = auth.uid() AND role = 'admin'));

-- Create functions for automatic timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_claimants_updated_at BEFORE UPDATE ON claimants
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_claims_updated_at BEFORE UPDATE ON claims
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_fee_configs_updated_at BEFORE UPDATE ON fee_configs
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to automatically update last_status_at when status changes
CREATE OR REPLACE FUNCTION update_claim_status_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status IS DISTINCT FROM NEW.status THEN
        NEW.last_status_at = NOW();
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_claim_status_timestamp BEFORE UPDATE ON claims
    FOR EACH ROW EXECUTE FUNCTION update_claim_status_timestamp();

-- Insert sample fee configurations
INSERT INTO fee_configs (state, max_fee_pct, bond_required, bond_amount, license_required) VALUES
('CA', 10.00, true, 50000, true),
('TX', 15.00, true, 25000, false),
('FL', 20.00, false, 0, true),
('NY', 12.50, true, 100000, true),
('IL', 15.00, true, 50000, false);

-- Insert sample admin user (you'll need to update this with real data)
INSERT INTO users (email, role, name) VALUES
('<EMAIL>', 'admin', 'System Administrator');

-- Create a view for dashboard statistics
CREATE OR REPLACE VIEW dashboard_stats AS
SELECT 
  COUNT(*) as total_claims,
  COUNT(*) FILTER (WHERE status NOT IN ('paid', 'closed')) as active_claims,
  COALESCE(SUM(amount_reported), 0) as total_value,
  ROUND(
    COUNT(*) FILTER (WHERE status = 'paid')::DECIMAL / 
    NULLIF(COUNT(*) FILTER (WHERE status IN ('paid', 'closed')), 0) * 100, 
    2
  ) as success_rate
FROM claims;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated; 