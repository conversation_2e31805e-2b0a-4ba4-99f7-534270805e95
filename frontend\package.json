{"name": "assethunterpro-frontend", "private": true, "version": "0.0.0", "type": "module", "description": "AssetHunterPro - Professional Asset Recovery Platform", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "devDependencies": {"@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/node": "^22.10.5", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5.7.3", "vite": "^6.3.5"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@supabase/supabase-js": "^2.49.8", "@tanstack/react-query": "^5.77.2", "@tanstack/react-router": "^1.120.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "lucide-react": "^0.511.0", "node-fetch": "^3.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.56.4", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "zod": "^3.25.30"}}