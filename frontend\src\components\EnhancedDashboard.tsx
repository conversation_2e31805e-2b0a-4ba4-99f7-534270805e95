// Enhanced Dashboard Component
// Integrates the new backend services from implementation plan

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  TrendingUp, 
  AlertTriangle, 
  Users, 
  DollarSign, 
  Clock, 
  Activity,
  FileText,
  Download,
  Zap,
  BarChart3
} from 'lucide-react';

// Import our new services
import { enhancedDashboardService } from '@/services/enhancedDashboardService';
import { realTimeMonitoringService, getMonitoringMetrics } from '@/services/realTimeMonitoringService';
import { batchProcessingEngine, getBatchProgress } from '@/services/batchProcessingEngine';
import type { BatchProgress } from '@/services/batchProcessingEngine';
import type { DashboardStats, ChartData, MonitoringAlert } from '@/types/backend';

interface EnhancedDashboardProps {
  userId: string;
  userRole: string;
  onNavigate?: (section: string) => void;
}

export const EnhancedDashboard: React.FC<EnhancedDashboardProps> = ({ 
  userId, 
  userRole, 
  onNavigate 
}) => {
  // State management
  const [timeRange, setTimeRange] = useState('30d');
  const [dashboardStats, setDashboardStats] = useState<DashboardStats | null>(null);
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [recentAlerts, setRecentAlerts] = useState<MonitoringAlert[]>([]);
  const [batchJobs, setBatchJobs] = useState<BatchProgress[]>([]);
  const [monitoringMetrics, setMonitoringMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);

  // Load dashboard data
  useEffect(() => {
    loadDashboardData();
  }, [userId, timeRange, refreshKey]);

  // Real-time updates every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      loadRealtimeData();
    }, 30000);

    return () => clearInterval(interval);
  }, [userId]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      const [stats, charts, alerts] = await Promise.all([
        enhancedDashboardService.getDashboardStats(userId, timeRange),
        enhancedDashboardService.getChartData(userId, timeRange),
        enhancedDashboardService.getRecentAlerts(userId),
      ]);

      setDashboardStats(stats);
      setChartData(charts);
      setRecentAlerts(alerts);
    } catch (error) {
      console.error('Error loading dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadRealtimeData = async () => {
    try {
      const [alerts, jobs, metrics] = await Promise.all([
        enhancedDashboardService.getRecentAlerts(userId, 5),
        batchProcessingEngine.getAllActiveJobs(),
        getMonitoringMetrics(),
      ]);

      setRecentAlerts(alerts);
      setBatchJobs(jobs);
      setMonitoringMetrics(metrics);
    } catch (error) {
      console.error('Error loading realtime data:', error);
    }
  };

  const handleRefresh = () => {
    setRefreshKey(prev => prev + 1);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getAlertLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'destructive';
      case 'high': return 'destructive';
      case 'medium': return 'default';
      case 'low': return 'secondary';
      default: return 'secondary';
    }
  };

  const getJobStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'processing': return 'bg-blue-100 text-blue-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'queued': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="flex flex-col items-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <p className="text-sm text-gray-600">Loading enhanced dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6 p-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Enhanced Dashboard</h1>
          <p className="text-gray-600">Advanced analytics and real-time monitoring</p>
        </div>
        <div className="flex items-center space-x-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Select time range" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
          <Button onClick={handleRefresh} variant="outline" size="sm">
            <Activity className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Claims Found</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.totalClaimsFound?.toLocaleString()}</div>
            <p className="text-xs text-muted-foreground">
              {timeRange === '7d' ? 'Last 7 days' : timeRange === '30d' ? 'Last 30 days' : 'Selected period'}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(dashboardStats?.totalValue || 0)}</div>
            <p className="text-xs text-muted-foreground">
              Potential recovery value
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Targets</CardTitle>
            <Users className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.activeTargets}</div>
            <p className="text-xs text-muted-foreground">
              Currently monitored
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardStats?.successRate?.toFixed(1)}%</div>
            <p className="text-xs text-muted-foreground">
              Claims success rate
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="alerts" className="space-y-4">
        <TabsList>
          <TabsTrigger value="alerts">Recent Alerts</TabsTrigger>
          <TabsTrigger value="batch">Batch Processing</TabsTrigger>
          <TabsTrigger value="monitoring">System Monitoring</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Recent Alerts Tab */}
        <TabsContent value="alerts">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Recent Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              {recentAlerts.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No recent alerts</p>
              ) : (
                <div className="space-y-4">
                  {recentAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2">
                          <Badge variant={getAlertLevelColor(alert.alertLevel)}>
                            {alert.alertLevel.toUpperCase()}
                          </Badge>
                          <h4 className="font-medium">{alert.alertData.personName}</h4>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">
                          Value: {formatCurrency(alert.alertData.totalValue)} | 
                          Confidence: {(alert.alertData.highestConfidence * 100).toFixed(1)}%
                        </p>
                        <p className="text-xs text-gray-500">
                          {new Date(alert.createdAt).toLocaleDateString()} at {new Date(alert.createdAt).toLocaleTimeString()}
                        </p>
                      </div>
                      {alert.alertData.actionRequired && (
                        <Button size="sm" variant="outline">
                          Action Required
                        </Button>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Batch Processing Tab */}
        <TabsContent value="batch">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Zap className="h-5 w-5 mr-2" />
                Batch Processing Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              {batchJobs.length === 0 ? (
                <p className="text-gray-500 text-center py-8">No active batch jobs</p>
              ) : (
                <div className="space-y-4">
                  {batchJobs.map((job) => (
                    <div key={job.jobId} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium">Job {job.jobId}</h4>
                        <Badge className={getJobStatusColor(job.status)}>
                          {job.status.toUpperCase()}
                        </Badge>
                      </div>
                      <div className="space-y-2">
                        <div className="flex justify-between text-sm">
                          <span>Progress: {job.processedItems}/{job.totalItems}</span>
                          <span>{job.progress.toFixed(1)}%</span>
                        </div>
                        <Progress value={job.progress} className="h-2" />
                        <div className="flex justify-between text-xs text-gray-500">
                          <span>Matches Found: {job.matchesFound}</span>
                          <span>Errors: {job.errorsCount}</span>
                        </div>
                        {job.throughputPerMinute > 0 && (
                          <p className="text-xs text-gray-500">
                            Throughput: {job.throughputPerMinute.toFixed(1)} items/min
                          </p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* System Monitoring Tab */}
        <TabsContent value="monitoring">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                System Monitoring
              </CardTitle>
            </CardHeader>
            <CardContent>
              {monitoringMetrics ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="p-4 bg-blue-50 rounded-lg">
                    <h4 className="font-medium text-blue-900">Active Targets</h4>
                    <p className="text-2xl font-bold text-blue-600">{monitoringMetrics.activeTargets}</p>
                  </div>
                  <div className="p-4 bg-green-50 rounded-lg">
                    <h4 className="font-medium text-green-900">24h Alerts</h4>
                    <p className="text-2xl font-bold text-green-600">{monitoringMetrics.alertsGenerated24h}</p>
                  </div>
                  <div className="p-4 bg-purple-50 rounded-lg">
                    <h4 className="font-medium text-purple-900">Success Rate</h4>
                    <p className="text-2xl font-bold text-purple-600">{monitoringMetrics.successRate}%</p>
                  </div>
                  <div className="p-4 bg-yellow-50 rounded-lg">
                    <h4 className="font-medium text-yellow-900">Avg Response</h4>
                    <p className="text-2xl font-bold text-yellow-600">{monitoringMetrics.averageResponseTime}ms</p>
                  </div>
                  <div className="p-4 bg-indigo-50 rounded-lg col-span-2">
                    <h4 className="font-medium text-indigo-900">States Monitored</h4>
                    <p className="text-sm text-indigo-600 mt-2">
                      {monitoringMetrics.statesMonitored?.join(', ') || 'None'}
                    </p>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">Loading monitoring metrics...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Analytics Overview
              </CardTitle>
            </CardHeader>
            <CardContent>
              {chartData ? (
                <div className="space-y-6">
                  {/* Property Types Distribution */}
                  <div>
                    <h4 className="font-medium mb-4">Property Types Distribution</h4>
                    <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                      {chartData.propertyTypes.map((type) => (
                        <div key={type.type} className="p-3 border rounded-lg">
                          <h5 className="font-medium text-sm">{type.type}</h5>
                          <p className="text-lg font-bold">{type.count}</p>
                          <p className="text-xs text-gray-500">{type.percentage}%</p>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Top States */}
                  <div>
                    <h4 className="font-medium mb-4">Top States by Value</h4>
                    <div className="space-y-2">
                      {chartData.stateDistribution.slice(0, 5).map((state) => (
                        <div key={state.state} className="flex justify-between items-center p-2 border rounded">
                          <span className="font-medium">{state.state}</span>
                          <div className="text-right">
                            <p className="font-bold">{formatCurrency(state.value)}</p>
                            <p className="text-xs text-gray-500">{state.count} claims</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ) : (
                <p className="text-gray-500 text-center py-8">Loading analytics data...</p>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-4">
            <Button onClick={() => onNavigate?.('batch-upload')} variant="default">
              <Download className="h-4 w-4 mr-2" />
              Start Batch Upload
            </Button>
            <Button onClick={() => onNavigate?.('monitoring')} variant="outline">
              <Activity className="h-4 w-4 mr-2" />
              Configure Monitoring
            </Button>
            <Button onClick={() => onNavigate?.('reports')} variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button onClick={() => onNavigate?.('ai-discovery')} variant="outline">
              <Zap className="h-4 w-4 mr-2" />
              AI Discovery
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default EnhancedDashboard; 