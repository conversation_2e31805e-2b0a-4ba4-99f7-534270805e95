import React, { useState, useEffect } from 'react';
import { 
  Activity, Server, Database, Wifi, HardDrive, Cpu, 
  AlertTriangle, CheckCircle, Clock, TrendingUp, Zap,
  RefreshCw, Download, Setting<PERSON>, Bell, Monitor
} from 'lucide-react';
import { AdministratorState } from '../types';

interface SystemMonitoringProps {
  adminState: AdministratorState;
}

export const SystemMonitoring: React.FC<SystemMonitoringProps> = ({ adminState }) => {
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [selectedTimeRange, setSelectedTimeRange] = useState<'1h' | '24h' | '7d' | '30d'>('24h');

  useEffect(() => {
    const mockMetrics: SystemMetrics = {
      servers: {
        webServers: { healthy: 3, total: 3, uptime: 99.98 },
        databases: { healthy: 2, total: 2, uptime: 99.99 },
        apiServers: { healthy: 4, total: 4, uptime: 99.97 }
      },
      performance: {
        avgResponseTime: 142,
        throughput: 1247,
        errorRate: 0.02,
        activeConnections: 234
      },
      resources: {
        cpuUsage: 68,
        memoryUsage: 73,
        diskUsage: 45,
        networkBandwidth: 52
      },
      security: {
        loginAttempts: 1247,
        blockedAttacks: 23,
        securityEvents: 5
      }
    };

    const mockAlerts: Alert[] = [
      {
        id: 'alert-001',
        type: 'performance',
        severity: 'medium',
        title: 'High Memory Usage',
        message: 'Memory usage at 87% on DB-01 server',
        timestamp: new Date(Date.now() - 1800000),
        status: 'active'
      },
      {
        id: 'alert-002',
        type: 'security',
        severity: 'high',
        title: 'Suspicious Login Activity',
        message: 'Multiple failed login attempts from IP *************',
        timestamp: new Date(Date.now() - 3600000),
        status: 'active'
      }
    ];

    setSystemMetrics(mockMetrics);
    setAlerts(mockAlerts);
  }, [selectedTimeRange]);

  const getHealthColor = (percentage: number) => {
    if (percentage >= 95) return 'text-green-600';
    if (percentage >= 90) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getUsageColor = (percentage: number) => {
    if (percentage <= 70) return 'bg-green-500';
    if (percentage <= 85) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (!systemMetrics) {
    return <div className="flex items-center justify-center h-96">Loading...</div>;
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">System Monitoring</h2>
          <p className="text-gray-600">Real-time system health and performance monitoring</p>
        </div>
        <div className="flex space-x-3">
          <select
            value={selectedTimeRange}
            onChange={(e) => setSelectedTimeRange(e.target.value as any)}
            className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* System Status Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Server className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">System Status</p>
              <p className="text-2xl font-bold text-green-600">Healthy</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Uptime</p>
              <p className="text-2xl font-bold text-gray-900">99.98%</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Response Time</p>
              <p className="text-2xl font-bold text-gray-900">{systemMetrics.performance.avgResponseTime}ms</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Active Alerts</p>
              <p className="text-2xl font-bold text-gray-900">{alerts.filter(a => a.status === 'active').length}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Server Health */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Server Health</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">Web Servers</h4>
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {systemMetrics.servers.webServers.healthy}/{systemMetrics.servers.webServers.total} Healthy
            </p>
            <p className={`text-lg font-semibold ${getHealthColor(systemMetrics.servers.webServers.uptime)}`}>
              {systemMetrics.servers.webServers.uptime}% Uptime
            </p>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">Databases</h4>
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {systemMetrics.servers.databases.healthy}/{systemMetrics.servers.databases.total} Healthy
            </p>
            <p className={`text-lg font-semibold ${getHealthColor(systemMetrics.servers.databases.uptime)}`}>
              {systemMetrics.servers.databases.uptime}% Uptime
            </p>
          </div>

          <div className="p-4 border border-gray-200 rounded-lg">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">API Servers</h4>
              <CheckCircle className="w-5 h-5 text-green-500" />
            </div>
            <p className="text-sm text-gray-600 mb-2">
              {systemMetrics.servers.apiServers.healthy}/{systemMetrics.servers.apiServers.total} Healthy
            </p>
            <p className={`text-lg font-semibold ${getHealthColor(systemMetrics.servers.apiServers.uptime)}`}>
              {systemMetrics.servers.apiServers.uptime}% Uptime
            </p>
          </div>
        </div>
      </div>

      {/* Resource Usage */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-6">Resource Usage</h3>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">CPU Usage</span>
              <span className="text-sm text-gray-600">{systemMetrics.resources.cpuUsage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(systemMetrics.resources.cpuUsage)}`}
                style={{ width: `${systemMetrics.resources.cpuUsage}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Memory Usage</span>
              <span className="text-sm text-gray-600">{systemMetrics.resources.memoryUsage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(systemMetrics.resources.memoryUsage)}`}
                style={{ width: `${systemMetrics.resources.memoryUsage}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Disk Usage</span>
              <span className="text-sm text-gray-600">{systemMetrics.resources.diskUsage}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(systemMetrics.resources.diskUsage)}`}
                style={{ width: `${systemMetrics.resources.diskUsage}%` }}
              ></div>
            </div>
          </div>

          <div>
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium text-gray-700">Network</span>
              <span className="text-sm text-gray-600">{systemMetrics.resources.networkBandwidth}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className={`h-2 rounded-full ${getUsageColor(systemMetrics.resources.networkBandwidth)}`}
                style={{ width: `${systemMetrics.resources.networkBandwidth}%` }}
              ></div>
            </div>
          </div>
        </div>
      </div>

      {/* Active Alerts */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-gray-900">Active Alerts</h3>
          <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
            View All Alerts
          </button>
        </div>
        <div className="space-y-4">
          {alerts.filter(alert => alert.status === 'active').map(alert => (
            <div key={alert.id} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
              <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
                alert.severity === 'high' ? 'bg-red-100' :
                alert.severity === 'medium' ? 'bg-yellow-100' : 'bg-blue-100'
              }`}>
                <AlertTriangle className={`w-4 h-4 ${
                  alert.severity === 'high' ? 'text-red-600' :
                  alert.severity === 'medium' ? 'text-yellow-600' : 'text-blue-600'
                }`} />
              </div>
              <div className="flex-1">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium text-gray-900">{alert.title}</h4>
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    alert.severity === 'high' ? 'bg-red-100 text-red-800' :
                    alert.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' : 
                    'bg-blue-100 text-blue-800'
                  }`}>
                    {alert.severity.toUpperCase()}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mt-1">{alert.message}</p>
                <p className="text-xs text-gray-500 mt-2">
                  {new Date(alert.timestamp).toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

interface SystemMetrics {
  servers: {
    webServers: { healthy: number; total: number; uptime: number };
    databases: { healthy: number; total: number; uptime: number };
    apiServers: { healthy: number; total: number; uptime: number };
  };
  performance: {
    avgResponseTime: number;
    throughput: number;
    errorRate: number;
    activeConnections: number;
  };
  resources: {
    cpuUsage: number;
    memoryUsage: number;
    diskUsage: number;
    networkBandwidth: number;
  };
  security: {
    loginAttempts: number;
    blockedAttacks: number;
    securityEvents: number;
  };
}

interface Alert {
  id: string;
  type: 'performance' | 'security' | 'error' | 'warning';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  status: 'active' | 'resolved';
}