import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  FileText, 
  Send, 
  Eye, 
  Download, 
  Clock, 
  CheckCircle, 
  XCircle,
  AlertTriangle,
  User,
  Mail,
  Calendar,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { docuSignService, type EnvelopeRequest, type EnvelopeStatus, type Signer } from '@/services/docusignService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

interface DocumentSigningProps {
  claimId: string
  claimantId: string
  documentTemplate: string
  onSigningComplete?: (envelopeId: string) => void
  onSigningCancelled?: () => void
}

export const DocumentSigning: React.FC<DocumentSigningProps> = ({
  claimId,
  claimantId,
  documentTemplate,
  onSigningComplete,
  onSigningCancelled
}) => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [currentStep, setCurrentStep] = useState<'setup' | 'sent' | 'tracking'>('setup')
  
  // Envelope data
  const [envelopeId, setEnvelopeId] = useState<string>('')
  const [envelopeStatus, setEnvelopeStatus] = useState<EnvelopeStatus | null>(null)
  const [signingUrl, setSigningUrl] = useState<string>('')
  
  // Signer information
  const [signerEmail, setSignerEmail] = useState('')
  const [signerName, setSignerName] = useState('')
  const [subject, setSubject] = useState('Asset Recovery Agreement - Signature Required')
  const [message, setMessage] = useState('Please review and sign the attached asset recovery agreement.')

  useEffect(() => {
    if (envelopeId && currentStep === 'tracking') {
      const interval = setInterval(() => {
        checkEnvelopeStatus()
      }, 30000) // Check every 30 seconds

      return () => clearInterval(interval)
    }
  }, [envelopeId, currentStep])

  const createAndSendEnvelope = async () => {
    if (!signerEmail || !signerName) {
      setError('Please provide signer email and name')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Prepare document data
      const documentData = {
        id: '1',
        name: 'Asset Recovery Agreement.pdf',
        content: documentTemplate, // This should be base64 encoded PDF
        contentType: 'application/pdf'
      }

      // Prepare signer information
      const signer: Signer = {
        email: signerEmail,
        name: signerName,
        recipientId: '1',
        routingOrder: 1,
        tabs: {
          signHereTabs: [
            {
              anchorString: '/sn1/',
              anchorXOffset: '20',
              anchorYOffset: '10'
            }
          ],
          dateSignedTabs: [
            {
              anchorString: '/ds1/',
              anchorXOffset: '20',
              anchorYOffset: '10'
            }
          ],
          textTabs: [
            {
              anchorString: '/claimant_name/',
              value: signerName,
              required: true
            }
          ]
        }
      }

      // Create envelope request
      const envelopeRequest: EnvelopeRequest = {
        documents: [documentData],
        signers: [signer],
        subject,
        message,
        status: 'sent',
        claimId,
        claimantId
      }

      // Send to DocuSign
      const { data, error } = await docuSignService.createEnvelope(envelopeRequest)

      if (error) {
        setError(error.message || 'Failed to create envelope')
        return
      }

      if (data) {
        setEnvelopeId(data.envelopeId)
        setCurrentStep('sent')
        
        // Get signing URL
        const { data: urlData } = await docuSignService.getSigningUrl(data.envelopeId, '1')
        if (urlData) {
          setSigningUrl(urlData.url)
        }

        toast({
          title: "Document Sent for Signature",
          description: `Envelope ${data.envelopeId} has been sent to ${signerEmail}`,
        })

        // Start tracking
        setTimeout(() => {
          setCurrentStep('tracking')
          checkEnvelopeStatus()
        }, 2000)
      }
    } catch (error) {
      setError('Failed to send document for signature')
      console.error('Document signing error:', error)
    } finally {
      setLoading(false)
    }
  }

  const checkEnvelopeStatus = async () => {
    if (!envelopeId) return

    try {
      const { data, error } = await docuSignService.getEnvelopeStatus(envelopeId)

      if (error) {
        console.error('Error checking envelope status:', error)
        return
      }

      if (data) {
        setEnvelopeStatus(data)

        // Check if signing is complete
        if (data.status === 'completed') {
          if (onSigningComplete) {
            onSigningComplete(envelopeId)
          }
          
          toast({
            title: "Document Signed Successfully",
            description: "The asset recovery agreement has been completed.",
          })
        }
      }
    } catch (error) {
      console.error('Failed to check envelope status:', error)
    }
  }

  const downloadCompletedDocument = async () => {
    if (!envelopeId) return

    setLoading(true)

    try {
      const { data, error } = await docuSignService.downloadDocuments(envelopeId)

      if (error) {
        setError('Failed to download document')
        return
      }

      if (data) {
        // Create download link
        const blob = new Blob([data], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `signed-agreement-${envelopeId}.pdf`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)

        toast({
          title: "Document Downloaded",
          description: "The signed agreement has been downloaded.",
        })
      }
    } catch (error) {
      setError('Failed to download document')
      console.error('Document download error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-700'
      case 'signed': return 'bg-blue-100 text-blue-700'
      case 'sent': return 'bg-yellow-100 text-yellow-700'
      case 'delivered': return 'bg-purple-100 text-purple-700'
      case 'declined': return 'bg-red-100 text-red-700'
      case 'voided': return 'bg-gray-100 text-gray-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4" />
      case 'signed': return <CheckCircle className="h-4 w-4" />
      case 'sent': return <Send className="h-4 w-4" />
      case 'delivered': return <Mail className="h-4 w-4" />
      case 'declined': return <XCircle className="h-4 w-4" />
      case 'voided': return <XCircle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Signing</h2>
          <p className="text-gray-600">Send documents for electronic signature</p>
        </div>
        {onSigningCancelled && (
          <Button variant="outline" onClick={onSigningCancelled}>
            Cancel
          </Button>
        )}
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs value={currentStep} className="space-y-6">
        <TabsList>
          <TabsTrigger value="setup" disabled={currentStep !== 'setup'}>Setup</TabsTrigger>
          <TabsTrigger value="sent" disabled={currentStep !== 'sent'}>Sent</TabsTrigger>
          <TabsTrigger value="tracking" disabled={currentStep !== 'tracking'}>Tracking</TabsTrigger>
        </TabsList>

        <TabsContent value="setup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Document Setup</span>
              </CardTitle>
              <CardDescription>
                Configure the document and signer information
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Signer Name</label>
                  <Input
                    placeholder="Enter signer's full name"
                    value={signerName}
                    onChange={(e) => setSignerName(e.target.value)}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Signer Email</label>
                  <Input
                    type="email"
                    placeholder="Enter signer's email"
                    value={signerEmail}
                    onChange={(e) => setSignerEmail(e.target.value)}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Email Subject</label>
                <Input
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                />
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Email Message</label>
                <textarea
                  className="w-full p-3 border rounded-lg resize-none"
                  rows={3}
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                />
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center space-x-3">
                  <FileText className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium">Asset Recovery Agreement</div>
                    <div className="text-sm text-gray-500">PDF Document</div>
                  </div>
                </div>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-2" />
                  Preview
                </Button>
              </div>

              <Button 
                onClick={createAndSendEnvelope}
                disabled={loading || !signerEmail || !signerName}
                className="w-full"
              >
                {loading ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                Send for Signature
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="sent" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Send className="h-5 w-5 text-green-600" />
                <span>Document Sent Successfully</span>
              </CardTitle>
              <CardDescription>
                The document has been sent to {signerEmail} for signature
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg bg-green-50">
                <div>
                  <div className="font-medium">Envelope ID: {envelopeId}</div>
                  <div className="text-sm text-gray-600">Sent to: {signerEmail}</div>
                </div>
                <Badge className="bg-green-100 text-green-700">
                  Sent
                </Badge>
              </div>

              {signingUrl && (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Direct Signing Link</label>
                  <div className="flex space-x-2">
                    <Input value={signingUrl} readOnly />
                    <Button
                      variant="outline"
                      onClick={() => window.open(signingUrl, '_blank')}
                    >
                      <ExternalLink className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}

              <div className="text-center py-4">
                <p className="text-gray-600 mb-4">
                  The signer will receive an email with instructions to sign the document.
                </p>
                <Button onClick={() => setCurrentStep('tracking')}>
                  <Clock className="mr-2 h-4 w-4" />
                  Start Tracking
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="tracking" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <Clock className="h-5 w-5" />
                  <span>Signature Status</span>
                </span>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={checkEnvelopeStatus}
                  disabled={loading}
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  Refresh
                </Button>
              </CardTitle>
              <CardDescription>
                Track the progress of your document signature
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {envelopeStatus && (
                <>
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(envelopeStatus.status)}
                      <div>
                        <div className="font-medium">Envelope Status</div>
                        <div className="text-sm text-gray-500">
                          Last updated: {new Date(envelopeStatus.statusDateTime).toLocaleString()}
                        </div>
                      </div>
                    </div>
                    <Badge className={getStatusColor(envelopeStatus.status)}>
                      {envelopeStatus.status.charAt(0).toUpperCase() + envelopeStatus.status.slice(1)}
                    </Badge>
                  </div>

                  {envelopeStatus.recipients && (
                    <div className="space-y-2">
                      <h4 className="font-medium">Recipients</h4>
                      {envelopeStatus.recipients.map((recipient, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div className="flex items-center space-x-3">
                            <User className="h-4 w-4 text-gray-500" />
                            <div>
                              <div className="font-medium">{recipient.name}</div>
                              <div className="text-sm text-gray-500">{recipient.email}</div>
                            </div>
                          </div>
                          <Badge className={getStatusColor(recipient.status)}>
                            {recipient.status}
                          </Badge>
                        </div>
                      ))}
                    </div>
                  )}

                  {envelopeStatus.status === 'completed' && (
                    <Button 
                      onClick={downloadCompletedDocument}
                      disabled={loading}
                      className="w-full"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download Signed Document
                    </Button>
                  )}
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
