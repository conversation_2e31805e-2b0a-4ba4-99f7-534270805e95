import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { 
  Phone, 
  Mail, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  MessageSquare,
  Calendar,
  ExternalLink
} from 'lucide-react';
import { Claim } from '../../types';

interface QuickActionsProps {
  claim: Claim;
  onAddActivity: (title: string, description: string, activityType: string) => Promise<void>;
  onUpdateStatus: (status: string) => Promise<void>;
  onOpenEmailTemplates?: () => void;
}

export const QuickActions: React.FC<QuickActionsProps> = ({
  claim,
  onAddActivity,
  onUpdateStatus,
  onOpenEmailTemplates
}) => {
  const [loading, setLoading] = useState<string | null>(null);

  const handleQuickAction = async (
    title: string, 
    description: string, 
    activityType: string,
    actionKey: string
  ) => {
    setLoading(actionKey);
    try {
      await onAddActivity(title, description, activityType);
    } finally {
      setLoading(null);
    }
  };

  const handleStatusUpdate = async (status: string, actionKey: string) => {
    setLoading(actionKey);
    try {
      await onUpdateStatus(status);
    } finally {
      setLoading(null);
    }
  };

  const formatPhoneNumber = (phone: string) => {
    if (!phone) return '';
    // Simple phone formatting - can be enhanced
    return phone.replace(/[^\d]/g, '');
  };

  return (
    <div className="bg-white border rounded-lg p-4 space-y-4">
      <h3 className="font-semibold text-gray-900 mb-3">Quick Actions</h3>
      
      {/* Communication Actions */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-600">Contact</h4>
        <div className="grid grid-cols-2 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Open phone dialer (web browsers will handle tel: links)
              const phone = formatPhoneNumber(claim.owner_address || ''); // Note: you might want a dedicated phone field
              if (phone) {
                window.open(`tel:${phone}`, '_self');
              }
              handleQuickAction(
                'Phone call attempted',
                `Called ${claim.owner_name} at ${phone || 'primary number'}`,
                'call',
                'call'
              );
            }}
            disabled={loading === 'call'}
            className="justify-start"
          >
            <Phone className="h-4 w-4 mr-2" />
            Call
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => {
              // Pre-compose email
              const subject = `RE: Unclaimed Property Claim ${claim.property_id || claim.id.slice(0, 8)}`;
              const body = `Dear ${claim.owner_name},\n\nI am contacting you regarding your unclaimed property claim in the amount of ${new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(claim.amount)}.\n\nPlease contact me at your earliest convenience to discuss next steps.\n\nBest regards,\n[Your Name]`;
              window.open(`mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`, '_self');
              
              handleQuickAction(
                'Email sent',
                `Sent initial contact email to ${claim.owner_name}`,
                'email',
                'email'
              );
            }}
            disabled={loading === 'email'}
            className="justify-start"
          >
            <Mail className="h-4 w-4 mr-2" />
            Email
          </Button>
        </div>
      </div>

      {/* Status Updates */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-600">Status Updates</h4>
        <div className="grid grid-cols-1 gap-2">
          {claim.status === 'new' && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('contacted', 'contacted')}
              disabled={loading === 'contacted'}
              className="justify-start bg-blue-600 hover:bg-blue-700"
            >
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark as Contacted
            </Button>
          )}
          
          {claim.status === 'contacted' && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('in_progress', 'in_progress')}
              disabled={loading === 'in_progress'}
              className="justify-start bg-orange-600 hover:bg-orange-700"
            >
              <Clock className="h-4 w-4 mr-2" />
              Mark In Progress
            </Button>
          )}
          
          {['contacted', 'in_progress'].includes(claim.status) && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('documents_requested', 'documents_requested')}
              disabled={loading === 'documents_requested'}
              className="justify-start bg-purple-600 hover:bg-purple-700"
            >
              <ExternalLink className="h-4 w-4 mr-2" />
              Request Documents
            </Button>
          )}
        </div>
      </div>

      {/* Quick Notes */}
      <div className="space-y-2">
        <h4 className="text-sm font-medium text-gray-600">Quick Notes</h4>
        <div className="grid grid-cols-1 gap-2">
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleQuickAction(
              'Left voicemail',
              'Called claimant, left detailed voicemail with callback instructions',
              'call',
              'voicemail'
            )}
            disabled={loading === 'voicemail'}
            className="justify-start text-left"
          >
            <MessageSquare className="h-4 w-4 mr-2" />
            Left Voicemail
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleQuickAction(
              'Documents requested',
              'Requested identification and supporting documentation via email',
              'document_request',
              'docs_requested'
            )}
            disabled={loading === 'docs_requested'}
            className="justify-start"
          >
            <ExternalLink className="h-4 w-4 mr-2" />
            Docs Requested
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleQuickAction(
              'Follow-up scheduled',
              `Scheduled follow-up for ${new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toLocaleDateString()}`,
              'follow_up',
              'follow_up'
            )}
            disabled={loading === 'follow_up'}
            className="justify-start"
          >
            <Calendar className="h-4 w-4 mr-2" />
            Schedule Follow-up
          </Button>
          
          <Button
            size="sm"
            variant="outline"
            onClick={() => handleQuickAction(
              'Needs review',
              'Case requires additional review due to complexity or documentation issues',
              'review_needed',
              'review'
            )}
            disabled={loading === 'review'}
            className="justify-start"
          >
            <AlertTriangle className="h-4 w-4 mr-2" />
            Needs Review
          </Button>
        </div>
      </div>

      {/* Call Owner */}
      <Button
        variant="outline" 
        className="h-auto py-3 px-4 text-left"
        onClick={() => handleQuickAction(
          'Phone Call Made',
          `Called ${claim.owner_name} regarding claim ${claim.property_id || claim.id.slice(0, 8)}`,
          'phone_call',
          'call'
        )}
        disabled={loading === 'call'}
      >
        <div className="flex items-center gap-3 w-full">
          <Phone className="h-5 w-5 text-blue-600" />
          <div>
            <div className="font-medium">Call Owner</div>
            <div className="text-xs text-gray-500">Quick phone call</div>
          </div>
        </div>
      </Button>

      {/* Send Email */}
      <Button
        variant="outline" 
        className="h-auto py-3 px-4 text-left"
        onClick={onOpenEmailTemplates}
      >
        <div className="flex items-center gap-3 w-full">
          <Mail className="h-5 w-5 text-green-600" />
          <div>
            <div className="font-medium">Send Email</div>
            <div className="text-xs text-gray-500">Use templates</div>
          </div>
        </div>
      </Button>
    </div>
  );
}; 