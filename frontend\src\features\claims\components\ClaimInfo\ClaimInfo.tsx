import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { FileText, MapPin } from 'lucide-react';
import { Claim, ClaimFormData, ClaimStatus, ClaimPriority } from '../../types/claim.types';
import { getStatusColor, getPriorityColor } from '../../utils/statusHelpers';
import { formatCurrency } from '@/utils/formatting/currency';

interface ClaimInfoProps {
  claim: Claim;
  isEditing: boolean;
  editedClaim: ClaimFormData;
  onUpdate: (data: ClaimFormData) => void;
}

export const ClaimInfo: React.FC<ClaimInfoProps> = ({
  claim,
  isEditing,
  editedClaim,
  onUpdate
}) => {
  const handleInputChange = (field: keyof ClaimFormData, value: any) => {
    onUpdate({ ...editedClaim, [field]: value });
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Claim Information
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Basic Information */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-600">Owner Name</label>
            {isEditing ? (
              <Input
                value={editedClaim.owner_name || ''}
                onChange={(e) => handleInputChange('owner_name', e.target.value)}
                className="mt-1"
              />
            ) : (
              <p className="text-lg font-semibold">{claim.owner_name}</p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Amount</label>
            {isEditing ? (
              <Input
                type="number"
                value={editedClaim.amount || ''}
                onChange={(e) => handleInputChange('amount', parseFloat(e.target.value))}
                className="mt-1"
              />
            ) : (
              <p className="text-lg font-semibold text-green-600">{formatCurrency(claim.amount)}</p>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Status</label>
            {isEditing ? (
              <Select 
                value={editedClaim.status || ''} 
                onValueChange={(value) => handleInputChange('status', value as ClaimStatus)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="new">New</SelectItem>
                  <SelectItem value="assigned">Assigned</SelectItem>
                  <SelectItem value="contacted">Contacted</SelectItem>
                  <SelectItem value="in_progress">In Progress</SelectItem>
                  <SelectItem value="documents_requested">Documents Requested</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="approved">Approved</SelectItem>
                  <SelectItem value="completed">Completed</SelectItem>
                  <SelectItem value="on_hold">On Hold</SelectItem>
                  <SelectItem value="cancelled">Cancelled</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Badge className={getStatusColor(claim.status as ClaimStatus)}>
                {claim.status.replace('_', ' ')}
              </Badge>
            )}
          </div>
          
          <div>
            <label className="text-sm font-medium text-gray-600">Priority</label>
            {isEditing ? (
              <Select 
                value={editedClaim.priority || ''} 
                onValueChange={(value) => handleInputChange('priority', value as ClaimPriority)}
              >
                <SelectTrigger className="mt-1">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="low">Low</SelectItem>
                  <SelectItem value="medium">Medium</SelectItem>
                  <SelectItem value="high">High</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                </SelectContent>
              </Select>
            ) : (
              <Badge className={getPriorityColor(claim.priority as ClaimPriority)}>
                {claim.priority}
              </Badge>
            )}
          </div>
        </div>

        <Separator />

        {/* Address Information */}
        <div>
          <h4 className="font-semibold mb-3 flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            Address Information
          </h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="text-sm font-medium text-gray-600">Owner Address</label>
              <p className="text-sm">
                {claim.owner_address && (
                  <>
                    {claim.owner_address}<br />
                    {claim.owner_city}, {claim.owner_state} {claim.owner_zip}
                  </>
                )}
              </p>
            </div>
            <div>
              <label className="text-sm font-medium text-gray-600">Holder Information</label>
              <p className="text-sm">
                {claim.holder_name && (
                  <>
                    {claim.holder_name}<br />
                    {claim.holder_address && (
                      <>
                        {claim.holder_address}<br />
                        {claim.holder_city}, {claim.holder_state} {claim.holder_zip}
                      </>
                    )}
                  </>
                )}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 