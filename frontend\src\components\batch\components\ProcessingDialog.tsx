// Processing dialog for large file handling

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { AlertTriangle, Zap, Shield } from 'lucide-react'
import { BatchFile } from '@/types/batchImport'
import { formatNumber } from '@/utils/formatters'

interface ProcessingDialogProps {
  show: boolean
  file?: BatchFile
  recordCount?: number
  onChoice: (processAll: boolean) => void
  onClose: () => void
}

export function ProcessingDialog({
  show,
  file,
  recordCount,
  onChoice,
  onClose
}: ProcessingDialogProps) {
  if (!show || !file || !recordCount) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-2xl mx-4">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-orange-500" />
            Large File Detected
          </CardTitle>
          <CardDescription>
            The file "{file.name}" contains {formatNumber(recordCount)} records. 
            Please choose how you'd like to process it.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Process All Option */}
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-blue-500" />
                  <h3 className="font-medium">Process All Records</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Process all {formatNumber(recordCount)} records using chunked storage. 
                  This may take several minutes but preserves all data.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Full data integrity</li>
                  <li>• Chunked storage system</li>
                  <li>• Longer processing time</li>
                  <li>• Memory optimized</li>
                </ul>
                <Button 
                  onClick={() => onChoice(true)}
                  className="w-full flex items-center gap-2"
                >
                  <Zap className="h-4 w-4" />
                  Process All
                </Button>
              </div>

              {/* Process Limited Option */}
              <div className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-500" />
                  <h3 className="font-medium">Process First 100,000</h3>
                </div>
                <p className="text-sm text-gray-600">
                  Process only the first 100,000 records for faster processing 
                  and testing. You can always re-upload for full processing.
                </p>
                <ul className="text-xs text-gray-500 space-y-1">
                  <li>• Faster processing</li>
                  <li>• Good for testing</li>
                  <li>• Reduced memory usage</li>
                  <li>• Sample data analysis</li>
                </ul>
                <Button 
                  variant="outline"
                  onClick={() => onChoice(false)}
                  className="w-full flex items-center gap-2"
                >
                  <Shield className="h-4 w-4" />
                  Process Sample
                </Button>
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
              <p className="text-sm text-yellow-800">
                <strong>Recommendation:</strong> For files over 1 million records, 
                consider using the "Process Sample" option first to verify data structure 
                and field mappings before processing the full dataset.
              </p>
            </div>

            <div className="flex justify-end">
              <Button variant="ghost" onClick={onClose}>
                Cancel
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 