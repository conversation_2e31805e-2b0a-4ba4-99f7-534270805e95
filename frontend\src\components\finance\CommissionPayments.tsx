import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  DollarSign, 
  TrendingUp, 
  Calendar, 
  User,
  CreditCard,
  Bank,
  CheckCircle,
  Clock,
  AlertTriangle,
  Send,
  Download,
  Filter,
  Search
} from 'lucide-react'
import { stripeService, type Commission, type PaymentIntent } from '@/services/stripeService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

interface CommissionPaymentsProps {
  agentId?: string
  viewMode?: 'agent' | 'admin'
}

export const CommissionPayments: React.FC<CommissionPaymentsProps> = ({ 
  agentId, 
  viewMode = 'agent' 
}) => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Commission data
  const [commissions, setCommissions] = useState<Commission[]>([])
  const [totalEarnings, setTotalEarnings] = useState(0)
  const [pendingPayments, setPendingPayments] = useState(0)
  const [selectedCommissions, setSelectedCommissions] = useState<string[]>([])
  
  // Filters
  const [statusFilter, setStatusFilter] = useState<string>('all')
  const [dateFilter, setDateFilter] = useState<string>('all')
  const [searchTerm, setSearchTerm] = useState('')

  useEffect(() => {
    loadCommissionData()
  }, [agentId, user])

  const loadCommissionData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // In a real implementation, this would fetch from your API
      // For now, we'll use mock data
      const mockCommissions: Commission[] = [
        {
          id: 'comm_1',
          agentId: agentId || user?.id || '',
          claimId: 'claim_1',
          amount: 2500.00,
          percentage: 25,
          status: 'pending',
        },
        {
          id: 'comm_2',
          agentId: agentId || user?.id || '',
          claimId: 'claim_2',
          amount: 1800.00,
          percentage: 30,
          status: 'paid',
          paidAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
          paymentIntentId: 'pi_paid_123'
        },
        {
          id: 'comm_3',
          agentId: agentId || user?.id || '',
          claimId: 'claim_3',
          amount: 3200.00,
          percentage: 20,
          status: 'processing',
        }
      ]
      
      setCommissions(mockCommissions)
      
      // Calculate totals
      const total = mockCommissions.reduce((sum, comm) => sum + comm.amount, 0)
      const pending = mockCommissions
        .filter(comm => comm.status === 'pending')
        .reduce((sum, comm) => sum + comm.amount, 0)
      
      setTotalEarnings(total)
      setPendingPayments(pending)
    } catch (error) {
      setError('Failed to load commission data')
      console.error('Commission data loading error:', error)
    } finally {
      setLoading(false)
    }
  }

  const processCommissionPayment = async (commissionId: string) => {
    setLoading(true)
    
    try {
      const commission = commissions.find(c => c.id === commissionId)
      if (!commission) {
        setError('Commission not found')
        return
      }

      const { data, error } = await stripeService.createCommissionPayment(
        commission.agentId,
        commission.amount,
        commission.claimId
      )

      if (error) {
        setError('Failed to process payment')
        return
      }

      if (data) {
        // Update commission status
        setCommissions(prev => prev.map(c => 
          c.id === commissionId 
            ? { ...c, status: 'processing', paymentIntentId: data.id }
            : c
        ))

        toast({
          title: "Payment Processing",
          description: `Commission payment of $${commission.amount.toFixed(2)} is being processed.`,
        })
      }
    } catch (error) {
      setError('Failed to process commission payment')
      console.error('Commission payment error:', error)
    } finally {
      setLoading(false)
    }
  }

  const processBulkPayments = async () => {
    if (selectedCommissions.length === 0) {
      setError('Please select commissions to pay')
      return
    }

    setLoading(true)
    
    try {
      for (const commissionId of selectedCommissions) {
        await processCommissionPayment(commissionId)
      }
      
      setSelectedCommissions([])
      
      toast({
        title: "Bulk Payments Initiated",
        description: `${selectedCommissions.length} commission payments are being processed.`,
      })
    } catch (error) {
      setError('Failed to process bulk payments')
      console.error('Bulk payment error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-700'
      case 'processing': return 'bg-blue-100 text-blue-700'
      case 'pending': return 'bg-yellow-100 text-yellow-700'
      case 'failed': return 'bg-red-100 text-red-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'paid': return <CheckCircle className="h-4 w-4" />
      case 'processing': return <Clock className="h-4 w-4" />
      case 'pending': return <Clock className="h-4 w-4" />
      case 'failed': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const filteredCommissions = commissions.filter(commission => {
    if (statusFilter !== 'all' && commission.status !== statusFilter) return false
    if (searchTerm && !commission.claimId.toLowerCase().includes(searchTerm.toLowerCase())) return false
    return true
  })

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            {viewMode === 'admin' ? 'Commission Payments' : 'My Commissions'}
          </h2>
          <p className="text-gray-600">
            {viewMode === 'admin' 
              ? 'Manage agent commission payments and earnings'
              : 'Track your earnings and commission payments'
            }
          </p>
        </div>
        {viewMode === 'admin' && selectedCommissions.length > 0 && (
          <Button onClick={processBulkPayments} disabled={loading}>
            <Send className="h-4 w-4 mr-2" />
            Pay Selected ({selectedCommissions.length})
          </Button>
        )}
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Earnings</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${totalEarnings.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              All time earnings
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Payments</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">${pendingPayments.toFixed(2)}</div>
            <p className="text-xs text-muted-foreground">
              Awaiting payment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">This Month</CardTitle>
            <TrendingUp className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              ${commissions
                .filter(c => new Date(c.paidAt || Date.now()).getMonth() === new Date().getMonth())
                .reduce((sum, c) => sum + c.amount, 0)
                .toFixed(2)
              }
            </div>
            <p className="text-xs text-muted-foreground">
              Current month earnings
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="commissions" className="space-y-6">
        <TabsList>
          <TabsTrigger value="commissions">Commissions</TabsTrigger>
          <TabsTrigger value="payments">Payment History</TabsTrigger>
          {viewMode === 'admin' && <TabsTrigger value="bulk">Bulk Actions</TabsTrigger>}
        </TabsList>

        <TabsContent value="commissions" className="space-y-6">
          {/* Filters */}
          <Card>
            <CardContent className="pt-6">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by claim ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border rounded-lg"
                >
                  <option value="all">All Status</option>
                  <option value="pending">Pending</option>
                  <option value="processing">Processing</option>
                  <option value="paid">Paid</option>
                  <option value="failed">Failed</option>
                </select>
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-3 py-2 border rounded-lg"
                >
                  <option value="all">All Time</option>
                  <option value="this_month">This Month</option>
                  <option value="last_month">Last Month</option>
                  <option value="this_year">This Year</option>
                </select>
              </div>
            </CardContent>
          </Card>

          {/* Commissions List */}
          <Card>
            <CardHeader>
              <CardTitle>Commission Details</CardTitle>
              <CardDescription>
                Detailed breakdown of your commission earnings
              </CardDescription>
            </CardHeader>
            <CardContent>
              {filteredCommissions.length > 0 ? (
                <div className="space-y-3">
                  {filteredCommissions.map((commission) => (
                    <div key={commission.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        {viewMode === 'admin' && (
                          <input
                            type="checkbox"
                            checked={selectedCommissions.includes(commission.id)}
                            onChange={(e) => {
                              if (e.target.checked) {
                                setSelectedCommissions(prev => [...prev, commission.id])
                              } else {
                                setSelectedCommissions(prev => prev.filter(id => id !== commission.id))
                              }
                            }}
                            className="rounded"
                          />
                        )}
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <DollarSign className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">Claim #{commission.claimId}</div>
                          <div className="text-sm text-gray-500">
                            {commission.percentage}% commission
                            {commission.paidAt && ` • Paid ${new Date(commission.paidAt).toLocaleDateString()}`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium text-lg">${commission.amount.toFixed(2)}</div>
                          <Badge className={getStatusColor(commission.status)}>
                            {getStatusIcon(commission.status)}
                            <span className="ml-1 capitalize">{commission.status}</span>
                          </Badge>
                        </div>
                        {viewMode === 'admin' && commission.status === 'pending' && (
                          <Button
                            size="sm"
                            onClick={() => processCommissionPayment(commission.id)}
                            disabled={loading}
                          >
                            <Send className="h-4 w-4 mr-2" />
                            Pay Now
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <DollarSign className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Commissions Found</h3>
                  <p className="text-gray-500">
                    {statusFilter !== 'all' 
                      ? `No commissions with status "${statusFilter}"`
                      : 'No commission data available'
                    }
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payments" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Payment History</CardTitle>
              <CardDescription>
                History of processed commission payments
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {commissions
                  .filter(c => c.status === 'paid')
                  .map((commission) => (
                    <div key={commission.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        </div>
                        <div>
                          <div className="font-medium">Payment #{commission.paymentIntentId}</div>
                          <div className="text-sm text-gray-500">
                            Claim #{commission.claimId} • {commission.paidAt && new Date(commission.paidAt).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium text-lg">${commission.amount.toFixed(2)}</div>
                          <div className="text-sm text-gray-500">Paid</div>
                        </div>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {viewMode === 'admin' && (
          <TabsContent value="bulk" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Bulk Payment Actions</CardTitle>
                <CardDescription>
                  Process multiple commission payments at once
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg bg-blue-50">
                  <div>
                    <h3 className="font-medium">Selected Commissions</h3>
                    <p className="text-sm text-gray-600">
                      {selectedCommissions.length} commissions selected
                    </p>
                  </div>
                  <div className="text-right">
                    <div className="font-medium text-lg">
                      ${commissions
                        .filter(c => selectedCommissions.includes(c.id))
                        .reduce((sum, c) => sum + c.amount, 0)
                        .toFixed(2)
                      }
                    </div>
                    <div className="text-sm text-gray-600">Total amount</div>
                  </div>
                </div>

                <div className="flex space-x-2">
                  <Button 
                    onClick={processBulkPayments}
                    disabled={loading || selectedCommissions.length === 0}
                    className="flex-1"
                  >
                    <Send className="h-4 w-4 mr-2" />
                    Process All Payments
                  </Button>
                  <Button 
                    variant="outline"
                    onClick={() => setSelectedCommissions([])}
                    disabled={selectedCommissions.length === 0}
                  >
                    Clear Selection
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  )
}
