<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM Debug Tool</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .found { color: #00ff00; }
        .not-found { color: #ff4444; }
        .info { color: #ffaa00; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #444;
        }
        pre {
            background: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 AssetHunterPro DOM Debug Tool</h1>
    <p>This tool helps debug what elements are actually present in the DOM</p>
    
    <button onclick="analyzeDOM()">🔍 Analyze DOM</button>
    <button onclick="checkReactApp()">⚛️ Check React App</button>
    <button onclick="checkNavigation()">🧭 Check Navigation</button>
    <button onclick="checkInputs()">📝 Check Form Inputs</button>
    <button onclick="clearResults()">🗑️ Clear</button>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function analyzeDOM() {
            clearResults();
            log('<h2>🔍 DOM Analysis Results</h2>', 'info');
            
            // Basic DOM info
            log(`<h3>Basic DOM Info</h3>
                <p>Document ready state: <span class="found">${document.readyState}</span></p>
                <p>Total elements: <span class="found">${document.querySelectorAll('*').length}</span></p>
                <p>Body children: <span class="found">${document.body.children.length}</span></p>
                <p>Head children: <span class="found">${document.head.children.length}</span></p>`, 'info');

            // Check for key elements
            const checks = [
                { name: 'Root div', selector: '#root' },
                { name: 'React root with data attr', selector: '[data-reactroot]' },
                { name: 'Any buttons', selector: 'button' },
                { name: 'Any inputs', selector: 'input' },
                { name: 'Any nav elements', selector: 'nav' },
                { name: 'Any aside elements', selector: 'aside' },
                { name: 'Any links', selector: 'a' },
                { name: 'Any forms', selector: 'form' },
                { name: 'CSS stylesheets', selector: 'link[rel="stylesheet"]' },
                { name: 'Style tags', selector: 'style' },
                { name: 'Script tags', selector: 'script' }
            ];

            let foundElements = '<h3>Element Detection</h3>';
            checks.forEach(check => {
                const elements = document.querySelectorAll(check.selector);
                const found = elements.length > 0;
                foundElements += `<p>${check.name}: <span class="${found ? 'found' : 'not-found'}">${found ? '✅ Found (' + elements.length + ')' : '❌ Not found'}</span></p>`;
            });
            log(foundElements, 'info');
        }

        function checkReactApp() {
            log('<h2>⚛️ React App Analysis</h2>', 'info');
            
            const root = document.getElementById('root');
            if (!root) {
                log('<p class="not-found">❌ Root element not found!</p>', 'not-found');
                return;
            }

            log(`<h3>Root Element Analysis</h3>
                <p>Root element exists: <span class="found">✅ Yes</span></p>
                <p>Has data-reactroot: <span class="${root.hasAttribute('data-reactroot') ? 'found' : 'not-found'}">${root.hasAttribute('data-reactroot') ? '✅ Yes' : '❌ No'}</span></p>
                <p>Children count: <span class="found">${root.children.length}</span></p>
                <p>Inner HTML length: <span class="found">${root.innerHTML.length} characters</span></p>`, 'info');

            if (root.children.length > 0) {
                let childrenInfo = '<h3>Root Children</h3>';
                Array.from(root.children).forEach((child, index) => {
                    childrenInfo += `<p>Child ${index + 1}: <span class="found">&lt;${child.tagName.toLowerCase()}&gt;</span> (classes: ${child.className || 'none'})</p>`;
                });
                log(childrenInfo, 'info');
            }

            // Check for React-specific indicators
            const reactIndicators = [
                { name: 'React DevTools', check: () => window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== undefined },
                { name: 'React Fiber', check: () => root._reactRootContainer !== undefined || root._reactInternalFiber !== undefined },
                { name: 'React 18 Root', check: () => root._reactRootContainer !== undefined }
            ];

            let reactInfo = '<h3>React Detection</h3>';
            reactIndicators.forEach(indicator => {
                const found = indicator.check();
                reactInfo += `<p>${indicator.name}: <span class="${found ? 'found' : 'not-found'}">${found ? '✅ Detected' : '❌ Not detected'}</span></p>`;
            });
            log(reactInfo, 'info');
        }

        function checkNavigation() {
            log('<h2>🧭 Navigation Analysis</h2>', 'info');
            
            const navChecks = [
                { name: 'nav elements', selector: 'nav' },
                { name: 'aside elements (sidebar)', selector: 'aside' },
                { name: 'role="navigation"', selector: '[role="navigation"]' },
                { name: 'Navigation buttons', selector: 'button[class*="nav"], .nav button' },
                { name: 'Navigation links', selector: 'a[class*="nav"], .nav a' },
                { name: 'Sidebar classes', selector: '.sidebar, [class*="sidebar"]' },
                { name: 'Menu elements', selector: '.menu, [class*="menu"]' }
            ];

            let navInfo = '<h3>Navigation Elements</h3>';
            navChecks.forEach(check => {
                const elements = document.querySelectorAll(check.selector);
                navInfo += `<p>${check.name}: <span class="${elements.length > 0 ? 'found' : 'not-found'}">${elements.length > 0 ? '✅ Found (' + elements.length + ')' : '❌ Not found'}</span></p>`;
                
                if (elements.length > 0 && elements.length <= 5) {
                    Array.from(elements).forEach((el, index) => {
                        navInfo += `<p style="margin-left: 20px;">└ ${index + 1}: &lt;${el.tagName.toLowerCase()}&gt; (classes: ${el.className || 'none'})</p>`;
                    });
                }
            });
            log(navInfo, 'info');
        }

        function checkInputs() {
            log('<h2>📝 Form Input Analysis</h2>', 'info');
            
            const inputs = document.querySelectorAll('input');
            const forms = document.querySelectorAll('form');
            const textareas = document.querySelectorAll('textarea');
            const selects = document.querySelectorAll('select');

            log(`<h3>Form Elements Count</h3>
                <p>Input elements: <span class="${inputs.length > 0 ? 'found' : 'not-found'}">${inputs.length}</span></p>
                <p>Form elements: <span class="${forms.length > 0 ? 'found' : 'not-found'}">${forms.length}</span></p>
                <p>Textarea elements: <span class="${textareas.length > 0 ? 'found' : 'not-found'}">${textareas.length}</span></p>
                <p>Select elements: <span class="${selects.length > 0 ? 'found' : 'not-found'}">${selects.length}</span></p>`, 'info');

            if (inputs.length > 0) {
                let inputDetails = '<h3>Input Details</h3>';
                Array.from(inputs).forEach((input, index) => {
                    inputDetails += `<p>Input ${index + 1}: type="${input.type}", id="${input.id || 'none'}", name="${input.name || 'none'}"</p>`;
                });
                log(inputDetails, 'info');
            }

            // Check for login-related elements
            const loginChecks = [
                { name: 'Login form', selector: 'form[class*="login"], .login form' },
                { name: 'Email input', selector: 'input[type="email"], input[name*="email"]' },
                { name: 'Password input', selector: 'input[type="password"], input[name*="password"]' },
                { name: 'Login button', selector: 'button[class*="login"], .login button' }
            ];

            let loginInfo = '<h3>Login Form Detection</h3>';
            loginChecks.forEach(check => {
                const elements = document.querySelectorAll(check.selector);
                loginInfo += `<p>${check.name}: <span class="${elements.length > 0 ? 'found' : 'not-found'}">${elements.length > 0 ? '✅ Found' : '❌ Not found'}</span></p>`;
            });
            log(loginInfo, 'info');

            // Check if user appears to be logged in
            const loggedInIndicators = [
                { name: 'User menu/profile', selector: '[class*="user"], [data-testid*="user"]' },
                { name: 'Logout button', selector: 'button[class*="logout"], [class*="logout"]' },
                { name: 'Dashboard elements', selector: '[class*="dashboard"], [data-testid*="dashboard"]' },
                { name: 'Sidebar/navigation', selector: 'aside, .sidebar, nav' }
            ];

            let loginStatus = '<h3>Login Status Indicators</h3>';
            loggedInIndicators.forEach(indicator => {
                const elements = document.querySelectorAll(indicator.selector);
                loginStatus += `<p>${indicator.name}: <span class="${elements.length > 0 ? 'found' : 'not-found'}">${elements.length > 0 ? '✅ Present' : '❌ Not present'}</span></p>`;
            });
            log(loginStatus, 'info');
        }

        // Auto-run basic analysis on load
        setTimeout(() => {
            log('<h2>🚀 Auto-Analysis on Page Load</h2>', 'info');
            analyzeDOM();
        }, 1000);
    </script>
</body>
</html>
