{"name": "@fastify/multipart", "version": "9.0.3", "description": "Multipart plugin for Fastify", "main": "index.js", "type": "commonjs", "types": "types/index.d.ts", "dependencies": {"@fastify/busboy": "^3.0.0", "@fastify/deepmerge": "^2.0.0", "@fastify/error": "^4.0.0", "fastify-plugin": "^5.0.0", "secure-json-parse": "^3.0.0"}, "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@fastify/swagger": "^9.0.0", "@fastify/swagger-ui": "^5.0.0", "@types/node": "^22.0.0", "benchmark": "^2.1.4", "climem": "^2.0.0", "concat-stream": "^2.0.0", "eslint": "^9.17.0", "fastify": "^5.0.0", "form-data": "^4.0.0", "h2url": "^0.2.0", "neostandard": "^0.12.0", "noop-stream": "^0.1.0", "pump": "^3.0.0", "readable-stream": "^4.5.2", "tap": "^18.6.1", "tsd": "^0.31.0"}, "scripts": {"coverage": "npm run test:unit -- --coverage-report=html", "climem": "climem 8999 localhost", "lint": "eslint", "lint:fix": "eslint --fix", "start": "CLIMEM=8999 node -r climem ./examples/example", "test": "npm run test:unit && npm run test:typescript", "test:typescript": "tsd", "test:unit": "tap -t 120"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify-multipart.git"}, "bugs": {"url": "https://github.com/fastify/fastify-multipart/issues"}, "homepage": "https://github.com/fastify/fastify-multipart#readme", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "keywords": ["fastify", "multipart", "form"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}], "license": "MIT", "tsd": {"directory": "test"}, "publishConfig": {"access": "public"}}