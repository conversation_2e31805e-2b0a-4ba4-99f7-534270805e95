import { ClaimStatus, ClaimPriority } from '../types/claim.types';

export const getStatusColor = (status: ClaimStatus): string => {
  const colors = {
    'new': 'bg-blue-100 text-blue-800',
    'assigned': 'bg-yellow-100 text-yellow-800',
    'contacted': 'bg-purple-100 text-purple-800',
    'in_progress': 'bg-orange-100 text-orange-800',
    'documents_requested': 'bg-indigo-100 text-indigo-800',
    'under_review': 'bg-cyan-100 text-cyan-800',
    'approved': 'bg-green-100 text-green-800',
    'completed': 'bg-emerald-100 text-emerald-800',
    'on_hold': 'bg-gray-100 text-gray-800',
    'cancelled': 'bg-red-100 text-red-800'
  };
  return colors[status] || 'bg-gray-100 text-gray-800';
};

export const getPriorityColor = (priority: ClaimPriority): string => {
  const colors = {
    'low': 'bg-green-100 text-green-800',
    'medium': 'bg-yellow-100 text-yellow-800',
    'high': 'bg-orange-100 text-orange-800',
    'urgent': 'bg-red-100 text-red-800'
  };
  return colors[priority] || 'bg-gray-100 text-gray-800';
};

export const getStatusDisplayName = (status: ClaimStatus): string => {
  return status.replace('_', ' ').replace(/\b\w/g, char => char.toUpperCase());
};

export const getPriorityDisplayName = (priority: ClaimPriority): string => {
  return priority.charAt(0).toUpperCase() + priority.slice(1);
}; 