-- Create storage bucket for documents (development mode)
INSERT INTO storage.buckets (id, name, public)
VALUES ('documents', 'documents', true);

-- Allow public access for development (remove in production)
CREATE POLICY "Public upload access" ON storage.objects
  FOR INSERT TO public
  WITH CHECK (bucket_id = 'documents');

CREATE POLICY "Public read access" ON storage.objects
  FOR SELECT TO public
  USING (bucket_id = 'documents');

-- Enable RLS
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY; 