import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  Users, 
  AlertTriangle, 
  CheckCircle,
  FileText,
  RefreshCw,
  Lightbulb,
  Loader
} from 'lucide-react';
import { agentAISearchService } from '@/services/agentAISearchService';

interface AgentAISearchProps {
  agentId: string;
  userRole?: string;
  maxSearches?: number;
  recordId?: string;
  tenantId?: string;
  onSearchComplete?: (results: any[]) => void;
}

interface PersonSearchQuery {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  city?: string;
  state?: string;
}

interface PersonSearchResult {
  fullName: string;
  confidence: number;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  state?: string;
  additionalInfo?: string;
}

interface AgentSearchQuota {
  dailyUsed: number;
  dailyLimit: number;
  monthlyUsed: number;
  monthlyLimit: number;
  totalSearches: number;
}

export default function AgentAISearch({ 
  agentId, 
  userRole = 'junior_agent', 
  maxSearches = 10, 
  recordId, 
  tenantId, 
  onSearchComplete 
}: AgentAISearchProps) {
  const [searchQuery, setSearchQuery] = useState<PersonSearchQuery>({});
  const [searchResults, setSearchResults] = useState<PersonSearchResult[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [quota, setQuota] = useState<AgentSearchQuota | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [selectedResult, setSelectedResult] = useState<PersonSearchResult | null>(null);

  // Role-based logic for assigned leads
  const isManagementRole = ['admin', 'senior_agent', 'compliance'].includes(userRole);
  
  // Demo/sample data for management roles or assigned leads for agents
  const sampleLeads = [
    {
      id: '1',
      owner_name: 'John Smith',
      amount: 50000,
      state: 'CA',
      priority: 'high',
      property_type: 'real_estate',
      phone: '(*************',
      email: '<EMAIL>',
      city: 'Los Angeles',
      address: '123 Main St'
    },
    {
      id: '2', 
      owner_name: 'Sarah Johnson',
      amount: 75000,
      state: 'TX',
      priority: 'medium',
      property_type: 'stocks',
      phone: '(*************',
      email: '<EMAIL>',
      city: 'Houston',
      address: '456 Oak Ave'
    },
    {
      id: '3',
      owner_name: 'Michael Rodriguez',
      amount: 25000,
      state: 'FL', 
      priority: 'low',
      property_type: 'insurance',
      phone: '(*************',
      email: '<EMAIL>',
      city: 'Miami',
      address: '789 Palm Dr'
    }
  ];

  // Use sample data for both management and agents (in real app, agents would get actual assigned leads)
  const displayLeads = sampleLeads;
  const leadsTitle = isManagementRole ? 'Demo Search Examples' : 'Your Assigned Leads';
  const leadsCount = displayLeads.length;

  // Load quota on component mount
  useEffect(() => {
    loadQuota();
  }, [agentId]);

  const loadQuota = async () => {
    try {
      // For management roles, we don't need to load quota (unlimited)
      if (isManagementRole) {
        setQuota({
          dailyUsed: 0,
          dailyLimit: 999,
          monthlyUsed: 0,
          monthlyLimit: 999,
          totalSearches: 0
        });
        return;
      }

      // For agents, try to load actual quota
      try {
        const quotaData = await agentAISearchService.getAgentQuota(agentId);
        setQuota(quotaData);
      } catch (error) {
        console.warn('Could not load quota, using defaults:', error);
        setQuota({
          dailyUsed: 0,
          dailyLimit: maxSearches,
          monthlyUsed: 0,
          monthlyLimit: maxSearches * 30,
          totalSearches: 0
        });
      }
    } catch (error) {
      console.error('Failed to load search quota:', error);
      setError('Failed to load search quota');
    }
  };

  const performSearch = async () => {
    if (!searchQuery.firstName && !searchQuery.lastName && !searchQuery.email && !searchQuery.phone) {
      setError('Please provide at least a name, email, or phone number');
      return;
    }

    // Check quota for non-management roles
    if (!isManagementRole && quota && quota.dailyUsed >= quota.dailyLimit) {
      setError('Daily search quota exceeded. Please try again tomorrow.');
      return;
    }

    setIsSearching(true);
    setError(null);

    try {
      const results = await agentAISearchService.searchPerson(
        agentId,
        searchQuery,
        recordId,
        tenantId
      );

      if (results.success) {
        setSearchResults(results.results || []);
        
        // Update quota after successful search
        await loadQuota();
        
        // Call completion callback if provided
        if (onSearchComplete) {
          onSearchComplete(results.results || []);
        }
        
        console.log(`✅ Search completed for ${agentId}: ${results.results?.length || 0} results found`);
      } else {
        setError(results.error || 'Search failed');
      }
    } catch (error) {
      console.error('Search error:', error);
      setError('An error occurred during the search. Please try again.');
    } finally {
      setIsSearching(false);
    }
  };

  // Auto-populate search from selected lead
  const handleLeadSelection = (lead: any) => {
    // Extract name parts
    const nameParts = lead.owner_name.split(' ');
    const firstName = nameParts[0] || '';
    const lastName = nameParts.slice(1).join(' ') || '';

    // Auto-populate the search form
    setSearchQuery({
      firstName,
      lastName,
      phone: lead.phone || '',
      email: lead.email || '',
      city: lead.city || '',
      state: lead.state || ''
    });

    // Show success message
    const message = isManagementRole 
      ? `Demo data loaded for ${lead.owner_name}` 
      : `Lead data loaded for ${lead.owner_name} - Ready to search!`;
    
    console.log(`✅ ${message}`);
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Use safe quota values
  const safeQuota = quota || { dailyUsed: 0, dailyLimit: maxSearches, monthlyUsed: 0, monthlyLimit: maxSearches * 30, totalSearches: 0 };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-8">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 flex items-center">
              <Search className="h-8 w-8 mr-3 text-blue-600" />
              🤖 AI Person Search
            </h1>
            <p className="text-gray-600 mt-2">
              {isManagementRole 
                ? 'Use AI to find detailed information about any person - Demo mode for management users'
                : 'Use AI to find detailed information about your assigned leads'
              }
            </p>
          </div>
          <div className="text-right">
            <div className="text-sm text-gray-500">
              {userRole === 'admin' ? 'System Administrator' : 
               userRole === 'senior_agent' ? 'Senior Agent' :
               userRole === 'compliance' ? 'Compliance Officer' : 
               'Agent'} • {agentId}
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column: Assigned Leads / Demo Data */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  {leadsTitle} ({leadsCount})
                </CardTitle>
                {isManagementRole && (
                  <p className="text-sm text-gray-600">
                    Click any demo entry to auto-populate search form
                  </p>
                )}
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {displayLeads.map((lead) => (
                    <div 
                      key={lead.id} 
                      className={`p-4 border rounded-lg cursor-pointer transition-all duration-200 hover:border-blue-300 hover:shadow-md ${
                        isManagementRole ? 'bg-blue-50 border-blue-200' : 'bg-gray-50 border-gray-200'
                      }`}
                      onClick={() => handleLeadSelection(lead)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <div className="font-medium text-gray-900">{lead.owner_name}</div>
                          <div className="text-sm text-gray-600">
                            {formatCurrency(lead.amount)} • {lead.state} • {lead.property_type}
                          </div>
                          {lead.phone && (
                            <div className="text-xs text-gray-500 mt-1">
                              📞 {lead.phone}
                            </div>
                          )}
                        </div>
                        <div className="text-right">
                          <Badge 
                            className={isManagementRole ? 'bg-blue-100 text-blue-800' : 'bg-yellow-100 text-yellow-800'}
                          >
                            {isManagementRole ? 'demo' : 'assigned'}
                          </Badge>
                        </div>
                      </div>
                      {!isManagementRole && (
                        <div className="mt-2 text-xs text-gray-500">
                          Click to auto-fill search form →
                        </div>
                      )}
                    </div>
                  ))}

                  {leadsCount === 0 && !isManagementRole && (
                    <div className="text-center py-8 text-gray-500">
                      <Users className="h-12 w-12 mx-auto mb-3 text-gray-300" />
                      <p className="text-lg font-medium mb-2">No Assigned Leads</p>
                      <p className="text-sm">Contact your admin to get leads assigned to you.</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Auto-populate helper for agents */}
            {!isManagementRole && leadsCount > 0 && (
              <Card className="mt-4">
                <CardContent className="pt-6">
                  <div className="flex items-center p-3 bg-green-50 rounded-lg">
                    <Lightbulb className="h-5 w-5 text-green-600 mr-2" />
                    <div className="text-sm text-green-800">
                      <div className="font-medium">💡 Pro Tip</div>
                      <div>Click any assigned lead above to automatically fill the search form with their information!</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Right Columns: Search Form and Results */}
          <div className="lg:col-span-2">
            {/* Search Form */}
            <Card className="mb-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Search className="h-5 w-5 mr-2 text-blue-600" />
                  🔍 AI Person Search
                  {isManagementRole && (
                    <Badge className="ml-3 bg-purple-100 text-purple-800">
                      Management Demo Mode
                    </Badge>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {/* Search Quota Display */}
                <div className="mb-6">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-700">
                      {isManagementRole ? 'Demo Searches' : 'Search Quota'}
                    </span>
                    <span className="text-sm text-gray-600">
                      {isManagementRole ? 'Unlimited' : `${safeQuota.dailyUsed} / ${safeQuota.dailyLimit}`}
                    </span>
                  </div>
                  {!isManagementRole && (
                    <Progress 
                      value={(safeQuota.dailyUsed / safeQuota.dailyLimit) * 100} 
                      className="h-2"
                    />
                  )}
                </div>

                {/* Search Input Grid */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                  <div>
                    <Label htmlFor="firstName" className="text-sm font-medium">
                      First Name {searchQuery.firstName && '✓'}
                    </Label>
                    <Input
                      id="firstName"
                      value={searchQuery.firstName || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, firstName: e.target.value})}
                      placeholder="John"
                      className={searchQuery.firstName ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                  <div>
                    <Label htmlFor="lastName" className="text-sm font-medium">
                      Last Name {searchQuery.lastName && '✓'}
                    </Label>
                    <Input
                      id="lastName"
                      value={searchQuery.lastName || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, lastName: e.target.value})}
                      placeholder="Smith"
                      className={searchQuery.lastName ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                  <div>
                    <Label htmlFor="phone" className="text-sm font-medium">
                      Phone {searchQuery.phone && '✓'}
                    </Label>
                    <Input
                      id="phone"
                      value={searchQuery.phone || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, phone: e.target.value})}
                      placeholder="(*************"
                      className={searchQuery.phone ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                  <div>
                    <Label htmlFor="email" className="text-sm font-medium">
                      Email {searchQuery.email && '✓'}
                    </Label>
                    <Input
                      id="email"
                      value={searchQuery.email || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, email: e.target.value})}
                      placeholder="<EMAIL>"
                      className={searchQuery.email ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                  <div>
                    <Label htmlFor="city" className="text-sm font-medium">
                      City {searchQuery.city && '✓'}
                    </Label>
                    <Input
                      id="city"
                      value={searchQuery.city || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, city: e.target.value})}
                      placeholder="Los Angeles"
                      className={searchQuery.city ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                  <div>
                    <Label htmlFor="state" className="text-sm font-medium">
                      State {searchQuery.state && '✓'}
                    </Label>
                    <Input
                      id="state"
                      value={searchQuery.state || ''}
                      onChange={(e) => setSearchQuery({...searchQuery, state: e.target.value})}
                      placeholder="CA"
                      className={searchQuery.state ? 'border-green-300 bg-green-50' : ''}
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <Button 
                    onClick={performSearch}
                    disabled={isSearching || (!isManagementRole && safeQuota.dailyUsed >= safeQuota.dailyLimit)}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 text-white"
                  >
                    {isSearching ? (
                      <>
                        <Loader className="h-4 w-4 mr-2 animate-spin" />
                        Searching...
                      </>
                    ) : (
                      <>
                        <Search className="h-4 w-4 mr-2" />
                        {isManagementRole ? 'Demo Search' : 'Search Person'} ({safeQuota.dailyUsed})
                      </>
                    )}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => setSearchQuery({
                      firstName: '',
                      lastName: '',
                      phone: '',
                      email: '',
                      city: '',
                      state: ''
                    })}
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    Clear
                  </Button>
                </div>

                {/* Form Status */}
                {Object.values(searchQuery).some(val => val) && (
                  <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                    <div className="flex items-center">
                      <CheckCircle className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="text-sm text-blue-800">
                        Form filled with {Object.values(searchQuery).filter(val => val).length} fields
                        {isManagementRole ? ' (Demo Mode)' : ''}
                      </span>
                    </div>
                  </div>
                )}

                {/* Error Display */}
                {error && (
                  <Alert className="mt-4" variant="destructive">
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>

            {/* Search Results */}
            {searchResults.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2 text-green-600" />
                    Search Results ({searchResults.length})
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {searchResults.map((result, index) => (
                      <div key={index} className="border rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div className="flex items-center justify-between mb-2">
                          <h3 className="font-semibold text-lg">{result.fullName}</h3>
                          <Badge variant="outline" className="text-green-700 border-green-300">
                            Match Score: {Math.round(result.confidence * 100)}%
                          </Badge>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                          {result.email && (
                            <div>
                              <span className="text-gray-600">Email:</span>
                              <div className="font-medium">{result.email}</div>
                            </div>
                          )}
                          {result.phone && (
                            <div>
                              <span className="text-gray-600">Phone:</span>
                              <div className="font-medium">{result.phone}</div>
                            </div>
                          )}
                          {result.address && (
                            <div>
                              <span className="text-gray-600">Address:</span>
                              <div className="font-medium">{result.address}</div>
                            </div>
                          )}
                          {result.city && result.state && (
                            <div>
                              <span className="text-gray-600">Location:</span>
                              <div className="font-medium">{result.city}, {result.state}</div>
                            </div>
                          )}
                        </div>

                        {result.additionalInfo && (
                          <div className="mt-3 p-3 bg-gray-50 rounded-lg">
                            <div className="text-sm">
                              <span className="text-gray-600">Additional Info:</span>
                              <div className="mt-1">{result.additionalInfo}</div>
                            </div>
                          </div>
                        )}

                        <div className="mt-3 flex justify-end">
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => setSelectedResult(result)}
                          >
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    </div>
  );
} 