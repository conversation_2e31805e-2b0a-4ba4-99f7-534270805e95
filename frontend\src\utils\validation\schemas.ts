// Comprehensive Validation Schemas for AssetHunterPro
// Uses Zod for type-safe validation across frontend and backend

import { z } from 'zod'

// ===================================
// COMMON VALIDATION PATTERNS
// ===================================

// Phone number validation (US format)
const phoneRegex = /^(\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/
const phoneSchema = z.string().regex(phoneRegex, 'Invalid phone number format')

// Email validation (enhanced)
const emailSchema = z.string()
  .email('Invalid email format')
  .min(5, 'Email must be at least 5 characters')
  .max(255, 'Email must be less than 255 characters')
  .toLowerCase()

// Password validation (strong password requirements)
const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .max(128, 'Password must be less than 128 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character')

// US State validation
const stateSchema = z.string()
  .length(2, 'State must be 2 characters')
  .regex(/^[A-Z]{2}$/, 'State must be uppercase letters')

// Currency amount validation
const currencySchema = z.number()
  .positive('Amount must be positive')
  .max(10000000, 'Amount cannot exceed $10,000,000')
  .multipleOf(0.01, 'Amount must be in cents')

// Percentage validation
const percentageSchema = z.number()
  .min(0, 'Percentage cannot be negative')
  .max(100, 'Percentage cannot exceed 100%')

// ===================================
// USER VALIDATION SCHEMAS
// ===================================

export const UserRoleSchema = z.enum([
  'admin',
  'senior_agent', 
  'junior_agent',
  'contractor',
  'compliance',
  'finance'
], {
  errorMap: () => ({ message: 'Invalid user role' })
})

export const UserCreateSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  first_name: z.string()
    .min(1, 'First name is required')
    .max(100, 'First name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name contains invalid characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(100, 'Last name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name contains invalid characters'),
  role: UserRoleSchema,
  team_id: z.string().uuid('Invalid team ID').optional(),
  phone: phoneSchema.optional(),
  is_active: z.boolean().default(true)
})

export const UserUpdateSchema = UserCreateSchema.partial().omit({ password: true })

export const UserLoginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required')
})

export const PasswordResetSchema = z.object({
  email: emailSchema
})

export const PasswordChangeSchema = z.object({
  current_password: z.string().min(1, 'Current password is required'),
  new_password: passwordSchema,
  confirm_password: z.string()
}).refine((data) => data.new_password === data.confirm_password, {
  message: "Passwords don't match",
  path: ["confirm_password"]
})

// ===================================
// CLAIM VALIDATION SCHEMAS
// ===================================

export const ClaimStatusSchema = z.enum([
  'new',
  'assigned',
  'contacted',
  'in_progress',
  'documents_requested',
  'under_review',
  'approved',
  'completed',
  'on_hold',
  'cancelled'
], {
  errorMap: () => ({ message: 'Invalid claim status' })
})

export const ClaimPrioritySchema = z.enum(['low', 'medium', 'high', 'urgent'], {
  errorMap: () => ({ message: 'Invalid priority level' })
})

export const ClaimCreateSchema = z.object({
  property_id: z.string()
    .min(1, 'Property ID is required')
    .max(100, 'Property ID must be less than 100 characters')
    .optional(),
  owner_name: z.string()
    .min(2, 'Owner name must be at least 2 characters')
    .max(255, 'Owner name must be less than 255 characters')
    .regex(/^[a-zA-Z\s.'-]+$/, 'Owner name contains invalid characters'),
  amount: currencySchema,
  status: ClaimStatusSchema.default('new'),
  priority: ClaimPrioritySchema.default('medium'),
  state: stateSchema,
  assigned_agent_id: z.string().uuid('Invalid agent ID').optional(),
  description: z.string()
    .max(2000, 'Description must be less than 2000 characters')
    .optional(),
  source: z.string()
    .max(100, 'Source must be less than 100 characters')
    .optional()
})

export const ClaimUpdateSchema = ClaimCreateSchema.partial()

export const ClaimSearchSchema = z.object({
  query: z.string().max(255, 'Search query too long').optional(),
  status: ClaimStatusSchema.optional(),
  priority: ClaimPrioritySchema.optional(),
  state: stateSchema.optional(),
  assigned_agent_id: z.string().uuid('Invalid agent ID').optional(),
  amount_min: currencySchema.optional(),
  amount_max: currencySchema.optional(),
  date_from: z.string().datetime('Invalid date format').optional(),
  date_to: z.string().datetime('Invalid date format').optional(),
  limit: z.number().min(1).max(100).default(20),
  offset: z.number().min(0).default(0)
})

// ===================================
// CLAIMANT VALIDATION SCHEMAS
// ===================================

export const ClaimantCreateSchema = z.object({
  claim_id: z.string().uuid('Invalid claim ID'),
  first_name: z.string()
    .min(1, 'First name is required')
    .max(100, 'First name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'First name contains invalid characters'),
  last_name: z.string()
    .min(1, 'Last name is required')
    .max(100, 'Last name must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'Last name contains invalid characters'),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  address_line1: z.string()
    .max(255, 'Address line 1 must be less than 255 characters')
    .optional(),
  address_line2: z.string()
    .max(255, 'Address line 2 must be less than 255 characters')
    .optional(),
  city: z.string()
    .max(100, 'City must be less than 100 characters')
    .regex(/^[a-zA-Z\s'-]+$/, 'City contains invalid characters')
    .optional(),
  state: stateSchema.optional(),
  zip_code: z.string()
    .regex(/^\d{5}(-\d{4})?$/, 'Invalid ZIP code format')
    .optional(),
  date_of_birth: z.string()
    .datetime('Invalid date format')
    .optional(),
  ssn_last4: z.string()
    .regex(/^\d{4}$/, 'SSN last 4 must be 4 digits')
    .optional()
})

export const ClaimantUpdateSchema = ClaimantCreateSchema.partial().omit({ claim_id: true })

// ===================================
// ACTIVITY VALIDATION SCHEMAS
// ===================================

export const ActivityTypeSchema = z.enum([
  'call',
  'email',
  'letter',
  'meeting',
  'research',
  'document_review',
  'status_update',
  'note',
  'system'
], {
  errorMap: () => ({ message: 'Invalid activity type' })
})

export const ActivityCreateSchema = z.object({
  claim_id: z.string().uuid('Invalid claim ID'),
  type: ActivityTypeSchema,
  description: z.string()
    .min(1, 'Description is required')
    .max(2000, 'Description must be less than 2000 characters'),
  notes: z.string()
    .max(5000, 'Notes must be less than 5000 characters')
    .optional(),
  duration_minutes: z.number()
    .min(0, 'Duration cannot be negative')
    .max(1440, 'Duration cannot exceed 24 hours')
    .optional(),
  outcome: z.string()
    .max(500, 'Outcome must be less than 500 characters')
    .optional(),
  follow_up_date: z.string()
    .datetime('Invalid date format')
    .optional()
})

export const ActivityUpdateSchema = ActivityCreateSchema.partial().omit({ claim_id: true })

// ===================================
// DOCUMENT VALIDATION SCHEMAS
// ===================================

export const DocumentTypeSchema = z.enum([
  'agreement',
  'power_of_attorney',
  'identification',
  'proof_of_ownership',
  'correspondence',
  'court_document',
  'financial_statement',
  'other'
], {
  errorMap: () => ({ message: 'Invalid document type' })
})

export const DocumentCreateSchema = z.object({
  claim_id: z.string().uuid('Invalid claim ID'),
  name: z.string()
    .min(1, 'Document name is required')
    .max(255, 'Document name must be less than 255 characters'),
  type: DocumentTypeSchema,
  description: z.string()
    .max(1000, 'Description must be less than 1000 characters')
    .optional(),
  file_size: z.number()
    .min(1, 'File size must be positive')
    .max(50 * 1024 * 1024, 'File size cannot exceed 50MB'), // 50MB limit
  file_type: z.string()
    .regex(/^[a-zA-Z0-9\/\-\.]+$/, 'Invalid file type format'),
  is_signed: z.boolean().default(false),
  requires_signature: z.boolean().default(false)
})

export const DocumentUpdateSchema = DocumentCreateSchema.partial().omit({ claim_id: true, file_size: true, file_type: true })

// ===================================
// COMMISSION VALIDATION SCHEMAS
// ===================================

export const CommissionCreateSchema = z.object({
  agent_id: z.string().uuid('Invalid agent ID'),
  claim_id: z.string().uuid('Invalid claim ID'),
  amount: currencySchema,
  percentage: percentageSchema,
  description: z.string()
    .max(500, 'Description must be less than 500 characters')
    .optional()
})

export const CommissionUpdateSchema = CommissionCreateSchema.partial()

// ===================================
// BULK OPERATION SCHEMAS
// ===================================

export const BulkClaimUpdateSchema = z.object({
  claim_ids: z.array(z.string().uuid('Invalid claim ID'))
    .min(1, 'At least one claim ID is required')
    .max(100, 'Cannot update more than 100 claims at once'),
  updates: ClaimUpdateSchema
})

export const BulkAssignmentSchema = z.object({
  claim_ids: z.array(z.string().uuid('Invalid claim ID'))
    .min(1, 'At least one claim ID is required')
    .max(50, 'Cannot assign more than 50 claims at once'),
  assigned_agent_id: z.string().uuid('Invalid agent ID')
})

// ===================================
// API RESPONSE SCHEMAS
// ===================================

export const PaginationSchema = z.object({
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  total: z.number().min(0),
  total_pages: z.number().min(0)
})

export const ApiResponseSchema = z.object({
  success: z.boolean(),
  data: z.any().optional(),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.any().optional()
  }).optional(),
  pagination: PaginationSchema.optional()
})

// ===================================
// VALIDATION HELPER FUNCTIONS
// ===================================

export const validateField = <T>(schema: z.ZodSchema<T>, value: unknown): { success: boolean; data?: T; error?: string } => {
  try {
    const data = schema.parse(value)
    return { success: true, data }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => e.message).join(', ')
      }
    }
    return { success: false, error: 'Validation failed' }
  }
}

export const validatePartial = <T>(schema: z.ZodSchema<T>, value: unknown): { success: boolean; data?: Partial<T>; error?: string } => {
  try {
    const data = schema.partial().parse(value)
    return { success: true, data }
  } catch (error) {
    if (error instanceof z.ZodError) {
      return { 
        success: false, 
        error: error.errors.map(e => e.message).join(', ')
      }
    }
    return { success: false, error: 'Validation failed' }
  }
}

export const sanitizeInput = (input: string): string => {
  return input
    .trim()
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
}

export const sanitizeObject = (obj: Record<string, any>): Record<string, any> => {
  const sanitized: Record<string, any> = {}
  
  for (const [key, value] of Object.entries(obj)) {
    if (typeof value === 'string') {
      sanitized[key] = sanitizeInput(value)
    } else if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
      sanitized[key] = sanitizeObject(value)
    } else {
      sanitized[key] = value
    }
  }
  
  return sanitized
}

// Export all schemas for use in components and API
export const ValidationSchemas = {
  // User schemas
  UserCreate: UserCreateSchema,
  UserUpdate: UserUpdateSchema,
  UserLogin: UserLoginSchema,
  PasswordReset: PasswordResetSchema,
  PasswordChange: PasswordChangeSchema,
  
  // Claim schemas
  ClaimCreate: ClaimCreateSchema,
  ClaimUpdate: ClaimUpdateSchema,
  ClaimSearch: ClaimSearchSchema,
  
  // Claimant schemas
  ClaimantCreate: ClaimantCreateSchema,
  ClaimantUpdate: ClaimantUpdateSchema,
  
  // Activity schemas
  ActivityCreate: ActivityCreateSchema,
  ActivityUpdate: ActivityUpdateSchema,
  
  // Document schemas
  DocumentCreate: DocumentCreateSchema,
  DocumentUpdate: DocumentUpdateSchema,
  
  // Commission schemas
  CommissionCreate: CommissionCreateSchema,
  CommissionUpdate: CommissionUpdateSchema,
  
  // Bulk operation schemas
  BulkClaimUpdate: BulkClaimUpdateSchema,
  BulkAssignment: BulkAssignmentSchema,
  
  // API schemas
  ApiResponse: ApiResponseSchema,
  Pagination: PaginationSchema
}
