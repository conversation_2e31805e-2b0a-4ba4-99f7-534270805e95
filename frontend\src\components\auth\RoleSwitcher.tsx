import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/AuthContext'
import { UserRole, ROLE_DISPLAY_NAMES, ROLE_DESCRIPTIONS } from '@/types/auth'
import { Users, Shield, AlertTriangle } from 'lucide-react'

export function RoleSwitcher() {
  const { user, switchRole } = useAuth()
  const [selectedRole, setSelectedRole] = useState<UserRole | ''>('')
  const [showSwitcher, setShowSwitcher] = useState(false)

  if (!user) return null

  const handleRoleSwitch = () => {
    if (selectedRole) {
      switchRole(selectedRole)
      setSelectedRole('')
      setShowSwitcher(false)
    }
  }

  const roles: UserRole[] = ['junior_agent', 'senior_agent', 'admin', 'contractor', 'compliance', 'finance']

  return (
    <div className="fixed bottom-4 right-4 z-50">
      {!showSwitcher ? (
        <Button
          onClick={() => setShowSwitcher(true)}
          className="rounded-full shadow-lg"
          size="sm"
        >
          <Users className="h-4 w-4 mr-2" />
          Demo Mode
        </Button>
      ) : (
        <Card className="w-80 shadow-xl">
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Shield className="h-5 w-5 text-blue-600" />
                <CardTitle className="text-lg">Role Switcher</CardTitle>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowSwitcher(false)}
              >
                ×
              </Button>
            </div>
            <CardDescription>
              Switch between different user roles to test permissions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Current Role */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Current Role
              </label>
              <div className="flex items-center space-x-2">
                <Badge variant="default" className="text-sm">
                  {ROLE_DISPLAY_NAMES[user.role]}
                </Badge>
                <span className="text-sm text-gray-600">{user.name}</span>
              </div>
            </div>

            {/* Role Selector */}
            <div>
              <label className="text-sm font-medium text-gray-700 mb-2 block">
                Switch to Role
              </label>
              <Select value={selectedRole} onValueChange={(value) => setSelectedRole(value as UserRole)}>
                <SelectTrigger>
                  <SelectValue placeholder="Select a role to switch to" />
                </SelectTrigger>
                <SelectContent>
                  {roles.map((role) => (
                    <SelectItem key={role} value={role} disabled={role === user.role}>
                      <div className="flex items-center justify-between w-full">
                        <span>{ROLE_DISPLAY_NAMES[role]}</span>
                        {role === user.role && (
                          <Badge variant="secondary" className="ml-2 text-xs">
                            Current
                          </Badge>
                        )}
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Role Description */}
            {selectedRole && (
              <div className="bg-blue-50 p-3 rounded-md">
                <h4 className="font-medium text-blue-900 mb-1">
                  {ROLE_DISPLAY_NAMES[selectedRole]}
                </h4>
                <p className="text-sm text-blue-700">
                  {ROLE_DESCRIPTIONS[selectedRole]}
                </p>
              </div>
            )}

            {/* Warning */}
            <div className="bg-yellow-50 p-3 rounded-md flex items-start space-x-2">
              <AlertTriangle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-700">
                <strong>Demo Mode:</strong> This feature is for demonstration purposes only. 
                In production, role changes would require proper authorization.
              </div>
            </div>

            {/* Actions */}
            <div className="flex space-x-2">
              <Button
                onClick={handleRoleSwitch}
                disabled={!selectedRole || selectedRole === user.role}
                className="flex-1"
              >
                Switch Role
              </Button>
              <Button
                variant="outline"
                onClick={() => setShowSwitcher(false)}
              >
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
} 