<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AssetHunterPro - Ultimate Function Tests</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(31, 38, 135, 0.37);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-result {
            padding: 12px;
            margin: 8px 0;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .pass { background: rgba(34, 197, 94, 0.2); border-left: 4px solid #22c55e; }
        .fail { background: rgba(239, 68, 68, 0.2); border-left: 4px solid #ef4444; }
        .warning { background: rgba(245, 158, 11, 0.2); border-left: 4px solid #f59e0b; }
        .summary {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 30px 0;
            text-align: center;
        }
        .button {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        .button:hover {
            transform: translateY(-2px);
        }
        .progress {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(45deg, #22c55e, #16a34a);
            width: 0%;
            transition: width 0.3s ease;
        }
        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        .diagnostic-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 12px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧪 AssetHunterPro - Ultimate Function Tests</h1>
            <p>Most comprehensive testing with advanced detection and diagnostics</p>
            <button class="button" onclick="runUltimateTests()">🚀 Run Ultimate Tests</button>
            <button class="button" onclick="runQuickCheck()">⚡ Quick Check</button>
            <button class="button" onclick="clearResults()">🗑️ Clear Results</button>
        </div>

        <div class="progress">
            <div class="progress-bar" id="progressBar"></div>
        </div>

        <div id="testResults"></div>

        <div class="summary" id="summary" style="display: none;">
            <h2>📊 Ultimate Test Summary</h2>
            <div id="summaryContent"></div>
        </div>
    </div>

    <script>
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0,
            tests: []
        };

        function updateProgress(current, total) {
            const percentage = (current / total) * 100;
            document.getElementById('progressBar').style.width = percentage + '%';
        }

        function logTest(name, status, details = {}) {
            testResults.total++;
            testResults[status === 'pass' ? 'passed' : status === 'fail' ? 'failed' : 'warnings']++;
            
            const test = { name, status, details, timestamp: new Date().toISOString() };
            testResults.tests.push(test);

            const resultsDiv = document.getElementById('testResults');
            const testDiv = document.createElement('div');
            testDiv.className = `test-result ${status}`;
            
            const icon = status === 'pass' ? '✅' : status === 'fail' ? '❌' : '⚠️';
            const statusBadge = `<span class="status-badge" style="background: ${status === 'pass' ? '#22c55e' : status === 'fail' ? '#ef4444' : '#f59e0b'}">${status.toUpperCase()}</span>`;
            
            testDiv.innerHTML = `
                <div>
                    <strong>${icon} ${name}</strong>
                    ${details.info ? `<br><small style="opacity: 0.8">${details.info}</small>` : ''}
                    ${details.diagnostic ? `<div class="diagnostic-info">${details.diagnostic}</div>` : ''}
                </div>
                <div>${statusBadge}</div>
            `;
            
            resultsDiv.appendChild(testDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function runQuickCheck() {
            clearResults();
            console.log('⚡ Running Quick Check...');
            
            // Quick environment check
            logTest('Environment: Browser APIs', 
                (typeof localStorage !== 'undefined' && typeof fetch !== 'undefined') ? 'pass' : 'fail',
                { info: 'localStorage and fetch API availability' }
            );

            // Quick server check
            logTest('Server: Development server', 
                window.location.port === '3005' ? 'pass' : 'warning',
                { info: `Running on port ${window.location.port}` }
            );

            // Quick backend check
            try {
                const response = await fetch('http://localhost:3001/health');
                logTest('Backend: Health endpoint', 
                    response.ok ? 'pass' : 'fail',
                    { info: `Status: ${response.status}` }
                );
            } catch (error) {
                logTest('Backend: Health endpoint', 'fail',
                    { info: `Cannot reach backend: ${error.message}` }
                );
            }

            // Quick UI check
            const hasButtons = document.querySelectorAll('button').length > 0;
            const hasStyles = document.querySelectorAll('link[rel="stylesheet"], style').length > 0;
            
            logTest('UI: Basic elements', 
                (hasButtons && hasStyles) ? 'pass' : 'warning',
                { info: `Buttons: ${hasButtons}, Styles: ${hasStyles}` }
            );

            updateProgress(4, 4);
            showSummary();
        }

        async function runUltimateTests() {
            clearResults();
            console.log('🚀 Running Ultimate Comprehensive Tests...');
            
            const totalTests = 20;
            let currentTest = 0;

            // Phase 1: Environment Tests (5 tests)
            console.log('🔍 Phase 1: Environment Testing');
            
            const envTests = [
                { name: 'localStorage', test: () => {
                    try {
                        localStorage.setItem('test', 'ok');
                        const result = localStorage.getItem('test') === 'ok';
                        localStorage.removeItem('test');
                        return result;
                    } catch (e) { return false; }
                }},
                { name: 'sessionStorage', test: () => {
                    try {
                        sessionStorage.setItem('test', 'ok');
                        const result = sessionStorage.getItem('test') === 'ok';
                        sessionStorage.removeItem('test');
                        return result;
                    } catch (e) { return false; }
                }},
                { name: 'fetch API', test: () => typeof fetch === 'function' },
                { name: 'Promise support', test: () => typeof Promise === 'function' },
                { name: 'JSON support', test: () => typeof JSON === 'object' && typeof JSON.parse === 'function' }
            ];

            for (const test of envTests) {
                currentTest++;
                updateProgress(currentTest, totalTests);
                
                try {
                    const result = test.test();
                    logTest(`Environment: ${test.name}`, result ? 'pass' : 'fail', {
                        info: result ? 'Working correctly' : 'Not available or not working'
                    });
                } catch (error) {
                    logTest(`Environment: ${test.name}`, 'fail', {
                        info: `Error: ${error.message}`
                    });
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Phase 2: Server Tests (3 tests)
            console.log('🌐 Phase 2: Server Testing');
            
            const serverTests = [
                { name: 'Development server port', test: () => window.location.port === '3005' },
                { name: 'Development server protocol', test: () => window.location.protocol === 'http:' },
                { name: 'Development server host', test: () => window.location.hostname === 'localhost' }
            ];

            for (const test of serverTests) {
                currentTest++;
                updateProgress(currentTest, totalTests);
                
                const result = test.test();
                logTest(`Server: ${test.name}`, result ? 'pass' : 'warning', {
                    info: result ? 'Configured correctly' : 'Configuration issue detected'
                });
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            // Phase 3: Backend API Tests (3 tests)
            console.log('🔌 Phase 3: Backend API Testing');
            
            const apiTests = [
                { name: 'Health endpoint', url: 'http://localhost:3001/health' },
                { name: 'Claims API', url: 'http://localhost:3001/api/claims' },
                { name: 'Dashboard API', url: 'http://localhost:3001/api/dashboard/stats' }
            ];

            for (const test of apiTests) {
                currentTest++;
                updateProgress(currentTest, totalTests);
                
                try {
                    const response = await fetch(test.url);
                    logTest(`API: ${test.name}`, response.ok ? 'pass' : 'fail', {
                        info: response.ok ? `Status: ${response.status}` : `Failed with status: ${response.status}`
                    });
                } catch (error) {
                    logTest(`API: ${test.name}`, 'fail', {
                        info: `Cannot reach endpoint: ${error.message}`
                    });
                }
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            // Phase 4: Advanced UI Tests (6 tests)
            console.log('🎨 Phase 4: Advanced UI Testing');
            
            // Wait for potential React loading
            await new Promise(resolve => setTimeout(resolve, 3000));
            
            // Test 1: React App Detection (Advanced)
            currentTest++;
            updateProgress(currentTest, totalTests);
            
            const root = document.getElementById('root');
            let reactDetected = false;
            let reactInfo = '';
            
            if (root) {
                const hasContent = root.children.length > 0;
                const hasDataAttr = root.hasAttribute('data-reactroot');
                const hasReactDevTools = window.__REACT_DEVTOOLS_GLOBAL_HOOK__ !== undefined;
                const hasModuleScript = document.querySelector('script[type="module"]') !== null;
                
                reactDetected = hasContent || hasDataAttr || hasReactDevTools || hasModuleScript;
                reactInfo = `Content: ${hasContent}, DataAttr: ${hasDataAttr}, DevTools: ${hasReactDevTools}, Module: ${hasModuleScript}`;
            }
            
            logTest('UI: React app detection', reactDetected ? 'pass' : 'warning', {
                info: root ? `React indicators detected` : 'Root element not found',
                diagnostic: reactInfo
            });

            // Test 2: CSS Styles
            currentTest++;
            updateProgress(currentTest, totalTests);
            const hasStyles = document.querySelectorAll('link[rel="stylesheet"], style').length > 0;
            logTest('UI: CSS styles loaded', hasStyles ? 'pass' : 'warning', {
                info: hasStyles ? 'Stylesheets detected' : 'No stylesheets found'
            });

            // Test 3: Interactive Elements
            currentTest++;
            updateProgress(currentTest, totalTests);
            const buttons = document.querySelectorAll('button');
            logTest('UI: Interactive buttons', buttons.length > 0 ? 'pass' : 'warning', {
                info: `Found ${buttons.length} button elements`
            });

            // Test 4: Form Elements (Smart Detection)
            currentTest++;
            updateProgress(currentTest, totalTests);
            const inputs = document.querySelectorAll('input');
            const hasNavigation = document.querySelector('aside, nav, .sidebar, [class*="nav"]') !== null;
            const hasUserIndicators = document.querySelector('[class*="user"], [class*="logout"], [class*="profile"]') !== null;
            const userLoggedIn = hasNavigation || hasUserIndicators;
            
            let formStatus = 'warning';
            let formInfo = 'No form inputs detected';
            
            if (inputs.length > 0) {
                formStatus = 'pass';
                formInfo = `Found ${inputs.length} input elements`;
            } else if (userLoggedIn) {
                formStatus = 'pass';
                formInfo = 'No inputs visible - user appears logged in (expected)';
            }
            
            logTest('UI: Form inputs', formStatus, {
                info: formInfo,
                diagnostic: `Navigation: ${hasNavigation}, UserIndicators: ${hasUserIndicators}`
            });

            // Test 5: Navigation Elements (Advanced Detection)
            currentTest++;
            updateProgress(currentTest, totalTests);
            const navElements = document.querySelectorAll('nav, aside, [role="navigation"]');
            const navButtons = document.querySelectorAll('button[class*="nav"], .nav button, aside button');
            const navLinks = document.querySelectorAll('a[class*="nav"], .nav a, aside a');
            const sidebarElements = document.querySelectorAll('.sidebar, [class*="sidebar"]');
            
            const totalNavElements = navElements.length + navButtons.length + navLinks.length + sidebarElements.length;
            const hasNavigation2 = totalNavElements > 0;
            
            logTest('UI: Navigation elements', hasNavigation2 ? 'pass' : 'warning', {
                info: hasNavigation2 ? 
                    `Found navigation elements: ${navElements.length} nav, ${navButtons.length} buttons, ${navLinks.length} links, ${sidebarElements.length} sidebars` :
                    'No navigation elements detected',
                diagnostic: `Total nav elements: ${totalNavElements}`
            });

            // Test 6: Application State
            currentTest++;
            updateProgress(currentTest, totalTests);
            const hasContent = document.body.children.length > 2; // More than just script tags
            const hasInteractivity = buttons.length > 0 || document.querySelectorAll('a, input, select, textarea').length > 0;
            const appLoaded = hasContent && hasInteractivity;
            
            logTest('UI: Application state', appLoaded ? 'pass' : 'warning', {
                info: appLoaded ? 'Application appears fully loaded and interactive' : 'Application may not be fully loaded',
                diagnostic: `Content: ${hasContent}, Interactive: ${hasInteractivity}`
            });

            // Phase 5: Data Validation Tests (3 tests)
            console.log('✅ Phase 5: Data Validation Testing');
            
            const validationTests = [
                { name: 'Email validation', test: () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test('<EMAIL>') },
                { name: 'Phone validation', test: () => /^\+?[\d\s\-\(\)]{10,}$/.test('************') },
                { name: 'SSN validation', test: () => /^\d{3}-?\d{2}-?\d{4}$/.test('***********') }
            ];

            for (const test of validationTests) {
                currentTest++;
                updateProgress(currentTest, totalTests);
                
                try {
                    const result = test.test();
                    logTest(`Validation: ${test.name}`, result ? 'pass' : 'fail', {
                        info: result ? 'Regex working correctly' : 'Validation regex failed'
                    });
                } catch (error) {
                    logTest(`Validation: ${test.name}`, 'fail', {
                        info: `Validation error: ${error.message}`
                    });
                }
                await new Promise(resolve => setTimeout(resolve, 100));
            }

            updateProgress(totalTests, totalTests);
            showSummary();
        }

        function showSummary() {
            const summaryDiv = document.getElementById('summary');
            const summaryContent = document.getElementById('summaryContent');
            
            const successRate = ((testResults.passed / testResults.total) * 100).toFixed(1);
            const status = successRate > 95 ? '🟢 EXCELLENT' : 
                          successRate > 85 ? '🟢 HEALTHY' : 
                          successRate > 70 ? '🟡 GOOD' :
                          successRate > 60 ? '🟡 NEEDS ATTENTION' : '🔴 CRITICAL';
            
            summaryContent.innerHTML = `
                <h3>Overall System Health: ${status}</h3>
                <p><strong>Success Rate:</strong> ${successRate}%</p>
                <p><strong>Total Tests:</strong> ${testResults.total}</p>
                <p><strong>✅ Passed:</strong> ${testResults.passed}</p>
                <p><strong>⚠️ Warnings:</strong> ${testResults.warnings}</p>
                <p><strong>❌ Failed:</strong> ${testResults.failed}</p>
                <p><strong>Timestamp:</strong> ${new Date().toLocaleString()}</p>
                
                <div style="margin-top: 20px; padding: 15px; background: rgba(255,255,255,0.1); border-radius: 10px;">
                    <h4>🎉 AssetHunterPro System Status</h4>
                    <p><strong>Core Functionality:</strong> ${testResults.passed >= (testResults.total * 0.8) ? '✅ Operational' : '⚠️ Issues Detected'}</p>
                    <p><strong>Backend APIs:</strong> ${testResults.tests.filter(t => t.name.includes('API:') && t.status === 'pass').length >= 2 ? '✅ Working' : '⚠️ Issues'}</p>
                    <p><strong>Frontend UI:</strong> ${testResults.tests.filter(t => t.name.includes('UI:') && t.status === 'pass').length >= 3 ? '✅ Functional' : '⚠️ Issues'}</p>
                    <p><strong>Recommendation:</strong> ${successRate > 85 ? 'System ready for use!' : 'Review warnings and failed tests.'}</p>
                </div>
            `;
            
            summaryDiv.style.display = 'block';
            
            // Save results
            localStorage.setItem('assetHunterPro_ultimateTestResults', JSON.stringify({
                timestamp: new Date().toISOString(),
                summary: { successRate: parseFloat(successRate), status },
                results: testResults
            }));
            
            console.log('🎉 Ultimate Test Results:', testResults);
        }

        function clearResults() {
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0, tests: [] };
            document.getElementById('testResults').innerHTML = '';
            document.getElementById('summary').style.display = 'none';
            document.getElementById('progressBar').style.width = '0%';
        }

        // Auto-message
        setTimeout(() => {
            console.log('🚀 Ultimate AssetHunterPro Function Tests Ready!');
            console.log('This version includes advanced detection logic and comprehensive diagnostics.');
        }, 1000);
    </script>
</body>
</html>
