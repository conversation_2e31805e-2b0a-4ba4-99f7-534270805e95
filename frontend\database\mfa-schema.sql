-- Multi-Factor Authentication Schema for AssetHunterPro
-- This schema supports MFA settings, backup codes, and security monitoring

-- Enable UUID extension (safe to run multiple times)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================
-- MFA SETTINGS TABLE
-- ===================================

-- User MFA settings and preferences
CREATE TABLE IF NOT EXISTS user_mfa_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    settings JSONB NOT NULL DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one settings record per user
    UNIQUE(user_id)
);

-- Index for faster user lookups
CREATE INDEX IF NOT EXISTS idx_user_mfa_settings_user_id ON user_mfa_settings(user_id);

-- ===================================
-- MFA AUDIT LOG
-- ===================================

-- Track MFA-related security events
CREATE TABLE IF NOT EXISTS mfa_audit_log (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'mfa_enrolled',
        'mfa_verified',
        'mfa_failed',
        'mfa_unenrolled',
        'backup_code_used',
        'backup_code_generated',
        'factor_added',
        'factor_removed'
    )),
    factor_id VARCHAR(255), -- Supabase factor ID
    factor_type VARCHAR(20) DEFAULT 'totp',
    ip_address INET,
    user_agent TEXT,
    success BOOLEAN NOT NULL,
    error_message TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for audit log queries
CREATE INDEX IF NOT EXISTS idx_mfa_audit_log_user_id ON mfa_audit_log(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_log_event_type ON mfa_audit_log(event_type);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_log_created_at ON mfa_audit_log(created_at);
CREATE INDEX IF NOT EXISTS idx_mfa_audit_log_success ON mfa_audit_log(success);

-- ===================================
-- MFA ENFORCEMENT RULES
-- ===================================

-- Define MFA requirements by role
CREATE TABLE IF NOT EXISTS mfa_enforcement_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role VARCHAR(50) NOT NULL,
    mfa_required BOOLEAN NOT NULL DEFAULT false,
    grace_period_days INTEGER DEFAULT 30,
    enforcement_date DATE,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure one rule per role
    UNIQUE(role)
);

-- Insert default MFA enforcement rules
INSERT INTO mfa_enforcement_rules (role, mfa_required, grace_period_days, enforcement_date, created_by) 
VALUES 
    ('admin', true, 7, CURRENT_DATE, (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),
    ('senior_agent', true, 30, CURRENT_DATE + INTERVAL '30 days', (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),
    ('junior_agent', false, 90, CURRENT_DATE + INTERVAL '90 days', (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),
    ('contractor', false, 90, CURRENT_DATE + INTERVAL '90 days', (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),
    ('compliance', true, 14, CURRENT_DATE + INTERVAL '14 days', (SELECT id FROM users WHERE role = 'admin' LIMIT 1)),
    ('finance', true, 30, CURRENT_DATE + INTERVAL '30 days', (SELECT id FROM users WHERE role = 'admin' LIMIT 1))
ON CONFLICT (role) DO NOTHING;

-- ===================================
-- SECURITY MONITORING
-- ===================================

-- Track suspicious MFA activities
CREATE TABLE IF NOT EXISTS mfa_security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'multiple_failed_attempts',
        'unusual_location',
        'device_change',
        'backup_code_exhausted',
        'factor_compromise_suspected'
    )),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    ip_address INET,
    user_agent TEXT,
    metadata JSONB DEFAULT '{}',
    resolved BOOLEAN DEFAULT false,
    resolved_by UUID REFERENCES users(id),
    resolved_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for security event queries
CREATE INDEX IF NOT EXISTS idx_mfa_security_events_user_id ON mfa_security_events(user_id);
CREATE INDEX IF NOT EXISTS idx_mfa_security_events_severity ON mfa_security_events(severity);
CREATE INDEX IF NOT EXISTS idx_mfa_security_events_resolved ON mfa_security_events(resolved);
CREATE INDEX IF NOT EXISTS idx_mfa_security_events_created_at ON mfa_security_events(created_at);

-- ===================================
-- FUNCTIONS AND TRIGGERS
-- ===================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_mfa_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger for user_mfa_settings
CREATE TRIGGER update_user_mfa_settings_updated_at 
    BEFORE UPDATE ON user_mfa_settings
    FOR EACH ROW 
    EXECUTE FUNCTION update_mfa_updated_at_column();

-- Trigger for mfa_enforcement_rules
CREATE TRIGGER update_mfa_enforcement_rules_updated_at 
    BEFORE UPDATE ON mfa_enforcement_rules
    FOR EACH ROW 
    EXECUTE FUNCTION update_mfa_updated_at_column();

-- ===================================
-- HELPER FUNCTIONS
-- ===================================

-- Function to check if MFA is required for a user
CREATE OR REPLACE FUNCTION is_mfa_required_for_user(user_id_param UUID)
RETURNS BOOLEAN AS $$
DECLARE
    user_role VARCHAR(50);
    rule_record RECORD;
BEGIN
    -- Get user role
    SELECT role INTO user_role FROM users WHERE id = user_id_param;
    
    IF user_role IS NULL THEN
        RETURN false;
    END IF;
    
    -- Get enforcement rule for role
    SELECT * INTO rule_record FROM mfa_enforcement_rules WHERE role = user_role;
    
    IF rule_record IS NULL THEN
        RETURN false;
    END IF;
    
    -- Check if MFA is required and enforcement date has passed
    RETURN rule_record.mfa_required AND (rule_record.enforcement_date IS NULL OR rule_record.enforcement_date <= CURRENT_DATE);
END;
$$ LANGUAGE plpgsql;

-- Function to get MFA status for a user
CREATE OR REPLACE FUNCTION get_user_mfa_status(user_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    mfa_settings JSONB;
    is_required BOOLEAN;
    result JSONB;
BEGIN
    -- Get MFA settings
    SELECT settings INTO mfa_settings FROM user_mfa_settings WHERE user_id = user_id_param;
    
    -- Check if MFA is required
    SELECT is_mfa_required_for_user(user_id_param) INTO is_required;
    
    -- Build result
    result := jsonb_build_object(
        'enabled', COALESCE((mfa_settings->>'enabled')::boolean, false),
        'required', is_required,
        'has_backup_codes', COALESCE(jsonb_array_length(mfa_settings->'backup_codes') > 0, false),
        'last_verified', mfa_settings->>'last_verified'
    );
    
    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Function to log MFA events
CREATE OR REPLACE FUNCTION log_mfa_event(
    user_id_param UUID,
    event_type_param VARCHAR(50),
    factor_id_param VARCHAR(255) DEFAULT NULL,
    success_param BOOLEAN DEFAULT true,
    ip_address_param INET DEFAULT NULL,
    user_agent_param TEXT DEFAULT NULL,
    error_message_param TEXT DEFAULT NULL,
    metadata_param JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    log_id UUID;
BEGIN
    INSERT INTO mfa_audit_log (
        user_id,
        event_type,
        factor_id,
        success,
        ip_address,
        user_agent,
        error_message,
        metadata
    ) VALUES (
        user_id_param,
        event_type_param,
        factor_id_param,
        success_param,
        ip_address_param,
        user_agent_param,
        error_message_param,
        metadata_param
    ) RETURNING id INTO log_id;
    
    RETURN log_id;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- ROW LEVEL SECURITY (RLS)
-- ===================================

-- Enable RLS on MFA tables
ALTER TABLE user_mfa_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_audit_log ENABLE ROW LEVEL SECURITY;
ALTER TABLE mfa_security_events ENABLE ROW LEVEL SECURITY;

-- Users can only access their own MFA settings
CREATE POLICY "Users can manage their own MFA settings" ON user_mfa_settings
    FOR ALL USING (user_id = auth.uid());

-- Users can view their own MFA audit logs
CREATE POLICY "Users can view their own MFA audit logs" ON mfa_audit_log
    FOR SELECT USING (user_id = auth.uid());

-- Admins can view all MFA audit logs
CREATE POLICY "Admins can view all MFA audit logs" ON mfa_audit_log
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Users can view their own security events
CREATE POLICY "Users can view their own MFA security events" ON mfa_security_events
    FOR SELECT USING (user_id = auth.uid());

-- Admins and compliance can view all security events
CREATE POLICY "Admins and compliance can view all MFA security events" ON mfa_security_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role IN ('admin', 'compliance')
        )
    );

-- ===================================
-- SAMPLE DATA (for development)
-- ===================================

-- Insert sample MFA settings for demo users
INSERT INTO user_mfa_settings (user_id, settings) 
SELECT 
    id,
    jsonb_build_object(
        'enabled', false,
        'required', false,
        'backup_codes', '[]'::jsonb
    )
FROM users
WHERE id NOT IN (SELECT user_id FROM user_mfa_settings)
ON CONFLICT (user_id) DO NOTHING;

-- ===================================
-- COMMENTS
-- ===================================

COMMENT ON TABLE user_mfa_settings IS 'Stores MFA settings and preferences for each user';
COMMENT ON TABLE mfa_audit_log IS 'Audit trail for all MFA-related activities';
COMMENT ON TABLE mfa_enforcement_rules IS 'Defines MFA requirements by user role';
COMMENT ON TABLE mfa_security_events IS 'Tracks suspicious MFA-related security events';

COMMENT ON FUNCTION is_mfa_required_for_user(UUID) IS 'Checks if MFA is required for a specific user based on their role';
COMMENT ON FUNCTION get_user_mfa_status(UUID) IS 'Returns comprehensive MFA status for a user';
COMMENT ON FUNCTION log_mfa_event(UUID, VARCHAR, VARCHAR, BOOLEAN, INET, TEXT, TEXT, JSONB) IS 'Logs MFA events for audit purposes';
