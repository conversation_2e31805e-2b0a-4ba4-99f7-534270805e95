import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Search,
  Bot,
  Brain,
  Users,
  Phone,
  Mail,
  MapPin,
  Shield,
  Target,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  FileText,
  TrendingUp,
  Activity,
  Database,
  Network,
  Settings,
  Play,
  Pause,
  RotateCcw,
  ArrowRight,
  Star,
  DollarSign,
  Lightbulb
} from 'lucide-react';
import { 
  UniversalDiscoveryPlatform,
  DiscoveryResult,
  UniversalDiscoveryMetrics,
  StandardizedRecord,
  ContactRecommendation
} from '@/types/universal-discovery';

interface UniversalDiscoveryProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const UniversalDiscoveryComponent: React.FC<UniversalDiscoveryProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  const [discoveryStatus, setDiscoveryStatus] = useState('idle');
  
  const [discoveryMetrics, setDiscoveryMetrics] = useState<UniversalDiscoveryMetrics>({
    processing_metrics: {
      records_processed: 15684,
      processing_speed: 127, // records per hour
      automation_rate: 87.3,
      manual_intervention_rate: 12.7,
      error_rate: 2.4
    },
    success_metrics: {
      discovery_success_rate: 81.7,
      contact_discovery_rate: 76.3,
      contact_quality_score: 89.2,
      response_rate: 34.8,
      conversion_rate: 28.5
    },
    cost_metrics: {
      cost_per_discovery: 8.47,
      cost_per_successful_contact: 11.09,
      roi_per_case: 267.3,
      cost_savings_vs_manual: 89.2
    },
    quality_metrics: {
      data_accuracy: 94.6,
      contact_accuracy: 91.8,
      false_positive_rate: 3.2,
      false_negative_rate: 5.4,
      customer_satisfaction: 92.1
    }
  });

  const activeDiscoveryResults = [
    {
      discovery_id: 'UD-2024-3847',
      original_name: 'Johnson, Mary Elizabeth',
      state_source: 'California',
      entity_type: 'individual_person',
      asset_value: 12500,
      discovery_status: 'completed',
      success_probability: 94.7,
      contact_methods_found: 6,
      best_contact: {
        type: 'phone',
        value: '(*************',
        quality_score: 92,
        verification_status: 'verified'
      },
      death_status: 'likely_living',
      processing_time: 12, // minutes
      cost: 6.75
    },
    {
      discovery_id: 'UD-2024-3848',
      original_name: 'TechFlow Industries LLC',
      state_source: 'Texas',
      entity_type: 'business_entity',
      asset_value: 48500,
      discovery_status: 'completed',
      success_probability: 78.3,
      contact_methods_found: 4,
      best_contact: {
        type: 'email',
        value: '<EMAIL>',
        quality_score: 87,
        verification_status: 'verified'
      },
      death_status: 'not_applicable',
      processing_time: 8,
      cost: 11.20
    },
    {
      discovery_id: 'UD-2024-3849',
      original_name: 'Smith, Robert James Sr',
      state_source: 'Florida',
      entity_type: 'individual_person',
      asset_value: 7800,
      discovery_status: 'deceased_heirs_found',
      success_probability: 71.9,
      contact_methods_found: 3,
      best_contact: {
        type: 'heir',
        value: 'Robert Smith Jr (Son)',
        quality_score: 89,
        verification_status: 'verified'
      },
      death_status: 'deceased',
      heir_info: {
        heirs_found: 2,
        primary_heir: 'Robert Smith Jr',
        estate_status: 'probate_completed'
      },
      processing_time: 25,
      cost: 18.95
    }
  ];

  const automationWorkflows = [
    {
      workflow_name: 'Universal Data Ingestion',
      workflow_type: 'Data Normalization',
      status: 'Active',
      records_processed: 2847,
      success_rate: 96.4,
      automation_level: 'Full',
      description: 'Automatically processes CSV files from any state format'
    },
    {
      workflow_name: 'Multi-Source Contact Discovery',
      workflow_type: 'Contact Intelligence',
      status: 'Active', 
      records_processed: 1923,
      success_rate: 83.7,
      automation_level: 'Full',
      description: 'Searches 15+ data sources simultaneously for contact information'
    },
    {
      workflow_name: 'Death Detection & Heir Discovery',
      workflow_type: 'Specialized Search',
      status: 'Active',
      records_processed: 892,
      success_rate: 79.1,
      automation_level: 'Hybrid',
      description: 'AI-powered death detection with automated heir discovery'
    },
    {
      workflow_name: 'Contact Quality Scoring',
      workflow_type: 'Intelligence Processing',
      status: 'Active',
      records_processed: 3456,
      success_rate: 91.8,
      automation_level: 'Full',
      description: 'Scores and ranks all discovered contact methods'
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Records Processed</p>
                <p className="text-2xl font-bold text-blue-600">
                  {discoveryMetrics.processing_metrics.records_processed.toLocaleString()}
                </p>
              </div>
              <Database className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              {discoveryMetrics.processing_metrics.processing_speed}/hour average
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-green-600">
                  {discoveryMetrics.success_metrics.discovery_success_rate}%
                </p>
              </div>
              <Target className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Contact discovery: {discoveryMetrics.success_metrics.contact_discovery_rate}%
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-purple-600">
                  {discoveryMetrics.processing_metrics.automation_rate}%
                </p>
              </div>
              <Bot className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              {100 - discoveryMetrics.processing_metrics.manual_intervention_rate}% hands-free
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cost per Discovery</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${discoveryMetrics.cost_metrics.cost_per_discovery}
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              {discoveryMetrics.cost_metrics.cost_savings_vs_manual}% vs manual
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Live Discovery Results
            </CardTitle>
            <CardDescription>
              Real-time automated discovery across all states
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {activeDiscoveryResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold">{result.original_name}</h4>
                    <div className="text-sm text-gray-600">
                      {result.state_source} • ${result.asset_value.toLocaleString()}
                    </div>
                  </div>
                  <Badge variant={
                    result.discovery_status === 'completed' ? 'default' :
                    result.discovery_status === 'deceased_heirs_found' ? 'secondary' : 'outline'
                  }>
                    {result.discovery_status.replace('_', ' ')}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Success Probability</div>
                    <div className="font-semibold text-green-600">{result.success_probability}%</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Contact Methods</div>
                    <div className="font-medium">{result.contact_methods_found} found</div>
                  </div>
                </div>

                <div className="bg-blue-50 rounded-lg p-3">
                  <div className="text-sm font-medium text-blue-800">Best Contact:</div>
                  <div className="text-sm text-blue-700">
                    {result.best_contact.type}: {result.best_contact.value}
                    <span className="ml-2 text-xs">
                      ({result.best_contact.quality_score}% quality)
                    </span>
                  </div>
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>Processed in {result.processing_time} min</span>
                  <span>Cost: ${result.cost}</span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5" />
              Automation Workflows
            </CardTitle>
            <CardDescription>
              AI-powered discovery processes running 24/7
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {automationWorkflows.map((workflow, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold">{workflow.workflow_name}</h4>
                    <div className="text-sm text-gray-600">{workflow.workflow_type}</div>
                  </div>
                  <Badge variant="default">{workflow.status}</Badge>
                </div>

                <p className="text-sm text-gray-600">{workflow.description}</p>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Processed</div>
                    <div className="font-medium">{workflow.records_processed.toLocaleString()}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Success Rate</div>
                    <div className="font-semibold text-green-600">{workflow.success_rate}%</div>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <Badge variant="outline">{workflow.automation_level}</Badge>
                  <Button size="sm" variant="outline">
                    <Settings className="h-4 w-4 mr-1" />
                    Configure
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Processing Performance</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: 'Data Accuracy', value: discoveryMetrics.quality_metrics.data_accuracy },
              { metric: 'Contact Accuracy', value: discoveryMetrics.quality_metrics.contact_accuracy },
              { metric: 'Response Rate', value: discoveryMetrics.success_metrics.response_rate },
              { metric: 'Conversion Rate', value: discoveryMetrics.success_metrics.conversion_rate }
            ].map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{metric.metric}</span>
                  <span className="text-sm font-medium">{metric.value}%</span>
                </div>
                <Progress value={metric.value} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Cost Efficiency</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">
                {discoveryMetrics.cost_metrics.cost_savings_vs_manual}%
              </div>
              <div className="text-sm text-gray-600">Cost Savings vs Manual</div>
            </div>
            <div className="grid grid-cols-1 gap-3 text-sm">
              <div className="flex justify-between">
                <span>Per Discovery:</span>
                <span className="font-medium">${discoveryMetrics.cost_metrics.cost_per_discovery}</span>
              </div>
              <div className="flex justify-between">
                <span>Per Contact:</span>
                <span className="font-medium">${discoveryMetrics.cost_metrics.cost_per_successful_contact}</span>
              </div>
              <div className="flex justify-between">
                <span>ROI per Case:</span>
                <span className="font-medium text-green-600">${discoveryMetrics.cost_metrics.roi_per_case}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Quality Metrics</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">
                {discoveryMetrics.quality_metrics.customer_satisfaction}%
              </div>
              <div className="text-sm text-gray-600">Customer Satisfaction</div>
            </div>
            <div className="grid grid-cols-1 gap-3 text-sm">
              <div className="flex justify-between">
                <span>False Positives:</span>
                <span className="font-medium">{discoveryMetrics.quality_metrics.false_positive_rate}%</span>
              </div>
              <div className="flex justify-between">
                <span>False Negatives:</span>
                <span className="font-medium">{discoveryMetrics.quality_metrics.false_negative_rate}%</span>
              </div>
              <div className="flex justify-between">
                <span>Contact Quality:</span>
                <span className="font-medium text-green-600">{discoveryMetrics.success_metrics.contact_quality_score}%</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Universal AI Discovery Engine</h2>
          <p className="text-gray-600">
            Automated contact discovery across all 50 states with 80-90% automation
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
            {userPlan === 'diamond' ? 'Full Discovery Suite' : 'Basic Discovery'}
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade for Universal AI
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Discovery Overview</TabsTrigger>
          <TabsTrigger value="automation">Smart Automation</TabsTrigger>
          <TabsTrigger value="death-heir">Death & Heir Discovery</TabsTrigger>
          <TabsTrigger value="analytics">Performance Analytics</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="automation" className="space-y-6">
          <div className="text-center text-gray-500">
            Smart Automation features coming in next update...
          </div>
        </TabsContent>

        <TabsContent value="death-heir" className="space-y-6">
          <div className="text-center text-gray-500">
            Death & Heir Discovery features coming in next update...
          </div>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-6">
          <div className="text-center text-gray-500">
            Performance Analytics coming in next update...
          </div>
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Search className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unlock Universal AI Discovery</h3>
              <p className="text-gray-600 mb-4">
                Upgrade to Diamond for automated contact discovery across all states, death detection, 
                and heir discovery with 80-90% automation rates
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 