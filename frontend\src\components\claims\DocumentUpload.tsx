import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Upload, 
  File, 
  X, 
  CheckCircle, 
  AlertCircle,
  FileText,
  Image,
  FileAudio,
  FileVideo,
  Archive
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

interface DocumentUploadProps {
  claimId: string;
  onUploadComplete: () => void;
  onClose: () => void;
}

interface FileWithPreview extends File {
  preview?: string;
  id?: string;
}

const DOCUMENT_CATEGORIES = [
  { value: 'id_documents', label: 'ID Documents' },
  { value: 'contracts', label: 'Contracts & Agreements' },
  { value: 'correspondence', label: 'Correspondence' },
  { value: 'state_forms', label: 'State Forms' },
  { value: 'signatures', label: 'Signatures & Authorization' },
  { value: 'other', label: 'Other Documents' }
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/gif',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain'
];

// Convert simple user ID to UUID format for database compatibility
const getUserUUID = (userId: string): string => {
  // Map mock user IDs to valid UUIDs
  const userIdMap: Record<string, string> = {
    '1': '11111111-1111-1111-1111-111111111111', // Junior Agent
    '2': '*************-2222-2222-************', // Senior Agent  
    '3': '*************-3333-3333-************', // Admin
    '4': '*************-4444-4444-************', // Contractor
    '5': '*************-5555-5555-************', // Compliance
    '6': '*************-6666-6666-************', // Finance
  };
  
  return userIdMap[userId] || '00000000-0000-0000-0000-000000000000'; // Fallback UUID
};

export default function DocumentUpload({ claimId, onUploadComplete, onClose }: DocumentUploadProps) {
  const { user } = useAuth();
  const [files, setFiles] = useState<FileWithPreview[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [category, setCategory] = useState('');
  const [errors, setErrors] = useState<string[]>([]);

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-8 w-8 text-blue-500" />;
    if (fileType.startsWith('audio/')) return <FileAudio className="h-8 w-8 text-green-500" />;
    if (fileType.startsWith('video/')) return <FileVideo className="h-8 w-8 text-purple-500" />;
    if (fileType === 'application/pdf') return <FileText className="h-8 w-8 text-red-500" />;
    if (fileType.includes('zip') || fileType.includes('archive')) return <Archive className="h-8 w-8 text-orange-500" />;
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File "${file.name}" exceeds 10MB limit`;
    }
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `File type "${file.type}" is not allowed`;
    }
    return null;
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFiles = (fileList: File[]) => {
    const newErrors: string[] = [];
    const validFiles: FileWithPreview[] = [];

    fileList.forEach(file => {
      const error = validateFile(file);
      if (error) {
        newErrors.push(error);
      } else {
        const fileWithPreview: FileWithPreview = Object.assign(file, {
          id: Math.random().toString(36).substr(2, 9),
          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined
        });
        validFiles.push(fileWithPreview);
      }
    });

    setErrors(newErrors);
    setFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setUploadProgress(prev => {
      const updated = { ...prev };
      delete updated[fileId];
      return updated;
    });
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const uploadFiles = async () => {
    if (!category) {
      setErrors(['Please select a document category']);
      return;
    }

    if (files.length === 0) {
      setErrors(['Please select at least one file']);
      return;
    }

    if (!user) {
      setErrors(['User not authenticated. Please log in again.']);
      return;
    }

    setUploading(true);
    setErrors([]);

    try {
      for (const file of files) {
        if (!file.id) continue;

        // Update progress
        setUploadProgress(prev => ({ ...prev, [file.id!]: 0 }));

        // Generate unique filename
        const fileName = `claim-${claimId}-${Date.now()}-${file.name}`;

        // Upload to Supabase Storage
        const { data, error } = await supabase.storage
          .from('documents')
          .upload(fileName, file);

        if (error) throw error;

        // Update progress
        setUploadProgress(prev => ({ ...prev, [file.id!]: 50 }));

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('documents')
          .getPublicUrl(fileName);

        // Save document record to database
        const { error: dbError } = await supabase
          .from('claim_documents')
          .insert({
            claim_id: claimId,
            file_name: file.name,
            category: category,
            file_size: file.size,
            file_url: publicUrl,
            uploaded_by: getUserUUID(user.id)
          });

        if (dbError) throw dbError;

        // Complete progress
        setUploadProgress(prev => ({ ...prev, [file.id!]: 100 }));
      }

      // Add activity log
      await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: getUserUUID(user.id),
          activity_type: 'document_upload',
          title: 'Documents uploaded',
          description: `${files.length} document(s) uploaded in category: ${DOCUMENT_CATEGORIES.find(c => c.value === category)?.label}`
        });

      onUploadComplete();
      onClose();

    } catch (error) {
      console.error('Upload error:', error);
      setErrors(['Failed to upload files. Please try again.']);
    } finally {
      setUploading(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Upload Documents
            </CardTitle>
            <CardDescription>
              Add documents to this claim. Max file size: 10MB
            </CardDescription>
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Category Selection */}
        <div>
          <Label htmlFor="category">Document Category</Label>
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="mt-1">
              <SelectValue placeholder="Select document category" />
            </SelectTrigger>
            <SelectContent>
              {DOCUMENT_CATEGORIES.map(cat => (
                <SelectItem key={cat.value} value={cat.value}>
                  {cat.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Upload Area */}
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
            dragActive 
              ? 'border-blue-500 bg-blue-50' 
              : 'border-gray-300 hover:border-gray-400'
          }`}
          onDragEnter={handleDrag}
          onDragLeave={handleDrag}
          onDragOver={handleDrag}
          onDrop={handleDrop}
        >
          <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            Drop files here or click to browse
          </h3>
          <p className="text-sm text-gray-600 mb-4">
            Supports: PDF, Word, Excel, Images, Text files
          </p>
          <Input
            type="file"
            multiple
            accept={ALLOWED_FILE_TYPES.join(',')}
            onChange={(e) => {
              if (e.target.files) {
                handleFiles(Array.from(e.target.files));
              }
            }}
            className="hidden"
            id="file-upload"
          />
          <Label 
            htmlFor="file-upload"
            className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 border border-input bg-background hover:bg-accent hover:text-accent-foreground h-10 px-4 py-2 cursor-pointer"
          >
            Select Files
          </Label>
        </div>

        {/* Error Messages */}
        {errors.length > 0 && (
          <div className="space-y-2">
            {errors.map((error, index) => (
              <div key={index} className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                {error}
              </div>
            ))}
          </div>
        )}

        {/* File List */}
        {files.length > 0 && (
          <div className="space-y-3">
            <h4 className="font-medium">Selected Files ({files.length})</h4>
            <div className="space-y-2 max-h-60 overflow-y-auto">
              {files.map((file) => (
                <div key={file.id} className="flex items-center gap-3 p-3 border rounded-lg">
                  {getFileIcon(file.type)}
                  <div className="flex-1 min-w-0">
                    <p className="text-sm font-medium truncate">{file.name}</p>
                    <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                    {file.id && uploadProgress[file.id] !== undefined && (
                      <div className="mt-1">
                        <div className="h-1 bg-gray-200 rounded-full overflow-hidden">
                          <div 
                            className="h-full bg-blue-500 transition-all duration-300"
                            style={{ width: `${uploadProgress[file.id]}%` }}
                          />
                        </div>
                      </div>
                    )}
                  </div>
                  {file.id && uploadProgress[file.id] === 100 ? (
                    <CheckCircle className="h-5 w-5 text-green-500" />
                  ) : (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => removeFile(file.id!)}
                      disabled={uploading}
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Upload Button */}
        <div className="flex gap-2 pt-4">
          <Button variant="outline" onClick={onClose} disabled={uploading}>
            Cancel
          </Button>
          <Button 
            onClick={uploadFiles} 
            disabled={uploading || files.length === 0 || !category}
            className="flex-1"
          >
            {uploading ? 'Uploading...' : `Upload ${files.length} File${files.length !== 1 ? 's' : ''}`}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 