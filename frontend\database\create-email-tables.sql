-- Create missing email tables for <PERSON><PERSON><PERSON>unter<PERSON>ro

-- Email threads (conversation grouping)
CREATE TABLE IF NOT EXISTS email_threads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
  subject VARCHAR(500) NOT NULL,
  participants JSONB DEFAULT '[]'::jsonb,
  status VARCHAR(50) DEFAULT 'active',
  last_email_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual emails sent/received
CREATE TABLE IF NOT EXISTS emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  thread_id UUID REFERENCES email_threads(id) ON DELETE CASCADE,
  claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
  
  -- Email details
  message_id VARCHAR(255) UNIQUE,
  subject VA<PERSON>HA<PERSON>(500) NOT NULL,
  body_html TEXT,
  body_text TEXT,
  
  -- Sender/recipient info
  from_email VARCHAR(255) NOT NULL,
  from_name VARCHAR(255),
  to_emails JSONB NOT NULL DEFAULT '[]'::jsonb,
  cc_emails JSONB DEFAULT '[]'::jsonb,
  bcc_emails JSONB DEFAULT '[]'::jsonb,
  reply_to VARCHAR(255),
  
  -- Email metadata
  direction VARCHAR(20) NOT NULL DEFAULT 'outbound',
  email_type VARCHAR(50) DEFAULT 'general',
  template_id UUID,
  
  -- Tracking and status
  status VARCHAR(50) DEFAULT 'draft',
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  replied_at TIMESTAMP WITH TIME ZONE,
  
  -- Engagement tracking
  open_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  tracking_pixel_url VARCHAR(500),
  
  -- Follow-up scheduling
  follow_up_date TIMESTAMP WITH TIME ZONE,
  follow_up_completed BOOLEAN DEFAULT false,
  
  -- Attachments and metadata
  attachments JSONB DEFAULT '[]'::jsonb,
  headers JSONB DEFAULT '{}'::jsonb,
  error_message TEXT,
  provider_response JSONB,
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_by UUID REFERENCES users(id),
  
  -- Constraints
  CONSTRAINT emails_direction_check CHECK (direction IN ('outbound', 'inbound')),
  CONSTRAINT emails_status_check CHECK (status IN ('draft', 'queued', 'sent', 'delivered', 'failed', 'bounced', 'rejected'))
);

-- Email templates (if not exists)
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  subject VARCHAR(500) NOT NULL,
  body_html TEXT,
  body_text TEXT,
  category VARCHAR(100) DEFAULT 'general',
  is_active BOOLEAN DEFAULT true,
  variables JSONB DEFAULT '[]'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- Email events for tracking
CREATE TABLE IF NOT EXISTS email_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email_id UUID NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
  event_type VARCHAR(50) NOT NULL,
  event_data JSONB DEFAULT '{}'::jsonb,
  ip_address INET,
  user_agent TEXT,
  location JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_emails_claim_id ON emails(claim_id);
CREATE INDEX IF NOT EXISTS idx_emails_thread_id ON emails(thread_id);
CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status);
CREATE INDEX IF NOT EXISTS idx_emails_sent_at ON emails(sent_at);
CREATE INDEX IF NOT EXISTS idx_email_threads_claim_id ON email_threads(claim_id);
CREATE INDEX IF NOT EXISTS idx_email_events_email_id ON email_events(email_id);

-- Enable Row Level Security
ALTER TABLE email_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE emails ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_events ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can access email threads" ON email_threads FOR ALL USING (true);
CREATE POLICY "Users can access emails" ON emails FOR ALL USING (true);
CREATE POLICY "Users can access email templates" ON email_templates FOR ALL USING (true);
CREATE POLICY "Users can access email events" ON email_events FOR ALL USING (true);

-- Insert some sample email templates
INSERT INTO email_templates (name, subject, body_html, body_text, category, variables) VALUES
('Initial Contact', 'Regarding Your Unclaimed Property - {{claim_amount}}', 
 '<p>Dear {{owner_name}},</p><p>We have located unclaimed property in your name worth {{claim_amount}}. Please contact us to begin the recovery process.</p><p>Best regards,<br>AssetHunterPro Team</p>', 
 'Dear {{owner_name}}, We have located unclaimed property in your name worth {{claim_amount}}. Please contact us to begin the recovery process. Best regards, AssetHunterPro Team', 
 'initial', 
 '["owner_name", "claim_amount", "state"]'::jsonb),

('Follow Up', 'Follow Up: Your Unclaimed Property Case {{claim_number}}', 
 '<p>Dear {{owner_name}},</p><p>This is a follow-up regarding your unclaimed property case {{claim_number}}. We would like to assist you in recovering {{claim_amount}}.</p><p>Please respond at your earliest convenience.</p><p>Best regards,<br>AssetHunterPro Team</p>', 
 'Dear {{owner_name}}, This is a follow-up regarding your unclaimed property case {{claim_number}}. We would like to assist you in recovering {{claim_amount}}. Please respond at your earliest convenience. Best regards, AssetHunterPro Team', 
 'follow_up', 
 '["owner_name", "claim_number", "claim_amount"]'::jsonb),

('Document Request', 'Documents Required for Claim {{claim_number}}', 
 '<p>Dear {{owner_name}},</p><p>To process your unclaimed property claim {{claim_number}}, we need the following documents:</p><ul><li>Valid ID</li><li>Proof of address</li></ul><p>Please send these at your earliest convenience.</p><p>Best regards,<br>AssetHunterPro Team</p>', 
 'Dear {{owner_name}}, To process your unclaimed property claim {{claim_number}}, we need the following documents: Valid ID, Proof of address. Please send these at your earliest convenience. Best regards, AssetHunterPro Team', 
 'documents', 
 '["owner_name", "claim_number"]'::jsonb)

ON CONFLICT (id) DO NOTHING; 