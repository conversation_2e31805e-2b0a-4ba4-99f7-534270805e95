import React, { useState } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from '@/components/ui/dialog';
import { usePricingLogic } from '@/hooks/usePricingLogic';
import { PRICING_PLANS } from '@/data/pricing-plans';
import { 
  Crown,
  TrendingUp,
  AlertTriangle,
  Clock,
  Users,
  FileText,
  HardDrive,
  Zap,
  Download,
  CreditCard,
  Star,
  CheckCircle,
  XCircle
} from 'lucide-react';

interface PricingWidgetProps {
  compact?: boolean;
  showUsageOnly?: boolean;
}

export const PricingWidget: React.FC<PricingWidgetProps> = ({ 
  compact = false, 
  showUsageOnly = false 
}) => {
  const {
    currentPlan,
    subscription,
    usage,
    usageAlerts,
    getRemainingLimit,
    calculateOverageCharges,
    upgradeRecommendation,
    isTrialExpiring,
    daysUntilTrialExpires
  } = usePricingLogic();

  const [showPricingModal, setShowPricingModal] = useState(false);
  const overageCharges = calculateOverageCharges();

  if (!currentPlan || !subscription) {
    return null;
  }

  const getUsageIcon = (type: string) => {
    switch (type) {
      case 'claims_this_month': return <FileText className="h-4 w-4" />;
      case 'team_members': return <Users className="h-4 w-4" />;
      case 'storage_used_gb': return <HardDrive className="h-4 w-4" />;
      case 'api_calls_this_month': return <Zap className="h-4 w-4" />;
      case 'exports_this_month': return <Download className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getUsageColor = (percentage: number) => {
    if (percentage >= 100) return 'bg-red-500';
    if (percentage >= 80) return 'bg-yellow-500';
    if (percentage >= 60) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const formatUsageValue = (type: string, value: number) => {
    switch (type) {
      case 'storage_used_gb':
        return `${value.toFixed(1)} GB`;
      case 'api_calls_this_month':
        return value.toLocaleString();
      default:
        return value.toString();
    }
  };

  const getUsageLabel = (type: string) => {
    switch (type) {
      case 'claims_this_month': return 'Claims This Month';
      case 'active_claims': return 'Active Claims';
      case 'team_members': return 'Team Members';
      case 'storage_used_gb': return 'Storage Used';
      case 'api_calls_this_month': return 'API Calls This Month';
      case 'exports_this_month': return 'Exports This Month';
      default: return type;
    }
  };

  if (compact) {
    return (
      <Card className="border-0 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Crown className="h-4 w-4 text-yellow-500" />
              <span className="font-medium">{currentPlan.name}</span>
              {subscription.status === 'trial' && (
                <Badge variant="outline" className="text-xs">
                  Trial: {daysUntilTrialExpires}d left
                </Badge>
              )}
            </div>
            {upgradeRecommendation && (
              <Button size="sm" variant="outline" onClick={() => setShowPricingModal(true)}>
                <TrendingUp className="h-3 w-3 mr-1" />
                Upgrade
              </Button>
            )}
          </div>
          
          {usageAlerts.length > 0 && (
            <Alert className="mt-3">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="text-xs">
                {usageAlerts.length} usage limit{usageAlerts.length > 1 ? 's' : ''} need attention
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    );
  }

  if (showUsageOnly) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-lg flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Usage Overview
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {Object.entries(usage).map(([key, value]) => {
            const remaining = getRemainingLimit(key as keyof typeof usage);
            const limit = currentPlan.limits.find(l => 
              l.type === key.replace('_this_month', '_per_month') || l.type === key
            );
            
            if (!limit || remaining === 'unlimited') return null;

            const percentage = (value / (limit.value as number)) * 100;
            
            return (
              <div key={key} className="space-y-2">
                <div className="flex items-center justify-between text-sm">
                  <div className="flex items-center gap-2">
                    {getUsageIcon(key)}
                    <span>{getUsageLabel(key)}</span>
                  </div>
                  <span className="font-medium">
                    {formatUsageValue(key, value)} / {limit.value as number}
                  </span>
                </div>
                <Progress 
                  value={Math.min(percentage, 100)} 
                  className={`h-2 ${getUsageColor(percentage)}`}
                />
                {percentage >= 80 && (
                  <p className="text-xs text-yellow-600">
                    {percentage >= 100 ? 'Limit exceeded' : 'Approaching limit'}
                  </p>
                )}
              </div>
            );
          })}
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="text-xl flex items-center gap-2">
              <Crown className="h-6 w-6 text-yellow-500" />
              {currentPlan.name} Plan
            </CardTitle>
            <div className="flex items-center gap-2">
              {subscription.status === 'trial' && (
                <Badge variant="outline" className={isTrialExpiring ? 'border-orange-500 text-orange-700' : ''}>
                  <Clock className="h-3 w-3 mr-1" />
                  Trial: {daysUntilTrialExpires} days left
                </Badge>
              )}
              <Badge variant="secondary">
                ${currentPlan.pricing.monthly}/month
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Trial Expiration Warning */}
          {isTrialExpiring && (
            <Alert className="border-orange-200 bg-orange-50">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <AlertDescription className="text-orange-800">
                Your trial expires in {daysUntilTrialExpires} days. 
                <Button 
                  variant="link" 
                  className="p-0 ml-1 text-orange-800"
                  onClick={() => setShowPricingModal(true)}
                >
                  Upgrade now to continue using AssetHunterPro.
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Usage Alerts */}
          {usageAlerts.length > 0 && (
            <Alert className="border-red-200 bg-red-50">
              <AlertTriangle className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                {usageAlerts.filter(a => a.alert_type === 'limit_exceeded').length > 0 
                  ? 'You have exceeded usage limits. '
                  : 'You are approaching usage limits. '
                }
                <Button 
                  variant="link" 
                  className="p-0 ml-1 text-red-800"
                  onClick={() => setShowPricingModal(true)}
                >
                  Consider upgrading your plan.
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {/* Usage Breakdown */}
          <div className="space-y-4">
            <h4 className="font-semibold text-gray-900">Usage This Period</h4>
            
            {Object.entries(usage).map(([key, value]) => {
              const remaining = getRemainingLimit(key as keyof typeof usage);
              const limit = currentPlan.limits.find(l => 
                l.type === key.replace('_this_month', '_per_month') || l.type === key
              );
              
              if (!limit) return null;

              if (remaining === 'unlimited') {
                return (
                  <div key={key} className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center gap-2">
                      {getUsageIcon(key)}
                      <span className="font-medium">{getUsageLabel(key)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">{formatUsageValue(key, value)}</span>
                      <Badge variant="secondary">Unlimited</Badge>
                    </div>
                  </div>
                );
              }

              const percentage = (value / (limit.value as number)) * 100;
              const isOverLimit = percentage >= 100;
              const isNearLimit = percentage >= 80;
              
              return (
                <div key={key} className={`p-3 rounded-lg border ${
                  isOverLimit ? 'border-red-200 bg-red-50' :
                  isNearLimit ? 'border-yellow-200 bg-yellow-50' :
                  'border-gray-200 bg-gray-50'
                }`}>
                  <div className="flex items-center justify-between mb-2">
                    <div className="flex items-center gap-2">
                      {getUsageIcon(key)}
                      <span className="font-medium">{getUsageLabel(key)}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="font-semibold">
                        {formatUsageValue(key, value)} / {limit.value as number}
                      </span>
                      {isOverLimit && <XCircle className="h-4 w-4 text-red-500" />}
                      {!isOverLimit && !isNearLimit && <CheckCircle className="h-4 w-4 text-green-500" />}
                    </div>
                  </div>
                  <Progress 
                    value={Math.min(percentage, 100)} 
                    className={`h-2`}
                  />
                  <div className="flex justify-between text-xs mt-1">
                    <span className={isOverLimit ? 'text-red-600' : isNearLimit ? 'text-yellow-600' : 'text-gray-600'}>
                      {Math.round(percentage)}% used
                    </span>
                    <span className="text-gray-500">
                      {remaining as number} remaining
                    </span>
                  </div>
                </div>
              );
            })}
          </div>

          {/* Overage Charges */}
          {overageCharges > 0 && (
            <Alert className="border-red-200 bg-red-50">
              <CreditCard className="h-4 w-4 text-red-600" />
              <AlertDescription className="text-red-800">
                <strong>Overage charges this month: ${overageCharges.toFixed(2)}</strong>
                <br />
                You've exceeded your plan limits. Consider upgrading to avoid overage fees.
              </AlertDescription>
            </Alert>
          )}

          {/* Upgrade Recommendation */}
          {upgradeRecommendation && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Star className="h-5 w-5 text-blue-600" />
                <h5 className="font-semibold text-blue-900">Upgrade Recommended</h5>
              </div>
              <p className="text-blue-800 text-sm mb-3">
                Based on your usage patterns, we recommend upgrading to the {' '}
                {PRICING_PLANS.find(p => p.id === upgradeRecommendation)?.name} plan 
                for better value and unlimited features.
              </p>
              <Button onClick={() => setShowPricingModal(true)}>
                View Upgrade Options
              </Button>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 pt-4 border-t">
            <Dialog open={showPricingModal} onOpenChange={setShowPricingModal}>
              <DialogTrigger asChild>
                <Button variant="outline">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Upgrade Plan
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl">
                <DialogHeader>
                  <DialogTitle>Choose Your AssetHunterPro Plan</DialogTitle>
                  <DialogDescription>
                    Select the plan that best fits your organization's needs
                  </DialogDescription>
                </DialogHeader>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6 py-4">
                  {PRICING_PLANS.map((plan) => (
                    <div
                      key={plan.id}
                      className={`relative p-6 rounded-xl border-2 ${
                        plan.id === subscription.plan_id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                    >
                      {plan.popular && (
                        <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                          <Badge className="bg-gradient-to-r from-purple-600 to-pink-600 text-white">
                            <Star className="h-3 w-3 mr-1" />
                            Most Popular
                          </Badge>
                        </div>
                      )}
                      
                      <div className="text-center">
                        <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
                        <div className="mb-4">
                          <span className="text-3xl font-bold text-gray-900">${plan.pricing.monthly}</span>
                          <span className="text-gray-600">/month</span>
                        </div>
                        <p className="text-gray-600 mb-6">{plan.description}</p>
                        
                        <ul className="space-y-2 text-left mb-6">
                          {plan.limits.slice(0, 4).map((limit, index) => (
                            <li key={index} className="flex items-center text-sm">
                              <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                              {limit.value === 'unlimited' ? 'Unlimited' : limit.value} {' '}
                              {limit.type.replace(/_/g, ' ')}
                            </li>
                          ))}
                        </ul>
                        
                        <Button 
                          className="w-full" 
                          variant={plan.id === subscription.plan_id ? "secondary" : "default"}
                          disabled={plan.id === subscription.plan_id}
                        >
                          {plan.id === subscription.plan_id ? 'Current Plan' : `Upgrade to ${plan.name}`}
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </DialogContent>
            </Dialog>
            
            <Button variant="ghost">
              <CreditCard className="h-4 w-4 mr-2" />
              Billing
            </Button>
          </div>
        </CardContent>
      </Card>
    </>
  );
}; 