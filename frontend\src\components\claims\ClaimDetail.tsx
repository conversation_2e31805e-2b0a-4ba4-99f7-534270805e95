import { useState } from 'react'
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { 
  Phone, 
  Mail, 
  FileText, 
  Calendar,
  MapPin,
  User,
  Building,
  Clock,
  Plus,
  MessageSquare,
  Upload
} from 'lucide-react'

// Types
interface Activity {
  id: string
  type: 'call' | 'email' | 'sms' | 'note' | 'document_upload' | 'status_change'
  summary: string
  detail: string
  outcome: string
  nextFollowUp?: string
  createdAt: string
  createdBy: string
}

interface ClaimDetail {
  id: string
  claimNumber: string
  state: string
  amountReported: number
  amountNet: number
  status: string
  claimantName: string
  claimantType: 'individual' | 'business'
  assignedAgent: string
  createdAt: string
  lastActivity: string
  priority: 'high' | 'medium' | 'low'
  
  // Contact Information
  phone: string
  email: string
  address: {
    line1: string
    line2?: string
    city: string
    state: string
    zip: string
  }
  
  // Activities
  activities: Activity[]
}

// Mock data
const mockClaimDetail: ClaimDetail = {
  id: '1',
  claimNumber: 'CLM-001',
  state: 'CA',
  amountReported: 15000,
  amountNet: 13500,
  status: 'contacted',
  claimantName: 'Robert Anderson',
  claimantType: 'individual',
  assignedAgent: 'John Doe',
  createdAt: '2024-01-10',
  lastActivity: '2024-01-15',
  priority: 'high',
  phone: '******-1001',
  email: '<EMAIL>',
  address: {
    line1: '123 Main St',
    city: 'Los Angeles',
    state: 'CA',
    zip: '90210'
  },
  activities: [
    {
      id: '1',
      type: 'note',
      summary: 'Initial research completed',
      detail: 'Found claimant contact information through public records. Phone number verified through skip trace.',
      outcome: 'Contact info obtained',
      createdAt: '2024-01-10T10:00:00Z',
      createdBy: 'John Doe'
    },
    {
      id: '2',
      type: 'call',
      summary: 'First contact attempt',
      detail: 'Called primary phone number. No answer, left detailed voicemail explaining unclaimed property.',
      outcome: 'Voicemail left',
      nextFollowUp: '2024-01-12',
      createdAt: '2024-01-11T14:30:00Z',
      createdBy: 'John Doe'
    },
    {
      id: '3',
      type: 'call',
      summary: 'Successful contact',
      detail: 'Spoke with Robert Anderson. Explained the unclaimed property process. He was interested and agreed to receive engagement letter.',
      outcome: 'Interested - sending contract',
      createdAt: '2024-01-15T09:15:00Z',
      createdBy: 'John Doe'
    }
  ]
}

const activityTypeColors = {
  call: 'info',
  email: 'warning',
  sms: 'success',
  note: 'secondary',
  document_upload: 'default',
  status_change: 'destructive'
} as const

export function ClaimDetail({ }: { claimId: string }) {
  const [claim, setClaim] = useState<ClaimDetail>(mockClaimDetail)
  const [newActivity, setNewActivity] = useState({
    type: 'call' as Activity['type'],
    summary: '',
    detail: '',
    outcome: '',
    nextFollowUp: ''
  })
  const [showAddActivity, setShowAddActivity] = useState(false)

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  const handleAddActivity = () => {
    if (!newActivity.summary || !newActivity.detail) return

    const activity: Activity = {
      id: Date.now().toString(),
      type: newActivity.type,
      summary: newActivity.summary,
      detail: newActivity.detail,
      outcome: newActivity.outcome,
      nextFollowUp: newActivity.nextFollowUp || undefined,
      createdAt: new Date().toISOString(),
      createdBy: claim.assignedAgent
    }

    setClaim(prev => ({
      ...prev,
      activities: [activity, ...prev.activities],
      lastActivity: new Date().toISOString().split('T')[0]
    }))

    setNewActivity({
      type: 'call',
      summary: '',
      detail: '',
      outcome: '',
      nextFollowUp: ''
    })
    setShowAddActivity(false)
  }

  const handleQuickAction = (action: string) => {
    let summary = ''
    let detail = ''
    let type: Activity['type'] = 'call'

    switch (action) {
      case 'call':
        summary = 'Phone call made'
        detail = 'Called claimant to discuss claim status and next steps.'
        type = 'call'
        break
      case 'email':
        summary = 'Email sent'
        detail = 'Sent follow-up email with claim information and next steps.'
        type = 'email'
        break
      case 'contract':
        summary = 'Contract sent'
        detail = 'Engagement letter sent via DocuSign for electronic signature.'
        type = 'document_upload'
        break
    }

    setNewActivity({
      type,
      summary,
      detail,
      outcome: '',
      nextFollowUp: ''
    })
    setShowAddActivity(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Claim Details</h2>
          <p className="text-gray-600">
            View and manage claim information and activities
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" onClick={() => handleQuickAction('call')}>
            <Phone className="h-4 w-4 mr-2" />
            Call
          </Button>
          <Button variant="outline" onClick={() => handleQuickAction('email')}>
            <Mail className="h-4 w-4 mr-2" />
            Email
          </Button>
          <Button onClick={() => handleQuickAction('contract')}>
            <FileText className="h-4 w-4 mr-2" />
            Send Contract
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Claim Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card>
            <CardHeader>
              <CardTitle>Claim Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  <div className="mt-1">
                    <Badge variant="info">{claim.status.replace('_', ' ')}</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Priority</label>
                  <div className="mt-1">
                    <Badge variant="destructive">{claim.priority}</Badge>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Amount Reported</label>
                  <div className="mt-1 font-medium">{formatCurrency(claim.amountReported)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Net Amount</label>
                  <div className="mt-1 font-medium">{formatCurrency(claim.amountNet)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Created</label>
                  <div className="mt-1">{formatDate(claim.createdAt)}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Last Activity</label>
                  <div className="mt-1">{formatDate(claim.lastActivity)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle>Contact Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Name</label>
                  <div className="mt-1 flex items-center space-x-2">
                    {claim.claimantType === 'individual' ? (
                      <User className="h-4 w-4 text-gray-600" />
                    ) : (
                      <Building className="h-4 w-4 text-gray-600" />
                    )}
                    <span className="font-medium">{claim.claimantName}</span>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Type</label>
                  <div className="mt-1">{claim.claimantType}</div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Phone</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-600" />
                    <span>{claim.phone}</span>
                    <Button variant="ghost" size="sm" onClick={() => handleQuickAction('call')}>
                      Call
                    </Button>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Email</label>
                  <div className="mt-1 flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-600" />
                    <span>{claim.email}</span>
                    <Button variant="ghost" size="sm" onClick={() => handleQuickAction('email')}>
                      Email
                    </Button>
                  </div>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Address</label>
                <div className="mt-1 flex items-start space-x-2">
                  <MapPin className="h-4 w-4 text-gray-600 mt-0.5" />
                  <div>
                    <div>{claim.address.line1}</div>
                    {claim.address.line2 && <div>{claim.address.line2}</div>}
                    <div>{claim.address.city}, {claim.address.state} {claim.address.zip}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Activities Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <Button 
                className="w-full justify-start" 
                variant="outline"
                onClick={() => setShowAddActivity(true)}
              >
                <Plus className="h-4 w-4 mr-2" />
                Add Activity
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Upload className="h-4 w-4 mr-2" />
                Upload Document
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Follow-up
              </Button>
            </CardContent>
          </Card>

          {/* Add Activity Form */}
          {showAddActivity && (
            <Card>
              <CardHeader>
                <CardTitle>Add Activity</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium">Type</label>
                  <Select 
                    value={newActivity.type} 
                    onValueChange={(value) => setNewActivity(prev => ({ ...prev, type: value as Activity['type'] }))}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="call">Phone Call</SelectItem>
                      <SelectItem value="email">Email</SelectItem>
                      <SelectItem value="sms">SMS</SelectItem>
                      <SelectItem value="note">Note</SelectItem>
                      <SelectItem value="document_upload">Document Upload</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label className="text-sm font-medium">Summary</label>
                  <Input
                    placeholder="Brief summary of activity"
                    value={newActivity.summary}
                    onChange={(e) => setNewActivity(prev => ({ ...prev, summary: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Details</label>
                  <Textarea
                    placeholder="Detailed description of what happened"
                    value={newActivity.detail}
                    onChange={(e) => setNewActivity(prev => ({ ...prev, detail: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Outcome</label>
                  <Input
                    placeholder="Result or outcome"
                    value={newActivity.outcome}
                    onChange={(e) => setNewActivity(prev => ({ ...prev, outcome: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Next Follow-up (optional)</label>
                  <Input
                    type="date"
                    value={newActivity.nextFollowUp}
                    onChange={(e) => setNewActivity(prev => ({ ...prev, nextFollowUp: e.target.value }))}
                  />
                </div>
                <div className="flex space-x-2">
                  <Button onClick={handleAddActivity}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Activity
                  </Button>
                  <Button variant="outline" onClick={() => setShowAddActivity(false)}>
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Activities Timeline */}
      <Card>
        <CardHeader>
          <CardTitle>Activity Timeline</CardTitle>
          <CardDescription>
            Complete history of all interactions and activities for this claim
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {claim.activities.map((activity, index) => (
              <div key={activity.id} className="flex space-x-4">
                <div className="flex flex-col items-center">
                  <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gray-100">
                    {activity.type === 'call' && <Phone className="h-4 w-4" />}
                    {activity.type === 'email' && <Mail className="h-4 w-4" />}
                    {activity.type === 'sms' && <MessageSquare className="h-4 w-4" />}
                    {activity.type === 'note' && <FileText className="h-4 w-4" />}
                    {activity.type === 'document_upload' && <Upload className="h-4 w-4" />}
                    {activity.type === 'status_change' && <Clock className="h-4 w-4" />}
                  </div>
                  {index < claim.activities.length - 1 && (
                    <div className="h-8 w-px bg-border mt-2" />
                  )}
                </div>
                <div className="flex-1 space-y-2">
                  <div className="flex items-center space-x-2">
                    <Badge variant={activityTypeColors[activity.type]} className="text-xs">
                      {activity.type.replace('_', ' ')}
                    </Badge>
                    <span className="text-sm text-gray-600">
                      {formatDateTime(activity.createdAt)} by {activity.createdBy}
                    </span>
                  </div>
                  <div>
                    <h4 className="font-medium">{activity.summary}</h4>
                    <p className="text-sm text-gray-600 mt-1">{activity.detail}</p>
                    {activity.outcome && (
                      <p className="text-sm font-medium mt-2">Outcome: {activity.outcome}</p>
                    )}
                    {activity.nextFollowUp && (
                      <p className="text-sm text-gray-600 mt-1">
                        Next follow-up: {formatDate(activity.nextFollowUp)}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 