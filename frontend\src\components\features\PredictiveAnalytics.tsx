import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  Target,
  AlertTriangle,
  Zap,
  BarChart3,
  Eye,
  Clock,
  DollarSign,
  Lightbulb,
  Activity,
  Shield,
  ArrowUp,
  ArrowDown,
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Gauge,
  Sparkles,
  Database,
  Cpu,
  Network,
  Bot
} from 'lucide-react';
import { 
  PredictiveAnalytics, 
  RecoveryPredictionEngine,
  AIMLPlatform 
} from '@/types/ai-ml-platform';

interface PredictiveAnalyticsProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const PredictiveAnalyticsComponent: React.FC<PredictiveAnalyticsProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedTimeframe, setSelectedTimeframe] = useState('30d');
  
  const [analyticsMetrics, setAnalyticsMetrics] = useState({
    prediction_accuracy: 94.7,
    models_deployed: 12,
    insights_generated: 2847,
    automation_rate: 78.5,
    cost_savings: 1240000,
    success_rate_improvement: 23.8
  });

  const recoveryPredictions = [
    {
      case_id: 'RC-2024-1847',
      client_name: 'TechFlow Industries',
      asset_value: 850000,
      success_probability: 92.3,
      predicted_timeline: 45,
      risk_level: 'Low',
      recommended_strategy: 'Standard Recovery',
      confidence: 'High',
      factors: ['Strong payment history', 'Valuable assets', 'Cooperative debtor'],
      predicted_amount: 782000
    },
    {
      case_id: 'RC-2024-1923',
      client_name: 'Global Manufacturing Co',
      asset_value: 1200000,
      success_probability: 76.8,
      predicted_timeline: 72,
      risk_level: 'Medium',
      recommended_strategy: 'Negotiated Settlement',
      confidence: 'Medium',
      factors: ['Complex asset structure', 'Multiple stakeholders', 'Market volatility'],
      predicted_amount: 920000
    },
    {
      case_id: 'RC-2024-2001',
      client_name: 'Retail Solutions LLC',
      asset_value: 450000,
      success_probability: 88.5,
      predicted_timeline: 28,
      risk_level: 'Low',
      recommended_strategy: 'Accelerated Recovery',
      confidence: 'High',
      factors: ['Clear documentation', 'Liquid assets', 'Willing cooperation'],
      predicted_amount: 398000
    }
  ];

  const aiInsights = [
    {
      type: 'Market Trend',
      insight: 'Commercial real estate recovery rates have increased 15% this quarter due to improved market conditions',
      confidence: 87,
      impact: 'High',
      recommendation: 'Prioritize real estate-backed cases for faster processing',
      actionable: true,
      category: 'opportunity'
    },
    {
      type: 'Risk Alert',
      insight: 'Manufacturing sector showing 23% increase in default risk indicators',
      confidence: 94,
      impact: 'Medium',
      recommendation: 'Implement enhanced due diligence for manufacturing clients',
      actionable: true,
      category: 'risk'
    },
    {
      type: 'Process Optimization',
      insight: 'Document verification automation could reduce processing time by 40%',
      confidence: 91,
      impact: 'High',
      recommendation: 'Deploy AI document processing for routine verifications',
      actionable: true,
      category: 'efficiency'
    },
    {
      type: 'Behavioral Pattern',
      insight: 'Debtors contacted within 48 hours show 35% higher cooperation rates',
      confidence: 96,
      impact: 'Medium',
      recommendation: 'Implement rapid contact protocol for new cases',
      actionable: true,
      category: 'behavioral'
    }
  ];

  const mlModels = [
    {
      model_name: 'Recovery Success Predictor',
      model_type: 'Gradient Boosting',
      accuracy: 94.7,
      deployment_status: 'Production',
      last_updated: '2024-01-15',
      predictions_made: 15678,
      performance_trend: 'improving',
      features_count: 47,
      training_data_size: 125000
    },
    {
      model_name: 'Timeline Estimator',
      model_type: 'Neural Network',
      accuracy: 89.2,
      deployment_status: 'Production',
      last_updated: '2024-01-12',
      predictions_made: 12456,
      performance_trend: 'stable',
      features_count: 35,
      training_data_size: 89000
    },
    {
      model_name: 'Risk Assessment Engine',
      model_type: 'Random Forest',
      accuracy: 91.8,
      deployment_status: 'Production',
      last_updated: '2024-01-18',
      predictions_made: 8934,
      performance_trend: 'improving',
      features_count: 52,
      training_data_size: 156000
    },
    {
      model_name: 'Value Estimation Model',
      model_type: 'Ensemble',
      accuracy: 87.4,
      deployment_status: 'Staging',
      last_updated: '2024-01-20',
      predictions_made: 3421,
      performance_trend: 'testing',
      features_count: 28,
      training_data_size: 67000
    }
  ];

  const behavioralAnalytics = [
    {
      behavior_type: 'Payment Patterns',
      insight: 'Debtors with consistent partial payments show 67% higher full recovery rates',
      data_points: 23456,
      confidence: 92,
      trend: 'increasing',
      actionable_strategy: 'Encourage partial payment plans as pathway to full recovery'
    },
    {
      behavior_type: 'Communication Response',
      insight: 'Email-first contact increases cooperation by 28% vs phone-first',
      data_points: 18923,
      confidence: 89,
      trend: 'stable',
      actionable_strategy: 'Implement email-first communication protocol'
    },
    {
      behavior_type: 'Seasonal Patterns',
      insight: 'Q4 recovery rates 15% higher due to year-end financial planning',
      data_points: 45678,
      confidence: 95,
      trend: 'cyclical',
      actionable_strategy: 'Increase recovery efforts in Q4 for maximum impact'
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Prediction Accuracy</p>
                <p className="text-2xl font-bold text-blue-600">{analyticsMetrics.prediction_accuracy}%</p>
              </div>
              <Target className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +2.3% vs last month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active ML Models</p>
                <p className="text-2xl font-bold text-purple-600">{analyticsMetrics.models_deployed}</p>
              </div>
              <Brain className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              3 in production, 1 in staging
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">AI Insights Generated</p>
                <p className="text-2xl font-bold text-green-600">{analyticsMetrics.insights_generated.toLocaleString()}</p>
              </div>
              <Lightbulb className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              847 actionable this month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-orange-600">{analyticsMetrics.automation_rate}%</p>
              </div>
              <Bot className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +12% vs last quarter
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cost Savings</p>
                <p className="text-2xl font-bold text-green-600">
                  ${(analyticsMetrics.cost_savings / 1000000).toFixed(1)}M
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Through AI optimization
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Success Rate Boost</p>
                <p className="text-2xl font-bold text-indigo-600">+{analyticsMetrics.success_rate_improvement}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              AI-driven improvements
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Top AI Insights
            </CardTitle>
            <CardDescription>
              Latest AI-generated insights and recommendations
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {aiInsights.slice(0, 3).map((insight, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant={insight.category === 'opportunity' ? 'default' : 
                                   insight.category === 'risk' ? 'destructive' : 'secondary'}>
                      {insight.type}
                    </Badge>
                    <div className="flex items-center gap-1">
                      <Gauge className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">{insight.confidence}% confidence</span>
                    </div>
                  </div>
                  <Badge variant="outline">{insight.impact} Impact</Badge>
                </div>
                <p className="text-sm text-gray-700">{insight.insight}</p>
                <div className="flex items-center justify-between text-sm">
                  <span className="text-green-600 font-medium">💡 {insight.recommendation}</span>
                  {insight.actionable && (
                    <Button size="sm" variant="outline">
                      Take Action
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              ML Model Performance
            </CardTitle>
            <CardDescription>
              Performance metrics for deployed machine learning models
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {mlModels.filter(model => model.deployment_status === 'Production').map((model, index) => (
              <div key={index} className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <div className="font-medium">{model.model_name}</div>
                    <div className="text-sm text-gray-600">{model.model_type}</div>
                  </div>
                  <div className="text-right">
                    <div className="font-semibold text-green-600">{model.accuracy}%</div>
                    <div className="text-xs text-gray-500">
                      {model.predictions_made.toLocaleString()} predictions
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-4 text-sm">
                  <div className="flex items-center gap-1">
                    {model.performance_trend === 'improving' && <ArrowUp className="h-3 w-3 text-green-600" />}
                    {model.performance_trend === 'stable' && <Activity className="h-3 w-3 text-blue-600" />}
                    <span className="text-gray-600 capitalize">{model.performance_trend}</span>
                  </div>
                  <div className="text-gray-500">
                    {model.features_count} features
                  </div>
                  <div className="text-gray-500">
                    Updated {model.last_updated}
                  </div>
                </div>
                <Progress value={model.accuracy} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderPredictions = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Recovery Predictions</h3>
        <div className="flex gap-2">
          {['7d', '30d', '90d'].map((timeframe) => (
            <Button
              key={timeframe}
              variant={selectedTimeframe === timeframe ? "default" : "outline"}
              size="sm"
              onClick={() => setSelectedTimeframe(timeframe)}
            >
              {timeframe}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {recoveryPredictions.map((prediction, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-semibold text-lg">{prediction.client_name}</div>
                      <div className="text-sm text-gray-600">{prediction.case_id}</div>
                    </div>
                    <Badge variant={
                      prediction.risk_level === 'Low' ? 'default' :
                      prediction.risk_level === 'Medium' ? 'secondary' : 'destructive'
                    }>
                      {prediction.risk_level} Risk
                    </Badge>
                  </div>
                  <div className="text-sm text-gray-600">
                    <div>Asset Value: <span className="font-medium">${prediction.asset_value.toLocaleString()}</span></div>
                    <div>Strategy: <span className="font-medium">{prediction.recommended_strategy}</span></div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between text-sm">
                      <span>Success Probability</span>
                      <span className="font-medium">{prediction.success_probability}%</span>
                    </div>
                    <Progress value={prediction.success_probability} className="h-2" />
                  </div>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Timeline</div>
                      <div className="font-medium flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {prediction.predicted_timeline} days
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Confidence</div>
                      <div className="font-medium flex items-center gap-1">
                        <Eye className="h-4 w-4" />
                        {prediction.confidence}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      ${prediction.predicted_amount.toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-600">Predicted Recovery</div>
                    <div className="text-xs text-gray-500">
                      {((prediction.predicted_amount / prediction.asset_value) * 100).toFixed(1)}% of asset value
                    </div>
                  </div>
                  <div className="space-y-1">
                    <div className="text-sm font-medium text-gray-700">Key Factors:</div>
                    {prediction.factors.map((factor, factorIndex) => (
                      <div key={factorIndex} className="text-xs text-gray-600 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        {factor}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderBehavioralAnalytics = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Behavioral Pattern Analysis
          </CardTitle>
          <CardDescription>
            AI-powered insights into debtor behavior patterns and optimal strategies
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {behavioralAnalytics.map((analytics, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-lg">{analytics.behavior_type}</h4>
                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span>{analytics.data_points.toLocaleString()} data points</span>
                    <span className="flex items-center gap-1">
                      <Gauge className="h-4 w-4" />
                      {analytics.confidence}% confidence
                    </span>
                    <div className="flex items-center gap-1">
                      {analytics.trend === 'increasing' && <ArrowUp className="h-4 w-4 text-green-600" />}
                      {analytics.trend === 'stable' && <Activity className="h-4 w-4 text-blue-600" />}
                      {analytics.trend === 'cyclical' && <Activity className="h-4 w-4 text-purple-600" />}
                      <span className="capitalize">{analytics.trend}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="bg-blue-50 rounded-lg p-3">
                <div className="text-sm font-medium text-blue-800 mb-1">Key Insight:</div>
                <div className="text-sm text-blue-700">{analytics.insight}</div>
              </div>
              
              <div className="bg-green-50 rounded-lg p-3">
                <div className="text-sm font-medium text-green-800 mb-1">Recommended Action:</div>
                <div className="text-sm text-green-700">{analytics.actionable_strategy}</div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Communication Effectiveness</CardTitle>
            <CardDescription>AI analysis of communication channel performance</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { channel: 'Email First Contact', effectiveness: 89, response_rate: 67, conversion: 34 },
              { channel: 'Phone Follow-up', effectiveness: 76, response_rate: 82, conversion: 28 },
              { channel: 'Text Message Reminder', effectiveness: 92, response_rate: 91, conversion: 19 },
              { channel: 'Letter Notice', effectiveness: 45, response_rate: 23, conversion: 41 }
            ].map((channel, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{channel.channel}</span>
                  <span className="text-sm font-medium">{channel.effectiveness}% effective</span>
                </div>
                <Progress value={channel.effectiveness} className="h-2" />
                <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                  <div>Response: {channel.response_rate}%</div>
                  <div>Conversion: {channel.conversion}%</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Timing Optimization</CardTitle>
            <CardDescription>Best times for contact based on AI analysis</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { time_slot: 'Morning (9-11 AM)', success_rate: 78, volume: 'High', recommendation: 'Optimal for initial contact' },
              { time_slot: 'Afternoon (2-4 PM)', success_rate: 65, volume: 'Medium', recommendation: 'Good for follow-ups' },
              { time_slot: 'Evening (6-8 PM)', success_rate: 82, volume: 'Low', recommendation: 'Best for personal calls' },
              { time_slot: 'Weekend (Sat 10-12)', success_rate: 71, volume: 'Low', recommendation: 'Emergency contacts only' }
            ].map((slot, index) => (
              <div key={index} className="border rounded-lg p-3 space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{slot.time_slot}</span>
                  <Badge variant="outline">{slot.volume} Volume</Badge>
                </div>
                <div className="flex items-center justify-between text-sm">
                  <span>Success Rate: <span className="font-medium">{slot.success_rate}%</span></span>
                </div>
                <Progress value={slot.success_rate} className="h-2" />
                <div className="text-xs text-gray-600">{slot.recommendation}</div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderModelManagement = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            ML Model Management
          </CardTitle>
          <CardDescription>
            Monitor and manage machine learning models in production
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {mlModels.map((model, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-3">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold">{model.model_name}</h4>
                  <div className="text-sm text-gray-600">{model.model_type}</div>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant={model.deployment_status === 'Production' ? 'default' : 'secondary'}>
                    {model.deployment_status}
                  </Badge>
                  <div className="text-right">
                    <div className="font-semibold text-green-600">{model.accuracy}%</div>
                    <div className="text-xs text-gray-500">Accuracy</div>
                  </div>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <div className="text-gray-600">Predictions Made</div>
                  <div className="font-medium">{model.predictions_made.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-gray-600">Features</div>
                  <div className="font-medium">{model.features_count}</div>
                </div>
                <div>
                  <div className="text-gray-600">Training Data</div>
                  <div className="font-medium">{model.training_data_size.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-gray-600">Last Updated</div>
                  <div className="font-medium">{model.last_updated}</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {model.performance_trend === 'improving' && (
                    <>
                      <ArrowUp className="h-4 w-4 text-green-600" />
                      <span className="text-sm text-green-600">Improving</span>
                    </>
                  )}
                  {model.performance_trend === 'stable' && (
                    <>
                      <Activity className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-600">Stable</span>
                    </>
                  )}
                  {model.performance_trend === 'testing' && (
                    <>
                      <Sparkles className="h-4 w-4 text-purple-600" />
                      <span className="text-sm text-purple-600">Testing</span>
                    </>
                  )}
                </div>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    View Details
                  </Button>
                  {model.deployment_status === 'Staging' && (
                    <Button size="sm" variant="default">
                      Deploy to Prod
                    </Button>
                  )}
                </div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Model Performance Trends</CardTitle>
            <CardDescription>Accuracy trends over time</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { model: 'Recovery Success', trend: [89, 91, 93, 94, 95], current: 94.7 },
              { model: 'Timeline Estimation', trend: [85, 87, 88, 89, 89], current: 89.2 },
              { model: 'Risk Assessment', trend: [88, 90, 91, 92, 92], current: 91.8 },
              { model: 'Value Estimation', trend: [82, 84, 86, 87, 87], current: 87.4 }
            ].map((model, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{model.model}</span>
                  <span className="text-sm font-medium">{model.current}%</span>
                </div>
                <Progress value={model.current} className="h-2" />
                <div className="text-xs text-gray-600">
                  5-month trend: {model.trend.join('% → ')}%
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>AI Infrastructure Status</CardTitle>
            <CardDescription>Real-time system health metrics</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { component: 'ML Training Pipeline', status: 'Healthy', uptime: 99.8, load: 67 },
              { component: 'Inference Engine', status: 'Healthy', uptime: 99.9, load: 45 },
              { component: 'Feature Store', status: 'Healthy', uptime: 99.7, load: 32 },
              { component: 'Model Registry', status: 'Warning', uptime: 98.9, load: 78 }
            ].map((component, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center gap-3">
                  <div className={`w-3 h-3 rounded-full ${
                    component.status === 'Healthy' ? 'bg-green-500' : 'bg-yellow-500'
                  }`}></div>
                  <div>
                    <div className="font-medium">{component.component}</div>
                    <div className="text-sm text-gray-600">{component.uptime}% uptime</div>
                  </div>
                </div>
                <div className="text-right">
                  <Badge variant={component.status === 'Healthy' ? 'default' : 'secondary'}>
                    {component.status}
                  </Badge>
                  <div className="text-xs text-gray-500 mt-1">{component.load}% load</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Predictive Analytics & AI Platform</h2>
          <p className="text-gray-600">
            Advanced AI-powered insights, predictions, and intelligent automation
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
            {userPlan === 'diamond' ? 'Full AI Platform' : 'Basic AI Features'}
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade for Full AI
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">AI Overview</TabsTrigger>
          <TabsTrigger value="predictions">Recovery Predictions</TabsTrigger>
          <TabsTrigger value="behavioral">Behavioral Analytics</TabsTrigger>
          <TabsTrigger value="models">Model Management</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="predictions" className="space-y-6">
          {renderPredictions()}
        </TabsContent>

        <TabsContent value="behavioral" className="space-y-6">
          {renderBehavioralAnalytics()}
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          {renderModelManagement()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-purple-200 bg-purple-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Brain className="h-12 w-12 text-purple-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unlock Full AI Platform</h3>
              <p className="text-gray-600 mb-4">
                Upgrade to Diamond for advanced neural networks, automated ML, and enterprise AI capabilities
              </p>
              <Button onClick={onUpgrade} className="bg-purple-600 hover:bg-purple-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 