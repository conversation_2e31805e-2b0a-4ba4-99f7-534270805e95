# AssetHunterPro - Development Server Starter
# This script ensures the frontend always runs on port 3005

Write-Host "Starting AssetHunterPro Development Server..." -ForegroundColor Green
Write-Host ""

# Function to kill processes on port 3005
function Kill-Port3005 {
    Write-Host "Checking for processes on port 3005..." -ForegroundColor Yellow

    try {
        # Find processes using port 3005
        $processes = Get-NetTCPConnection -LocalPort 3005 -ErrorAction SilentlyContinue | Select-Object -ExpandProperty OwningProcess

        if ($processes) {
            Write-Host "Found processes using port 3005. Terminating..." -ForegroundColor Red
            foreach ($pid in $processes) {
                try {
                    $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                    if ($process) {
                        Write-Host "   Killing process: $($process.ProcessName) (PID: $pid)"
                        Stop-Process -Id $pid -Force
                    }
                } catch {
                    Write-Host "   Could not kill process with PID: $pid" -ForegroundColor Yellow
                }
            }
            # Wait a moment for processes to fully terminate
            Start-Sleep -Seconds 2
            Write-Host "Port 3005 cleared!" -ForegroundColor Green
        } else {
            Write-Host "Port 3005 is available!" -ForegroundColor Green
        }
    } catch {
        Write-Host "Could not check port 3005 status. Proceeding anyway..." -ForegroundColor Yellow
    }
}

# Kill anything on port 3005
Kill-Port3005

Write-Host ""
Write-Host "Starting Vite dev server on http://localhost:3005..." -ForegroundColor Cyan
Write-Host ""

# Start the dev server on port 3005 specifically
npx vite --port 3005 --host