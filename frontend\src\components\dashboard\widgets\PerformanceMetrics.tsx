import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useWidgetData } from '@/hooks/useWidgetData';
import { AgentMetrics } from '@/types/dashboard';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Target, 
  Clock, 
  Users,
  Activity,
  BarChart3,
  CalendarDays,
  ArrowUpRight,
  ArrowDownRight,
  Settings,
  RefreshCw
} from 'lucide-react';

interface PerformanceMetricsProps {
  widgetId: string;
  className?: string;
}

export const PerformanceMetrics: React.FC<PerformanceMetricsProps> = ({ 
  widgetId, 
  className 
}) => {
  const { data, isLoading, error, refreshData } = useWidgetData<AgentMetrics>(
    widgetId, 
    'performance-metrics'
  );
  const [timeRange, setTimeRange] = useState<'7d' | '30d' | '90d'>('30d');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-3 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-2/3 mb-1"></div>
                <div className="h-2 bg-gray-200 rounded w-full"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data?.data) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Performance Metrics</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <BarChart3 className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Failed to load performance data</p>
            <p className="text-xs text-gray-400">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const metrics = data.data;

  const formatValue = (value: number): string => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`;
    return `$${value.toLocaleString()}`;
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatGrowth = (growth: number): { 
    value: string; 
    isPositive: boolean; 
    icon: React.ReactNode 
  } => {
    const isPositive = growth >= 0;
    const percentage = (Math.abs(growth) * 100).toFixed(1);
    return {
      value: `${isPositive ? '+' : '-'}${percentage}%`,
      isPositive,
      icon: isPositive ? 
        <ArrowUpRight className="h-3 w-3" /> : 
        <ArrowDownRight className="h-3 w-3" />
    };
  };

  const weeklyGrowth = formatGrowth(metrics.weeklyGrowth);
  const monthlyGrowth = formatGrowth(metrics.monthlyGrowth);

  const performanceScore = Math.round(
    (metrics.successRate * 40) + 
    (Math.min(metrics.responseTime, 10) / 10 * 30) + 
    (Math.min(metrics.weeklyGrowth, 0.5) * 30)
  );

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Performance Metrics
          </CardTitle>
          <div className="flex items-center gap-2">
            <select
              value={timeRange}
              onChange={(e) => setTimeRange(e.target.value as typeof timeRange)}
              className="text-xs border border-gray-200 rounded px-2 py-1 focus:outline-none focus:ring-1 focus:ring-blue-500"
            >
              <option value="7d">7 days</option>
              <option value="30d">30 days</option>
              <option value="90d">90 days</option>
            </select>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="h-6 w-6 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Performance Score */}
        <div className="text-center bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-3 border border-blue-100">
          <div className="text-2xl font-bold text-blue-700 mb-1">
            {performanceScore}
          </div>
          <div className="text-xs text-blue-600 mb-2">Overall Performance Score</div>
          <Progress 
            value={performanceScore} 
            className="w-full h-2"
          />
          <div className="text-xs text-gray-500 mt-1">
            {performanceScore >= 80 ? 'Excellent' : 
             performanceScore >= 60 ? 'Good' : 
             performanceScore >= 40 ? 'Average' : 'Needs Improvement'}
          </div>
        </div>

        {/* Key Metrics Grid */}
        <div className="grid grid-cols-2 gap-3">
          {/* Total Value */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 flex items-center gap-1">
                <DollarSign className="h-3 w-3" />
                Total Value
              </span>
              <div className={`flex items-center gap-1 text-xs ${
                monthlyGrowth.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {monthlyGrowth.icon}
                {monthlyGrowth.value}
              </div>
            </div>
            <div className="text-lg font-bold text-gray-900">
              {formatValue(metrics.totalValue)}
            </div>
            <div className="text-xs text-gray-500">
              Avg: {formatValue(metrics.averageClaimValue)}
            </div>
          </div>

          {/* Success Rate */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 flex items-center gap-1">
                <Target className="h-3 w-3" />
                Success Rate
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  metrics.successRate >= 0.8 ? 'bg-green-100 text-green-800' :
                  metrics.successRate >= 0.6 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}
              >
                {metrics.successRate >= 0.8 ? 'High' :
                 metrics.successRate >= 0.6 ? 'Medium' : 'Low'}
              </Badge>
            </div>
            <div className="text-lg font-bold text-green-600">
              {formatPercentage(metrics.successRate)}
            </div>
            <Progress 
              value={metrics.successRate * 100} 
              className="w-full h-1"
            />
          </div>

          {/* Response Time */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 flex items-center gap-1">
                <Clock className="h-3 w-3" />
                Avg Response
              </span>
              <Badge 
                variant="outline" 
                className={`text-xs ${
                  metrics.responseTime <= 2 ? 'bg-green-100 text-green-800' :
                  metrics.responseTime <= 5 ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}
              >
                {metrics.responseTime <= 2 ? 'Fast' :
                 metrics.responseTime <= 5 ? 'Good' : 'Slow'}
              </Badge>
            </div>
            <div className="text-lg font-bold text-blue-600">
              {metrics.responseTime}d
            </div>
            <div className="text-xs text-gray-500">
              Target: ≤ 2 days
            </div>
          </div>

          {/* Claims Activity */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-xs text-gray-600 flex items-center gap-1">
                <Activity className="h-3 w-3" />
                Claims Activity
              </span>
              <div className={`flex items-center gap-1 text-xs ${
                weeklyGrowth.isPositive ? 'text-green-600' : 'text-red-600'
              }`}>
                {weeklyGrowth.icon}
                {weeklyGrowth.value}
              </div>
            </div>
            <div className="text-lg font-bold text-gray-900">
              {metrics.activeClaims}
            </div>
            <div className="text-xs text-gray-500">
              {metrics.completedClaims} completed
            </div>
          </div>
        </div>

        {/* Detailed Breakdown */}
        <div className="space-y-3">
          <h4 className="text-xs font-medium text-gray-700 flex items-center gap-1">
            <CalendarDays className="h-3 w-3" />
            This {timeRange === '7d' ? 'Week' : 'Month'} Breakdown
          </h4>
          
          <div className="space-y-2">
            {/* Claims Progress */}
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Claims Completion</span>
              <span className="font-medium">
                {metrics.completedClaims}/{metrics.totalClaims}
              </span>
            </div>
            <Progress 
              value={(metrics.completedClaims / metrics.totalClaims) * 100} 
              className="w-full h-1"
            />

            {/* Value Recovery Progress */}
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Value Recovery</span>
              <span className="font-medium text-green-600">
                {formatValue(metrics.totalValue)}
              </span>
            </div>
            <Progress 
              value={Math.min((metrics.totalValue / 15000000) * 100, 100)} 
              className="w-full h-1"
            />

            {/* Efficiency Score */}
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-600">Efficiency Score</span>
              <span className="font-medium">
                {Math.round((metrics.successRate * metrics.responseTime) * 100)}%
              </span>
            </div>
            <Progress 
              value={Math.round((metrics.successRate * metrics.responseTime) * 100)} 
              className="w-full h-1"
            />
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2 pt-2 border-t border-gray-100">
          <Button size="sm" variant="outline" className="h-7 text-xs">
            <Users className="h-3 w-3 mr-1" />
            Team Compare
          </Button>
          <Button size="sm" variant="outline" className="h-7 text-xs">
            <TrendingUp className="h-3 w-3 mr-1" />
            View Trends
          </Button>
        </div>

        {/* Last Updated */}
        <div className="pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-400 text-center">
            Last updated: {data.lastUpdated.toLocaleTimeString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 