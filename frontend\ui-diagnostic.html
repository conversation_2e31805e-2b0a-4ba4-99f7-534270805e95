<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Component Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .pass { background-color: #d4edda; color: #155724; }
        .fail { background-color: #f8d7da; color: #721c24; }
        .warning { background-color: #fff3cd; color: #856404; }
        .section { margin: 20px 0; border: 1px solid #ddd; padding: 15px; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 3px; overflow-x: auto; }
    </style>
</head>
<body>
    <h1>🔍 UI Component Diagnostic Tool</h1>
    <p>This tool checks for the presence of key UI components that were reported as missing.</p>
    
    <div id="results"></div>
    
    <script>
        const results = document.getElementById('results');
        
        function addResult(test, status, message, details = '') {
            const div = document.createElement('div');
            div.className = `test-result ${status}`;
            div.innerHTML = `
                <strong>${test}:</strong> ${message}
                ${details ? `<pre>${details}</pre>` : ''}
            `;
            results.appendChild(div);
        }
        
        function checkReactApp() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>🎨 React App Detection</h2>';
            results.appendChild(section);
            
            // Check if React root exists
            const reactRoot = document.getElementById('root');
            if (reactRoot) {
                addResult('React Root Element', 'pass', 'Found #root element');
                
                // Check if React has rendered content
                if (reactRoot.children.length > 0) {
                    addResult('React Content', 'pass', `React has rendered ${reactRoot.children.length} child elements`);
                    
                    // Check for data-reactroot attribute
                    const hasReactRoot = reactRoot.hasAttribute('data-reactroot') || 
                                       reactRoot.querySelector('[data-reactroot]');
                    if (hasReactRoot) {
                        addResult('React Root Attribute', 'pass', 'Found data-reactroot attribute');
                    } else {
                        addResult('React Root Attribute', 'warning', 'data-reactroot attribute not found (may be normal in React 18+)');
                    }
                } else {
                    addResult('React Content', 'fail', 'React root element is empty - React app may not have loaded');
                }
            } else {
                addResult('React Root Element', 'fail', '#root element not found');
            }
        }
        
        function checkFormInputs() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>📝 Form Input Detection</h2>';
            results.appendChild(section);
            
            const inputs = document.querySelectorAll('input, textarea, select');
            const forms = document.querySelectorAll('form');
            
            addResult('Total Input Elements', inputs.length > 0 ? 'pass' : 'fail', 
                     `Found ${inputs.length} input elements`);
            addResult('Total Form Elements', forms.length > 0 ? 'pass' : 'fail', 
                     `Found ${forms.length} form elements`);
            
            // Check for specific input types
            const inputTypes = ['text', 'email', 'password', 'tel', 'number', 'date'];
            inputTypes.forEach(type => {
                const count = document.querySelectorAll(`input[type="${type}"]`).length;
                addResult(`${type.charAt(0).toUpperCase() + type.slice(1)} Inputs`, 
                         count > 0 ? 'pass' : 'warning', 
                         `Found ${count} ${type} inputs`);
            });
        }
        
        function checkNavigation() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>🧭 Navigation Detection</h2>';
            results.appendChild(section);
            
            // Check for common navigation elements
            const nav = document.querySelector('nav');
            const sidebar = document.querySelector('aside, .sidebar, [class*="sidebar"]');
            const menuButtons = document.querySelectorAll('button[class*="menu"], .menu-button, [aria-label*="menu"]');
            const navLinks = document.querySelectorAll('a[href], button[onclick], [role="button"]');
            
            addResult('Navigation Element', nav ? 'pass' : 'warning', 
                     nav ? 'Found <nav> element' : 'No <nav> element found');
            addResult('Sidebar Element', sidebar ? 'pass' : 'warning', 
                     sidebar ? 'Found sidebar element' : 'No sidebar element found');
            addResult('Menu Buttons', menuButtons.length > 0 ? 'pass' : 'warning', 
                     `Found ${menuButtons.length} menu buttons`);
            addResult('Navigation Links', navLinks.length > 0 ? 'pass' : 'fail', 
                     `Found ${navLinks.length} clickable navigation elements`);
        }
        
        function checkInteractiveElements() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>🔘 Interactive Elements</h2>';
            results.appendChild(section);
            
            const buttons = document.querySelectorAll('button');
            const clickableElements = document.querySelectorAll('[onclick], [role="button"], .btn, .button');
            const interactiveElements = document.querySelectorAll('input, button, select, textarea, a[href]');
            
            addResult('Button Elements', buttons.length > 0 ? 'pass' : 'fail', 
                     `Found ${buttons.length} button elements`);
            addResult('Clickable Elements', clickableElements.length > 0 ? 'pass' : 'warning', 
                     `Found ${clickableElements.length} clickable elements`);
            addResult('Total Interactive Elements', interactiveElements.length > 0 ? 'pass' : 'fail', 
                     `Found ${interactiveElements.length} interactive elements`);
        }
        
        function checkCSS() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>🎨 CSS and Styling</h2>';
            results.appendChild(section);
            
            const stylesheets = document.querySelectorAll('link[rel="stylesheet"], style');
            const styledElements = document.querySelectorAll('[class], [style]');
            
            addResult('Stylesheets', stylesheets.length > 0 ? 'pass' : 'warning', 
                     `Found ${stylesheets.length} stylesheets`);
            addResult('Styled Elements', styledElements.length > 0 ? 'pass' : 'warning', 
                     `Found ${styledElements.length} elements with styling`);
            
            // Check for Tailwind CSS classes
            const tailwindElements = document.querySelectorAll('[class*="bg-"], [class*="text-"], [class*="p-"], [class*="m-"]');
            addResult('Tailwind CSS Classes', tailwindElements.length > 0 ? 'pass' : 'warning', 
                     `Found ${tailwindElements.length} elements with Tailwind classes`);
        }
        
        function checkErrors() {
            const section = document.createElement('div');
            section.className = 'section';
            section.innerHTML = '<h2>⚠️ Console Errors</h2>';
            results.appendChild(section);
            
            // Override console.error to capture errors
            const originalError = console.error;
            const errors = [];
            console.error = function(...args) {
                errors.push(args.join(' '));
                originalError.apply(console, args);
            };
            
            setTimeout(() => {
                if (errors.length > 0) {
                    addResult('Console Errors', 'fail', 
                             `Found ${errors.length} console errors`, 
                             errors.join('\n'));
                } else {
                    addResult('Console Errors', 'pass', 'No console errors detected');
                }
            }, 2000);
        }
        
        // Run all checks
        function runDiagnostics() {
            results.innerHTML = '<h2>Running diagnostics...</h2>';
            
            setTimeout(() => {
                checkReactApp();
                checkFormInputs();
                checkNavigation();
                checkInteractiveElements();
                checkCSS();
                checkErrors();
                
                // Add summary
                const summary = document.createElement('div');
                summary.className = 'section';
                summary.innerHTML = `
                    <h2>📊 Summary</h2>
                    <p>Diagnostic completed at ${new Date().toLocaleString()}</p>
                    <p>Check the results above to identify missing UI components.</p>
                `;
                results.appendChild(summary);
            }, 1000);
        }
        
        // Auto-run diagnostics when page loads
        window.addEventListener('load', runDiagnostics);
    </script>
</body>
</html>
