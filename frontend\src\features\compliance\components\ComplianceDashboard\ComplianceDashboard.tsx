import React, { useState, useMemo } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText, 
  Download, 
  Search,
  Filter,
  Calendar,
  Users,
  Eye,
  Activity,
  TrendingUp,
  BarChart3,
  Archive,
  Settings,
  Bell,
  Briefcase,
  Scale,
  Lock,
  Flag
} from 'lucide-react';

interface AuditTrail {
  id: string;
  action: string;
  entity: string;
  entityId: string;
  user: string;
  userRole: string;
  timestamp: string;
  ip: string;
  details: string;
  category: 'access' | 'data_change' | 'system' | 'security';
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface ComplianceRequirement {
  id: string;
  regulation: string;
  title: string;
  description: string;
  status: 'compliant' | 'at_risk' | 'non_compliant';
  dueDate: string;
  assignee: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  documents: string[];
  lastReview: string;
}

interface RiskAssessment {
  id: string;
  title: string;
  category: 'data_privacy' | 'financial' | 'operational' | 'regulatory';
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  probability: number;
  impact: number;
  mitigationPlan: string;
  owner: string;
  dueDate: string;
  status: 'open' | 'in_progress' | 'mitigated' | 'closed';
}

interface ComplianceReport {
  id: string;
  title: string;
  type: 'audit' | 'risk_assessment' | 'compliance_check' | 'incident_report';
  period: string;
  status: 'draft' | 'pending_review' | 'approved' | 'submitted';
  generatedDate: string;
  dueDate: string;
  assignee: string;
  size: string;
}

interface ComplianceDashboardProps {
  className?: string;
}

// Mock data
const mockAuditTrails: AuditTrail[] = [
  {
    id: '1',
    action: 'User Login',
    entity: 'Authentication System',
    entityId: 'auth-001',
    user: '<EMAIL>',
    userRole: 'Senior Agent',
    timestamp: '2024-01-15T10:30:00Z',
    ip: '*************',
    details: 'Successful login from web browser',
    category: 'access',
    severity: 'low'
  },
  {
    id: '2',
    action: 'Claim Data Modified',
    entity: 'Claim',
    entityId: 'CLM-2024-001',
    user: '<EMAIL>',
    userRole: 'Admin',
    timestamp: '2024-01-15T09:15:00Z',
    ip: '*************',
    details: 'Updated claim amount from $1,000 to $1,250',
    category: 'data_change',
    severity: 'medium'
  },
  {
    id: '3',
    action: 'Failed Login Attempt',
    entity: 'Authentication System',
    entityId: 'auth-002',
    user: '<EMAIL>',
    userRole: 'Unknown',
    timestamp: '2024-01-15T08:45:00Z',
    ip: '************',
    details: 'Multiple failed login attempts detected',
    category: 'security',
    severity: 'high'
  }
];

const mockRequirements: ComplianceRequirement[] = [
  {
    id: '1',
    regulation: 'GDPR',
    title: 'Data Protection Assessment',
    description: 'Quarterly review of data protection measures and privacy controls',
    status: 'compliant',
    dueDate: '2024-03-31',
    assignee: '<EMAIL>',
    priority: 'high',
    documents: ['DPA-2024-Q1.pdf', 'Privacy-Controls-Audit.pdf'],
    lastReview: '2024-01-15'
  },
  {
    id: '2',
    regulation: 'SOX',
    title: 'Financial Controls Review',
    description: 'Annual review of internal financial controls and procedures',
    status: 'at_risk',
    dueDate: '2024-02-28',
    assignee: '<EMAIL>',
    priority: 'critical',
    documents: ['SOX-Controls-2024.pdf'],
    lastReview: '2023-12-01'
  },
  {
    id: '3',
    regulation: 'HIPAA',
    title: 'Healthcare Data Security',
    description: 'Bi-annual security assessment for healthcare data handling',
    status: 'non_compliant',
    dueDate: '2024-01-31',
    assignee: '<EMAIL>',
    priority: 'critical',
    documents: [],
    lastReview: '2023-08-15'
  }
];

const mockRisks: RiskAssessment[] = [
  {
    id: '1',
    title: 'Data Breach Risk',
    category: 'data_privacy',
    riskLevel: 'high',
    probability: 30,
    impact: 85,
    mitigationPlan: 'Implement additional encryption and access controls',
    owner: '<EMAIL>',
    dueDate: '2024-02-15',
    status: 'in_progress'
  },
  {
    id: '2',
    title: 'Regulatory Penalty Risk',
    category: 'regulatory',
    riskLevel: 'medium',
    probability: 45,
    impact: 60,
    mitigationPlan: 'Complete outstanding compliance requirements',
    owner: '<EMAIL>',
    dueDate: '2024-01-30',
    status: 'open'
  }
];

const mockReports: ComplianceReport[] = [
  {
    id: '1',
    title: 'Q4 2023 Compliance Report',
    type: 'compliance_check',
    period: '2023 Q4',
    status: 'approved',
    generatedDate: '2024-01-05',
    dueDate: '2024-01-15',
    assignee: '<EMAIL>',
    size: '2.4 MB'
  },
  {
    id: '2',
    title: 'Annual Risk Assessment 2024',
    type: 'risk_assessment',
    period: '2024',
    status: 'draft',
    generatedDate: '2024-01-10',
    dueDate: '2024-01-31',
    assignee: '<EMAIL>',
    size: '1.8 MB'
  }
];

export const ComplianceDashboard: React.FC<ComplianceDashboardProps> = ({ className }) => {
  const [selectedTab, setSelectedTab] = useState<'overview' | 'audit' | 'requirements' | 'risks' | 'reports'>('overview');
  const [searchTerm, setSearchTerm] = useState('');
  const [filterCategory, setFilterCategory] = useState<string>('all');
  const [dateRange, setDateRange] = useState<string>('7days');

  const filteredAuditTrails = useMemo(() => {
    let filtered = [...mockAuditTrails];

    if (searchTerm) {
      filtered = filtered.filter(trail =>
        trail.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.entity.toLowerCase().includes(searchTerm.toLowerCase()) ||
        trail.user.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (filterCategory !== 'all') {
      filtered = filtered.filter(trail => trail.category === filterCategory);
    }

    return filtered.sort((a, b) => 
      new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );
  }, [searchTerm, filterCategory]);

  const complianceStats = useMemo(() => {
    const total = mockRequirements.length;
    const compliant = mockRequirements.filter(req => req.status === 'compliant').length;
    const atRisk = mockRequirements.filter(req => req.status === 'at_risk').length;
    const nonCompliant = mockRequirements.filter(req => req.status === 'non_compliant').length;

    return {
      total,
      compliant,
      atRisk,
      nonCompliant,
      complianceRate: total > 0 ? Math.round((compliant / total) * 100) : 0
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'compliant':
      case 'approved':
      case 'mitigated':
      case 'closed':
        return 'text-green-600 bg-green-100';
      case 'at_risk':
      case 'pending_review':
      case 'in_progress':
        return 'text-yellow-600 bg-yellow-100';
      case 'non_compliant':
      case 'open':
      case 'draft':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'text-red-800 bg-red-100 border-red-200';
      case 'high': return 'text-orange-800 bg-orange-100 border-orange-200';
      case 'medium': return 'text-yellow-800 bg-yellow-100 border-yellow-200';
      case 'low': return 'text-gray-800 bg-gray-100 border-gray-200';
      default: return 'text-gray-800 bg-gray-100 border-gray-200';
    }
  };

  const getRiskLevelColor = (level: string) => {
    switch (level) {
      case 'critical': return 'text-red-600 bg-red-100';
      case 'high': return 'text-orange-600 bg-orange-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const renderOverview = () => (
    <div className="space-y-6">
      {/* Compliance Summary */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Rate</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{complianceStats.complianceRate}%</div>
            <p className="text-xs text-gray-600">
              {complianceStats.compliant} of {complianceStats.total} requirements
            </p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">At Risk</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{complianceStats.atRisk}</div>
            <p className="text-xs text-gray-600">Requirements need attention</p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Non-Compliant</CardTitle>
            <Flag className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{complianceStats.nonCompliant}</div>
            <p className="text-xs text-gray-600">Immediate action required</p>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Risk Score</CardTitle>
            <BarChart3 className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">Medium</div>
            <p className="text-xs text-gray-600">Overall risk assessment</p>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity & Upcoming Deadlines */}
      <div className="grid gap-6 lg:grid-cols-2">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>Recent Compliance Activity</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {filteredAuditTrails.slice(0, 5).map((trail) => (
                <div key={trail.id} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{trail.action}</p>
                    <p className="text-xs text-gray-500">{trail.user} • {trail.entity}</p>
                  </div>
                  <Badge className={`text-xs ${getSeverityColor(trail.severity)}`}>
                    {trail.severity}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Clock className="h-5 w-5" />
              <span>Upcoming Deadlines</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockRequirements
                .filter(req => new Date(req.dueDate) > new Date())
                .sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime())
                .slice(0, 5)
                .map((req) => (
                  <div key={req.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <p className="font-medium text-sm">{req.title}</p>
                      <p className="text-xs text-gray-500">{req.regulation} • Due {new Date(req.dueDate).toLocaleDateString()}</p>
                    </div>
                    <Badge className={`text-xs ${getStatusColor(req.status)}`}>
                      {req.status}
                    </Badge>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Compliance Dashboard</h2>
          <p className="text-gray-600">Monitor regulatory compliance and audit trails</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button size="sm">
            <FileText className="h-4 w-4 mr-2" />
            Generate Report
          </Button>
        </div>
      </div>

      {/* Navigation Tabs */}
      <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
        {[
          { id: 'overview', label: 'Overview', icon: BarChart3 },
          { id: 'audit', label: 'Audit Trail', icon: Eye },
          { id: 'requirements', label: 'Requirements', icon: Shield },
          { id: 'risks', label: 'Risk Assessment', icon: AlertTriangle },
          { id: 'reports', label: 'Reports', icon: FileText }
        ].map((tab) => {
          const Icon = tab.icon;
          return (
            <Button
              key={tab.id}
              variant={selectedTab === tab.id ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setSelectedTab(tab.id as any)}
              className="flex items-center space-x-2"
            >
              <Icon className="h-4 w-4" />
              <span>{tab.label}</span>
            </Button>
          );
        })}
      </div>

      {/* Tab Content */}
      {selectedTab === 'overview' && renderOverview()}

      {selectedTab === 'audit' && (
        <div className="space-y-6">
          {/* Filters */}
          <Card className="border-0 shadow-lg">
            <CardContent className="p-4">
              <div className="flex space-x-4">
                <div className="flex-1">
                  <Input
                    placeholder="Search audit trails..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <Select value={filterCategory} onValueChange={setFilterCategory}>
                  <SelectTrigger className="w-48">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Categories</SelectItem>
                    <SelectItem value="access">Access</SelectItem>
                    <SelectItem value="data_change">Data Changes</SelectItem>
                    <SelectItem value="system">System</SelectItem>
                    <SelectItem value="security">Security</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7days">7 Days</SelectItem>
                    <SelectItem value="30days">30 Days</SelectItem>
                    <SelectItem value="90days">90 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Audit Trail Table */}
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Audit Trail</CardTitle>
              <CardDescription>
                Detailed log of all system activities and user actions
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredAuditTrails.map((trail) => (
                  <div key={trail.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{trail.action}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={`text-xs ${getSeverityColor(trail.severity)}`}>
                          {trail.severity}
                        </Badge>
                        <span className="text-sm text-gray-500">
                          {new Date(trail.timestamp).toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm text-gray-600">
                      <div>
                        <strong>User:</strong> {trail.user} ({trail.userRole})
                      </div>
                      <div>
                        <strong>IP:</strong> {trail.ip}
                      </div>
                      <div>
                        <strong>Entity:</strong> {trail.entity} ({trail.entityId})
                      </div>
                      <div>
                        <strong>Category:</strong> {trail.category}
                      </div>
                    </div>
                    <div className="mt-2 text-sm text-gray-600">
                      <strong>Details:</strong> {trail.details}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === 'requirements' && (
        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Compliance Requirements</CardTitle>
              <CardDescription>
                Track and manage regulatory compliance requirements
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRequirements.map((req) => (
                  <div key={req.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">{req.title}</h4>
                        <p className="text-sm text-gray-600">{req.regulation}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={`${getSeverityColor(req.priority)}`}>
                          {req.priority}
                        </Badge>
                        <Badge className={`${getStatusColor(req.status)}`}>
                          {req.status}
                        </Badge>
                      </div>
                    </div>
                    <p className="text-sm text-gray-700 mb-3">{req.description}</p>
                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <strong>Due Date:</strong> {new Date(req.dueDate).toLocaleDateString()}
                      </div>
                      <div>
                        <strong>Assignee:</strong> {req.assignee}
                      </div>
                      <div>
                        <strong>Last Review:</strong> {new Date(req.lastReview).toLocaleDateString()}
                      </div>
                    </div>
                    {req.documents.length > 0 && (
                      <div className="mt-3">
                        <strong className="text-sm">Documents:</strong>
                        <div className="flex space-x-2 mt-1">
                          {req.documents.map((doc, index) => (
                            <Badge key={index} variant="outline" className="text-xs">
                              {doc}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === 'risks' && (
        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Risk Assessment</CardTitle>
              <CardDescription>
                Monitor and manage compliance-related risks
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRisks.map((risk) => (
                  <div key={risk.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h4 className="font-semibold">{risk.title}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={`${getRiskLevelColor(risk.riskLevel)}`}>
                          {risk.riskLevel} risk
                        </Badge>
                        <Badge className={`${getStatusColor(risk.status)}`}>
                          {risk.status}
                        </Badge>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 mb-3">
                      <div>
                        <Label className="text-sm">Probability</Label>
                        <Progress value={risk.probability} className="mt-1" />
                        <span className="text-xs text-gray-500">{risk.probability}%</span>
                      </div>
                      <div>
                        <Label className="text-sm">Impact</Label>
                        <Progress value={risk.impact} className="mt-1" />
                        <span className="text-xs text-gray-500">{risk.impact}%</span>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <strong>Category:</strong> {risk.category.replace('_', ' ')}
                      </div>
                      <div>
                        <strong>Owner:</strong> {risk.owner}
                      </div>
                      <div>
                        <strong>Due Date:</strong> {new Date(risk.dueDate).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="mt-3">
                      <strong className="text-sm">Mitigation Plan:</strong>
                      <p className="text-sm text-gray-700 mt-1">{risk.mitigationPlan}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {selectedTab === 'reports' && (
        <div className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle>Compliance Reports</CardTitle>
              <CardDescription>
                Generate and manage compliance reports
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockReports.map((report) => (
                  <div key={report.id} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-semibold">{report.title}</h4>
                      <div className="flex items-center space-x-2">
                        <Badge className={`${getStatusColor(report.status)}`}>
                          {report.status}
                        </Badge>
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                    <div className="grid grid-cols-4 gap-4 text-sm text-gray-600">
                      <div>
                        <strong>Type:</strong> {report.type.replace('_', ' ')}
                      </div>
                      <div>
                        <strong>Period:</strong> {report.period}
                      </div>
                      <div>
                        <strong>Generated:</strong> {new Date(report.generatedDate).toLocaleDateString()}
                      </div>
                      <div>
                        <strong>Size:</strong> {report.size}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}; 