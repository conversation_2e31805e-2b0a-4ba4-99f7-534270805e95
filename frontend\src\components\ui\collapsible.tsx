import * as React from "react"

interface CollapsibleProps {
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
  children: React.ReactNode;
}

interface CollapsibleTriggerProps {
  asChild?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

interface CollapsibleContentProps {
  children: React.ReactNode;
}

const CollapsibleContext = React.createContext<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
}>({
  open: false,
  onOpenChange: () => {}
});

const Collapsible: React.FC<CollapsibleProps> = ({ 
  open = false, 
  onOpenChange = () => {}, 
  children 
}) => {
  return (
    <CollapsibleContext.Provider value={{ open, onOpenChange }}>
      <div>
        {children}
      </div>
    </CollapsibleContext.Provider>
  );
};

const CollapsibleTrigger: React.FC<CollapsibleTriggerProps> = ({ 
  asChild = false, 
  children, 
  onClick 
}) => {
  const { open, onOpenChange } = React.useContext(CollapsibleContext);
  
  const handleClick = () => {
    onOpenChange(!open);
    onClick?.();
  };

  if (asChild && React.isValidElement(children)) {
    return React.cloneElement(children, {
      ...children.props,
      onClick: handleClick
    });
  }

  return (
    <button onClick={handleClick}>
      {children}
    </button>
  );
};

const CollapsibleContent: React.FC<CollapsibleContentProps> = ({ children }) => {
  const { open } = React.useContext(CollapsibleContext);
  
  if (!open) return null;
  
  return <div>{children}</div>;
};

export { Collapsible, CollapsibleTrigger, CollapsibleContent } 