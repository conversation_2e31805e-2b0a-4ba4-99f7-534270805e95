/**
 * Production Streaming File Upload Component
 * Handles 300MB+ CSV files with chunked uploads and memory efficiency
 */

import React, { useState, useRef, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  XCircle, 
  Pause, 
  Play, 
  Trash2, 
  AlertTriangle,
  Activity,
  Database,
  Timer,
  Zap
} from 'lucide-react';

// =============================================================================
// INTERFACES FOR LARGE FILE PROCESSING
// =============================================================================

export interface ChunkedUploadConfig {
  chunkSize: number; // bytes per chunk (default: 10MB)
  maxConcurrentChunks: number; // parallel upload limit
  retryAttempts: number;
  timeout: number; // milliseconds
  enableCompression: boolean;
  validateIntegrity: boolean;
}

export interface UploadProgress {
  uploadId: string;
  fileName: string;
  fileSize: number;
  uploadedBytes: number;
  uploadedChunks: number;
  totalChunks: number;
  uploadSpeed: number; // bytes per second
  estimatedTimeRemaining: number; // seconds
  status: 'preparing' | 'uploading' | 'paused' | 'completed' | 'failed' | 'cancelled';
  errorMessage?: string;
  canResume: boolean;
  startTime: Date;
  lastUpdateTime: Date;
  chunksInProgress: number[];
  failedChunks: number[];
}

export interface FileValidationResult {
  isValid: boolean;
  fileType: 'csv' | 'xlsx' | 'json' | 'unknown';
  encoding: string;
  estimatedRecords: number;
  headerRow?: string[];
  sampleData?: string[][];
  warnings: string[];
  errors: string[];
  memoryEstimate: number; // MB required for processing
}

export interface ProcessingPreview {
  totalRecords: number;
  validRecords: number;
  invalidRecords: number;
  duplicateRecords: number;
  estimatedProcessingTime: number; // minutes
  estimatedCredits: number;
  suggestedBatchSize: number;
  memoryRequired: number; // MB
  processingPlan: ProcessingStep[];
}

export interface ProcessingStep {
  step: string;
  description: string;
  estimatedTime: number; // seconds
  resourceUsage: 'low' | 'medium' | 'high';
  canOptimize: boolean;
}

// =============================================================================
// MAIN COMPONENT
// =============================================================================

interface StreamingFileUploadProps {
  onUploadComplete: (uploadId: string, validationResult: FileValidationResult) => void;
  onProcessingStart: (uploadId: string, preview: ProcessingPreview) => void;
  maxFileSize?: number; // bytes (default: 500MB)
  acceptedTypes?: string[];
  enablePreview?: boolean;
  uploadConfig?: Partial<ChunkedUploadConfig>;
  className?: string;
}

export const StreamingFileUpload: React.FC<StreamingFileUploadProps> = ({
  onUploadComplete,
  onProcessingStart,
  maxFileSize = 500 * 1024 * 1024, // 500MB
  acceptedTypes = ['.csv', '.xlsx', '.json'],
  enablePreview = true,
  uploadConfig = {},
  className
}) => {
  const [activeUploads, setActiveUploads] = useState<Map<string, UploadProgress>>(new Map());
  const [validationResults, setValidationResults] = useState<Map<string, FileValidationResult>>(new Map());
  const [processingPreviews, setProcessingPreviews] = useState<Map<string, ProcessingPreview>>(new Map());
  const [isDragging, setIsDragging] = useState(false);
  const [systemMetrics, setSystemMetrics] = useState({
    memoryUsage: 0,
    activeUploads: 0,
    networkSpeed: 0
  });

  const fileInputRef = useRef<HTMLInputElement>(null);
  const uploadManagerRef = useRef<StreamingUploadManager | null>(null);

  // Initialize upload manager
  React.useEffect(() => {
    uploadManagerRef.current = new StreamingUploadManager({
      chunkSize: 10 * 1024 * 1024, // 10MB chunks
      maxConcurrentChunks: 3,
      retryAttempts: 3,
      timeout: 30000,
      enableCompression: true,
      validateIntegrity: true,
      ...uploadConfig
    });

    const manager = uploadManagerRef.current;

    // Set up event listeners
    manager.onProgress((progress: UploadProgress) => {
      setActiveUploads(prev => new Map(prev.set(progress.uploadId, progress)));
    });

    manager.onValidationComplete((uploadId: string, result: FileValidationResult) => {
      setValidationResults(prev => new Map(prev.set(uploadId, result)));
    });

    manager.onProcessingPreview((uploadId: string, preview: ProcessingPreview) => {
      setProcessingPreviews(prev => new Map(prev.set(uploadId, preview)));
    });

    // Monitor system metrics
    const metricsInterval = setInterval(() => {
      setSystemMetrics({
        memoryUsage: manager.getCurrentMemoryUsage(),
        activeUploads: manager.getActiveUploadCount(),
        networkSpeed: manager.getAverageUploadSpeed()
      });
    }, 1000);

    return () => {
      clearInterval(metricsInterval);
      manager.cleanup();
    };
  }, [uploadConfig]);

  // =============================================================================
  // EVENT HANDLERS
  // =============================================================================

  const handleFileSelect = useCallback(async (files: FileList) => {
    if (!uploadManagerRef.current) return;

    for (const file of Array.from(files)) {
      // Validate file before upload
      if (file.size > maxFileSize) {
        alert(`File "${file.name}" exceeds maximum size of ${formatFileSize(maxFileSize)}`);
        continue;
      }

      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
      if (!acceptedTypes.includes(fileExtension)) {
        alert(`File type "${fileExtension}" is not supported. Accepted types: ${acceptedTypes.join(', ')}`);
        continue;
      }

      try {
        const uploadId = await uploadManagerRef.current.startUpload(file);
        console.log(`Started upload: ${uploadId} for file: ${file.name}`);
      } catch (error) {
        console.error('Failed to start upload:', error);
        alert(`Failed to start upload for "${file.name}": ${(error as Error).message}`);
      }
    }
  }, [maxFileSize, acceptedTypes]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  }, [handleFileSelect]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
  }, []);

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      handleFileSelect(e.target.files);
    }
  }, [handleFileSelect]);

  const handleUploadAction = useCallback(async (uploadId: string, action: 'pause' | 'resume' | 'cancel') => {
    if (!uploadManagerRef.current) return;

    try {
      switch (action) {
        case 'pause':
          await uploadManagerRef.current.pauseUpload(uploadId);
          break;
        case 'resume':
          await uploadManagerRef.current.resumeUpload(uploadId);
          break;
        case 'cancel':
          await uploadManagerRef.current.cancelUpload(uploadId);
          setActiveUploads(prev => {
            const newMap = new Map(prev);
            newMap.delete(uploadId);
            return newMap;
          });
          break;
      }
    } catch (error) {
      console.error(`Failed to ${action} upload:`, error);
    }
  }, []);

  const handleStartProcessing = useCallback(async (uploadId: string) => {
    const validationResult = validationResults.get(uploadId);
    const processingPreview = processingPreviews.get(uploadId);
    
    if (validationResult && processingPreview) {
      onProcessingStart(uploadId, processingPreview);
    }
  }, [validationResults, processingPreviews, onProcessingStart]);

  // =============================================================================
  // UTILITY FUNCTIONS
  // =============================================================================

  const formatFileSize = (bytes: number): string => {
    if (bytes >= 1024 * 1024 * 1024) return `${(bytes / (1024 * 1024 * 1024)).toFixed(1)} GB`;
    if (bytes >= 1024 * 1024) return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
    if (bytes >= 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${bytes} B`;
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const formatTime = (seconds: number): string => {
    if (seconds < 60) return `${seconds}s`;
    if (seconds < 3600) return `${Math.floor(seconds / 60)}m ${seconds % 60}s`;
    return `${Math.floor(seconds / 3600)}h ${Math.floor((seconds % 3600) / 60)}m`;
  };

  const getStatusColor = (status: UploadProgress['status']) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'paused': return 'text-yellow-600';
      case 'uploading': return 'text-blue-600';
      default: return 'text-gray-600';
    }
  };

  const getStatusIcon = (status: UploadProgress['status']) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'failed': return <XCircle className="h-4 w-4 text-red-600" />;
      case 'paused': return <Pause className="h-4 w-4 text-yellow-600" />;
      case 'uploading': return <Activity className="h-4 w-4 text-blue-600 animate-pulse" />;
      default: return <Timer className="h-4 w-4 text-gray-600" />;
    }
  };

  // =============================================================================
  // RENDER COMPONENTS
  // =============================================================================

  const renderSystemMetrics = () => (
    <div className="grid grid-cols-3 gap-4 mb-6">
      <Card className="border-blue-200 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Activity className="h-4 w-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-900">Memory Usage</span>
            </div>
            <span className="text-lg font-bold text-blue-700">
              {systemMetrics.memoryUsage.toFixed(1)} MB
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className="border-green-200 bg-green-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Upload className="h-4 w-4 text-green-600" />
              <span className="text-sm font-medium text-green-900">Active Uploads</span>
            </div>
            <span className="text-lg font-bold text-green-700">
              {systemMetrics.activeUploads}
            </span>
          </div>
        </CardContent>
      </Card>

      <Card className="border-purple-200 bg-purple-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-purple-600" />
              <span className="text-sm font-medium text-purple-900">Network Speed</span>
            </div>
            <span className="text-lg font-bold text-purple-700">
              {formatSpeed(systemMetrics.networkSpeed)}
            </span>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderDropZone = () => (
    <Card className={`border-2 border-dashed transition-all duration-300 ${
      isDragging 
        ? 'border-blue-500 bg-blue-50' 
        : 'border-gray-300 hover:border-gray-400'
    }`}>
      <CardContent 
        className="p-12 text-center cursor-pointer"
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onClick={() => fileInputRef.current?.click()}
      >
        <div className="flex flex-col items-center gap-4">
          <div className={`p-4 rounded-full ${
            isDragging ? 'bg-blue-100' : 'bg-gray-100'
          }`}>
            <Upload className={`h-8 w-8 ${
              isDragging ? 'text-blue-600' : 'text-gray-600'
            }`} />
          </div>
          
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">
              {isDragging ? 'Drop files here' : 'Upload Large CSV Files'}
            </h3>
            <p className="text-gray-600 mb-4">
              Drag & drop files or click to browse
            </p>
            <div className="text-sm text-gray-500 space-y-1">
              <p>Supported formats: {acceptedTypes.join(', ')}</p>
              <p>Maximum size: {formatFileSize(maxFileSize)}</p>
              <p>Optimized for files up to 500MB with 2M+ records</p>
            </div>
          </div>

          <Button size="lg" className="mt-4">
            <FileText className="h-4 w-4 mr-2" />
            Select Files
          </Button>
        </div>

        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileInputChange}
          className="hidden"
        />
      </CardContent>
    </Card>
  );

  const renderUploadProgress = (progress: UploadProgress) => {
    const validationResult = validationResults.get(progress.uploadId);
    const processingPreview = processingPreviews.get(progress.uploadId);
    const uploadPercent = progress.fileSize > 0 ? (progress.uploadedBytes / progress.fileSize) * 100 : 0;

    return (
      <Card key={progress.uploadId} className="border border-gray-200">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon(progress.status)}
              <div>
                <CardTitle className="text-sm font-medium truncate max-w-xs">
                  {progress.fileName}
                </CardTitle>
                <p className="text-xs text-gray-500">
                  {formatFileSize(progress.fileSize)} • {progress.totalChunks} chunks
                </p>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Badge variant="outline" className={getStatusColor(progress.status)}>
                {progress.status}
              </Badge>
              
              {progress.status === 'uploading' && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleUploadAction(progress.uploadId, 'pause')}
                >
                  <Pause className="h-3 w-3" />
                </Button>
              )}
              
              {progress.status === 'paused' && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleUploadAction(progress.uploadId, 'resume')}
                >
                  <Play className="h-3 w-3" />
                </Button>
              )}
              
              {['uploading', 'paused', 'failed'].includes(progress.status) && (
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => handleUploadAction(progress.uploadId, 'cancel')}
                >
                  <Trash2 className="h-3 w-3" />
                </Button>
              )}
            </div>
          </div>
        </CardHeader>

        <CardContent>
          {/* Upload Progress */}
          <div className="space-y-3">
            <div className="flex justify-between text-sm">
              <span>Upload Progress</span>
              <span>{uploadPercent.toFixed(1)}%</span>
            </div>
            <Progress value={uploadPercent} className="h-2" />
            
            {progress.status === 'uploading' && (
              <div className="grid grid-cols-2 gap-4 text-xs text-gray-600">
                <div>Speed: {formatSpeed(progress.uploadSpeed)}</div>
                <div>ETA: {formatTime(progress.estimatedTimeRemaining)}</div>
                <div>Chunks: {progress.uploadedChunks}/{progress.totalChunks}</div>
                <div>In Progress: {progress.chunksInProgress.length}</div>
              </div>
            )}

            {progress.failedChunks.length > 0 && (
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription>
                  {progress.failedChunks.length} chunks failed. Retrying automatically...
                </AlertDescription>
              </Alert>
            )}
          </div>

          {/* Validation Results */}
          {validationResult && (
            <div className="mt-4 p-4 bg-gray-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <Database className="h-4 w-4" />
                File Validation Results
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>Type: {validationResult.fileType.toUpperCase()}</div>
                <div>Encoding: {validationResult.encoding}</div>
                <div>Estimated Records: {validationResult.estimatedRecords.toLocaleString()}</div>
                <div>Memory Required: {validationResult.memoryEstimate} MB</div>
              </div>
              
              {validationResult.warnings.length > 0 && (
                <Alert className="mt-3">
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    {validationResult.warnings.length} warnings found. Check data quality.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}

          {/* Processing Preview */}
          {processingPreview && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-sm mb-2 flex items-center gap-2">
                <Zap className="h-4 w-4" />
                Processing Preview
              </h4>
              <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                <div>Valid Records: {processingPreview.validRecords.toLocaleString()}</div>
                <div>Invalid Records: {processingPreview.invalidRecords.toLocaleString()}</div>
                <div>Est. Time: {processingPreview.estimatedProcessingTime} min</div>
                <div>Credits Required: {processingPreview.estimatedCredits}</div>
              </div>
              
              {progress.status === 'completed' && (
                <Button
                  size="sm"
                  onClick={() => handleStartProcessing(progress.uploadId)}
                  className="w-full"
                >
                  Start AI Discovery Processing
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    );
  };

  // =============================================================================
  // MAIN RENDER
  // =============================================================================

  return (
    <div className={className}>
      {renderSystemMetrics()}
      
      {activeUploads.size === 0 ? (
        renderDropZone()
      ) : (
        <div className="space-y-4">
          {renderDropZone()}
          
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Active Uploads</h3>
            {Array.from(activeUploads.values()).map(renderUploadProgress)}
          </div>
        </div>
      )}
    </div>
  );
};

// =============================================================================
// STREAMING UPLOAD MANAGER CLASS
// =============================================================================

class StreamingUploadManager {
  private activeUploads = new Map<string, UploadProgress>();
  private progressCallbacks: ((progress: UploadProgress) => void)[] = [];
  private validationCallbacks: ((uploadId: string, result: FileValidationResult) => void)[] = [];
  private previewCallbacks: ((uploadId: string, preview: ProcessingPreview) => void)[] = [];
  
  constructor(private config: ChunkedUploadConfig) {}

  async startUpload(file: File): Promise<string> {
    const uploadId = this.generateUploadId();
    
    const progress: UploadProgress = {
      uploadId,
      fileName: file.name,
      fileSize: file.size,
      uploadedBytes: 0,
      uploadedChunks: 0,
      totalChunks: Math.ceil(file.size / this.config.chunkSize),
      uploadSpeed: 0,
      estimatedTimeRemaining: 0,
      status: 'preparing',
      canResume: true,
      startTime: new Date(),
      lastUpdateTime: new Date(),
      chunksInProgress: [],
      failedChunks: []
    };

    this.activeUploads.set(uploadId, progress);
    this.notifyProgress(progress);

    // Start the upload process
    this.processUpload(file, uploadId);
    
    return uploadId;
  }

  private async processUpload(file: File, uploadId: string): Promise<void> {
    try {
      // Step 1: Validate file
      const validationResult = await this.validateFile(file);
      this.validationCallbacks.forEach(cb => cb(uploadId, validationResult));

      if (!validationResult.isValid) {
        this.updateUploadStatus(uploadId, 'failed', validationResult.errors.join(', '));
        return;
      }

      // Step 2: Start chunked upload
      this.updateUploadStatus(uploadId, 'uploading');
      await this.uploadInChunks(file, uploadId);

      // Step 3: Generate processing preview
      const preview = await this.generateProcessingPreview(validationResult);
      this.previewCallbacks.forEach(cb => cb(uploadId, preview));

      this.updateUploadStatus(uploadId, 'completed');
    } catch (error) {
      this.updateUploadStatus(uploadId, 'failed', (error as Error).message);
    }
  }

  private async validateFile(file: File): Promise<FileValidationResult> {
    // Simulate file validation
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      isValid: true,
      fileType: 'csv',
      encoding: 'UTF-8',
      estimatedRecords: Math.floor(file.size / 100), // Rough estimate
      headerRow: ['Name', 'Address', 'State', 'Amount'],
      sampleData: [
        ['John Doe', '123 Main St', 'CA', '$1,000'],
        ['Jane Smith', '456 Oak Ave', 'TX', '$2,500']
      ],
      warnings: [],
      errors: [],
      memoryEstimate: Math.ceil(file.size / (1024 * 1024) * 1.5) // 1.5x file size
    };
  }

  private async uploadInChunks(file: File, uploadId: string): Promise<void> {
    const progress = this.activeUploads.get(uploadId);
    if (!progress) return;

    // Simulate chunked upload with progress updates
    for (let i = 0; i < progress.totalChunks; i++) {
      if (progress.status === 'cancelled') break;
      
      while (progress.status === 'paused') {
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Simulate chunk upload
      await new Promise(resolve => setTimeout(resolve, 200 + Math.random() * 300));

      progress.uploadedChunks++;
      progress.uploadedBytes = Math.min(
        progress.uploadedBytes + this.config.chunkSize,
        progress.fileSize
      );
      progress.uploadSpeed = this.calculateUploadSpeed(progress);
      progress.estimatedTimeRemaining = this.calculateETA(progress);
      progress.lastUpdateTime = new Date();

      this.notifyProgress(progress);
    }
  }

  private async generateProcessingPreview(validation: FileValidationResult): Promise<ProcessingPreview> {
    return {
      totalRecords: validation.estimatedRecords,
      validRecords: Math.floor(validation.estimatedRecords * 0.95),
      invalidRecords: Math.floor(validation.estimatedRecords * 0.05),
      duplicateRecords: Math.floor(validation.estimatedRecords * 0.02),
      estimatedProcessingTime: Math.ceil(validation.estimatedRecords / 1000), // 1 minute per 1000 records
      estimatedCredits: Math.ceil(validation.estimatedRecords * 0.1), // 0.1 credit per record
      suggestedBatchSize: Math.min(1000, Math.ceil(validation.estimatedRecords / 10)),
      memoryRequired: validation.memoryEstimate,
      processingPlan: [
        {
          step: 'Data Validation',
          description: 'Validate and clean record data',
          estimatedTime: 30,
          resourceUsage: 'low',
          canOptimize: true
        },
        {
          step: 'AI Discovery',
          description: 'Execute AI-powered asset discovery',
          estimatedTime: validation.estimatedRecords * 0.05, // 50ms per record
          resourceUsage: 'high',
          canOptimize: false
        },
        {
          step: 'Results Processing',
          description: 'Process and rank discovery results',
          estimatedTime: 60,
          resourceUsage: 'medium',
          canOptimize: true
        }
      ]
    };
  }

  // Utility methods
  private generateUploadId(): string {
    return `upload_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private updateUploadStatus(uploadId: string, status: UploadProgress['status'], errorMessage?: string): void {
    const progress = this.activeUploads.get(uploadId);
    if (progress) {
      progress.status = status;
      if (errorMessage) progress.errorMessage = errorMessage;
      this.notifyProgress(progress);
    }
  }

  private calculateUploadSpeed(progress: UploadProgress): number {
    const elapsedTime = (Date.now() - progress.startTime.getTime()) / 1000;
    return elapsedTime > 0 ? progress.uploadedBytes / elapsedTime : 0;
  }

  private calculateETA(progress: UploadProgress): number {
    const remainingBytes = progress.fileSize - progress.uploadedBytes;
    return progress.uploadSpeed > 0 ? remainingBytes / progress.uploadSpeed : 0;
  }

  private notifyProgress(progress: UploadProgress): void {
    this.progressCallbacks.forEach(cb => cb(progress));
  }

  // Public interface methods
  onProgress(callback: (progress: UploadProgress) => void): void {
    this.progressCallbacks.push(callback);
  }

  onValidationComplete(callback: (uploadId: string, result: FileValidationResult) => void): void {
    this.validationCallbacks.push(callback);
  }

  onProcessingPreview(callback: (uploadId: string, preview: ProcessingPreview) => void): void {
    this.previewCallbacks.push(callback);
  }

  async pauseUpload(uploadId: string): Promise<void> {
    this.updateUploadStatus(uploadId, 'paused');
  }

  async resumeUpload(uploadId: string): Promise<void> {
    this.updateUploadStatus(uploadId, 'uploading');
  }

  async cancelUpload(uploadId: string): Promise<void> {
    this.updateUploadStatus(uploadId, 'cancelled');
    this.activeUploads.delete(uploadId);
  }

  getCurrentMemoryUsage(): number {
    if (typeof window !== 'undefined' && window.performance && window.performance.memory) {
      return window.performance.memory.usedJSHeapSize / 1024 / 1024;
    }
    return 0;
  }

  getActiveUploadCount(): number {
    return Array.from(this.activeUploads.values()).filter(u => 
      ['preparing', 'uploading'].includes(u.status)
    ).length;
  }

  getAverageUploadSpeed(): number {
    const activeUploads = Array.from(this.activeUploads.values()).filter(u => 
      u.status === 'uploading'
    );
    if (activeUploads.length === 0) return 0;
    
    const totalSpeed = activeUploads.reduce((sum, upload) => sum + upload.uploadSpeed, 0);
    return totalSpeed / activeUploads.length;
  }

  cleanup(): void {
    this.activeUploads.clear();
    this.progressCallbacks.length = 0;
    this.validationCallbacks.length = 0;
    this.previewCallbacks.length = 0;
  }
} 