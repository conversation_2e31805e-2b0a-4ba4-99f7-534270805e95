import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { 
  Search,
  Filter,
  SortAsc,
  SortDesc,
  Users,
  DollarSign,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Star,
  AlertCircle,
  CheckCircle,
  Clock,
  Eye,
  Bot,
  ChevronRight
} from 'lucide-react';

interface Lead {
  id: string;
  owner_name: string;
  amount: number;
  state: string;
  property_type: string;
  status: 'not_started' | 'in_progress' | 'contacted' | 'completed' | 'on_hold';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assignment_date: string;
  last_activity: string | null;
  contact_attempts: number;
  ai_search_status: 'not_searched' | 'searching' | 'results_available' | 'completed';
  contact_info: {
    email?: string;
    phone?: string;
    address?: string;
  };
  city?: string;
  estimated_value?: number;
  data_quality_score: number;
}

interface LeadListViewProps {
  agentId: string;
  userRole: string;
  onLeadSelect: (leadId: string) => void;
  onLeadDetailView: (leadId: string) => void;
  onAISearchInitiate: (leadIds: string[]) => void;
}

export default function LeadListView({ 
  agentId, 
  userRole, 
  onLeadSelect, 
  onLeadDetailView, 
  onAISearchInitiate 
}: LeadListViewProps) {
  const [leads, setLeads] = useState<Lead[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedLeads, setSelectedLeads] = useState<Set<string>>(new Set());
  const [searchTerm, setSearchTerm] = useState('');
  const [sortField, setSortField] = useState<keyof Lead>('assignment_date');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterPriority, setFilterPriority] = useState<string>('all');
  const [filterState, setFilterState] = useState<string>('all');
  const [filterValueRange, setFilterValueRange] = useState<string>('all');

  // Mock data - in real app, this would come from API
  useEffect(() => {
    const mockLeads: Lead[] = [
      {
        id: '1',
        owner_name: 'John Smith',
        amount: 75000,
        state: 'CA',
        property_type: 'real_estate',
        status: 'not_started',
        priority: 'high',
        assignment_date: '2024-01-15T10:00:00Z',
        last_activity: null,
        contact_attempts: 0,
        ai_search_status: 'not_searched',
        contact_info: {},
        city: 'Los Angeles',
        estimated_value: 75000,
        data_quality_score: 0.85
      },
      {
        id: '2',
        owner_name: 'Sarah Johnson',
        amount: 45000,
        state: 'TX',
        property_type: 'bank_account',
        status: 'in_progress',
        priority: 'medium',
        assignment_date: '2024-01-14T14:30:00Z',
        last_activity: '2024-01-15T09:15:00Z',
        contact_attempts: 2,
        ai_search_status: 'results_available',
        contact_info: {
          phone: '(*************',
          email: '<EMAIL>'
        },
        city: 'Houston',
        estimated_value: 45000,
        data_quality_score: 0.92
      },
      {
        id: '3',
        owner_name: 'Michael Rodriguez',
        amount: 25000,
        state: 'FL',
        property_type: 'insurance',
        status: 'contacted',
        priority: 'low',
        assignment_date: '2024-01-13T16:45:00Z',
        last_activity: '2024-01-15T11:30:00Z',
        contact_attempts: 1,
        ai_search_status: 'completed',
        contact_info: {
          phone: '(*************',
          address: '789 Palm Dr, Miami, FL'
        },
        city: 'Miami',
        estimated_value: 25000,
        data_quality_score: 0.78
      },
      {
        id: '4',
        owner_name: 'Emily Chen',
        amount: 120000,
        state: 'NY',
        property_type: 'stocks',
        status: 'not_started',
        priority: 'urgent',
        assignment_date: '2024-01-15T08:00:00Z',
        last_activity: null,
        contact_attempts: 0,
        ai_search_status: 'not_searched',
        contact_info: {},
        city: 'New York',
        estimated_value: 120000,
        data_quality_score: 0.65
      },
      {
        id: '5',
        owner_name: 'David Wilson',
        amount: 35000,
        state: 'IL',
        property_type: 'utilities',
        status: 'on_hold',
        priority: 'medium',
        assignment_date: '2024-01-12T13:20:00Z',
        last_activity: '2024-01-14T16:45:00Z',
        contact_attempts: 3,
        ai_search_status: 'searching',
        contact_info: {
          email: '<EMAIL>'
        },
        city: 'Chicago',
        estimated_value: 35000,
        data_quality_score: 0.88
      }
    ];

    setTimeout(() => {
      setLeads(mockLeads);
      setLoading(false);
    }, 1000);
  }, [agentId]);

  // Filtering and sorting logic
  const filteredAndSortedLeads = useMemo(() => {
    let filtered = leads.filter(lead => {
      const matchesSearch = !searchTerm || 
        lead.owner_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.state.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.city?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        lead.property_type.toLowerCase().includes(searchTerm.toLowerCase());

      const matchesStatus = filterStatus === 'all' || lead.status === filterStatus;
      const matchesPriority = filterPriority === 'all' || lead.priority === filterPriority;
      const matchesState = filterState === 'all' || lead.state === filterState;
      
      const matchesValueRange = filterValueRange === 'all' || (() => {
        switch (filterValueRange) {
          case 'under_25k': return lead.amount < 25000;
          case '25k_50k': return lead.amount >= 25000 && lead.amount < 50000;
          case '50k_100k': return lead.amount >= 50000 && lead.amount < 100000;
          case 'over_100k': return lead.amount >= 100000;
          default: return true;
        }
      })();

      return matchesSearch && matchesStatus && matchesPriority && matchesState && matchesValueRange;
    });

    // Sort results
    filtered.sort((a, b) => {
      let aValue = a[sortField];
      let bValue = b[sortField];

      // Handle null/undefined values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortDirection === 'asc' ? -1 : 1;
      if (bValue == null) return sortDirection === 'asc' ? 1 : -1;

      // Handle different data types
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
      return 0;
    });

    return filtered;
  }, [leads, searchTerm, sortField, sortDirection, filterStatus, filterPriority, filterState, filterValueRange]);

  // Selection handlers
  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedLeads(new Set(filteredAndSortedLeads.map(lead => lead.id)));
    } else {
      setSelectedLeads(new Set());
    }
  };

  const handleLeadSelection = (leadId: string, checked: boolean) => {
    const newSelection = new Set(selectedLeads);
    if (checked) {
      newSelection.add(leadId);
    } else {
      newSelection.delete(leadId);
    }
    setSelectedLeads(newSelection);
    onLeadSelect(leadId);
  };

  const handleSort = (field: keyof Lead) => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
  };

  // Status and priority helpers
  const getStatusColor = (status: Lead['status']) => {
    switch (status) {
      case 'not_started': return 'bg-gray-100 text-gray-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'contacted': return 'bg-green-100 text-green-800';
      case 'completed': return 'bg-emerald-100 text-emerald-800';
      case 'on_hold': return 'bg-yellow-100 text-yellow-800';
    }
  };

  const getPriorityColor = (priority: Lead['priority']) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-600';
      case 'medium': return 'bg-blue-100 text-blue-600';
      case 'high': return 'bg-orange-100 text-orange-600';
      case 'urgent': return 'bg-red-100 text-red-600';
    }
  };

  const getAISearchStatusIcon = (status: Lead['ai_search_status']) => {
    switch (status) {
      case 'not_searched': return <AlertCircle className="h-4 w-4 text-gray-400" />;
      case 'searching': return <Bot className="h-4 w-4 text-blue-500 animate-pulse" />;
      case 'results_available': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'completed': return <CheckCircle className="h-4 w-4 text-emerald-500" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatTimeAgo = (dateString: string) => {
    const now = new Date();
    const date = new Date(dateString);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays}d ago`;
    return formatDate(dateString);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading your assigned leads...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Summary Stats */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">My Assigned Leads</h2>
          <p className="text-gray-600">
            {filteredAndSortedLeads.length} of {leads.length} leads • 
            {selectedLeads.size} selected
          </p>
        </div>
        
        {/* Quick Stats */}
        <div className="flex space-x-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {leads.filter(l => l.status === 'not_started').length}
            </div>
            <div className="text-sm text-gray-500">Not Started</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {leads.filter(l => l.ai_search_status === 'results_available').length}
            </div>
            <div className="text-sm text-gray-500">Ready to Contact</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {formatCurrency(leads.reduce((sum, l) => sum + l.amount, 0))}
            </div>
            <div className="text-sm text-gray-500">Total Value</div>
          </div>
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-6 gap-4">
            {/* Search */}
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search leads by name, state, city..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            {/* Status Filter */}
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger>
                <SelectValue placeholder="All Statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="not_started">Not Started</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="on_hold">On Hold</SelectItem>
              </SelectContent>
            </Select>

            {/* Priority Filter */}
            <Select value={filterPriority} onValueChange={setFilterPriority}>
              <SelectTrigger>
                <SelectValue placeholder="All Priorities" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priorities</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>

            {/* State Filter */}
            <Select value={filterState} onValueChange={setFilterState}>
              <SelectTrigger>
                <SelectValue placeholder="All States" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                <SelectItem value="CA">California</SelectItem>
                <SelectItem value="TX">Texas</SelectItem>
                <SelectItem value="FL">Florida</SelectItem>
                <SelectItem value="NY">New York</SelectItem>
                <SelectItem value="IL">Illinois</SelectItem>
              </SelectContent>
            </Select>

            {/* Value Range Filter */}
            <Select value={filterValueRange} onValueChange={setFilterValueRange}>
              <SelectTrigger>
                <SelectValue placeholder="All Values" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Values</SelectItem>
                <SelectItem value="under_25k">Under $25K</SelectItem>
                <SelectItem value="25k_50k">$25K - $50K</SelectItem>
                <SelectItem value="50k_100k">$50K - $100K</SelectItem>
                <SelectItem value="over_100k">Over $100K</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Bulk Actions */}
          {selectedLeads.size > 0 && (
            <div className="mt-4 p-4 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-800">
                {selectedLeads.size} lead{selectedLeads.size !== 1 ? 's' : ''} selected
              </span>
              <div className="flex space-x-2">
                <Button
                  size="sm"
                  onClick={() => onAISearchInitiate(Array.from(selectedLeads))}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Bot className="h-4 w-4 mr-2" />
                  Start AI Search ({selectedLeads.size})
                </Button>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => setSelectedLeads(new Set())}
                >
                  Clear Selection
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Leads Table */}
      <Card>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-4 py-3 text-left">
                    <Checkbox
                      checked={selectedLeads.size === filteredAndSortedLeads.length && filteredAndSortedLeads.length > 0}
                      onCheckedChange={handleSelectAll}
                    />
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('owner_name')}
                  >
                    <div className="flex items-center">
                      Name
                      {sortField === 'owner_name' && (
                        sortDirection === 'asc' ? <SortAsc className="ml-1 h-3 w-3" /> : <SortDesc className="ml-1 h-3 w-3" />
                      )}
                    </div>
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('amount')}
                  >
                    <div className="flex items-center">
                      Value
                      {sortField === 'amount' && (
                        sortDirection === 'asc' ? <SortAsc className="ml-1 h-3 w-3" /> : <SortDesc className="ml-1 h-3 w-3" />
                      )}
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Location
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Priority
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    AI Search
                  </th>
                  <th 
                    className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSort('assignment_date')}
                  >
                    <div className="flex items-center">
                      Assigned
                      {sortField === 'assignment_date' && (
                        sortDirection === 'asc' ? <SortAsc className="ml-1 h-3 w-3" /> : <SortDesc className="ml-1 h-3 w-3" />
                      )}
                    </div>
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredAndSortedLeads.map((lead) => (
                  <tr key={lead.id} className="hover:bg-gray-50">
                    <td className="px-4 py-4">
                      <Checkbox
                        checked={selectedLeads.has(lead.id)}
                        onCheckedChange={(checked) => handleLeadSelection(lead.id, checked as boolean)}
                      />
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <div className="flex-shrink-0 h-10 w-10">
                          <div className="h-10 w-10 rounded-full bg-gray-200 flex items-center justify-center">
                            <Users className="h-5 w-5 text-gray-500" />
                          </div>
                        </div>
                        <div className="ml-4">
                          <div className="text-sm font-medium text-gray-900">{lead.owner_name}</div>
                          <div className="text-sm text-gray-500">{lead.property_type.replace('_', ' ')}</div>
                        </div>
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="text-sm font-medium text-gray-900">{formatCurrency(lead.amount)}</div>
                      <div className="flex items-center text-xs text-gray-500">
                        <div className={`w-2 h-2 rounded-full mr-1 ${
                          lead.data_quality_score > 0.8 ? 'bg-green-400' : 
                          lead.data_quality_score > 0.6 ? 'bg-yellow-400' : 'bg-red-400'
                        }`}></div>
                        {Math.round(lead.data_quality_score * 100)}% quality
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center text-sm text-gray-900">
                        <MapPin className="h-3 w-3 mr-1 text-gray-400" />
                        {lead.city}, {lead.state}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <Badge className={getStatusColor(lead.status)}>
                        {lead.status.replace('_', ' ')}
                      </Badge>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        <Badge className={getPriorityColor(lead.priority)}>
                          {lead.priority}
                        </Badge>
                        {lead.priority === 'urgent' && <Star className="h-3 w-3 ml-1 text-red-500" />}
                      </div>
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center">
                        {getAISearchStatusIcon(lead.ai_search_status)}
                        <span className="ml-2 text-xs text-gray-500">
                          {lead.ai_search_status.replace('_', ' ')}
                        </span>
                      </div>
                    </td>
                    <td className="px-4 py-4 text-sm text-gray-500">
                      <div>{formatDate(lead.assignment_date)}</div>
                      {lead.last_activity && (
                        <div className="text-xs text-gray-400">
                          Active {formatTimeAgo(lead.last_activity)}
                        </div>
                      )}
                    </td>
                    <td className="px-4 py-4">
                      <div className="flex items-center space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => onLeadDetailView(lead.id)}
                        >
                          <Eye className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        {lead.ai_search_status === 'not_searched' && (
                          <Button
                            size="sm"
                            onClick={() => onAISearchInitiate([lead.id])}
                            className="bg-blue-600 hover:bg-blue-700"
                          >
                            <Bot className="h-3 w-3 mr-1" />
                            Search
                          </Button>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {filteredAndSortedLeads.length === 0 && (
            <div className="text-center py-12">
              <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No leads found</h3>
              <p className="text-gray-500">
                {searchTerm || filterStatus !== 'all' || filterPriority !== 'all' || filterState !== 'all' || filterValueRange !== 'all'
                  ? 'Try adjusting your filters or search terms'
                  : 'You don\'t have any assigned leads yet. Check with your administrator.'}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 