import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { useWidgetData } from '@/hooks/useWidgetData';
import { ClaimsPipelineData, ClaimStage } from '@/types/dashboard';
import { 
  GitBranch, 
  TrendingUp, 
  TrendingDown, 
  Minus, 
  DollarSign, 
  Clock, 
  Filter,
  RefreshCw,
  BarChart3,
  ArrowRight,
  AlertCircle,
  CheckCircle
} from 'lucide-react';

interface ClaimsPipelineProps {
  widgetId: string;
  className?: string;
}

export const ClaimsPipeline: React.FC<ClaimsPipelineProps> = ({ 
  widgetId, 
  className 
}) => {
  const { data, isLoading, error, refreshData } = useWidgetData<ClaimsPipelineData>(
    widgetId, 
    'claims-pipeline'
  );
  const [viewMode, setViewMode] = useState<'summary' | 'detailed'>('summary');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshData();
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Claims Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-3 bg-gray-200 rounded w-1/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-full mb-1"></div>
                <div className="h-2 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data?.data) {
    return (
      <Card className={className}>
        <CardHeader className="pb-3">
          <CardTitle className="text-sm font-medium">Claims Pipeline</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <GitBranch className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Failed to load pipeline data</p>
            <p className="text-xs text-gray-400">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const pipeline = data.data;

  const formatValue = (value: number): string => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `$${(value / 1000).toFixed(0)}K`;
    return `$${value.toLocaleString()}`;
  };

  const getTrendIcon = (trend: ClaimStage['trend']) => {
    switch (trend) {
      case 'up':
        return <TrendingUp className="h-3 w-3 text-green-600" />;
      case 'down':
        return <TrendingDown className="h-3 w-3 text-red-600" />;
      case 'stable':
        return <Minus className="h-3 w-3 text-gray-600" />;
      default:
        return <Minus className="h-3 w-3 text-gray-600" />;
    }
  };

  const getTrendColor = (trend: ClaimStage['trend']) => {
    switch (trend) {
      case 'up':
        return 'text-green-600';
      case 'down':
        return 'text-red-600';
      case 'stable':
        return 'text-gray-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStageIcon = (stage: string) => {
    switch (stage.toLowerCase()) {
      case 'discovery':
        return '🔍';
      case 'contact':
        return '📞';
      case 'verification':
        return '✅';
      case 'documentation':
        return '📄';
      case 'completed':
        return '🎉';
      default:
        return '📋';
    }
  };

  const totalActive = pipeline.pending + pipeline.inProgress + pipeline.underReview + pipeline.awaitingClient;
  const totalClaims = totalActive + pipeline.completed;
  const progressPercentage = totalClaims > 0 ? (pipeline.completed / totalClaims) * 100 : 0;

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <GitBranch className="h-4 w-4" />
            Claims Pipeline
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setViewMode(viewMode === 'summary' ? 'detailed' : 'summary')}
              className="h-6 text-xs"
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              {viewMode === 'summary' ? 'Details' : 'Summary'}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="h-6 w-6 p-0"
            >
              <RefreshCw className={`h-3 w-3 ${isRefreshing ? 'animate-spin' : ''}`} />
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Summary Overview */}
        <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
          <div className="flex items-center justify-between mb-3">
            <div className="text-sm font-medium text-gray-700">Pipeline Progress</div>
            <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700 border-blue-200">
              {totalActive} Active
            </Badge>
          </div>
          <div className="space-y-2">
            <div className="flex justify-between text-xs">
              <span className="text-gray-600">Completed vs Total</span>
              <span className="font-medium">
                {pipeline.completed}/{totalClaims} ({progressPercentage.toFixed(1)}%)
              </span>
            </div>
            <Progress value={progressPercentage} className="w-full h-2" />
          </div>
        </div>

        {viewMode === 'summary' ? (
          /* Summary View */
          <div className="space-y-3">
            {/* Quick Stats Grid */}
            <div className="grid grid-cols-2 gap-3">
              <div className="bg-yellow-50 rounded-lg p-3 border border-yellow-200">
                <div className="text-lg font-bold text-yellow-800">{pipeline.pending}</div>
                <div className="text-xs text-yellow-600">Pending</div>
              </div>
              <div className="bg-blue-50 rounded-lg p-3 border border-blue-200">
                <div className="text-lg font-bold text-blue-800">{pipeline.inProgress}</div>
                <div className="text-xs text-blue-600">In Progress</div>
              </div>
              <div className="bg-orange-50 rounded-lg p-3 border border-orange-200">
                <div className="text-lg font-bold text-orange-800">{pipeline.underReview}</div>
                <div className="text-xs text-orange-600">Under Review</div>
              </div>
              <div className="bg-purple-50 rounded-lg p-3 border border-purple-200">
                <div className="text-lg font-bold text-purple-800">{pipeline.awaitingClient}</div>
                <div className="text-xs text-purple-600">Awaiting Client</div>
              </div>
            </div>

            {/* Completed Claims */}
            <div className="bg-green-50 rounded-lg p-3 border border-green-200">
              <div className="flex items-center justify-between">
                <div>
                  <div className="text-xl font-bold text-green-800">{pipeline.completed}</div>
                  <div className="text-xs text-green-600">Completed Claims</div>
                </div>
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        ) : (
          /* Detailed View */
          <div className="space-y-3">
            {pipeline.pipeline.map((stage, index) => (
              <div 
                key={stage.stage}
                className="bg-white rounded-lg p-3 border border-gray-200 hover:border-gray-300 transition-colors"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <span className="text-lg">{getStageIcon(stage.stage)}</span>
                    <h4 className="text-sm font-medium text-gray-800">{stage.stage}</h4>
                    <Badge 
                      variant="outline" 
                      className="text-xs"
                      style={{
                        backgroundColor: index === 0 ? '#fef3c7' : 
                                       index === 1 ? '#dbeafe' : 
                                       index === 2 ? '#fed7aa' : 
                                       index === 3 ? '#e9d5ff' : '#dcfce7',
                        color: index === 0 ? '#92400e' : 
                               index === 1 ? '#1e40af' : 
                               index === 2 ? '#c2410c' : 
                               index === 3 ? '#7c3aed' : '#166534'
                      }}
                    >
                      {stage.count} claims
                    </Badge>
                  </div>
                  <div className={`flex items-center gap-1 ${getTrendColor(stage.trend)}`}>
                    {getTrendIcon(stage.trend)}
                  </div>
                </div>

                <div className="grid grid-cols-3 gap-4 text-xs">
                  <div>
                    <span className="text-gray-500">Total Value</span>
                    <div className="font-medium text-gray-900">{formatValue(stage.value)}</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Avg Days</span>
                    <div className="font-medium text-gray-900">{stage.averageDays}d</div>
                  </div>
                  <div>
                    <span className="text-gray-500">Avg Value</span>
                    <div className="font-medium text-gray-900">
                      {stage.count > 0 ? formatValue(stage.value / stage.count) : '$0'}
                    </div>
                  </div>
                </div>

                {/* Progress bar for stage */}
                <div className="mt-2">
                  <Progress 
                    value={totalClaims > 0 ? (stage.count / totalClaims) * 100 : 0} 
                    className="w-full h-1"
                  />
                </div>

                {/* Stage Actions */}
                {index < pipeline.pipeline.length - 1 && (
                  <div className="mt-2 flex justify-end">
                    <Button size="sm" variant="ghost" className="h-5 text-xs text-gray-500">
                      View Details
                      <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                )}
              </div>
            ))}
          </div>
        )}

        {/* Pipeline Health */}
        <div className="bg-gray-50 rounded-lg p-3 border border-gray-100">
          <h4 className="text-xs font-medium text-gray-700 mb-2 flex items-center gap-1">
            <AlertCircle className="h-3 w-3" />
            Pipeline Health
          </h4>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-500">Total Value in Pipeline</span>
              <div className="font-medium text-gray-900">
                {formatValue(pipeline.pipeline.reduce((sum, stage) => sum + stage.value, 0))}
              </div>
            </div>
            <div>
              <span className="text-gray-500">Avg Processing Time</span>
              <div className="font-medium text-gray-900">
                {Math.round(pipeline.pipeline.reduce((sum, stage) => sum + stage.averageDays, 0) / pipeline.pipeline.length)}d
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="grid grid-cols-2 gap-2 pt-2 border-t border-gray-100">
          <Button size="sm" variant="outline" className="h-7 text-xs">
            <Filter className="h-3 w-3 mr-1" />
            Filter Pipeline
          </Button>
          <Button size="sm" variant="outline" className="h-7 text-xs">
            <DollarSign className="h-3 w-3 mr-1" />
            Value Analysis
          </Button>
        </div>

        {/* Last Updated */}
        <div className="pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-400 text-center">
            Last updated: {data.lastUpdated.toLocaleTimeString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 