/**
 * 🚀 QUICK FUNCTION TEST RUNNER
 * =============================
 * 
 * This script provides an immediate test of the most critical functions
 * in the AssetHunterPro application to quickly identify what needs fixing.
 * 
 * Usage: Run in browser console at http://localhost:3000
 */

console.log(`
🚀 QUICK FUNCTION TEST RUNNER
=============================

Running immediate tests on critical functions...
This will give you a quick overview of what needs fixing.

Timestamp: ${new Date().toISOString()}
`);

// Quick test results
let quickResults = {
  tested: 0,
  passed: 0,
  failed: 0,
  issues: []
};

function quickTest(name, testFn, fix = '') {
  try {
    const result = testFn();
    quickResults.tested++;
    
    if (result) {
      quickResults.passed++;
      console.log(`✅ ${name}: PASS`);
    } else {
      quickResults.failed++;
      quickResults.issues.push({ name, fix });
      console.log(`❌ ${name}: FAIL - ${fix}`);
    }
  } catch (error) {
    quickResults.failed++;
    quickResults.issues.push({ name, fix: `Error: ${error.message}` });
    console.log(`❌ ${name}: ERROR - ${error.message}`);
  }
}

async function runQuickTests() {
  console.log('\n🔍 TESTING CRITICAL FUNCTIONS...\n');
  
  // 1. Browser Environment
  quickTest('Browser localStorage', 
    () => {
      localStorage.setItem('test', 'ok');
      const result = localStorage.getItem('test') === 'ok';
      localStorage.removeItem('test');
      return result;
    },
    'Enable localStorage in browser'
  );
  
  quickTest('Browser fetch API', 
    () => typeof fetch === 'function',
    'Use modern browser with fetch support'
  );
  
  quickTest('Browser Promise support', 
    () => typeof Promise === 'function',
    'Use modern browser with Promise support'
  );
  
  // 2. Development Server
  quickTest('Development server port',
    () => window.location.port === '3005',
    'Start frontend dev server on port 3005'
  );
  
  quickTest('Development server protocol', 
    () => window.location.protocol === 'http:',
    'Use http:// protocol for development'
  );
  
  // 3. Backend API (async test)
  console.log('🔍 Testing backend API...');
  try {
    const response = await fetch('http://localhost:3001/health');
    quickTest('Backend health endpoint',
      () => response.ok,
      'Start backend server: cd backend && npm run dev'
    );
  } catch (error) {
    quickResults.failed++;
    quickResults.issues.push({ 
      name: 'Backend health endpoint', 
      fix: 'Start backend server: cd backend && npm run dev' 
    });
    console.log(`❌ Backend health endpoint: FAIL - ${error.message}`);
  }
  
  // 4. React/UI Framework
  quickTest('React components detected',
    () => document.querySelector('[data-reactroot]') !== null || 
          document.querySelector('[class*="react"]') !== null,
    'Ensure React app is properly loaded'
  );
  
  quickTest('CSS styles loaded',
    () => document.querySelectorAll('link[rel="stylesheet"]').length > 0 ||
          document.querySelectorAll('style').length > 0,
    'Check CSS/Tailwind loading'
  );
  
  // 5. Essential Services (check if available in window)
  const essentialServices = [
    'supabase',
    'productionDataService', 
    'agentAISearchService',
    'aiDiscoveryEngine'
  ];
  
  essentialServices.forEach(service => {
    quickTest(`Service: ${service}`,
      () => window[service] !== undefined || 
            window.assetHunterPro?.[service] !== undefined,
      `Import and initialize ${service}`
    );
  });
  
  // 6. Database Connection (if Supabase is available)
  if (window.supabase) {
    console.log('🔍 Testing database connection...');
    try {
      const { data, error } = await window.supabase.from('claims').select('id').limit(1);
      quickTest('Database connection',
        () => !error,
        'Check Supabase configuration and permissions'
      );
    } catch (error) {
      quickResults.failed++;
      quickResults.issues.push({ 
        name: 'Database connection', 
        fix: 'Configure Supabase URL and API key' 
      });
      console.log(`❌ Database connection: ERROR - ${error.message}`);
    }
  }
  
  // 7. Data Validation Functions
  quickTest('Email validation',
    () => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test('<EMAIL>'),
    'Fix email validation regex'
  );
  
  quickTest('Phone validation',
    () => /^\+?[\d\s\-\(\)]{10,}$/.test('************'),
    'Fix phone validation regex'
  );
  
  // Generate Quick Report
  generateQuickReport();
}

function generateQuickReport() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 QUICK TEST RESULTS');
  console.log('='.repeat(60));
  
  const successRate = ((quickResults.passed / quickResults.tested) * 100).toFixed(1);
  const failureRate = ((quickResults.failed / quickResults.tested) * 100).toFixed(1);
  
  console.log(`
✅ Passed: ${quickResults.passed}/${quickResults.tested} (${successRate}%)
❌ Failed: ${quickResults.failed}/${quickResults.tested} (${failureRate}%)

🎯 SYSTEM STATUS: ${successRate > 80 ? '🟢 HEALTHY' : successRate > 50 ? '🟡 NEEDS ATTENTION' : '🔴 CRITICAL'}
`);
  
  if (quickResults.issues.length > 0) {
    console.log('🚨 ISSUES TO FIX:');
    console.log('='.repeat(40));
    
    quickResults.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue.name}`);
      console.log(`   Fix: ${issue.fix}\n`);
    });
  }
  
  console.log(`
🔄 NEXT STEPS:
==============
1. Fix the issues listed above
2. Run comprehensive tests: runComprehensiveFunctionTest()
3. Check the detailed test report for more insights

💡 QUICK FIXES:
===============
• Backend not running? → cd backend && npm install && npm run dev
• Frontend not loading? → npm install && npm run dev
• Database issues? → Check Supabase configuration
• Services missing? → Check import statements in main files
`);
  
  // Save quick results
  localStorage.setItem('assetHunterPro_quickTest', JSON.stringify({
    timestamp: new Date().toISOString(),
    results: quickResults
  }));
}

// Make function available globally
window.runQuickTests = runQuickTests;

// Auto-run quick tests
setTimeout(() => {
  console.log(`
🚀 STARTING QUICK TESTS...

This will test the most critical functions immediately.
For comprehensive testing, use: runComprehensiveFunctionTest()
`);
  
  runQuickTests();
}, 1000);
