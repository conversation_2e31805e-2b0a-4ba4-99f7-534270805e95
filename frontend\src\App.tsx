import { useState } from 'react'
import { AuthProvider, useAuth } from './contexts/AuthContext'
import { ThemeProvider } from './contexts/ThemeContext'
import { ToastProvider } from './components/ui/toast'
import { LoginForm } from './components/auth/LoginForm'
import { RoleSwitcher } from './components/auth/RoleSwitcher'
import { AppLayout } from './components/layout/AppLayout'
import { RouteRenderer, type View } from './components/navigation/RouteRenderer'

function AppContent() {
  const [currentView, setCurrentView] = useState<View>('dashboard')
  const [selectedClaimId] = useState<string | null>(null)
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false)

  const { user } = useAuth()

  // Show login form if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full space-y-8">
          <LoginForm />
          <RoleSwitcher />
        </div>
      </div>
    )
  }

  return (
    <AppLayout
      currentView={currentView}
      onViewChange={setCurrentView}
      commandPaletteOpen={commandPaletteOpen}
      onCommandPaletteToggle={setCommandPaletteOpen}
    >
      <RouteRenderer
        currentView={currentView}
        selectedClaimId={selectedClaimId}
        onViewChange={setCurrentView}
      />
    </AppLayout>
  )
}

function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <ToastProvider>
          <AppContent />
        </ToastProvider>
      </ThemeProvider>
    </AuthProvider>
  )
}

export default App
