import { useState } from 'react'
import { AuthProvider, useAuth } from '@/contexts/AuthContext'
import { ThemeProvider } from '@/contexts/ThemeContext'
import { ToastProvider, useToast } from '@/components/ui/toast'
import { useGlobalKeyboardShortcuts, useSequenceShortcuts } from '@/hooks/useKeyboardShortcuts'
import { AppLayout } from '@/components/layout/AppLayout'
import { RouteRenderer, type View } from '@/components/navigation/RouteRenderer'
import { LoginForm } from '@/components/auth/LoginForm'
import { RoleSwitcher } from '@/components/auth/RoleSwitcher'

function AppContent() {
  const [currentView, setCurrentView] = useState<View>('dashboard')
  const [selectedClaimId] = useState<string | null>(null)
  const [commandPaletteOpen, setCommandPaletteOpen] = useState(false)
  const [showKeyboardHelp, setShowKeyboardHelp] = useState(false)
  
  const { user } = useAuth()
  const { success, info, warning, error } = useToast()

  // Keyboard shortcuts
  const shortcuts = useGlobalKeyboardShortcuts({
    onOpenCommandPalette: () => setCommandPaletteOpen(true),
    onNavigate: (view: string) => setCurrentView(view as View),
    onToggleTheme: () => {}, // Handled by theme context
    onToggleSidebar: () => {}, // Handled by layout
    onQuickAction: (action: string) => {
      switch (action) {
        case 'new-claim':
          setCurrentView('claims')
          info('Quick Action', 'Navigate to Claims to create new claim')
          break
        case 'import':
          setCurrentView('batch-import')
          info('Quick Action', 'Navigate to Batch Import')
          break
        case 'export':
          info('Export', 'Use Export buttons in each section for data export')
          break
        case 'search':
          setCurrentView('search')
          break
        case 'help':
          setShowKeyboardHelp(true)
          break
        case 'notifications':
          info('Notifications', 'You have 3 new notifications')
          break
      }
    }
  })

  // Sequence shortcuts (vim-style)
  const sequenceShortcuts = useSequenceShortcuts({
    onNavigate: (view: string) => setCurrentView(view as View),
    onQuickAction: (action: string) => {
      success('Quick Action', `Executed: ${action}`)
    }
  })

  // Show login form if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full space-y-8">
          <LoginForm />
          <RoleSwitcher />
        </div>
      </div>
    )
  }

  return (
    <AppLayout
      currentView={currentView}
      onViewChange={setCurrentView}
      commandPaletteOpen={commandPaletteOpen}
      onCommandPaletteToggle={setCommandPaletteOpen}
    >
      <RouteRenderer
        currentView={currentView}
        selectedClaimId={selectedClaimId}
        onViewChange={setCurrentView}
      />
    </AppLayout>
  )
}

export default function App() {
  return (
    <AuthProvider>
      <ThemeProvider>
        <ToastProvider>
          <AppContent />
        </ToastProvider>
      </ThemeProvider>
    </AuthProvider>
  )
}
