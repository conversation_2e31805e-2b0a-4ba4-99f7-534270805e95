import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { CheckCircle, User, Phone, Mail, MapPin, Clock, AlertCircle } from 'lucide-react';
import { 
  OptimizedAISearchEngine, 
  LeadData, 
  SearchResult, 
  BatchSearchResult,
  BatchProgress,
  PersonCandidate,
  ContactMethod,
  ClaimantCard
} from '../services/optimizedAISearch';

// Component interfaces
interface SingleLeadSearchProps {
  leadData: LeadData;
  onComplete: (result: SearchResult) => void;
  searchEngine: OptimizedAISearchEngine;
}

interface BatchLeadSearchProps {
  leads: LeadData[];
  onComplete: (results: BatchSearchResult) => void;
  onIndividualComplete?: (result: SearchResult) => void;
  searchEngine: OptimizedAISearchEngine;
}

interface UsersFoundTabProps {
  searchEngine: OptimizedAISearchEngine;
  onUserValidated?: (candidate: PersonCandidate) => void;
  onUserImported?: (claimantCard: ClaimantCard) => void;
}

// React Component for Single Lead Search
export const SingleLeadSearch: React.FC<SingleLeadSearchProps> = ({ 
  leadData, 
  onComplete, 
  searchEngine 
}) => {
  const [isSearching, setIsSearching] = useState(false);
  const [progress, setProgress] = useState<any>(null);
  const [result, setResult] = useState<SearchResult | null>(null);
  
  const handleStartSearch = async () => {
    setIsSearching(true);
    setProgress({ phase: 'starting', progress: 0 });
    
    try {
      const searchResult = await searchEngine.searchSingleLead(leadData, true);
      setResult(searchResult);
      onComplete(searchResult);
    } catch (error) {
      console.error('Search failed:', error);
    } finally {
      setIsSearching(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Optimized AI Search</span>
          <Badge variant="outline">{leadData.claimantName}</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isSearching && !result && (
          <Button
            onClick={handleStartSearch}
            className="w-full"
            size="lg"
          >
            🔍 Start Optimized Search
          </Button>
        )}
        
        {isSearching && (
          <div className="space-y-3">
            <div className="text-sm text-gray-600">Searching...</div>
            {progress && (
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress.progress || 0}%` }}
                />
              </div>
            )}
          </div>
        )}
        
        {result && (
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <span className="font-medium">Search Complete</span>
              <Badge variant={
                result.searchStatus === 'completed' ? 'default' :
                result.searchStatus === 'requires_manual' ? 'secondary' :
                'destructive'
              }>
                {result.searchStatus.replace(/_/g, ' ').toUpperCase()}
              </Badge>
            </div>
            
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <CheckCircle className="w-4 h-4 text-green-600" />
                <span>Confidence: {(result.confidence * 100).toFixed(1)}%</span>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-600" />
                <span>Time: {(result.processingTime / 1000).toFixed(1)}s</span>
              </div>
            </div>
            
            {result.primaryCandidate && (
              <Card className="bg-green-50">
                <CardContent className="p-3">
                  <div className="text-sm font-medium">Primary Contact Found:</div>
                  <div className="text-sm text-gray-600 mt-1">
                    {result.primaryCandidate.matchedName} - {result.primaryCandidate.currentLocation.city}, {result.primaryCandidate.currentLocation.state}
                  </div>
                  {result.primaryCandidate.contactMethods.length > 0 && (
                    <div className="text-sm text-blue-600 mt-1 flex items-center gap-1">
                      {result.primaryCandidate.contactMethods[0].type === 'mobile' && <Phone className="w-3 h-3" />}
                      {result.primaryCandidate.contactMethods[0].type === 'email' && <Mail className="w-3 h-3" />}
                      {result.primaryCandidate.contactMethods[0].type === 'address' && <MapPin className="w-3 h-3" />}
                      {result.primaryCandidate.contactMethods[0].value}
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
            
            <Card className="bg-blue-50">
              <CardContent className="p-3">
                <div className="text-sm font-medium text-blue-900">Recommended Action:</div>
                <div className="text-sm text-blue-800 mt-1">
                  {result.actionRecommendation.primaryAction.replace(/_/g, ' ')} - {result.actionRecommendation.timing}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

// React Component for Batch Lead Search  
export const BatchLeadSearch: React.FC<BatchLeadSearchProps> = ({ 
  leads, 
  onComplete, 
  onIndividualComplete, 
  searchEngine 
}) => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [batchProgress, setBatchProgress] = useState<BatchProgress | null>(null);
  const [results, setResults] = useState<SearchResult[]>([]);
  const [batchResult, setBatchResult] = useState<BatchSearchResult | null>(null);
  
  const handleStartBatch = async () => {
    setIsProcessing(true);
    setBatchProgress(null);
    setResults([]);
    setBatchResult(null);
    
    try {
      const result = await searchEngine.searchBatchLeads(
        leads,
        (progress) => setBatchProgress(progress),
        (result) => {
          setResults(prev => [...prev, result]);
          onIndividualComplete?.(result);
        }
      );
      
      setBatchResult(result);
      onComplete(result);
    } catch (error) {
      console.error('Batch processing failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };
  
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex justify-between items-center">
          <span>Batch Optimized AI Search</span>
          <Badge variant="outline">{leads.length} leads queued</Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {!isProcessing && !batchResult && (
          <Button
            onClick={handleStartBatch}
            disabled={leads.length === 0}
            className="w-full"
            size="lg"
            variant="secondary"
          >
            🚀 Start Batch Search ({leads.length} leads)
          </Button>
        )}
        
        {isProcessing && batchProgress && (
          <div className="space-y-4">
            <div className="flex justify-between text-sm">
              <span>Progress: {batchProgress.completed}/{batchProgress.totalLeads}</span>
              <span>
                {batchProgress.estimatedTimeRemaining > 0 && 
                  `ETA: ${Math.round(batchProgress.estimatedTimeRemaining / 1000)}s`
                }
              </span>
            </div>
            
            <div className="w-full bg-gray-200 rounded-full h-3">
              <div 
                className="bg-purple-600 h-3 rounded-full transition-all duration-500"
                style={{ 
                  width: `${(batchProgress.completed / batchProgress.totalLeads) * 100}%` 
                }}
              />
            </div>
            
            <div className="grid grid-cols-3 gap-2 text-sm text-center">
              <Card className="p-2">
                <div className="font-medium text-blue-800">{batchProgress.completed}</div>
                <div className="text-blue-600 text-xs">Completed</div>
              </Card>
              <Card className="p-2">
                <div className="font-medium text-yellow-800">{batchProgress.processing}</div>
                <div className="text-yellow-600 text-xs">Processing</div>
              </Card>
              <Card className="p-2">
                <div className="font-medium text-gray-800">{batchProgress.queued}</div>
                <div className="text-gray-600 text-xs">Queued</div>
              </Card>
            </div>
          </div>
        )}
        
        {batchResult && (
          <div className="space-y-4">
            <Card className="bg-green-50">
              <CardContent className="p-4">
                <div className="text-lg font-medium text-green-900 mb-3">
                  Batch Processing Complete! 🎉
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Total Processed:</span>
                    <span className="ml-2 font-medium">{batchResult.totalProcessed}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Success Rate:</span>
                    <span className="ml-2 font-medium">{batchResult.summary.successRate.toFixed(1)}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">High Confidence:</span>
                    <span className="ml-2 font-medium">{batchResult.highConfidenceResults}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Processing Time:</span>
                    <span className="ml-2 font-medium">{(batchResult.processingTimeMs / 1000).toFixed(1)}s</span>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardContent className="p-3">
                <div className="text-sm font-medium mb-2">Batch Summary:</div>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• {batchResult.summary.readyToCall} leads ready for immediate contact</li>
                  <li>• {batchResult.summary.requiresManualReview} leads require manual review</li>
                  <li>• Average confidence: {(batchResult.summary.averageConfidence * 100).toFixed(1)}%</li>
                </ul>
              </CardContent>
            </Card>
            
            {batchResult.summary.recommendedNextActions.length > 0 && (
              <Card className="bg-blue-50">
                <CardContent className="p-3">
                  <div className="text-sm font-medium text-blue-900 mb-2">Recommendations:</div>
                  {batchResult.summary.recommendedNextActions.map((action, index) => (
                    <div key={index} className="text-sm text-blue-800">• {action}</div>
                  ))}
                </CardContent>
              </Card>
            )}
          </div>
        )}
        
        {results.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Individual Results</CardTitle>
            </CardHeader>
            <CardContent className="max-h-64 overflow-y-auto">
              <div className="space-y-2">
                {results.map((result, index) => (
                  <div key={index} className="flex justify-between items-center p-2 bg-gray-50 rounded">
                    <span className="text-sm">{leads.find(l => l.id === result.leadId)?.claimantName || 'Unknown'}</span>
                    <Badge variant={
                      result.searchStatus === 'completed' ? 'default' :
                      result.searchStatus === 'requires_manual' ? 'secondary' :
                      'destructive'
                    }>
                      {(result.confidence * 100).toFixed(0)}%
                    </Badge>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
};

// Users Found Tab Component
export const UsersFoundTab: React.FC<UsersFoundTabProps> = ({ 
  searchEngine, 
  onUserValidated, 
  onUserImported 
}) => {
  const [foundUsers, setFoundUsers] = useState<PersonCandidate[]>([]);
  const [validatedUsers, setValidatedUsers] = useState<PersonCandidate[]>([]);
  const [importedUsers, setImportedUsers] = useState<PersonCandidate[]>([]);
  const [refreshKey, setRefreshKey] = useState(0);

  useEffect(() => {
    const state = searchEngine.getSearchState();
    setFoundUsers(state.foundUsers);
    setValidatedUsers(state.validatedUsers);
    setImportedUsers(state.importedUsers);
  }, [searchEngine, refreshKey]);

  const handleValidateUser = (candidateId: string) => {
    const validatedCandidate = searchEngine.validateUser(candidateId);
    if (validatedCandidate) {
      onUserValidated?.(validatedCandidate);
      setRefreshKey(prev => prev + 1); // Trigger refresh
    }
  };

  const handleImportUser = (candidateId: string) => {
    const claimantCard = searchEngine.importUser(candidateId);
    if (claimantCard) {
      onUserImported?.(claimantCard);
      setRefreshKey(prev => prev + 1); // Trigger refresh
    }
  };

  const renderContactMethod = (method: ContactMethod) => {
    const icons = {
      mobile: <Phone className="w-3 h-3" />,
      landline: <Phone className="w-3 h-3" />,
      email: <Mail className="w-3 h-3" />,
      address: <MapPin className="w-3 h-3" />,
      work: <Phone className="w-3 h-3" />,
      social: <User className="w-3 h-3" />
    };

    return (
      <div key={method.value} className="flex items-center gap-1 text-xs">
        {icons[method.type]}
        <span>{method.value}</span>
        <Badge variant="outline" className="text-xs">
          {(method.confidence * 100).toFixed(0)}%
        </Badge>
      </div>
    );
  };

  const renderUserCard = (candidate: PersonCandidate, showActions: boolean = true) => (
    <Card key={candidate.candidateId} className="mb-3">
      <CardContent className="p-4">
        <div className="flex justify-between items-start mb-2">
          <div>
            <h4 className="font-medium">{candidate.matchedName}</h4>
            <p className="text-sm text-gray-600">
              {candidate.currentLocation.city}, {candidate.currentLocation.state}
            </p>
          </div>
          <div className="text-right">
            <Badge variant={candidate.confidence >= 0.8 ? 'default' : 'secondary'}>
              {(candidate.confidence * 100).toFixed(1)}% match
            </Badge>
            {candidate.validated && (
              <Badge variant="outline" className="ml-1">✓ Validated</Badge>
            )}
            {candidate.imported && (
              <Badge variant="outline" className="ml-1">📋 Imported</Badge>
            )}
          </div>
        </div>

        <div className="space-y-2 mb-3">
          <div className="text-sm font-medium">Contact Methods:</div>
          <div className="space-y-1">
            {candidate.contactMethods.slice(0, 3).map(renderContactMethod)}
            {candidate.contactMethods.length > 3 && (
              <div className="text-xs text-gray-500">
                +{candidate.contactMethods.length - 3} more
              </div>
            )}
          </div>
        </div>

        {showActions && (
          <div className="flex gap-2">
            {!candidate.validated && (
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleValidateUser(candidate.candidateId)}
              >
                <CheckCircle className="w-3 h-3 mr-1" />
                Validate
              </Button>
            )}
            {candidate.validated && !candidate.imported && (
              <Button
                size="sm"
                onClick={() => handleImportUser(candidate.candidateId)}
              >
                <User className="w-3 h-3 mr-1" />
                Import as Lead
              </Button>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      <Tabs defaultValue="found" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="found">
            Found Users ({foundUsers.length})
          </TabsTrigger>
          <TabsTrigger value="validated">
            Validated ({validatedUsers.length})
          </TabsTrigger>
          <TabsTrigger value="imported">
            Imported ({importedUsers.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="found" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="w-5 h-5" />
                Found Users
              </CardTitle>
              <p className="text-sm text-gray-600">
                Users discovered through AI search. Review and validate before importing.
              </p>
            </CardHeader>
            <CardContent>
              {foundUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <User className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No users found yet. Run a search to discover potential claimants.</p>
                </div>
              ) : (
                <div>
                  {foundUsers.map(candidate => renderUserCard(candidate, true))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="validated" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="w-5 h-5" />
                Validated Users
              </CardTitle>
              <p className="text-sm text-gray-600">
                Users that have been reviewed and validated by agents.
              </p>
            </CardHeader>
            <CardContent>
              {validatedUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <CheckCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No validated users yet. Validate found users to proceed with import.</p>
                </div>
              ) : (
                <div>
                  {validatedUsers.map(candidate => renderUserCard(candidate, true))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="imported" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertCircle className="w-5 h-5" />
                Imported Users
              </CardTitle>
              <p className="text-sm text-gray-600">
                Users that have been imported as claimant cards and are ready for agent work.
              </p>
            </CardHeader>
            <CardContent>
              {importedUsers.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <AlertCircle className="w-12 h-12 mx-auto mb-3 text-gray-300" />
                  <p>No imported users yet. Import validated users to create claimant cards.</p>
                </div>
              ) : (
                <div>
                  {importedUsers.map(candidate => renderUserCard(candidate, false))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default {
  SingleLeadSearch,
  BatchLeadSearch,
  UsersFoundTab
}; 