{"hash": "e8e515ff", "configHash": "e007746c", "lockfileHash": "08b54b09", "browserHash": "07fe98f5", "optimized": {"react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "56542bc8", "needsInterop": true}, "react-dom": {"src": "../../react-dom/index.js", "file": "react-dom.js", "fileHash": "ac273402", "needsInterop": true}, "react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "9711bb62", "needsInterop": true}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "4ae36988", "needsInterop": true}, "@radix-ui/react-checkbox": {"src": "../../@radix-ui/react-checkbox/dist/index.mjs", "file": "@radix-ui_react-checkbox.js", "fileHash": "77c79d2c", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "a51fdb89", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "b8db5595", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "07d859ff", "needsInterop": false}, "@radix-ui/react-separator": {"src": "../../@radix-ui/react-separator/dist/index.mjs", "file": "@radix-ui_react-separator.js", "fileHash": "5640c880", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "06849eb0", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "52f1d0fd", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "65fe1a86", "needsInterop": false}, "@supabase/supabase-js": {"src": "../../@supabase/supabase-js/dist/module/index.js", "file": "@supabase_supabase-js.js", "fileHash": "bff83d18", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "c6d2c589", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "0848e73b", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "1252b941", "needsInterop": false}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "62bc68ec", "needsInterop": true}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "a0f65fa1", "needsInterop": false}}, "chunks": {"browser-EMB7CRMN": {"file": "browser-EMB7CRMN.js"}, "browser-QO5QDEMX": {"file": "browser-QO5QDEMX.js"}, "chunk-5H4R2CZR": {"file": "chunk-5H4R2CZR.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-AM4XEHXZ": {"file": "chunk-AM4XEHXZ.js"}, "chunk-3O3AR7TV": {"file": "chunk-3O3AR7TV.js"}, "chunk-VFS37NM6": {"file": "chunk-VFS37NM6.js"}, "chunk-XTAQ5H5T": {"file": "chunk-XTAQ5H5T.js"}, "chunk-RW5B5UOJ": {"file": "chunk-RW5B5UOJ.js"}, "chunk-IFZBSVEC": {"file": "chunk-IFZBSVEC.js"}, "chunk-RALEVYWU": {"file": "chunk-RALEVYWU.js"}, "chunk-YQ5BCTVV": {"file": "chunk-YQ5BCTVV.js"}, "chunk-MUDEBT3V": {"file": "chunk-MUDEBT3V.js"}, "chunk-NRBATONI": {"file": "chunk-NRBATONI.js"}, "chunk-QJTFJ6OV": {"file": "chunk-QJTFJ6OV.js"}, "chunk-V4OQ3NZ2": {"file": "chunk-V4OQ3NZ2.js"}}}