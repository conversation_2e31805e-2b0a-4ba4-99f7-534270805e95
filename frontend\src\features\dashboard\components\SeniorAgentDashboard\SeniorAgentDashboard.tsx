import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  TrendingUp, 
  TrendingDown,
  Target, 
  Award,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  BarChart3,
  Settings,
  Upload,
  Download,
  Filter,
  Search,
  UserCheck,
  UserX,
  Activity,
  DollarSign,
  Calendar,
  Mail
} from 'lucide-react';

interface TeamMember {
  id: string;
  name: string;
  role: string;
  avatar?: string;
  stats: {
    activeClaims: number;
    completedThisMonth: number;
    successRate: number;
    responseTime: number;
  };
  status: 'active' | 'away' | 'offline';
  lastActivity: string;
}

interface TeamMetrics {
  totalClaims: number;
  activeClaims: number;
  completedThisMonth: number;
  teamSuccessRate: number;
  avgResponseTime: number;
  monthlyGoal: number;
  goalProgress: number;
  totalRevenue: number;
  revenueGrowth: number;
}

interface ClaimAlert {
  id: string;
  type: 'overdue' | 'high_value' | 'escalation' | 'approval_needed';
  claimId: string;
  claimantName: string;
  assignedTo: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  daysOld: number;
}

export const SeniorAgentDashboard: React.FC = () => {
  const [teamMetrics] = useState<TeamMetrics>({
    totalClaims: 342,
    activeClaims: 156,
    completedThisMonth: 89,
    teamSuccessRate: 73,
    avgResponseTime: 1.8,
    monthlyGoal: 120,
    goalProgress: 89,
    totalRevenue: 2450000,
    revenueGrowth: 15.3
  });

  const [teamMembers] = useState<TeamMember[]>([
    {
      id: '1',
      name: 'Sarah Johnson',
      role: 'Junior Agent',
      stats: {
        activeClaims: 23,
        completedThisMonth: 18,
        successRate: 76,
        responseTime: 1.5
      },
      status: 'active',
      lastActivity: '2024-12-12T14:30:00Z'
    },
    {
      id: '2',
      name: 'Mike Chen',
      role: 'Junior Agent',
      stats: {
        activeClaims: 19,
        completedThisMonth: 15,
        successRate: 68,
        responseTime: 2.1
      },
      status: 'active',
      lastActivity: '2024-12-12T13:45:00Z'
    },
    {
      id: '3',
      name: 'Emily Davis',
      role: 'Junior Agent',
      stats: {
        activeClaims: 21,
        completedThisMonth: 16,
        successRate: 81,
        responseTime: 1.2
      },
      status: 'away',
      lastActivity: '2024-12-12T12:15:00Z'
    },
    {
      id: '4',
      name: 'David Rodriguez',
      role: 'Contractor',
      stats: {
        activeClaims: 15,
        completedThisMonth: 12,
        successRate: 65,
        responseTime: 2.8
      },
      status: 'offline',
      lastActivity: '2024-12-11T16:30:00Z'
    }
  ]);

  const [claimAlerts] = useState<ClaimAlert[]>([
    {
      id: '1',
      type: 'overdue',
      claimId: 'CL-2024-156',
      claimantName: 'Robert Wilson',
      assignedTo: 'Sarah Johnson',
      description: 'No contact attempt in 5 days',
      priority: 'high',
      daysOld: 5
    },
    {
      id: '2',
      type: 'high_value',
      claimId: 'CL-2024-167',
      claimantName: 'Corporate LLC',
      assignedTo: 'Mike Chen',
      description: 'High-value claim ($50k+) needs approval',
      priority: 'urgent',
      daysOld: 2
    },
    {
      id: '3',
      type: 'escalation',
      claimId: 'CL-2024-142',
      claimantName: 'Lisa Anderson',
      assignedTo: 'Emily Davis',
      description: 'Claimant requested supervisor contact',
      priority: 'medium',
      daysOld: 1
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-800';
      case 'away': return 'bg-yellow-100 text-yellow-800';
      case 'offline': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'overdue': return <Clock className="h-4 w-4 text-red-600" />;
      case 'high_value': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'escalation': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'approval_needed': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Team Management</h1>
          <p className="text-gray-600">Monitor team performance and manage assignments</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Upload className="h-4 w-4 mr-2" />
            Batch Assign
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Settings className="h-4 w-4 mr-2" />
            Team Settings
          </Button>
        </div>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Team Claims */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Team Claims</CardTitle>
            <FileText className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{teamMetrics.totalClaims}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{teamMetrics.activeClaims} Active</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{teamMetrics.completedThisMonth} Done</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Team Success Rate */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Target className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{teamMetrics.teamSuccessRate}%</div>
            <div className="mt-2">
              <Progress value={teamMetrics.teamSuccessRate} className="h-2" />
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <p className="text-sm text-green-600">+5% from last month</p>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Goal */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Goal</CardTitle>
            <Award className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">
              {teamMetrics.goalProgress}/{teamMetrics.monthlyGoal}
            </div>
            <div className="mt-2">
              <Progress value={(teamMetrics.goalProgress / teamMetrics.monthlyGoal) * 100} className="h-2" />
            </div>
            <p className="text-sm text-gray-600 mt-1">
              {teamMetrics.monthlyGoal - teamMetrics.goalProgress} more to reach goal
            </p>
          </CardContent>
        </Card>

        {/* Revenue */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Revenue</CardTitle>
            <DollarSign className="h-5 w-5 text-emerald-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">
              {formatCurrency(teamMetrics.totalRevenue)}
            </div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-emerald-500 mr-1" />
              <p className="text-sm text-emerald-600">+{teamMetrics.revenueGrowth}% this quarter</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="team" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="team">Team Overview</TabsTrigger>
          <TabsTrigger value="alerts">Alerts & Issues</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="assignments">Assignments</TabsTrigger>
        </TabsList>

        {/* Team Overview Tab */}
        <TabsContent value="team" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-blue-600" />
                  Team Members
                </div>
                <Badge variant="outline">{teamMembers.length} Active</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {teamMembers.map((member) => (
                  <div key={member.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center text-white font-medium">
                        {member.name.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <div className="font-medium">{member.name}</div>
                        <div className="text-sm text-gray-500 flex items-center space-x-2">
                          <span>{member.role}</span>
                          <span>•</span>
                          <Badge className={`text-xs ${getStatusColor(member.status)}`}>
                            {member.status}
                          </Badge>
                          <span>•</span>
                          <span>Last active {formatTimeAgo(member.lastActivity)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-6 text-center">
                      <div>
                        <div className="text-lg font-bold text-blue-600">{member.stats.activeClaims}</div>
                        <div className="text-xs text-gray-500">Active Claims</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-green-600">{member.stats.completedThisMonth}</div>
                        <div className="text-xs text-gray-500">Completed</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-purple-600">{member.stats.successRate}%</div>
                        <div className="text-xs text-gray-500">Success Rate</div>
                      </div>
                      <div>
                        <div className="text-lg font-bold text-orange-600">{member.stats.responseTime}h</div>
                        <div className="text-xs text-gray-500">Avg Response</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Alerts Tab */}
        <TabsContent value="alerts" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
                  Active Alerts
                </div>
                <Badge variant="destructive">{claimAlerts.length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {claimAlerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getAlertIcon(alert.type)}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{alert.description}</div>
                        <div className="text-xs text-gray-500">
                          {alert.claimantName} • {alert.claimId} • Assigned to {alert.assignedTo}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${getPriorityColor(alert.priority)}`}>
                        {alert.priority}
                      </Badge>
                      <span className="text-xs text-gray-500">{alert.daysOld}d old</span>
                      <Button size="sm" variant="outline">
                        Resolve
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Team Performance Chart */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                  Performance Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p>Performance chart will be displayed here</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Rankings */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Award className="h-5 w-5 mr-2 text-yellow-600" />
                  Top Performers
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {teamMembers
                    .sort((a, b) => b.stats.successRate - a.stats.successRate)
                    .map((member, index) => (
                    <div key={member.id} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' : 
                          index === 1 ? 'bg-gray-400' : 
                          index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{member.name}</div>
                          <div className="text-sm text-gray-500">
                            {member.stats.completedThisMonth} completed this month
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">{member.stats.successRate}%</div>
                        <div className="text-sm text-gray-500">{member.stats.responseTime}h avg</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Assignments Tab */}
        <TabsContent value="assignments" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  Claim Assignments
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    Bulk Assign
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12 text-gray-500">
                <FileText className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                <p>Assignment management interface will be displayed here</p>
                <p className="text-sm mt-2">Drag and drop claims to reassign, bulk operations, workload balancing</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 