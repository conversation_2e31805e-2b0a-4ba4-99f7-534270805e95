-- Email System Database Schema for AssetHunterPro
-- This schema supports internal email tracking, follow-ups, and communication history

-- Email templates table (for reusable templates)
CREATE TABLE IF NOT EXISTS email_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  subject VARCHAR(500) NOT NULL,
  body_html TEXT,
  body_text TEXT,
  category VARCHAR(100) DEFAULT 'general',
  is_active BOOLEAN DEFAULT true,
  variables JSONB DEFAULT '[]'::jsonb, -- Array of variable names used in template
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- Email accounts/configurations
CREATE TABLE IF NOT EXISTS email_accounts (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VA<PERSON><PERSON><PERSON>(255) NOT NULL,
  email_address VARCHAR(255) NOT NULL UNIQUE,
  provider VARCHAR(50) NOT NULL, -- 'sendgrid', 'resend', 'ses', 'smtp'
  api_key TEXT, -- Encrypted API key
  smtp_host VARCHAR(255),
  smtp_port INTEGER,
  smtp_username VARCHAR(255),
  smtp_password TEXT, -- Encrypted password
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  daily_limit INTEGER DEFAULT 1000,
  monthly_limit INTEGER DEFAULT 30000,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email threads (conversation grouping)
CREATE TABLE IF NOT EXISTS email_threads (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
  subject VARCHAR(500) NOT NULL,
  participants JSONB DEFAULT '[]'::jsonb, -- Array of email addresses
  status VARCHAR(50) DEFAULT 'active', -- 'active', 'closed', 'awaiting_response'
  last_email_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Individual emails sent/received
CREATE TABLE IF NOT EXISTS emails (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  thread_id UUID REFERENCES email_threads(id) ON DELETE CASCADE,
  claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
  
  -- Email details
  message_id VARCHAR(255) UNIQUE, -- External message ID from email provider
  subject VARCHAR(500) NOT NULL,
  body_html TEXT,
  body_text TEXT,
  
  -- Sender/recipient info
  from_email VARCHAR(255) NOT NULL,
  from_name VARCHAR(255),
  to_emails JSONB NOT NULL DEFAULT '[]'::jsonb, -- Array of email addresses
  cc_emails JSONB DEFAULT '[]'::jsonb,
  bcc_emails JSONB DEFAULT '[]'::jsonb,
  reply_to VARCHAR(255),
  
  -- Email metadata
  direction VARCHAR(20) NOT NULL DEFAULT 'outbound', -- 'outbound', 'inbound'
  email_type VARCHAR(50) DEFAULT 'general', -- 'initial_contact', 'follow_up', 'document_request', etc.
  template_id UUID REFERENCES email_templates(id),
  
  -- Tracking and status
  status VARCHAR(50) DEFAULT 'draft', -- 'draft', 'queued', 'sent', 'delivered', 'failed', 'bounced'
  sent_at TIMESTAMP WITH TIME ZONE,
  delivered_at TIMESTAMP WITH TIME ZONE,
  opened_at TIMESTAMP WITH TIME ZONE,
  clicked_at TIMESTAMP WITH TIME ZONE,
  replied_at TIMESTAMP WITH TIME ZONE,
  
  -- Engagement tracking
  open_count INTEGER DEFAULT 0,
  click_count INTEGER DEFAULT 0,
  tracking_pixel_url VARCHAR(500),
  
  -- Follow-up scheduling
  follow_up_date TIMESTAMP WITH TIME ZONE,
  follow_up_completed BOOLEAN DEFAULT false,
  
  -- Attachments and metadata
  attachments JSONB DEFAULT '[]'::jsonb,
  headers JSONB DEFAULT '{}'::jsonb,
  error_message TEXT,
  provider_response JSONB,
  
  -- Audit fields
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  sent_by UUID REFERENCES users(id),
  
  -- Indexes for performance
  CONSTRAINT emails_direction_check CHECK (direction IN ('outbound', 'inbound')),
  CONSTRAINT emails_status_check CHECK (status IN ('draft', 'queued', 'sent', 'delivered', 'failed', 'bounced', 'rejected'))
);

-- Email events for detailed tracking
CREATE TABLE IF NOT EXISTS email_events (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email_id UUID NOT NULL REFERENCES emails(id) ON DELETE CASCADE,
  event_type VARCHAR(50) NOT NULL, -- 'sent', 'delivered', 'opened', 'clicked', 'bounced', 'spam', 'unsubscribed'
  event_data JSONB DEFAULT '{}'::jsonb,
  ip_address INET,
  user_agent TEXT,
  location JSONB, -- Country, city, etc.
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email automation rules
CREATE TABLE IF NOT EXISTS email_automation_rules (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  trigger_event VARCHAR(100) NOT NULL, -- 'claim_created', 'status_changed', 'no_response_after_days'
  trigger_conditions JSONB DEFAULT '{}'::jsonb,
  action_type VARCHAR(50) NOT NULL, -- 'send_email', 'schedule_follow_up', 'assign_agent'
  action_config JSONB DEFAULT '{}'::jsonb,
  template_id UUID REFERENCES email_templates(id),
  delay_hours INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_by UUID REFERENCES users(id)
);

-- Unsubscribe/opt-out tracking
CREATE TABLE IF NOT EXISTS email_unsubscribes (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  email_address VARCHAR(255) NOT NULL,
  claim_id UUID REFERENCES claims(id),
  reason VARCHAR(100), -- 'user_request', 'bounce', 'spam_complaint'
  unsubscribed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  ip_address INET,
  user_agent TEXT
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_emails_claim_id ON emails(claim_id);
CREATE INDEX IF NOT EXISTS idx_emails_thread_id ON emails(thread_id);
CREATE INDEX IF NOT EXISTS idx_emails_status ON emails(status);
CREATE INDEX IF NOT EXISTS idx_emails_sent_at ON emails(sent_at);
CREATE INDEX IF NOT EXISTS idx_emails_follow_up_date ON emails(follow_up_date) WHERE follow_up_date IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_email_events_email_id ON email_events(email_id);
CREATE INDEX IF NOT EXISTS idx_email_events_type ON email_events(event_type);
CREATE INDEX IF NOT EXISTS idx_email_threads_claim_id ON email_threads(claim_id);

-- Enable Row Level Security
ALTER TABLE email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_accounts ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_threads ENABLE ROW LEVEL SECURITY;
ALTER TABLE emails ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE email_unsubscribes ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies (can be customized based on your auth system)
CREATE POLICY "Users can manage their organization's email templates" ON email_templates
  FOR ALL USING (true); -- Adjust based on your user/organization structure

CREATE POLICY "Users can manage their organization's emails" ON emails
  FOR ALL USING (true); -- Adjust based on your user/organization structure

CREATE POLICY "Users can view their organization's email threads" ON email_threads
  FOR ALL USING (true); -- Adjust based on your user/organization structure

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON email_templates
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_email_threads_updated_at BEFORE UPDATE ON email_threads
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_emails_updated_at BEFORE UPDATE ON emails
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column(); 