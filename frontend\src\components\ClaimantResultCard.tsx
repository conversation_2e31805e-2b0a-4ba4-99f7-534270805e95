import React, { useState } from 'react';
import { PersonData } from '@/types/backend';

export interface ClaimantResult {
  // Core identification
  discoveryId: string;
  primaryName: string;
  nameVariations?: string[];
  
  // Contact information
  addresses?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
    type: string;
    source: string;
    confidence: number;
  }[];
  phoneNumbers?: {
    number: string;
    type: string;
    verified: boolean;
    source: string;
    confidence: number;
  }[];
  emailAddresses?: {
    email: string;
    verified: boolean;
    source: string;
    confidence: number;
  }[];
  
  // Discovery metadata
  confidence: number;
  lastActivity?: string;
  qualityScore: number;
  
  // Additional connections (different from PersonData to avoid conflicts)
  socialMediaFingerprints?: {
    platform: string;
    profile: string;
    url: string;
  }[];
  familyConnections?: {
    name: string;
    relationship: string;
    contact?: string;
  }[];
  businessConnections?: {
    company: string;
    role: string;
    contact?: string;
  }[];
  
  // Validation tracking
  validationStatus: 'pending' | 'in_progress' | 'validated' | 'invalid';
  contactAttempts?: {
    method: string;
    timestamp: Date;
    status: 'sent' | 'delivered' | 'responded' | 'failed';
    notes?: string;
  }[];
}

interface ClaimantResultCardProps {
  result: ClaimantResult;
  onValidate: (resultId: string, method: string, notes?: string) => void;
  onMarkInvalid: (resultId: string, reason: string) => void;
  onContactAttempt: (resultId: string, method: string, contact: string) => void;
}

export const ClaimantResultCard: React.FC<ClaimantResultCardProps> = ({
  result,
  onValidate,
  onMarkInvalid,
  onContactAttempt
}) => {
  const [showValidationPanel, setShowValidationPanel] = useState(false);
  const [showContactHistory, setShowContactHistory] = useState(false);
  const [selectedContactMethod, setSelectedContactMethod] = useState<string>('');
  const [contactNotes, setContactNotes] = useState('');

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return 'text-green-600 bg-green-50';
    if (confidence >= 0.6) return 'text-yellow-600 bg-yellow-50';
    return 'text-red-600 bg-red-50';
  };

  const getStatusBadge = (status: string) => {
    const badges = {
      pending: 'bg-gray-100 text-gray-800',
      in_progress: 'bg-blue-100 text-blue-800',
      validated: 'bg-green-100 text-green-800',
      invalid: 'bg-red-100 text-red-800'
    };
    return badges[status as keyof typeof badges] || badges.pending;
  };

  const getQualityScoreBar = (score: number) => {
    const percentage = Math.min(100, Math.max(0, score * 100));
    const color = percentage >= 70 ? 'bg-green-500' : percentage >= 50 ? 'bg-yellow-500' : 'bg-red-500';
    
    return (
      <div className="w-full bg-gray-200 rounded-full h-2">
        <div 
          className={`h-2 rounded-full ${color}`}
          style={{ width: `${percentage}%` }}
        ></div>
      </div>
    );
  };

  const handleContactAttempt = async (method: string, contact: string) => {
    await onContactAttempt(result.discoveryId, method, contact);
    setContactNotes('');
    setSelectedContactMethod('');
  };

  const handleValidation = async (method: string) => {
    await onValidate(result.discoveryId, method, contactNotes);
    setShowValidationPanel(false);
    setContactNotes('');
  };

  return (
    <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-6 mb-4">
      {/* Header */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex items-center space-x-3">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            <span className="text-blue-600 font-bold text-lg">
              {result.primaryName.split(' ').map(n => n[0]).join('')}
            </span>
          </div>
          <div>
            <h3 className="text-xl font-semibold text-gray-900">Claimant Found</h3>
            <p className="text-sm text-gray-500">ID {result.discoveryId}</p>
          </div>
        </div>
        <div className="text-right">
          <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${getConfidenceColor(result.confidence)}`}>
            Confidence {Math.round(result.confidence * 100)}%
          </div>
          <div className={`mt-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusBadge(result.validationStatus)}`}>
            {result.validationStatus.replace('_', ' ').toUpperCase()}
          </div>
        </div>
      </div>

      {/* Main Information Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        {/* Personal Information */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-600">Full Name</label>
            <p className="text-lg font-semibold text-gray-900">{result.primaryName}</p>
            {result.nameVariations && result.nameVariations.length > 0 && (
              <p className="text-sm text-gray-500">
                Also known as: {result.nameVariations.slice(0, 2).join(', ')}
                {result.nameVariations.length > 2 && ` +${result.nameVariations.length - 2} more`}
              </p>
            )}
          </div>

          <div>
            <label className="text-sm font-medium text-gray-600">Last Known Address</label>
            {result.addresses && result.addresses.length > 0 ? (
              <div className="space-y-1">
                <p className="text-gray-900">{result.addresses[0].street}</p>
                <p className="text-gray-900">
                  {result.addresses[0].city}, {result.addresses[0].state} {result.addresses[0].zipCode}
                </p>
              </div>
            ) : (
              <p className="text-gray-500 italic">No address available</p>
            )}
          </div>
        </div>

        {/* Contact Information */}
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium text-gray-600">Potential Contacts</label>
            <div className="space-y-2">
              {result.phoneNumbers && result.phoneNumbers.length > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-900">{result.phoneNumbers[0].number}</span>
                  <button
                    onClick={() => handleContactAttempt('phone', result.phoneNumbers![0].number)}
                    className="text-blue-600 hover:text-blue-800 text-sm underline"
                  >
                    Call
                  </button>
                </div>
              )}
              
              {result.emailAddresses && result.emailAddresses.length > 0 && (
                <div className="flex items-center justify-between">
                  <span className="text-gray-900">{result.emailAddresses[0].email}</span>
                  <button
                    onClick={() => handleContactAttempt('email', result.emailAddresses![0].email)}
                    className="text-blue-600 hover:text-blue-800 text-sm underline"
                  >
                    Email
                  </button>
                </div>
              )}

              {result.addresses && result.addresses.length > 1 && (
                <div className="space-y-1">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-gray-900">{result.addresses[1].street}</p>
                      <p className="text-gray-900">
                        {result.addresses[1].city}, {result.addresses[1].state} {result.addresses[1].zipCode}
                      </p>
                    </div>
                    <button
                      onClick={() => handleContactAttempt('mail', `${result.addresses![1].street}, ${result.addresses![1].city}, ${result.addresses![1].state} ${result.addresses![1].zipCode}`)}
                      className="text-blue-600 hover:text-blue-800 text-sm underline"
                    >
                      Mail
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {result.lastActivity && (
            <div>
              <label className="text-sm font-medium text-gray-600">Last Activity</label>
              <p className="text-gray-900">{result.lastActivity}</p>
            </div>
          )}
        </div>
      </div>

      {/* Quality Score */}
      <div className="mb-6">
        <div className="flex items-center justify-between mb-2">
          <label className="text-sm font-medium text-gray-600">Quality Score</label>
          <span className="text-sm font-medium text-gray-900">{Math.round(result.qualityScore * 100)}/100</span>
        </div>
        {getQualityScoreBar(result.qualityScore)}
      </div>

      {/* Social Media & Connections */}
      {(result.socialMediaFingerprints?.length || result.familyConnections?.length || result.businessConnections?.length) && (
        <div className="border-t pt-4 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Social Media */}
            {result.socialMediaFingerprints && result.socialMediaFingerprints.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-600 mb-2 block">Social Media Fingerprints</label>
                {result.socialMediaFingerprints.slice(0, 2).map((social, index) => (
                  <div key={index} className="flex items-center justify-between py-1">
                    <div className="flex items-center space-x-2">
                      <span className="text-blue-600 text-sm">📱</span>
                      <span className="text-sm text-gray-900">{social.platform}</span>
                    </div>
                    <a
                      href={social.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:text-blue-800 text-sm underline"
                      onClick={() => handleContactAttempt('social', social.url)}
                    >
                      View
                    </a>
                  </div>
                ))}
              </div>
            )}

            {/* Family Connections */}
            {result.familyConnections && result.familyConnections.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-600 mb-2 block">Family Connections</label>
                {result.familyConnections.slice(0, 2).map((family, index) => (
                  <div key={index} className="py-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-900">{family.name}</p>
                        <p className="text-xs text-gray-500">{family.relationship}</p>
                      </div>
                      {family.contact && (
                        <button
                          onClick={() => handleContactAttempt('family', family.contact!)}
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          Contact
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Business Connections */}
            {result.businessConnections && result.businessConnections.length > 0 && (
              <div>
                <label className="text-sm font-medium text-gray-600 mb-2 block">Business Connections</label>
                {result.businessConnections.slice(0, 2).map((business, index) => (
                  <div key={index} className="py-1">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm text-gray-900">{business.company}</p>
                        <p className="text-xs text-gray-500">{business.role}</p>
                      </div>
                      {business.contact && (
                        <button
                          onClick={() => handleContactAttempt('business', business.contact!)}
                          className="text-blue-600 hover:text-blue-800 text-sm underline"
                        >
                          Contact
                        </button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Contact History */}
      {result.contactAttempts && result.contactAttempts.length > 0 && (
        <div className="border-t pt-4 mb-6">
          <div className="flex items-center justify-between mb-3">
            <label className="text-sm font-medium text-gray-600">Contact History</label>
            <button
              onClick={() => setShowContactHistory(!showContactHistory)}
              className="text-blue-600 hover:text-blue-800 text-sm underline"
            >
              {showContactHistory ? 'Hide' : 'Show'} History
            </button>
          </div>
          
          {showContactHistory && (
            <div className="space-y-2">
              {result.contactAttempts.slice(0, 3).map((attempt, index) => (
                <div key={index} className="flex items-center justify-between py-2 px-3 bg-gray-50 rounded">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-900">{attempt.method}</span>
                    <span className="text-xs text-gray-500">
                      {attempt.timestamp.toLocaleDateString()}
                    </span>
                  </div>
                  <span className={`text-xs px-2 py-1 rounded-full ${
                    attempt.status === 'responded' ? 'bg-green-100 text-green-800' :
                    attempt.status === 'delivered' ? 'bg-blue-100 text-blue-800' :
                    attempt.status === 'sent' ? 'bg-yellow-100 text-yellow-800' :
                    'bg-red-100 text-red-800'
                  }`}>
                    {attempt.status}
                  </span>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-wrap gap-3 border-t pt-4">
        <button
          onClick={() => setShowValidationPanel(!showValidationPanel)}
          className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
        >
          Validate Claimant
        </button>
        
        <button
          onClick={() => onMarkInvalid(result.discoveryId, 'Agent review - incorrect match')}
          className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"
        >
          Mark Invalid
        </button>
        
        <button
          onClick={() => setShowValidationPanel(!showValidationPanel)}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Contact Options
        </button>
        
        <button
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
        >
          Generate Report
        </button>
      </div>

      {/* Validation Panel */}
      {showValidationPanel && (
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h4 className="font-medium text-blue-900 mb-3">Validation & Contact Options</h4>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Contact Method</label>
              <select
                value={selectedContactMethod}
                onChange={(e) => setSelectedContactMethod(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="">Select method...</option>
                <option value="phone">Phone Call</option>
                <option value="email">Email</option>
                <option value="mail">Physical Mail</option>
                <option value="social">Social Media</option>
                <option value="family">Through Family</option>
                <option value="business">Through Business</option>
                <option value="legal">Legal Notice</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Priority</label>
              <select className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                <option value="high">High Priority</option>
                <option value="medium">Medium Priority</option>
                <option value="low">Low Priority</option>
              </select>
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">Notes</label>
            <textarea
              value={contactNotes}
              onChange={(e) => setContactNotes(e.target.value)}
              placeholder="Add notes about validation or contact attempt..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              rows={3}
            />
          </div>

          <div className="flex space-x-3">
            <button
              onClick={() => selectedContactMethod && handleValidation(selectedContactMethod)}
              disabled={!selectedContactMethod}
              className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 disabled:bg-gray-400 transition-colors"
            >
              Initiate Contact
            </button>
            <button
              onClick={() => setShowValidationPanel(false)}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      )}
    </div>
  );
}; 