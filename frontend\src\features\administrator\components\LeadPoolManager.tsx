import React from 'react';
import { AdministratorState } from '../types';

interface LeadPoolManagerProps {
  adminState: AdministratorState;
}

export const LeadPoolManager: React.FC<LeadPoolManagerProps> = ({ adminState }) => {
  return (
    <div className="p-6">
      <h3 className="text-lg font-semibold mb-4">Lead Pool Management</h3>
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <p className="text-sm text-blue-800">
          Lead pool organization, analytics, and management tools will be available in the full implementation.
        </p>
      </div>
    </div>
  );
}; 