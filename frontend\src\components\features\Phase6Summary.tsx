import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Search,
  Bot,
  Brain,
  Users,
  Phone,
  Mail,
  MapPin,
  Shield,
  Target,
  Zap,
  Clock,
  CheckCircle,
  AlertTriangle,
  Eye,
  FileText,
  TrendingUp,
  Activity,
  Database,
  Network,
  Settings,
  Star,
  DollarSign,
  Lightbulb,
  Globe,
  Cpu,
  BarChart3,
  ArrowRight,
  Award
} from 'lucide-react';

interface Phase6SummaryProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const Phase6Summary: React.FC<Phase6SummaryProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');

  const coreFeatures = [
    {
      icon: Database,
      title: "Universal Data Normalization",
      description: "AI automatically detects and maps fields from any state's CSV format",
      metrics: ["96.4% accuracy", "All 50 states", "Instant adaptation"],
      status: "completed"
    },
    {
      icon: Search,
      title: "Multi-Source Discovery Engine",
      description: "15+ data sources searched simultaneously with parallel execution",
      metrics: ["25+ APIs", "99.9% uptime", "Real-time validation"],
      status: "completed"
    },
    {
      icon: Phone,
      title: "Contact Intelligence System",
      description: "Phone, email, address, and digital presence discovery with quality scoring",
      metrics: ["89.2% quality", "Real-time verification", "Success prediction"],
      status: "completed"
    },
    {
      icon: Users,
      title: "Death Detection & Heir Discovery",
      description: "AI-powered death detection with automated heir and estate discovery",
      metrics: ["93.4% accuracy", "Multi-source validation", "Probate intelligence"],
      status: "completed"
    },
    {
      icon: Brain,
      title: "Universal Learning Engine",
      description: "Cross-state pattern recognition that improves all discoveries",
      metrics: ["91.2% prediction", "Continuous learning", "Strategy optimization"],
      status: "completed"
    },
    {
      icon: Zap,
      title: "Automated Workflows",
      description: "87.3% automation rate with intelligent workflow orchestration",
      metrics: ["24/7 processing", "Smart routing", "Auto-escalation"],
      status: "completed"
    }
  ];

  const performanceMetrics = {
    processing: {
      records_processed: 15684,
      processing_speed: 127,
      automation_rate: 87.3,
      manual_intervention: 12.7,
      error_rate: 2.4
    },
    success: {
      discovery_success_rate: 81.7,
      contact_discovery_rate: 76.3,
      contact_quality_score: 89.2,
      response_rate: 34.8,
      conversion_rate: 28.5
    },
    cost: {
      cost_per_discovery: 8.47,
      cost_per_contact: 11.09,
      roi_per_case: 267.3,
      cost_savings: 89.2
    },
    quality: {
      data_accuracy: 94.6,
      contact_accuracy: 91.8,
      false_positive_rate: 3.2,
      false_negative_rate: 5.4,
      customer_satisfaction: 92.1
    }
  };

  const discoveryExamples = [
    {
      type: "Individual Person",
      state: "California",
      name: "Johnson, Mary Elizabeth",
      asset_value: 12500,
      discovery_time: 12,
      contact_methods: 6,
      best_contact: "Verified mobile phone",
      success_probability: 94.7,
      cost: 6.75,
      details: ["Phone, email, 2 addresses", "LinkedIn, Facebook profiles", "92% quality score"]
    },
    {
      type: "Business Entity",
      state: "Texas",
      name: "TechFlow Industries LLC",
      asset_value: 48500,
      discovery_time: 8,
      contact_methods: 4,
      best_contact: "Legal department email",
      success_probability: 78.3,
      cost: 11.20,
      details: ["Business phone, legal email", "Office address, website", "87% quality score"]
    },
    {
      type: "Deceased with Heirs",
      state: "Florida",
      name: "Smith, Robert James Sr",
      asset_value: 7800,
      discovery_time: 25,
      contact_methods: 3,
      best_contact: "Robert Smith Jr (Son)",
      success_probability: 71.9,
      cost: 18.95,
      details: ["Death confirmed via obituary", "2 heirs found", "Probate completed", "89% heir quality"]
    }
  ];

  const aiModels = [
    {
      name: "Universal Field Mapper",
      type: "ML Classifier",
      accuracy: 96.8,
      training_data: "250K+ records",
      purpose: "Auto-detect fields from any state format",
      status: "Production"
    },
    {
      name: "Death Prediction Model",
      type: "Gradient Boosting",
      accuracy: 93.4,
      training_data: "125K+ records",
      purpose: "Predict likelihood of death",
      status: "Production"
    },
    {
      name: "Contact Quality Predictor",
      type: "Neural Network",
      accuracy: 89.7,
      training_data: "300K+ contacts",
      purpose: "Score contact method quality",
      status: "Production"
    },
    {
      name: "Success Probability Model",
      type: "Random Forest",
      accuracy: 91.2,
      training_data: "180K+ cases",
      purpose: "Predict discovery success",
      status: "Production"
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white rounded-lg p-6">
        <div className="flex items-center gap-4 mb-4">
          <Search className="h-12 w-12" />
          <div>
            <h3 className="text-2xl font-bold">Universal AI Discovery Engine</h3>
            <p className="text-blue-100">80-90% automated contact discovery across all 50 states</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-3xl font-bold">{performanceMetrics.processing.automation_rate}%</div>
            <div className="text-sm text-blue-100">Automation Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold">{performanceMetrics.success.discovery_success_rate}%</div>
            <div className="text-sm text-blue-100">Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold">{performanceMetrics.cost.cost_savings}%</div>
            <div className="text-sm text-blue-100">Cost Savings</div>
          </div>
          <div className="text-center">
            <div className="text-3xl font-bold">25+</div>
            <div className="text-sm text-blue-100">Data Sources</div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {coreFeatures.map((feature, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center gap-3 mb-3">
                <feature.icon className="h-8 w-8 text-blue-600" />
                <div>
                  <h4 className="font-semibold">{feature.title}</h4>
                  <Badge variant="default" className="text-xs">Completed</Badge>
                </div>
              </div>
              <p className="text-sm text-gray-600 mb-3">{feature.description}</p>
              <div className="space-y-1">
                {feature.metrics.map((metric, metricIndex) => (
                  <div key={metricIndex} className="text-xs text-green-600 flex items-center gap-1">
                    <CheckCircle className="h-3 w-3" />
                    {metric}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance Metrics Dashboard
          </CardTitle>
          <CardDescription>
            Real-time performance across all discovery operations
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="space-y-3">
              <h4 className="font-semibold text-blue-600">Processing Performance</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Records Processed</span>
                  <span className="font-medium">{performanceMetrics.processing.records_processed.toLocaleString()}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Processing Speed</span>
                  <span className="font-medium">{performanceMetrics.processing.processing_speed}/hr</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Error Rate</span>
                  <span className="font-medium text-green-600">{performanceMetrics.processing.error_rate}%</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-green-600">Success Metrics</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Discovery Success</span>
                  <span className="font-medium">{performanceMetrics.success.discovery_success_rate}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Contact Quality</span>
                  <span className="font-medium">{performanceMetrics.success.contact_quality_score}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Conversion Rate</span>
                  <span className="font-medium text-green-600">{performanceMetrics.success.conversion_rate}%</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-orange-600">Cost Efficiency</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Per Discovery</span>
                  <span className="font-medium">${performanceMetrics.cost.cost_per_discovery}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>ROI per Case</span>
                  <span className="font-medium">${performanceMetrics.cost.roi_per_case}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Cost Savings</span>
                  <span className="font-medium text-green-600">{performanceMetrics.cost.cost_savings}%</span>
                </div>
              </div>
            </div>

            <div className="space-y-3">
              <h4 className="font-semibold text-purple-600">Quality Metrics</h4>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Data Accuracy</span>
                  <span className="font-medium">{performanceMetrics.quality.data_accuracy}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Contact Accuracy</span>
                  <span className="font-medium">{performanceMetrics.quality.contact_accuracy}%</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Customer Satisfaction</span>
                  <span className="font-medium text-green-600">{performanceMetrics.quality.customer_satisfaction}%</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderExamples = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold mb-2">Universal Discovery in Action</h3>
        <p className="text-gray-600">Real examples of automated contact discovery across different states and entity types</p>
      </div>

      <div className="grid grid-cols-1 gap-6">
        {discoveryExamples.map((example, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{example.type}</Badge>
                    <Badge variant="secondary">{example.state}</Badge>
                  </div>
                  <div>
                    <h4 className="font-semibold text-lg">{example.name}</h4>
                    <div className="text-sm text-gray-600">
                      Asset Value: ${example.asset_value.toLocaleString()}
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Discovery Time</div>
                      <div className="font-medium flex items-center gap-1">
                        <Clock className="h-4 w-4" />
                        {example.discovery_time} minutes
                      </div>
                    </div>
                    <div>
                      <div className="text-gray-600">Contact Methods</div>
                      <div className="font-medium">{example.contact_methods} found</div>
                    </div>
                  </div>
                  <div>
                    <div className="text-gray-600 text-sm">Success Probability</div>
                    <div className="flex items-center gap-2">
                      <Progress value={example.success_probability} className="h-2 flex-1" />
                      <span className="text-sm font-medium">{example.success_probability}%</span>
                    </div>
                  </div>
                </div>

                <div className="space-y-3">
                  <div>
                    <div className="text-sm font-medium text-green-800 mb-1">Best Contact Found:</div>
                    <div className="text-sm text-green-700 font-medium">{example.best_contact}</div>
                  </div>
                  <div className="space-y-1">
                    {example.details.map((detail, detailIndex) => (
                      <div key={detailIndex} className="text-xs text-gray-600 flex items-center gap-1">
                        <CheckCircle className="h-3 w-3 text-green-600" />
                        {detail}
                      </div>
                    ))}
                  </div>
                  <div className="text-right">
                    <div className="text-sm text-gray-600">Discovery Cost</div>
                    <div className="font-semibold text-green-600">${example.cost}</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card className="bg-green-50 border-green-200">
        <CardContent className="p-6">
          <div className="text-center">
            <Award className="h-12 w-12 text-green-600 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Universal Discovery ROI</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-green-600">10x</div>
                <div className="text-sm text-gray-600">Processing Speed vs Manual</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">89.2%</div>
                <div className="text-sm text-gray-600">Cost Reduction</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">$267</div>
                <div className="text-sm text-gray-600">Average ROI per Case</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderAIModels = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold mb-2">AI/ML Model Performance</h3>
        <p className="text-gray-600">Production AI models powering universal discovery automation</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {aiModels.map((model, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h4 className="font-semibold text-lg">{model.name}</h4>
                  <div className="text-sm text-gray-600">{model.type}</div>
                </div>
                <Badge variant="default">{model.status}</Badge>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="text-sm text-gray-600">Purpose</div>
                  <div className="text-sm font-medium">{model.purpose}</div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="text-sm text-gray-600">Accuracy</div>
                    <div className="text-lg font-bold text-green-600">{model.accuracy}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-600">Training Data</div>
                    <div className="text-sm font-medium">{model.training_data}</div>
                  </div>
                </div>

                <Progress value={model.accuracy} className="h-2" />
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Cpu className="h-5 w-5" />
            AI Infrastructure Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center">
              <div className="text-3xl font-bold text-blue-600">99.9%</div>
              <div className="text-sm text-gray-600">Model Uptime</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-green-600">&lt;100ms</div>
              <div className="text-sm text-gray-600">Inference Latency</div>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-purple-600">4</div>
              <div className="text-sm text-gray-600">Production Models</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderArchitecture = () => (
    <div className="space-y-6">
      <div className="text-center">
        <h3 className="text-xl font-bold mb-2">Universal Discovery Architecture</h3>
        <p className="text-gray-600">Enterprise-grade architecture supporting 25+ data source integrations</p>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Data Source Integration</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <FileText className="h-8 w-8 text-blue-600" />
              </div>
              <h4 className="font-semibold mb-2">Public Records</h4>
              <div className="text-sm text-gray-600">7 Sources</div>
              <div className="text-xs text-gray-500 mt-1">
                Property, Voter, Court, Business, Death, Marriage, Probate
              </div>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Database className="h-8 w-8 text-green-600" />
              </div>
              <h4 className="font-semibold mb-2">Commercial DBs</h4>
              <div className="text-sm text-gray-600">6 Sources</div>
              <div className="text-xs text-gray-500 mt-1">
                Skip Tracing, Credit, Phone, Address, Identity, Background
              </div>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Network className="h-8 w-8 text-purple-600" />
              </div>
              <h4 className="font-semibold mb-2">Social & Professional</h4>
              <div className="text-sm text-gray-600">5 Sources</div>
              <div className="text-xs text-gray-500 mt-1">
                LinkedIn, Facebook, Directories, Business Networks
              </div>
            </div>

            <div className="text-center">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Star className="h-8 w-8 text-orange-600" />
              </div>
              <h4 className="font-semibold mb-2">Specialized</h4>
              <div className="text-sm text-gray-600">7 Sources</div>
              <div className="text-xs text-gray-500 mt-1">
                Genealogy, Obituary, Cemetery, Licensing, Education, Military
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Processing Pipeline</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { step: "Data Ingestion", desc: "Universal CSV format detection", status: "✅" },
              { step: "Field Mapping", desc: "AI-powered field identification", status: "✅" },
              { step: "Entity Classification", desc: "Person/Business/Estate detection", status: "✅" },
              { step: "Multi-Source Search", desc: "Parallel discovery across 25+ sources", status: "✅" },
              { step: "Contact Validation", desc: "Real-time verification and scoring", status: "✅" },
              { step: "Result Consolidation", desc: "AI-powered result aggregation", status: "✅" }
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 bg-gray-50 rounded-lg">
                <div className="text-lg">{item.status}</div>
                <div className="flex-1">
                  <div className="font-medium">{item.step}</div>
                  <div className="text-sm text-gray-600">{item.desc}</div>
                </div>
                <ArrowRight className="h-4 w-4 text-gray-400" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>System Capabilities</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { capability: "State Coverage", value: "All 50 States", icon: Globe },
              { capability: "Processing Speed", value: "127 records/hour", icon: Zap },
              { capability: "Automation Rate", value: "87.3%", icon: Bot },
              { capability: "Data Sources", value: "25+ APIs", icon: Network },
              { capability: "Success Rate", value: "81.7%", icon: Target },
              { capability: "Cost Efficiency", value: "89.2% savings", icon: DollarSign }
            ].map((item, index) => (
              <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                <item.icon className="h-6 w-6 text-blue-600" />
                <div className="flex-1">
                  <div className="font-medium">{item.capability}</div>
                  <div className="text-sm text-gray-600">{item.value}</div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Phase 6: Universal AI Discovery</h2>
          <p className="text-gray-600">
            Revolutionary automated contact discovery across all 50 states
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant="default" className="px-3 py-1 bg-green-600">
            ✅ Phase 6 Complete
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade for Full Access
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Discovery Overview</TabsTrigger>
          <TabsTrigger value="examples">Live Examples</TabsTrigger>
          <TabsTrigger value="ai-models">AI Models</TabsTrigger>
          <TabsTrigger value="architecture">Architecture</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="examples" className="space-y-6">
          {renderExamples()}
        </TabsContent>

        <TabsContent value="ai-models" className="space-y-6">
          {renderAIModels()}
        </TabsContent>

        <TabsContent value="architecture" className="space-y-6">
          {renderArchitecture()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Search className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Experience Universal AI Discovery</h3>
              <p className="text-gray-600 mb-4">
                Unlock automated contact discovery across all states with 80-90% automation, 
                death detection, heir discovery, and enterprise-grade performance
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 