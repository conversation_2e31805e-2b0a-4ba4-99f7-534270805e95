import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  FileText, 
  Key, 
  Globe, 
  CheckCircle, 
  AlertTriangle,
  Save,
  TestTube,
  ExternalLink,
  RefreshCw,
  Gift,
  Zap
} from 'lucide-react'
import { signRequestService, type SignRequestConfig } from '@/services/signRequestService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

export const SignRequestConfigComponent: React.FC = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Configuration state
  const [config, setConfig] = useState<SignRequestConfig>({
    apiToken: '',
    baseUrl: 'https://signrequest.com/api/v1',
    webhookUrl: 'http://localhost:3005/api/signrequest/webhook'
  })
  
  const [isConfigured, setIsConfigured] = useState(false)

  useEffect(() => {
    loadConfiguration()
  }, [])

  const loadConfiguration = async () => {
    setLoading(true)
    
    try {
      // In a real implementation, this would load from your database
      // For now, we'll check if configuration exists in localStorage
      const savedConfig = localStorage.getItem('signrequest_config')
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig)
        setConfig(parsedConfig)
        setIsConfigured(true)
        
        // Initialize the service
        signRequestService.initialize(parsedConfig)
      }
    } catch (error) {
      console.error('Error loading SignRequest configuration:', error)
      setError('Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  const saveConfiguration = async () => {
    setLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate required fields
      if (!config.apiToken) {
        setError('Please enter your SignRequest API token')
        return
      }

      // Save to localStorage (in real app, save to database)
      localStorage.setItem('signrequest_config', JSON.stringify(config))
      
      // Initialize the service
      signRequestService.initialize(config)
      
      setIsConfigured(true)
      setSuccess('Configuration saved successfully')
      
      toast({
        title: "Configuration Saved",
        description: "SignRequest integration has been configured successfully.",
      })
    } catch (error) {
      setError('Failed to save configuration')
      console.error('Error saving SignRequest configuration:', error)
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    setTesting(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Test the SignRequest connection by creating a test document
      const testDocument = {
        from_email: user?.email || '<EMAIL>',
        subject: 'SignRequest Connection Test',
        message: 'This is a test document to verify SignRequest integration.',
        send_immediately: false,
        signers: [{
          email: user?.email || '<EMAIL>',
          first_name: user?.first_name || 'Test',
          last_name: user?.last_name || 'User',
          needs_to_sign: true
        }],
        documents: [{
          name: 'Test Document.pdf',
          file: 'JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNC=' // Minimal PDF
        }]
      }

      const { data, error } = await signRequestService.createSignRequest(testDocument)
      
      if (error) {
        setError(`Connection test failed: ${error.message || 'Unknown error'}`)
        return
      }
      
      if (data) {
        setSuccess(`Connection test successful! Test document created: ${data.uuid}`)
        
        toast({
          title: "Connection Test Successful",
          description: "SignRequest integration is working correctly.",
        })
      }
    } catch (error) {
      setError('Connection test failed')
      console.error('SignRequest connection test error:', error)
    } finally {
      setTesting(false)
    }
  }

  const resetConfiguration = () => {
    setConfig({
      apiToken: '',
      baseUrl: 'https://signrequest.com/api/v1',
      webhookUrl: 'http://localhost:3005/api/signrequest/webhook'
    })
    setIsConfigured(false)
    localStorage.removeItem('signrequest_config')
    
    toast({
      title: "Configuration Reset",
      description: "SignRequest configuration has been cleared.",
    })
  }

  // Check if user has admin permissions
  if (user?.role !== 'admin') {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You need administrator privileges to configure SignRequest integration.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">SignRequest Configuration</h2>
          <p className="text-gray-600">Configure electronic signature integration</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary" className="flex items-center space-x-1">
            <Gift className="h-3 w-3" />
            <span>10 Free Docs/Month</span>
          </Badge>
          {isConfigured && (
            <Badge variant="default" className="flex items-center space-x-1">
              <CheckCircle className="h-3 w-3" />
              <span>Configured</span>
            </Badge>
          )}
        </div>
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="configuration" className="space-y-6">
        <TabsList>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="configuration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>API Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure your SignRequest API credentials and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">API Token *</label>
                <Input
                  type="password"
                  placeholder="Enter your SignRequest API token"
                  value={config.apiToken}
                  onChange={(e) => setConfig({ ...config, apiToken: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Found in your SignRequest account settings under API
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Base URL</label>
                <Input
                  value={config.baseUrl}
                  readOnly
                  className="bg-gray-50"
                />
                <p className="text-xs text-gray-500">
                  SignRequest API endpoint (same for all users)
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Webhook URL</label>
                <Input
                  placeholder="Webhook endpoint URL"
                  value={config.webhookUrl}
                  onChange={(e) => setConfig({ ...config, webhookUrl: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  URL where SignRequest will send event notifications
                </p>
              </div>

              <div className="flex space-x-2">
                <Button 
                  onClick={saveConfiguration}
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Save Configuration
                </Button>
                <Button 
                  variant="outline"
                  onClick={resetConfiguration}
                  disabled={loading}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Setup Instructions</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">1</div>
                  <div>
                    <h4 className="font-medium">Create SignRequest Account</h4>
                    <p className="text-sm text-gray-600">
                      Sign up for a free account at{' '}
                      <a href="https://signrequest.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        signrequest.com
                      </a>{' '}
                      (10 free documents per month!)
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                  <div>
                    <h4 className="font-medium">Get API Token</h4>
                    <p className="text-sm text-gray-600">
                      Go to your account settings and generate an API token
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                  <div>
                    <h4 className="font-medium">Configure Webhook</h4>
                    <p className="text-sm text-gray-600">
                      Set up webhook URL to receive real-time updates
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                  <div>
                    <h4 className="font-medium">Test Integration</h4>
                    <p className="text-sm text-gray-600">
                      Use the testing tab to verify your configuration is working correctly
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TestTube className="h-5 w-5" />
                <span>Connection Testing</span>
              </CardTitle>
              <CardDescription>
                Test your SignRequest integration to ensure it's working correctly
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Test SignRequest Connection</h3>
                <p className="text-gray-500 mb-6">
                  This will create a test document to verify your SignRequest integration is working properly.
                </p>
                
                <Button 
                  onClick={testConnection}
                  disabled={testing || !isConfigured}
                  size="lg"
                >
                  {testing ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <TestTube className="mr-2 h-4 w-4" />
                  )}
                  {testing ? 'Testing Connection...' : 'Test Connection'}
                </Button>
                
                {!isConfigured && (
                  <p className="text-sm text-red-600 mt-2">
                    Please configure SignRequest settings first
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>SignRequest Features</span>
              </CardTitle>
              <CardDescription>
                What you get with SignRequest integration
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-medium text-green-600">✅ Free Tier Benefits</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• 10 documents per month (FREE)</li>
                    <li>• Unlimited signers per document</li>
                    <li>• Email notifications</li>
                    <li>• Basic templates</li>
                    <li>• Audit trail</li>
                    <li>• Mobile-friendly signing</li>
                  </ul>
                </div>
                
                <div className="space-y-4">
                  <h4 className="font-medium text-blue-600">🚀 Premium Features</h4>
                  <ul className="text-sm text-gray-600 space-y-2">
                    <li>• Unlimited documents ($8/month)</li>
                    <li>• Advanced templates</li>
                    <li>• Bulk sending</li>
                    <li>• API access</li>
                    <li>• Custom branding</li>
                    <li>• Advanced integrations</li>
                  </ul>
                </div>
              </div>

              <div className="mt-6 p-4 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">Perfect for AssetHunterPro</h4>
                <p className="text-sm text-green-700">
                  SignRequest's free tier gives you 10 documents per month, which is perfect for getting started 
                  with asset recovery agreements, power of attorney forms, and settlement documents. 
                  As your business grows, you can easily upgrade for unlimited documents.
                </p>
              </div>

              <Button variant="outline" className="w-full mt-4">
                <ExternalLink className="mr-2 h-4 w-4" />
                View SignRequest Pricing
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
