import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  TrendingDown, 
  DollarSign, 
  Users, 
  FileText, 
  Clock, 
  BarChart3, 
  PieChart,
  Activity,
  Target,
  Calendar,
  Download,
  Filter,
  RefreshCcw,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface AnalyticsData {
  overview: {
    totalClaims: number;
    totalValue: number;
    completedClaims: number;
    successRate: number;
    avgRecoveryTime: number;
    activeAgents: number;
    monthlyGrowth: number;
    revenueGrowth: number;
  };
  performanceMetrics: {
    agentPerformance: AgentPerformance[];
    teamPerformance: TeamPerformance[];
    timeMetrics: TimeMetrics;
  };
  claimAnalytics: {
    statusDistribution: StatusCount[];
    stateDistribution: StateCount[];
    valueDistribution: ValueRange[];
    trendData: TrendPoint[];
  };
  emailAnalytics: {
    totalSent: number;
    openRate: number;
    responseRate: number;
    followUpsPending: number;
  };
}

interface AgentPerformance {
  agentId: string;
  agentName: string;
  totalClaims: number;
  completedClaims: number;
  successRate: number;
  totalRecovery: number;
  avgDaysToComplete: number;
  activitiesLogged: number;
  lastActivityDate?: string;
}

interface TeamPerformance {
  teamId: string;
  teamName: string;
  memberCount: number;
  totalClaims: number;
  completedClaims: number;
  teamSuccessRate: number;
  totalTeamRecovery: number;
}

interface TimeMetrics {
  avgResponseTime: number;
  avgCompletionTime: number;
  medianCompletionTime: number;
  fastestCompletion: number;
}

interface StatusCount {
  status: string;
  count: number;
  percentage: number;
}

interface StateCount {
  state: string;
  count: number;
  totalValue: number;
}

interface ValueRange {
  range: string;
  count: number;
  totalValue: number;
}

interface TrendPoint {
  date: string;
  newClaims: number;
  completedClaims: number;
  totalValue: number;
}

export const AnalyticsDashboard: React.FC = () => {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [dateRange, setDateRange] = useState('30');
  const [selectedTeam, setSelectedTeam] = useState('all');
  const [teams, setTeams] = useState<{ id: string; name: string }[]>([]);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    loadAnalyticsData();
    loadTeams();
  }, [dateRange, selectedTeam]);

  const loadTeams = async () => {
    try {
      const { data } = await supabase
        .from('teams')
        .select('id, name')
        .eq('is_active', true);
      
      setTeams(data || []);
    } catch (error) {
      console.error('Failed to load teams:', error);
    }
  };

  const loadAnalyticsData = async () => {
    try {
      setLoading(true);
      
      // Calculate date range
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(endDate.getDate() - parseInt(dateRange));

      // Load overview data
      const overview = await loadOverviewData(startDate, endDate);
      
      // Load performance metrics
      const performanceMetrics = await loadPerformanceMetrics(startDate, endDate);
      
      // Load claim analytics
      const claimAnalytics = await loadClaimAnalytics(startDate, endDate);
      
      // Load email analytics
      const emailAnalytics = await loadEmailAnalytics(startDate, endDate);

      setAnalyticsData({
        overview,
        performanceMetrics,
        claimAnalytics,
        emailAnalytics
      });

    } catch (error) {
      console.error('Failed to load analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadOverviewData = async (startDate: Date, endDate: Date) => {
    const { data: claims } = await supabase
      .from('claims')
      .select('*')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    const { data: allClaims } = await supabase
      .from('claims')
      .select('id, status, amount, actual_recovery_amount, created_at, completed_at');

    const { data: agents } = await supabase
      .from('users')
      .select('id')
      .eq('role', 'agent')
      .eq('is_active', true);

    const totalClaims = claims?.length || 0;
    const totalValue = claims?.reduce((sum, claim) => sum + (claim.amount || 0), 0) || 0;
    const completedClaims = claims?.filter(c => c.status === 'completed').length || 0;
    const successRate = totalClaims > 0 ? (completedClaims / totalClaims) * 100 : 0;
    
    // Calculate average recovery time
    const completedClaimsWithDates = allClaims?.filter(c => 
      c.status === 'completed' && c.created_at && c.completed_at
    ) || [];
    
    const avgRecoveryTime = completedClaimsWithDates.length > 0 
      ? completedClaimsWithDates.reduce((sum, claim) => {
          const created = new Date(claim.created_at);
          const completed = new Date(claim.completed_at!);
          return sum + (completed.getTime() - created.getTime());
        }, 0) / (completedClaimsWithDates.length * 24 * 60 * 60 * 1000)
      : 0;

    // Calculate growth metrics (comparing to previous period)
    const previousPeriodStart = new Date(startDate);
    previousPeriodStart.setDate(previousPeriodStart.getDate() - parseInt(dateRange));
    
    const { data: previousClaims } = await supabase
      .from('claims')
      .select('id, amount')
      .gte('created_at', previousPeriodStart.toISOString())
      .lt('created_at', startDate.toISOString());

    const previousTotal = previousClaims?.length || 0;
    const previousValue = previousClaims?.reduce((sum, claim) => sum + (claim.amount || 0), 0) || 0;
    
    const monthlyGrowth = previousTotal > 0 ? ((totalClaims - previousTotal) / previousTotal) * 100 : 0;
    const revenueGrowth = previousValue > 0 ? ((totalValue - previousValue) / previousValue) * 100 : 0;

    return {
      totalClaims,
      totalValue,
      completedClaims,
      successRate,
      avgRecoveryTime,
      activeAgents: agents?.length || 0,
      monthlyGrowth,
      revenueGrowth
    };
  };

  const loadPerformanceMetrics = async (startDate: Date, endDate: Date) => {
    // Load agent performance
    const { data: agentData } = await supabase
      .from('users')
      .select(`
        id,
        first_name,
        last_name,
        claims!assigned_agent_id (
          id,
          status,
          amount,
          actual_recovery_amount,
          created_at,
          completed_at
        ),
        claim_activities!agent_id (
          id,
          created_at
        )
      `)
      .eq('role', 'agent')
      .eq('is_active', true);

    const agentPerformance: AgentPerformance[] = (agentData || []).map(agent => {
      const claims = agent.claims || [];
      const activities = agent.claim_activities || [];
      const completedClaims = claims.filter((c: any) => c.status === 'completed');
      
      const totalRecovery = completedClaims.reduce((sum: number, claim: any) => 
        sum + (claim.actual_recovery_amount || 0), 0
      );

      const avgDaysToComplete = completedClaims.length > 0
        ? completedClaims.reduce((sum: number, claim: any) => {
            if (claim.created_at && claim.completed_at) {
              const created = new Date(claim.created_at);
              const completed = new Date(claim.completed_at);
              return sum + (completed.getTime() - created.getTime()) / (24 * 60 * 60 * 1000);
            }
            return sum;
          }, 0) / completedClaims.length
        : 0;

      const lastActivity = activities.length > 0
        ? new Date(Math.max(...activities.map((a: any) => new Date(a.created_at).getTime())))
        : undefined;

      return {
        agentId: agent.id,
        agentName: `${agent.first_name} ${agent.last_name}`,
        totalClaims: claims.length,
        completedClaims: completedClaims.length,
        successRate: claims.length > 0 ? (completedClaims.length / claims.length) * 100 : 0,
        totalRecovery,
        avgDaysToComplete,
        activitiesLogged: activities.length,
        lastActivityDate: lastActivity?.toISOString()
      };
    });

    // Load team performance
    const { data: teamData } = await supabase
      .from('teams')
      .select(`
        id,
        name,
        users!team_id (
          id,
          claims!assigned_agent_id (
            id,
            status,
            amount,
            actual_recovery_amount
          )
        )
      `)
      .eq('is_active', true);

    const teamPerformance: TeamPerformance[] = (teamData || []).map(team => {
      const members = team.users || [];
      const allClaims = members.flatMap((user: any) => user.claims || []);
      const completedClaims = allClaims.filter((c: any) => c.status === 'completed');
      
      const totalTeamRecovery = completedClaims.reduce((sum: number, claim: any) => 
        sum + (claim.actual_recovery_amount || 0), 0
      );

      return {
        teamId: team.id,
        teamName: team.name,
        memberCount: members.length,
        totalClaims: allClaims.length,
        completedClaims: completedClaims.length,
        teamSuccessRate: allClaims.length > 0 ? (completedClaims.length / allClaims.length) * 100 : 0,
        totalTeamRecovery
      };
    });

    // Calculate time metrics
    const { data: completedClaims } = await supabase
      .from('claims')
      .select('created_at, completed_at, claim_activities!claim_id(created_at)')
      .eq('status', 'completed')
      .not('completed_at', 'is', null);

    const timeMetrics: TimeMetrics = {
      avgResponseTime: 0, // Calculate from first activity
      avgCompletionTime: 0,
      medianCompletionTime: 0,
      fastestCompletion: 0
    };

    if (completedClaims && completedClaims.length > 0) {
      const completionTimes = completedClaims
        .filter(claim => claim.created_at && claim.completed_at)
        .map(claim => {
          const created = new Date(claim.created_at);
          const completed = new Date(claim.completed_at!);
          return (completed.getTime() - created.getTime()) / (24 * 60 * 60 * 1000);
        });

      if (completionTimes.length > 0) {
        timeMetrics.avgCompletionTime = completionTimes.reduce((sum, time) => sum + time, 0) / completionTimes.length;
        completionTimes.sort((a, b) => a - b);
        timeMetrics.medianCompletionTime = completionTimes[Math.floor(completionTimes.length / 2)];
        timeMetrics.fastestCompletion = Math.min(...completionTimes);
      }
    }

    return {
      agentPerformance,
      teamPerformance,
      timeMetrics
    };
  };

  const loadClaimAnalytics = async (startDate: Date, endDate: Date) => {
    const { data: claims } = await supabase
      .from('claims')
      .select('status, state, amount, created_at')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (!claims) {
      return {
        statusDistribution: [],
        stateDistribution: [],
        valueDistribution: [],
        trendData: []
      };
    }

    // Status distribution
    const statusCounts: { [key: string]: number } = {};
    claims.forEach(claim => {
      statusCounts[claim.status] = (statusCounts[claim.status] || 0) + 1;
    });

    const statusDistribution: StatusCount[] = Object.entries(statusCounts).map(([status, count]) => ({
      status,
      count,
      percentage: (count / claims.length) * 100
    }));

    // State distribution
    const stateCounts: { [key: string]: { count: number; totalValue: number } } = {};
    claims.forEach(claim => {
      if (!stateCounts[claim.state]) {
        stateCounts[claim.state] = { count: 0, totalValue: 0 };
      }
      stateCounts[claim.state].count++;
      stateCounts[claim.state].totalValue += claim.amount || 0;
    });

    const stateDistribution: StateCount[] = Object.entries(stateCounts).map(([state, data]) => ({
      state,
      count: data.count,
      totalValue: data.totalValue
    }));

    // Value distribution
    const valueRanges = [
      { range: '$0 - $1K', min: 0, max: 1000 },
      { range: '$1K - $5K', min: 1000, max: 5000 },
      { range: '$5K - $10K', min: 5000, max: 10000 },
      { range: '$10K - $50K', min: 10000, max: 50000 },
      { range: '$50K+', min: 50000, max: Infinity }
    ];

    const valueDistribution: ValueRange[] = valueRanges.map(range => {
      const claimsInRange = claims.filter(claim => 
        (claim.amount || 0) >= range.min && (claim.amount || 0) < range.max
      );
      
      return {
        range: range.range,
        count: claimsInRange.length,
        totalValue: claimsInRange.reduce((sum, claim) => sum + (claim.amount || 0), 0)
      };
    });

    // Trend data (daily for last 30 days)
    const trendData: TrendPoint[] = [];
    for (let i = parseInt(dateRange) - 1; i >= 0; i--) {
      const date = new Date();
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];
      
      const dayStart = new Date(date);
      dayStart.setHours(0, 0, 0, 0);
      const dayEnd = new Date(date);
      dayEnd.setHours(23, 59, 59, 999);
      
      const dayClaims = claims.filter(claim => {
        const claimDate = new Date(claim.created_at);
        return claimDate >= dayStart && claimDate <= dayEnd;
      });

      trendData.push({
        date: dateStr,
        newClaims: dayClaims.length,
        completedClaims: dayClaims.filter(c => c.status === 'completed').length,
        totalValue: dayClaims.reduce((sum, claim) => sum + (claim.amount || 0), 0)
      });
    }

    return {
      statusDistribution,
      stateDistribution,
      valueDistribution,
      trendData
    };
  };

  const loadEmailAnalytics = async (startDate: Date, endDate: Date) => {
    const { data: emails } = await supabase
      .from('emails')
      .select('status, open_count, direction, follow_up_date, follow_up_completed')
      .gte('created_at', startDate.toISOString())
      .lte('created_at', endDate.toISOString());

    if (!emails) {
      return {
        totalSent: 0,
        openRate: 0,
        responseRate: 0,
        followUpsPending: 0
      };
    }

    const totalSent = emails.filter(e => e.status === 'sent' || e.status === 'delivered').length;
    const opened = emails.filter(e => e.open_count > 0).length;
    const responses = emails.filter(e => e.direction === 'inbound').length;
    const followUpsPending = emails.filter(e => 
      e.follow_up_date && !e.follow_up_completed && new Date(e.follow_up_date) <= new Date()
    ).length;

    return {
      totalSent,
      openRate: totalSent > 0 ? (opened / totalSent) * 100 : 0,
      responseRate: totalSent > 0 ? (responses / totalSent) * 100 : 0,
      followUpsPending
    };
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  const formatPercentage = (value: number) => {
    return `${value.toFixed(1)}%`;
  };

  const getGrowthIcon = (growth: number) => {
    if (growth > 0) return <TrendingUp className="h-4 w-4 text-green-600" />;
    if (growth < 0) return <TrendingDown className="h-4 w-4 text-red-600" />;
    return <Activity className="h-4 w-4 text-gray-400" />;
  };

  const getGrowthColor = (growth: number) => {
    if (growth > 0) return 'text-green-600';
    if (growth < 0) return 'text-red-600';
    return 'text-gray-400';
  };

  const exportReport = async () => {
    // TODO: Implement export functionality
    console.log('Exporting analytics report...');
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="text-center">
          <RefreshCcw className="h-8 w-8 animate-spin mx-auto mb-4 text-blue-600" />
          <p className="text-gray-600">Loading analytics data...</p>
        </div>
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="text-center py-12">
        <AlertTriangle className="h-12 w-12 text-orange-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">Unable to Load Analytics</h3>
        <p className="text-gray-600 mb-4">There was an error loading the analytics data.</p>
        <Button onClick={loadAnalyticsData} variant="outline">
          <RefreshCcw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Analytics Dashboard</h1>
          <p className="text-gray-600">Performance insights and key metrics</p>
        </div>
        
        <div className="flex items-center gap-3">
          {/* Date Range Filter */}
          <Select value={dateRange} onValueChange={setDateRange}>
            <SelectTrigger className="w-40">
              <Calendar className="h-4 w-4 mr-2" />
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7">Last 7 days</SelectItem>
              <SelectItem value="30">Last 30 days</SelectItem>
              <SelectItem value="90">Last 90 days</SelectItem>
              <SelectItem value="180">Last 6 months</SelectItem>
              <SelectItem value="365">Last year</SelectItem>
            </SelectContent>
          </Select>

          {/* Team Filter */}
          <Select value={selectedTeam} onValueChange={setSelectedTeam}>
            <SelectTrigger className="w-40">
              <Users className="h-4 w-4 mr-2" />
              <SelectValue placeholder="All Teams" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Teams</SelectItem>
              {teams.map(team => (
                <SelectItem key={team.id} value={team.id}>
                  {team.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Button variant="outline" onClick={loadAnalyticsData}>
            <RefreshCcw className="h-4 w-4 mr-2" />
            Refresh
          </Button>

          <Button variant="outline" onClick={exportReport}>
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="claims">Claims Analysis</TabsTrigger>
          <TabsTrigger value="communications">Communications</TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Key Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Claims</CardTitle>
                <FileText className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.overview.totalClaims.toLocaleString()}</div>
                <div className="flex items-center mt-1">
                  {getGrowthIcon(analyticsData.overview.monthlyGrowth)}
                  <span className={`text-xs ml-1 ${getGrowthColor(analyticsData.overview.monthlyGrowth)}`}>
                    {formatPercentage(Math.abs(analyticsData.overview.monthlyGrowth))} from last period
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Value</CardTitle>
                <DollarSign className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatCurrency(analyticsData.overview.totalValue)}</div>
                <div className="flex items-center mt-1">
                  {getGrowthIcon(analyticsData.overview.revenueGrowth)}
                  <span className={`text-xs ml-1 ${getGrowthColor(analyticsData.overview.revenueGrowth)}`}>
                    {formatPercentage(Math.abs(analyticsData.overview.revenueGrowth))} from last period
                  </span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
                <Target className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatPercentage(analyticsData.overview.successRate)}</div>
                <p className="text-xs text-muted-foreground mt-1">
                  {analyticsData.overview.completedClaims} of {analyticsData.overview.totalClaims} completed
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Avg Recovery Time</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.overview.avgRecoveryTime.toFixed(1)} days</div>
                <p className="text-xs text-muted-foreground mt-1">
                  From initial contact to completion
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Status Distribution */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <PieChart className="h-5 w-5" />
                  Claim Status Distribution
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.claimAnalytics.statusDistribution.map((status) => (
                    <div key={status.status} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-blue-500"></div>
                        <span className="capitalize text-sm">{status.status.replace('_', ' ')}</span>
                      </div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm font-medium">{status.count}</span>
                        <Badge variant="secondary">{formatPercentage(status.percentage)}</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <BarChart3 className="h-5 w-5" />
                  Top States by Claims
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.claimAnalytics.stateDistribution
                    .sort((a, b) => b.count - a.count)
                    .slice(0, 5)
                    .map((state) => (
                    <div key={state.state} className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <div className="w-3 h-3 rounded-full bg-green-500"></div>
                        <span className="text-sm font-medium">{state.state}</span>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium">{state.count} claims</div>
                        <div className="text-xs text-gray-500">{formatCurrency(state.totalValue)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Performance Tab */}
        <TabsContent value="performance" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Agent Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Top Performing Agents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.performanceMetrics.agentPerformance
                    .sort((a, b) => b.successRate - a.successRate)
                    .slice(0, 5)
                    .map((agent, index) => (
                    <div key={agent.agentId} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold text-white ${
                          index === 0 ? 'bg-yellow-500' : 
                          index === 1 ? 'bg-gray-400' : 
                          index === 2 ? 'bg-orange-600' : 'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <div className="font-medium">{agent.agentName}</div>
                          <div className="text-sm text-gray-500">
                            {agent.completedClaims}/{agent.totalClaims} claims completed
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-green-600">{formatPercentage(agent.successRate)}</div>
                        <div className="text-sm text-gray-500">{formatCurrency(agent.totalRecovery)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Team Performance */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Team Performance
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {analyticsData.performanceMetrics.teamPerformance.map((team) => (
                    <div key={team.teamId} className="flex items-center justify-between p-3 rounded-lg bg-gray-50">
                      <div>
                        <div className="font-medium">{team.teamName}</div>
                        <div className="text-sm text-gray-500">
                          {team.memberCount} agents • {team.completedClaims}/{team.totalClaims} completed
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-blue-600">{formatPercentage(team.teamSuccessRate)}</div>
                        <div className="text-sm text-gray-500">{formatCurrency(team.totalTeamRecovery)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Time Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Avg Response Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.performanceMetrics.timeMetrics.avgResponseTime.toFixed(1)}h</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Avg Completion Time</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.performanceMetrics.timeMetrics.avgCompletionTime.toFixed(1)} days</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Median Completion</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.performanceMetrics.timeMetrics.medianCompletionTime.toFixed(1)} days</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Fastest Completion</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.performanceMetrics.timeMetrics.fastestCompletion.toFixed(1)} days</div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Claims Analysis Tab */}
        <TabsContent value="claims" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Value Distribution */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Claims by Value Range
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {analyticsData.claimAnalytics.valueDistribution.map((range) => (
                    <div key={range.range} className="flex items-center justify-between">
                      <span className="text-sm">{range.range}</span>
                      <div className="text-right">
                        <div className="text-sm font-medium">{range.count} claims</div>
                        <div className="text-xs text-gray-500">{formatCurrency(range.totalValue)}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Trend Chart Placeholder */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5" />
                  Claims Trend
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>Chart visualization will be implemented here</p>
                    <p className="text-sm">Showing {analyticsData.claimAnalytics.trendData.length} data points</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Communications Tab */}
        <TabsContent value="communications" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Emails Sent</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{analyticsData.emailAnalytics.totalSent}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Open Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatPercentage(analyticsData.emailAnalytics.openRate)}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Response Rate</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{formatPercentage(analyticsData.emailAnalytics.responseRate)}</div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm">Pending Follow-ups</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{analyticsData.emailAnalytics.followUpsPending}</div>
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>Communication Insights</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center gap-3 p-3 bg-blue-50 rounded-lg">
                  <CheckCircle className="h-5 w-5 text-blue-600" />
                  <div>
                    <div className="font-medium">Email Performance</div>
                    <div className="text-sm text-gray-600">
                      Strong open rate of {formatPercentage(analyticsData.emailAnalytics.openRate)} indicates good subject lines
                    </div>
                  </div>
                </div>
                
                {analyticsData.emailAnalytics.followUpsPending > 0 && (
                  <div className="flex items-center gap-3 p-3 bg-orange-50 rounded-lg">
                    <AlertTriangle className="h-5 w-5 text-orange-600" />
                    <div>
                      <div className="font-medium">Follow-ups Needed</div>
                      <div className="text-sm text-gray-600">
                        {analyticsData.emailAnalytics.followUpsPending} emails require follow-up action
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 