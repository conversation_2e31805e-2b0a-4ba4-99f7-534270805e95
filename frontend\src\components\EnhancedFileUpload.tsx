import React, { useState, useCallback, useRef } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle, TrendingUp, Clock } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { LargeFileProcessor, ProcessingProgress, ProcessingStats } from '../services/largeFileProcessor';
import { PlanEnforcementEngine, ValidationResult, User } from '../services/planEnforcementService';
import { StateDetectionResult, StandardizedRecord } from '../services/universalFieldMapper';
import { UpgradeModal } from './UpgradeModal';
import { ProcessingProgressDisplay } from './ProcessingProgressDisplay';

interface EnhancedFileUploadProps {
  currentUser: User;
  onUploadComplete?: (results: {
    processedRecords: StandardizedRecord[];
    processingStats: ProcessingStats;
    stateDetection: StateDetectionResult;
  }) => void;
  onUploadError?: (error: string) => void;
}

export const EnhancedFileUpload: React.FC<EnhancedFileUploadProps> = ({
  currentUser,
  onUploadComplete,
  onUploadError
}) => {
  // State management
  const [isDragOver, setIsDragOver] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<ProcessingProgress | null>(null);
  const [validationResult, setValidationResult] = useState<ValidationResult | null>(null);
  const [showUpgradeModal, setShowUpgradeModal] = useState(false);
  const [processingStats, setProcessingStats] = useState<ProcessingStats | null>(null);
  const [stateDetection, setStateDetection] = useState<StateDetectionResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Service instances
  const processor = new LargeFileProcessor();
  const planEnforcement = new PlanEnforcementEngine();

  // File validation and processing
  const validateAndProcessFile = useCallback(async (file: File) => {
    try {
      setIsProcessing(true);
      setValidationResult(null);
      setUploadProgress(null);

      // Step 1: Plan validation
      console.log('🔍 Validating file against plan limitations...');
      const validation = planEnforcement.validateUpload(file, currentUser);
      setValidationResult(validation);

      if (!validation.allowed) {
        console.log('❌ Upload blocked:', validation.error);
        setShowUpgradeModal(true);
        setIsProcessing(false);
        return;
      }

      console.log('✅ Plan validation passed');

      // Step 2: Process file with real-time progress
      console.log('🚀 Starting file processing...');
      const results = await processor.processLargeCSV(
        file,
        (progress: ProcessingProgress) => {
          setUploadProgress(progress);
          console.log(`📊 Progress: ${progress.stage} - ${progress.percentage.toFixed(1)}%`);
        }
      );

      // Step 3: Store results and notify parent
      setProcessingStats(results.processingStats);
      setStateDetection(results.stateDetection);
      setIsProcessing(false);

      console.log('🎉 File processing completed successfully');
      console.log(`📈 Processed ${results.processedRecords.length} records`);
      console.log(`🕒 Processing time: ${(results.processingStats.processingTime / 1000).toFixed(1)}s`);
      console.log(`⚡ Speed: ${results.processingStats.recordsPerSecond.toFixed(0)} records/sec`);

      if (onUploadComplete) {
        onUploadComplete({
          processedRecords: results.processedRecords,
          processingStats: results.processingStats,
          stateDetection: results.stateDetection
        });
      }

    } catch (error) {
      console.error('❌ File processing failed:', error);
      setIsProcessing(false);
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      if (onUploadError) {
        onUploadError(errorMessage);
      }
    }
  }, [currentUser, processor, planEnforcement, onUploadComplete, onUploadError]);

  // Drag and drop handlers
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    const csvFile = files.find(file => file.name.toLowerCase().endsWith('.csv'));
    
    if (csvFile) {
      validateAndProcessFile(csvFile);
    } else {
      if (onUploadError) {
        onUploadError('Please select a CSV file');
      }
    }
  }, [validateAndProcessFile, onUploadError]);

  // File input handler
  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      validateAndProcessFile(file);
    }
  }, [validateAndProcessFile]);

  // Format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  // Format processing rate
  const formatRate = (rate: number): string => {
    if (rate < 1000) return `${rate.toFixed(0)} rec/sec`;
    return `${(rate / 1000).toFixed(1)}k rec/sec`;
  };

  return (
    <div className="space-y-6">
      {/* Main Upload Area */}
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="w-5 h-5" />
            Enhanced CSV Upload
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Upload Zone */}
          <div
            className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
              isDragOver
                ? 'border-blue-500 bg-blue-50'
                : isProcessing
                ? 'border-gray-300 bg-gray-50'
                : 'border-gray-300 hover:border-gray-400'
            }`}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
            onDrop={handleDrop}
          >
            <input
              ref={fileInputRef}
              type="file"
              accept=".csv"
              onChange={handleFileSelect}
              className="hidden"
              disabled={isProcessing}
            />
            
            {isProcessing ? (
              <div className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-blue-100 rounded-full flex items-center justify-center">
                  <Clock className="w-8 h-8 text-blue-600 animate-spin" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Processing Your File</h3>
                  <p className="text-gray-600">
                    {uploadProgress?.stage === 'detecting' && 'Detecting file format...'}
                    {uploadProgress?.stage === 'processing' && 'Processing records...'}
                    {uploadProgress?.stage === 'analyzing' && 'Analyzing for duplicates...'}
                    {uploadProgress?.stage === 'saving' && 'Saving to database...'}
                    {uploadProgress?.stage === 'complete' && 'Processing complete!'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                <div className="w-16 h-16 mx-auto bg-gray-100 rounded-full flex items-center justify-center">
                  <FileText className="w-8 h-8 text-gray-400" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">
                    Drop your CSV file here, or{' '}
                    <button
                      onClick={() => fileInputRef.current?.click()}
                      className="text-blue-600 hover:text-blue-800 underline"
                    >
                      browse
                    </button>
                  </h3>
                  <p className="text-gray-600 mt-2">
                    Supports any state format • Up to 300MB • Real-time processing
                  </p>
                </div>
              </div>
            )}
          </div>

          {/* Plan Information */}
          <div className="mt-4 p-4 bg-gray-50 rounded-lg">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Current Plan: {currentUser.planId.toUpperCase()}</p>
                <p className="text-sm text-gray-600">
                  {validationResult?.usageInfo && (
                    <>
                      {validationResult.usageInfo.currentRecords.toLocaleString()} / {' '}
                      {validationResult.usageInfo.maxRecords === -1 
                        ? 'Unlimited' 
                        : validationResult.usageInfo.maxRecords.toLocaleString()
                      } records used
                    </>
                  )}
                </p>
              </div>
              {validationResult?.usageInfo && (
                <div className="text-right">
                  <p className="text-sm text-gray-600">
                    {validationResult.usageInfo.utilizationPercentage.toFixed(1)}% utilized
                  </p>
                  <div className="w-24 h-2 bg-gray-200 rounded-full mt-1">
                    <div 
                      className="h-2 bg-blue-600 rounded-full transition-all"
                      style={{ width: `${Math.min(validationResult.usageInfo.utilizationPercentage, 100)}%` }}
                    />
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Processing Progress */}
      {uploadProgress && (
        <ProcessingProgressDisplay 
          progress={uploadProgress}
          stateDetection={stateDetection}
        />
      )}

      {/* Processing Results */}
      {processingStats && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              Processing Complete
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-2xl font-bold text-green-600">
                  {processingStats.totalRecords.toLocaleString()}
                </p>
                <p className="text-sm text-gray-600">Records Processed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-blue-600">
                  {(processingStats.processingTime / 1000).toFixed(1)}s
                </p>
                <p className="text-sm text-gray-600">Processing Time</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-purple-600">
                  {formatRate(processingStats.recordsPerSecond)}
                </p>
                <p className="text-sm text-gray-600">Processing Speed</p>
              </div>
              <div className="text-center">
                <p className="text-2xl font-bold text-orange-600">
                  {formatFileSize(processingStats.fileSize)}
                </p>
                <p className="text-sm text-gray-600">File Size</p>
              </div>
            </div>
            
            {stateDetection && (
              <div className="mt-4 p-3 bg-blue-50 rounded-lg">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">State Detected: {stateDetection.state.toUpperCase()}</p>
                    <p className="text-sm text-gray-600">
                      Confidence: {(stateDetection.confidence * 100).toFixed(1)}%
                    </p>
                  </div>
                  <Badge variant="secondary">
                    Auto-Mapped
                  </Badge>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {validationResult && !validationResult.allowed && !showUpgradeModal && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {validationResult.error}
          </AlertDescription>
        </Alert>
      )}

      {/* Upgrade Modal */}
      {showUpgradeModal && validationResult && (
        <UpgradeModal
          validation={validationResult}
          currentUser={currentUser}
          onClose={() => setShowUpgradeModal(false)}
          onUpgrade={() => {
            // Handle upgrade logic
            console.log('Upgrade initiated');
            setShowUpgradeModal(false);
          }}
        />
      )}
    </div>
  );
}; 