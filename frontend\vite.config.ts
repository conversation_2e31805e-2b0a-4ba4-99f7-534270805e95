import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    port: 3005,
    host: true,
    strictPort: true,
    open: true,
  },
  build: {
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          vendor: ['react', 'react-dom'],
          ui: ['@radix-ui/react-tabs', '@radix-ui/react-dialog', '@radix-ui/react-select'],
          icons: ['lucide-react'],
          // Dashboard chunks for better code splitting
          dashboards: [
            './src/features/dashboard/components/JuniorAgentDashboard/JuniorAgentDashboard.tsx',
            './src/features/dashboard/components/SeniorAgentDashboard/SeniorAgentDashboard.tsx',
            './src/features/dashboard/components/AdminDashboard/AdminDashboard.tsx',
            './src/features/dashboard/components/ContractorDashboard/ContractorDashboard.tsx',
            './src/features/dashboard/components/ComplianceDashboard/ComplianceDashboard.tsx',
            './src/features/dashboard/components/FinanceDashboard/FinanceDashboard.tsx'
          ]
        }
      }
    },
    chunkSizeWarningLimit: 600 // Increase limit slightly for dashboard components
  },
}) 