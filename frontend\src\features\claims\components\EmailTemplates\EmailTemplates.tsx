import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { 
  Mail,
  Copy,
  X,
  Database,
  ExternalLink,
  Edit3,
  CheckCircle,
  AlertCircle,
  Clock
} from "lucide-react";
import { Claim } from '../../types';
import { emailService, EmailRecipient } from '@/lib/emailService';

interface EmailTemplatesProps {
  claim: Claim;
  onSendEmail: (subject: string, body: string) => void;
  onClose?: () => void;
}

interface EmailTemplate {
  id: string;
  name: string;
  subject: string;
  body: string;
  category: 'initial' | 'follow-up' | 'documents' | 'resolution' | 'general';
  description: string;
}

const EMAIL_TEMPLATES: EmailTemplate[] = [
  {
    id: 'initial_contact',
    name: 'Initial Contact',
    category: 'initial',
    description: 'First contact with claim owner',
    subject: 'Important: Unclaimed Property Found - {{claimId}}',
    body: `Dear {{ownerName}},

We have located unclaimed property that may belong to you in the amount of {{amount}}.

Property Details:
- Claim ID: {{claimId}}
- Amount: {{amount}}
- State: {{state}}
- Property Type: {{propertyType}}

To claim your property, please contact us at your earliest convenience. We will need to verify your identity and may request supporting documentation.

You can reach us by:
- Email: {{agentEmail}}
- Phone: {{agentPhone}}

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  },
  {
    id: 'document_request',
    name: 'Document Request',
    category: 'documents',
    description: 'Request required documentation',
    subject: 'Documents Needed - Claim {{claimId}}',
    body: `Dear {{ownerName}},

Thank you for your interest in claiming your unclaimed property (Claim ID: {{claimId}}, Amount: {{amount}}).

To process your claim, we need the following documents:

Required Documents:
□ Government-issued photo ID (driver's license, passport, etc.)
□ Social Security card or W-2 form
□ Proof of address (utility bill, bank statement - within last 90 days)

Additional Documents (if applicable):
□ Death certificate (if claiming for deceased relative)
□ Power of attorney (if claiming on behalf of someone else)
□ Business documentation (if business-related claim)

Please send these documents via:
- Secure email: {{agentEmail}}
- Secure upload portal: [link]
- Mail: [address]

Once we receive and verify your documents, we can process your claim within 3-5 business days.

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  },
  {
    id: 'follow_up',
    name: 'Follow-up Reminder',
    category: 'follow-up',
    description: 'Gentle follow-up for pending claims',
    subject: 'Follow-up: Unclaimed Property Claim {{claimId}}',
    body: `Dear {{ownerName}},

I hope this message finds you well. I'm following up on your unclaimed property claim (ID: {{claimId}}) that we discussed previously.

Claim Summary:
- Amount: {{amount}}
- Status: Pending your response
- Last Contact: {{lastContactDate}}

We're here to help you recover your property. If you have any questions or need assistance with the process, please don't hesitate to reach out.

Next Steps:
{{nextSteps}}

You can contact me directly at:
- Email: {{agentEmail}}
- Phone: {{agentPhone}}

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  },
  {
    id: 'documents_received',
    name: 'Documents Received',
    category: 'documents',
    description: 'Confirmation of received documents',
    subject: 'Documents Received - Claim {{claimId}}',
    body: `Dear {{ownerName}},

Thank you for submitting your documentation for claim {{claimId}}.

We have received your documents and are currently reviewing them. Our verification process typically takes 3-5 business days.

What happens next:
1. Document verification (3-5 business days)
2. Claim approval and processing
3. Payment processing and disbursement

We will contact you as soon as the review is complete. If we need any additional information, we'll reach out promptly.

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  },
  {
    id: 'claim_approved',
    name: 'Claim Approved',
    category: 'resolution',
    description: 'Claim approval notification',
    subject: 'Great News! Claim {{claimId}} Approved',
    body: `Dear {{ownerName}},

Excellent news! Your unclaimed property claim has been approved.

Claim Details:
- Claim ID: {{claimId}}
- Approved Amount: {{amount}}
- Processing Fee: {{commissionAmount}}
- Net Amount: {{netAmount}}

Payment Information:
Your payment will be processed within 7-10 business days. You will receive it via [payment method].

Thank you for choosing our services to recover your unclaimed property. If you have any questions about your payment, please contact us.

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  },
  {
    id: 'additional_info',
    name: 'Additional Information Needed',
    category: 'follow-up',
    description: 'Request for additional information',
    subject: 'Additional Information Needed - Claim {{claimId}}',
    body: `Dear {{ownerName}},

While reviewing your claim ({{claimId}}), we need some additional information to proceed:

Information Needed:
{{additionalInfo}}

Please provide this information at your earliest convenience so we can continue processing your claim.

You can respond to this email or contact me directly:
- Email: {{agentEmail}}
- Phone: {{agentPhone}}

Best regards,
{{agentName}}
AssetHunterPro Specialist`
  }
];

export const EmailTemplates: React.FC<EmailTemplatesProps> = ({
  claim,
  onSendEmail,
  onClose
}) => {
  const [selectedTemplate, setSelectedTemplate] = useState<string>('');
  const [customSubject, setCustomSubject] = useState('');
  const [customBody, setCustomBody] = useState('');
  const [recipientEmail, setRecipientEmail] = useState('');
  const [recipientName, setRecipientName] = useState('');
  const [isEditing, setIsEditing] = useState(false);
  const [useInternalSending, setUseInternalSending] = useState(true);
  const [followUpDate, setFollowUpDate] = useState('');
  const [emailType, setEmailType] = useState('general');
  const [sending, setSending] = useState(false);
  const [sendResult, setSendResult] = useState<{ success?: boolean; error?: string }>({});

  // Initialize recipient with claim owner info
  React.useEffect(() => {
    if (claim && !recipientEmail) {
      // Try to extract email from claim data or use a placeholder
      setRecipientEmail(claim.owner_email || ''); // Assuming owner_email exists
      setRecipientName(claim.owner_name || '');
    }
  }, [claim, recipientEmail]);

  const replaceVariables = (text: string): string => {
    const variables = {
      '{{claimId}}': claim.property_id || claim.id.slice(0, 8),
      '{{ownerName}}': claim.owner_name,
      '{{amount}}': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(claim.amount),
      '{{state}}': claim.state,
      '{{propertyType}}': claim.property_type || 'Unclaimed Property',
      '{{agentName}}': 'Agent Name', // TODO: Get from user context
      '{{agentEmail}}': '<EMAIL>', // TODO: Get from user context
      '{{agentPhone}}': '(*************', // TODO: Get from user context
      '{{lastContactDate}}': claim.last_contact_date ? new Date(claim.last_contact_date).toLocaleDateString() : 'N/A',
      '{{commissionAmount}}': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(claim.commission_amount || 0),
      '{{netAmount}}': new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(claim.amount - (claim.commission_amount || 0)),
      '{{nextSteps}}': getNextSteps(claim.status),
      '{{additionalInfo}}': '• [Specify what information is needed]'
    };

    return Object.entries(variables).reduce((text, [key, value]) => {
      return text.replace(new RegExp(key, 'g'), value.toString());
    }, text);
  };

  const getNextSteps = (status: string): string => {
    const steps: Record<string, string> = {
      'new': '• Please review and respond to confirm your interest',
      'contacted': '• Provide required documentation for verification',
      'documents_requested': '• Submit the requested documents',
      'in_progress': '• We are processing your documentation',
      'under_review': '• Your claim is under final review'
    };
    return steps[status] || '• Contact us for next steps';
  };

  const handleTemplateSelect = (templateId: string) => {
    const template = EMAIL_TEMPLATES.find(t => t.id === templateId);
    if (template) {
      setSelectedTemplate(templateId);
      setCustomSubject(replaceVariables(template.subject));
      setCustomBody(replaceVariables(template.body));
      setEmailType(template.category);
      setIsEditing(false);
    }
  };

  const handleSendExternal = () => {
    if (customSubject && customBody) {
      onSendEmail(customSubject, customBody);
      // Open default email client
      const mailtoLink = `mailto:${recipientEmail}?subject=${encodeURIComponent(customSubject)}&body=${encodeURIComponent(customBody)}`;
      window.open(mailtoLink, '_self');
    }
  };

  const handleSendInternal = async () => {
    if (!customSubject || !customBody || !recipientEmail) {
      setSendResult({ error: 'Please fill in all required fields' });
      return;
    }

    setSending(true);
    setSendResult({});

    try {
      const recipients: EmailRecipient[] = [
        { email: recipientEmail, name: recipientName || undefined }
      ];

      const result = await emailService.sendEmail({
        to: recipients,
        subject: customSubject,
        bodyText: customBody,
        bodyHtml: customBody.replace(/\n/g, '<br>'), // Simple HTML conversion
        claimId: claim.id,
        emailType: emailType,
        followUpDate: followUpDate ? new Date(followUpDate) : undefined,
        templateId: selectedTemplate || undefined
      });

      setSendResult(result);

      if (result.success) {
        // Call the parent callback for activity tracking
        onSendEmail(customSubject, customBody);
        
        // Auto-close after successful send
        setTimeout(() => {
          onClose?.();
        }, 2000);
      }

    } catch (error) {
      setSendResult({ 
        success: false, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
    } finally {
      setSending(false);
    }
  };

  const handleCopy = () => {
    const emailContent = `Subject: ${customSubject}\n\n${customBody}`;
    navigator.clipboard.writeText(emailContent);
  };

  const categorizedTemplates = EMAIL_TEMPLATES.reduce((acc, template) => {
    if (!acc[template.category]) acc[template.category] = [];
    acc[template.category].push(template);
    return acc;
  }, {} as Record<string, EmailTemplate[]>);

  const categoryColors: Record<string, string> = {
    'initial': 'bg-blue-100 text-blue-800',
    'follow-up': 'bg-orange-100 text-orange-800',
    'documents': 'bg-purple-100 text-purple-800',
    'resolution': 'bg-green-100 text-green-800',
    'general': 'bg-gray-100 text-gray-800'
  };

  const categoryLabels: Record<string, string> = {
    'initial': 'Initial Contact',
    'follow-up': 'Follow-up',
    'documents': 'Documentation',
    'resolution': 'Resolution',
    'general': 'General'
  };

  return (
    <Card className="w-full max-w-5xl max-h-[90vh] overflow-hidden">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            Email Templates
          </CardTitle>
          {onClose && (
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-6 overflow-y-auto max-h-[calc(90vh-120px)]">
        {/* Sending Method Toggle */}
        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="internal-sending"
                checked={useInternalSending}
                onCheckedChange={setUseInternalSending}
              />
              <Label htmlFor="internal-sending" className="flex items-center gap-2">
                {useInternalSending ? (
                  <>
                    <Database className="h-4 w-4 text-blue-600" />
                    Internal Tracking
                  </>
                ) : (
                  <>
                    <ExternalLink className="h-4 w-4 text-gray-600" />
                    External Email Client
                  </>
                )}
              </Label>
            </div>
          </div>
          <div className="text-sm text-gray-600">
            {useInternalSending ? 
              'Emails will be tracked and logged in the system' : 
              'Opens your default email application'
            }
          </div>
        </div>

        {/* Recipient Information (only for internal sending) */}
        {useInternalSending && (
          <div className="space-y-4 p-4 border rounded-lg">
            <h3 className="font-medium">Recipient Information</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="recipient-email">Email Address *</Label>
                <Input
                  id="recipient-email"
                  type="email"
                  value={recipientEmail}
                  onChange={(e) => setRecipientEmail(e.target.value)}
                  placeholder="<EMAIL>"
                  required
                />
              </div>
              <div>
                <Label htmlFor="recipient-name">Name</Label>
                <Input
                  id="recipient-name"
                  value={recipientName}
                  onChange={(e) => setRecipientName(e.target.value)}
                  placeholder="Owner Name"
                />
              </div>
            </div>
          </div>
        )}

        {/* Template Selection */}
        <div className="space-y-4">
          <h3 className="font-medium">Choose Template</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {Object.entries(categorizedTemplates).map(([category, templates]) => (
              <div key={category} className="space-y-2">
                <Badge className={categoryColors[category]}>
                  {categoryLabels[category]}
                </Badge>
                <div className="space-y-1">
                  {templates.map((template) => (
                    <Button
                      key={template.id}
                      variant={selectedTemplate === template.id ? "default" : "outline"}
                      size="sm"
                      className="w-full justify-start text-left h-auto p-3"
                      onClick={() => handleTemplateSelect(template.id)}
                    >
                      <div>
                        <div className="font-medium">{template.name}</div>
                        <div className="text-xs text-muted-foreground">{template.description}</div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Email Preview/Edit */}
        {selectedTemplate && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-medium">Email Preview</h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setIsEditing(!isEditing)}
                >
                  <Edit3 className="h-4 w-4 mr-2" />
                  {isEditing ? 'Preview' : 'Edit'}
                </Button>
                <Button variant="outline" size="sm" onClick={handleCopy}>
                  <Copy className="h-4 w-4 mr-2" />
                  Copy
                </Button>
              </div>
            </div>

            {/* Subject */}
            <div>
              <label className="text-sm font-medium">Subject</label>
              {isEditing ? (
                <Textarea
                  value={customSubject}
                  onChange={(e) => setCustomSubject(e.target.value)}
                  className="mt-1 min-h-[40px]"
                />
              ) : (
                <div className="mt-1 p-3 bg-gray-50 rounded border text-sm">
                  {customSubject}
                </div>
              )}
            </div>

            {/* Body */}
            <div>
              <label className="text-sm font-medium">Message</label>
              {isEditing ? (
                <Textarea
                  value={customBody}
                  onChange={(e) => setCustomBody(e.target.value)}
                  className="mt-1 min-h-[300px]"
                />
              ) : (
                <div className="mt-1 p-3 bg-gray-50 rounded border text-sm whitespace-pre-wrap">
                  {customBody}
                </div>
              )}
            </div>

            {/* Additional Options for Internal Sending */}
            {useInternalSending && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 border rounded-lg">
                <div>
                  <Label htmlFor="email-type">Email Type</Label>
                  <Select value={emailType} onValueChange={setEmailType}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="initial_contact">Initial Contact</SelectItem>
                      <SelectItem value="follow_up">Follow-up</SelectItem>
                      <SelectItem value="document_request">Document Request</SelectItem>
                      <SelectItem value="general">General</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="follow-up-date">Schedule Follow-up</Label>
                  <Input
                    id="follow-up-date"
                    type="date"
                    value={followUpDate}
                    onChange={(e) => setFollowUpDate(e.target.value)}
                    min={new Date().toISOString().split('T')[0]}
                  />
                </div>
              </div>
            )}

            {/* Send Result Status */}
            {sendResult.success !== undefined && (
              <div className={`p-3 rounded-lg border flex items-center gap-2 ${
                sendResult.success 
                  ? 'bg-green-50 border-green-200 text-green-800' 
                  : 'bg-red-50 border-red-200 text-red-800'
              }`}>
                {sendResult.success ? (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    Email sent successfully! Activity logged to claim timeline.
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-4 w-4" />
                    Failed to send: {sendResult.error}
                  </>
                )}
              </div>
            )}

            <Separator />

            {/* Action Buttons */}
            <div className="flex gap-2 pt-4">
              {useInternalSending ? (
                <Button 
                  onClick={handleSendInternal} 
                  className="flex-1"
                  disabled={sending || !recipientEmail}
                >
                  {sending ? (
                    <>
                      <Clock className="h-4 w-4 mr-2 animate-spin" />
                      Sending...
                    </>
                  ) : (
                    <>
                      <Database className="h-4 w-4 mr-2" />
                      Send & Track Email
                    </>
                  )}
                </Button>
              ) : (
                <Button onClick={handleSendExternal} className="flex-1">
                  <ExternalLink className="h-4 w-4 mr-2" />
                  Open in Email Client
                </Button>
              )}
              <Button variant="outline" onClick={handleCopy}>
                <Copy className="h-4 w-4 mr-2" />
                Copy to Clipboard
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 