/**
 * Integration Example: Using Claimant Results with AI Discovery
 * 
 * This file demonstrates how to integrate the new claimant result display
 * with your existing AI Discovery Engine and workflow.
 */

import React, { useState } from 'react';
import { ClaimantResult } from './ClaimantResultCard';
import { ClaimantResultsManager } from './ClaimantResultsManager';
import { ClaimantResultsDemo } from './ClaimantResultsDemo';

// Example of how to convert existing AI Discovery results to ClaimantResult format
export function convertDiscoveryResultToClaimant(discoveryResult: any, searchId: string): ClaimantResult {
  return {
    discoveryId: `${searchId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
    primaryName: discoveryResult.primaryName || 'Unknown',
    nameVariations: discoveryResult.nameVariations || [],
    addresses: discoveryResult.addresses?.map((addr: any) => ({
      street: addr.street,
      city: addr.city,
      state: addr.state,
      zipCode: addr.zipCode,
      type: addr.type || 'unknown',
      source: addr.source || 'discovery',
      confidence: addr.confidence || 0.5
    })) || [],
    phoneNumbers: discoveryResult.phoneNumbers?.map((phone: any) => ({
      number: phone.number,
      type: phone.type || 'unknown',
      verified: phone.verified || false,
      source: phone.source || 'discovery',
      confidence: phone.confidence || 0.5
    })) || [],
    emailAddresses: discoveryResult.emailAddresses?.map((email: any) => ({
      email: email.email,
      verified: email.verified || false,
      source: email.source || 'discovery',
      confidence: email.confidence || 0.5
    })) || [],
    confidence: discoveryResult.confidence || 0.75,
    qualityScore: calculateQualityScore(discoveryResult),
    lastActivity: determineLastActivity(discoveryResult),
    socialMediaFingerprints: extractSocialMediaFingerprints(discoveryResult),
    familyConnections: extractFamilyConnections(discoveryResult),
    businessConnections: extractBusinessConnections(discoveryResult),
    validationStatus: 'pending',
    contactAttempts: []
  };
}

// Helper functions for data conversion
function calculateQualityScore(result: any): number {
  let score = 0;
  let factors = 0;

  // Address quality (30%)
  if (result.addresses?.length > 0) {
    score += 0.3;
    factors += 0.3;
  }

  // Contact info quality (40%)
  if (result.phoneNumbers?.length > 0) {
    score += 0.2;
    factors += 0.2;
  }
  if (result.emailAddresses?.length > 0) {
    score += 0.2;
    factors += 0.2;
  }

  // Additional data (30%)
  if (result.nameVariations?.length > 0) {
    score += 0.15;
    factors += 0.15;
  }
  if (result.socialMediaProfiles?.length > 0) {
    score += 0.15;
    factors += 0.15;
  }

  return factors > 0 ? score / factors : 0.5;
}

function determineLastActivity(result: any): string {
  if (result.propertyRecords?.length > 0) {
    return `Property Record - ${new Date().toLocaleDateString()}`;
  }
  if (result.voterRegistration) {
    return `Voter Registration - ${new Date().toLocaleDateString()}`;
  }
  return 'No recent activity found';
}

function extractSocialMediaFingerprints(result: any): {platform: string, profile: string, url: string}[] {
  if (!result.socialMediaProfiles) return [];
  
  return result.socialMediaProfiles.map((profile: any) => ({
    platform: extractPlatformFromUrl(profile.url),
    profile: profile.username || 'Profile Found',
    url: profile.url
  }));
}

function extractFamilyConnections(result: any): {name: string, relationship: string, contact?: string}[] {
  if (!result.familyConnections) return [];
  
  return result.familyConnections.map((family: any) => ({
    name: family.name,
    relationship: family.relationship,
    contact: family.contact
  }));
}

function extractBusinessConnections(result: any): {company: string, role: string, contact?: string}[] {
  if (!result.businessRecords) return [];
  
  return result.businessRecords.map((business: any) => ({
    company: business.companyName,
    role: business.role || 'Employee',
    contact: business.contactInfo
  }));
}

function extractPlatformFromUrl(url: string): string {
  if (url.includes('linkedin')) return 'LinkedIn';
  if (url.includes('facebook')) return 'Facebook';
  if (url.includes('twitter')) return 'Twitter';
  if (url.includes('instagram')) return 'Instagram';
  return 'Social Media';
}

// Example component showing how to integrate with existing AI Discovery
interface AIDiscoveryWithClaimantResultsProps {
  onStartDiscovery?: () => void;
}

export const AIDiscoveryWithClaimantResults: React.FC<AIDiscoveryWithClaimantResultsProps> = ({
  onStartDiscovery
}) => {
  const [showDemo, setShowDemo] = useState(false);
  const [discoveryResults, setDiscoveryResults] = useState<ClaimantResult[]>([]);
  const [isDiscovering, setIsDiscovering] = useState(false);

  const handleStartDiscovery = async () => {
    setIsDiscovering(true);
    
    try {
      // This is where you would call your existing AI Discovery Engine
      // const results = await aiDiscoveryEngine.performDiscovery(searchParams);
      
      // For demo purposes, we'll simulate this
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // Convert results to ClaimantResult format
      // const claimantResults = results.map(result => 
      //   convertDiscoveryResultToClaimant(result, 'SEARCH_123')
      // );
      
      // For demo, we'll use sample data
      const { generateSampleClaimantResults } = await import('./ClaimantResultsDemo');
      const claimantResults = generateSampleClaimantResults();
      
      setDiscoveryResults(claimantResults);
      onStartDiscovery?.();
      
    } catch (error) {
      console.error('Discovery failed:', error);
    } finally {
      setIsDiscovering(false);
    }
  };

  if (showDemo) {
    return <ClaimantResultsDemo onClose={() => setShowDemo(false)} />;
  }

  if (discoveryResults.length > 0) {
    return (
      <div>
        <div className="mb-4 flex justify-between items-center">
          <h2 className="text-2xl font-bold">Discovery Complete</h2>
          <button
            onClick={() => setDiscoveryResults([])}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          >
            New Search
          </button>
        </div>
        <ClaimantResultsManager
          results={discoveryResults}
          searchId="LIVE_SEARCH_001"
          onResultsUpdate={setDiscoveryResults}
        />
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-lg border border-gray-200 p-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">
          🧠 AI Discovery with Claimant Validation
        </h1>
        
        <p className="text-gray-600 mb-6">
          Enhanced AI Discovery that finds claimants and provides comprehensive validation 
          tools for agents to contact and verify potential matches.
        </p>

        <div className="space-y-4">
          <button
            onClick={handleStartDiscovery}
            disabled={isDiscovering}
            className="w-full px-6 py-3 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 transition-colors text-lg font-medium"
          >
            {isDiscovering ? (
              <>
                <span className="animate-spin inline-block w-4 h-4 border-2 border-white border-t-transparent rounded-full mr-2"></span>
                Running AI Discovery...
              </>
            ) : (
              '🚀 Start AI Discovery'
            )}
          </button>

          <button
            onClick={() => setShowDemo(true)}
            className="w-full px-6 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-lg font-medium"
          >
            📊 View Demo with Sample Data
          </button>
        </div>

        <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="font-medium text-blue-900 mb-2">✨ New Features</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Professional Result Cards:</strong> Clean, organized display matching your design</li>
            <li>• <strong>Multiple Contact Methods:</strong> Phone, email, mail, social media, family connections</li>
            <li>• <strong>Validation Workflow:</strong> Track contact attempts and validation status</li>
            <li>• <strong>Quality Scoring:</strong> Comprehensive confidence and quality metrics</li>
            <li>• <strong>Agent Tools:</strong> Mark invalid, validate, schedule follow-ups</li>
            <li>• <strong>Bulk Operations:</strong> Export, validate multiple, generate reports</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

// Usage examples and integration notes
export const INTEGRATION_NOTES = {
  // How to integrate with existing AI Discovery Engine
  aiDiscoveryIntegration: `
    // In your existing AI Discovery button handler:
    const results = await aiDiscoveryEngine.performDiscovery(person, config);
    const claimantResults = results.map(result => 
      convertDiscoveryResultToClaimant(result, searchId)
    );
    
    // Then display with:
    <ClaimantResultsManager 
      results={claimantResults} 
      searchId={searchId}
      onResultsUpdate={handleResultsUpdate}
    />
  `,
  
  // How to handle validation events
  validationHandling: `
    const handleValidateClaimant = async (resultId, method, notes) => {
      // Send notification
      await notificationService.sendNotification('validation_started', userId, {
        claimantId: resultId,
        method,
        notes
      });
      
      // Log for analytics
      analyticsEngine.recordSearchEvent({
        userId,
        searchType: 'validation',
        success: true,
        timestamp: new Date()
      });
      
      // Update result status
      updateClaimantStatus(resultId, 'in_progress');
    };
  `,
  
  // How to customize the display
  customization: `
    // You can customize the ClaimantResultCard by:
    // 1. Modifying the CSS classes
    // 2. Adding new fields to ClaimantResult interface
    // 3. Creating custom contact method handlers
    // 4. Adding new validation statuses
    // 5. Integrating with your existing notification system
  `
};

export default AIDiscoveryWithClaimantResults; 