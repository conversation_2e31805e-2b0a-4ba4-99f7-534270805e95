# AssetHunterPro Integration Setup Guide

This guide will help you configure the third-party integrations for AssetHunterPro's commercial features.

## 🗄️ Database Setup (Already Configured ✅)

Your Supabase database is already configured! The environment variables are set:
- `VITE_SUPABASE_URL`: ✅ Configured
- `VITE_SUPABASE_ANON_KEY`: ✅ Configured

### Next Steps for Database:
1. **Deploy Schema**: Run the SQL schema files in your Supabase dashboard
2. **Enable MFA**: Go to Authentication > Settings and enable MFA
3. **Configure RLS**: Ensure Row Level Security is enabled

## 🔐 Multi-Factor Authentication (MFA)

MFA is built into Supabase and ready to use!

### Setup Steps:
1. **Enable MFA in Supabase**:
   - Go to your Supabase dashboard
   - Navigate to Authentication > Settings
   - Enable "Multi-Factor Authentication"
   - Choose TOTP (Time-based One-Time Password)

2. **Configure MFA Enforcement**:
   - The app automatically enforces MFA for admin, compliance, and finance roles
   - Users can set up MFA in their profile settings

### Testing MFA:
1. Create a user with admin role
2. Login and go to Profile Settings
3. Click "Set up MFA" and scan QR code with authenticator app
4. Verify setup with 6-digit code

## 📝 SignRequest Integration

For electronic document signing workflows - **10 FREE documents per month!**

### Setup Steps:

1. **Create SignRequest Account**:
   - Go to [signrequest.com](https://signrequest.com)
   - Sign up for a free account
   - Get 10 free documents per month (perfect for getting started!)

2. **Get API Token**:
   - Log into your SignRequest account
   - Go to Account Settings > API
   - Generate a new API token
   - Copy the token (keep it secure!)

3. **Update Environment Variables**:
   ```bash
   VITE_SIGNREQUEST_API_TOKEN=your-api-token
   VITE_SIGNREQUEST_BASE_URL=https://signrequest.com/api/v1
   VITE_SIGNREQUEST_WEBHOOK_URL=http://localhost:3005/api/signrequest/webhook
   ```

4. **Test Integration**:
   - Go to Admin > SignRequest Configuration
   - Enter your API token and test connection

### Production Setup:
- Use the same API token for production
- Update webhook URL to your production domain
- Consider upgrading to paid plan for unlimited documents ($8/month)

## 💳 Stripe Integration

For subscription billing and commission payments.

### Setup Steps:

1. **Create Stripe Account**:
   - Go to [stripe.com](https://stripe.com)
   - Sign up for a Stripe account
   - Complete account verification

2. **Get API Keys**:
   - Go to Developers > API keys in your Stripe dashboard
   - Copy your Publishable key (starts with `pk_test_`)
   - Copy your Secret key (starts with `sk_test_`)

3. **Update Environment Variables**:
   ```bash
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-key
   VITE_STRIPE_SECRET_KEY=sk_test_your-key
   VITE_STRIPE_ENVIRONMENT=test
   ```

4. **Configure Webhooks**:
   - Go to Developers > Webhooks in Stripe dashboard
   - Add endpoint: `http://localhost:3005/api/stripe/webhook`
   - Select these events:
     - `payment_intent.succeeded`
     - `payment_intent.payment_failed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
   - Copy the webhook secret (starts with `whsec_`)

5. **Update Webhook Secret**:
   ```bash
   VITE_STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
   ```

6. **Test Integration**:
   - Go to Admin > Stripe Configuration
   - Enter your credentials and test connection

### Production Setup:
- Activate your Stripe account for live payments
- Use live API keys (starts with `pk_live_` and `sk_live_`)
- Update webhook URLs to your production domain
- Set `VITE_STRIPE_ENVIRONMENT=live`

## 🚀 Quick Start Checklist

### Immediate Setup (5 minutes):
- [ ] Copy `.env.example` to `.env` (if not already done)
- [ ] Your Supabase is already configured ✅
- [ ] Deploy database schema to Supabase
- [ ] Enable MFA in Supabase settings

### SignRequest Setup (5 minutes):
- [ ] Create SignRequest account (free!)
- [ ] Get API token from account settings
- [ ] Update environment variables
- [ ] Test in Admin > SignRequest Configuration

### Stripe Setup (15 minutes):
- [ ] Create Stripe account
- [ ] Get API keys from dashboard
- [ ] Configure webhook endpoint
- [ ] Update environment variables
- [ ] Test in Admin > Stripe Configuration

### Verification (5 minutes):
- [ ] Login to app and verify no errors
- [ ] Test MFA setup in profile settings
- [ ] Check admin configuration pages
- [ ] Verify all integrations show "Configured" status

## 🔧 Troubleshooting

### Common Issues:

1. **Supabase Connection Error**:
   - Verify URL and anon key are correct
   - Check if project is paused (free tier limitation)

2. **SignRequest Authentication Error**:
   - Verify API token is correct
   - Check token hasn't expired
   - Ensure account has API access enabled

3. **Stripe Connection Error**:
   - Verify API keys are for the same account
   - Check if test/live mode matches environment setting
   - Ensure webhook URL is accessible

4. **MFA Not Working**:
   - Verify MFA is enabled in Supabase settings
   - Check if user has compatible authenticator app
   - Ensure system time is synchronized

### Getting Help:

1. **Check Browser Console**: Look for error messages
2. **Check Network Tab**: Verify API calls are being made
3. **Verify Environment Variables**: Ensure all required vars are set
4. **Test API Keys**: Use provider's test tools to verify keys work

## 📚 Additional Resources

- [Supabase MFA Documentation](https://supabase.com/docs/guides/auth/auth-mfa)
- [SignRequest API Documentation](https://signrequest.com/api/v1/docs/)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [AssetHunterPro Admin Guide](./ADMIN_GUIDE.md)

## 🎯 Production Deployment

When ready for production:

1. **Update All URLs**: Change localhost to your production domain
2. **Use Production API Keys**: Switch to live/production keys
3. **Configure SSL**: Ensure all endpoints use HTTPS
4. **Set Up Monitoring**: Monitor API usage and errors
5. **Security Review**: Audit all configurations and permissions

Your AssetHunterPro instance is now ready for commercial use! 🎉
