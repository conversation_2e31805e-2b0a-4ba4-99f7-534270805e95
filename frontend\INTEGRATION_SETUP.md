# AssetHunterPro Integration Setup Guide

This guide will help you configure the third-party integrations for AssetHunterPro's commercial features.

## 🗄️ Database Setup (Already Configured ✅)

Your Supabase database is already configured! The environment variables are set:
- `VITE_SUPABASE_URL`: ✅ Configured
- `VITE_SUPABASE_ANON_KEY`: ✅ Configured

### Next Steps for Database:
1. **Deploy Schema**: Run the SQL schema files in your Supabase dashboard
2. **Enable MFA**: Go to Authentication > Settings and enable MFA
3. **Configure RLS**: Ensure Row Level Security is enabled

## 🔐 Multi-Factor Authentication (MFA)

MFA is built into Supabase and ready to use!

### Setup Steps:
1. **Enable MFA in Supabase**:
   - Go to your Supabase dashboard
   - Navigate to Authentication > Settings
   - Enable "Multi-Factor Authentication"
   - Choose TOTP (Time-based One-Time Password)

2. **Configure MFA Enforcement**:
   - The app automatically enforces MFA for admin, compliance, and finance roles
   - Users can set up MFA in their profile settings

### Testing MFA:
1. Create a user with admin role
2. Login and go to Profile Settings
3. Click "Set up MFA" and scan QR code with authenticator app
4. Verify setup with 6-digit code

## 📝 DocuSign Integration

For electronic document signing workflows.

### Setup Steps:

1. **Create DocuSign Developer Account**:
   - Go to [developers.docusign.com](https://developers.docusign.com)
   - Sign up for a free developer account
   - This gives you access to the sandbox environment

2. **Create Integration**:
   - In your DocuSign developer dashboard, go to "Apps & Keys"
   - Click "Add App and Integration Key"
   - Choose "Authorization Code Grant" for authentication
   - Note down your Integration Key

3. **Get Required IDs**:
   - **Integration Key**: Found in Apps & Keys section
   - **User ID**: Your DocuSign user GUID (in Apps & Keys)
   - **Account ID**: Your DocuSign account ID (in Apps & Keys)

4. **Configure OAuth**:
   - In your app settings, add redirect URI: `http://localhost:3005/docusign/callback`
   - For production, use your actual domain

5. **Update Environment Variables**:
   ```bash
   VITE_DOCUSIGN_INTEGRATION_KEY=your-integration-key
   VITE_DOCUSIGN_USER_ID=your-user-id
   VITE_DOCUSIGN_ACCOUNT_ID=your-account-id
   ```

6. **Test Integration**:
   - Go to Admin > DocuSign Configuration
   - Enter your credentials and test connection

### Production Setup:
- Apply for DocuSign production account
- Update base URL to `https://www.docusign.net/restapi`
- Configure production webhook URLs

## 💳 Stripe Integration

For subscription billing and commission payments.

### Setup Steps:

1. **Create Stripe Account**:
   - Go to [stripe.com](https://stripe.com)
   - Sign up for a Stripe account
   - Complete account verification

2. **Get API Keys**:
   - Go to Developers > API keys in your Stripe dashboard
   - Copy your Publishable key (starts with `pk_test_`)
   - Copy your Secret key (starts with `sk_test_`)

3. **Update Environment Variables**:
   ```bash
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your-key
   VITE_STRIPE_SECRET_KEY=sk_test_your-key
   VITE_STRIPE_ENVIRONMENT=test
   ```

4. **Configure Webhooks**:
   - Go to Developers > Webhooks in Stripe dashboard
   - Add endpoint: `http://localhost:3005/api/stripe/webhook`
   - Select these events:
     - `payment_intent.succeeded`
     - `payment_intent.payment_failed`
     - `customer.subscription.created`
     - `customer.subscription.updated`
     - `customer.subscription.deleted`
     - `invoice.payment_succeeded`
     - `invoice.payment_failed`
   - Copy the webhook secret (starts with `whsec_`)

5. **Update Webhook Secret**:
   ```bash
   VITE_STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret
   ```

6. **Test Integration**:
   - Go to Admin > Stripe Configuration
   - Enter your credentials and test connection

### Production Setup:
- Activate your Stripe account for live payments
- Use live API keys (starts with `pk_live_` and `sk_live_`)
- Update webhook URLs to your production domain
- Set `VITE_STRIPE_ENVIRONMENT=live`

## 🚀 Quick Start Checklist

### Immediate Setup (5 minutes):
- [ ] Copy `.env.example` to `.env` (if not already done)
- [ ] Your Supabase is already configured ✅
- [ ] Deploy database schema to Supabase
- [ ] Enable MFA in Supabase settings

### DocuSign Setup (15 minutes):
- [ ] Create DocuSign developer account
- [ ] Create integration and get API keys
- [ ] Update environment variables
- [ ] Test in Admin > DocuSign Configuration

### Stripe Setup (15 minutes):
- [ ] Create Stripe account
- [ ] Get API keys from dashboard
- [ ] Configure webhook endpoint
- [ ] Update environment variables
- [ ] Test in Admin > Stripe Configuration

### Verification (5 minutes):
- [ ] Login to app and verify no errors
- [ ] Test MFA setup in profile settings
- [ ] Check admin configuration pages
- [ ] Verify all integrations show "Configured" status

## 🔧 Troubleshooting

### Common Issues:

1. **Supabase Connection Error**:
   - Verify URL and anon key are correct
   - Check if project is paused (free tier limitation)

2. **DocuSign Authentication Error**:
   - Verify integration key format
   - Check OAuth redirect URL matches exactly
   - Ensure user has admin permissions in DocuSign

3. **Stripe Connection Error**:
   - Verify API keys are for the same account
   - Check if test/live mode matches environment setting
   - Ensure webhook URL is accessible

4. **MFA Not Working**:
   - Verify MFA is enabled in Supabase settings
   - Check if user has compatible authenticator app
   - Ensure system time is synchronized

### Getting Help:

1. **Check Browser Console**: Look for error messages
2. **Check Network Tab**: Verify API calls are being made
3. **Verify Environment Variables**: Ensure all required vars are set
4. **Test API Keys**: Use provider's test tools to verify keys work

## 📚 Additional Resources

- [Supabase MFA Documentation](https://supabase.com/docs/guides/auth/auth-mfa)
- [DocuSign Developer Center](https://developers.docusign.com)
- [Stripe API Documentation](https://stripe.com/docs/api)
- [AssetHunterPro Admin Guide](./ADMIN_GUIDE.md)

## 🎯 Production Deployment

When ready for production:

1. **Update All URLs**: Change localhost to your production domain
2. **Use Production API Keys**: Switch to live/production keys
3. **Configure SSL**: Ensure all endpoints use HTTPS
4. **Set Up Monitoring**: Monitor API usage and errors
5. **Security Review**: Audit all configurations and permissions

Your AssetHunterPro instance is now ready for commercial use! 🎉
