import { useState, useEffect } from 'react';
import { supabase } from '@/lib/supabase';
import { Claim, ClaimActivity, ClaimDocument } from '../types/claim.types';

export interface UseClaimResult {
  claim: Claim | null;
  activities: ClaimActivity[];
  documents: ClaimDocument[];
  loading: boolean;
  error: string | null;
  refreshClaim: () => Promise<void>;
}

export const useClaim = (claimId: string): UseClaimResult => {
  const [claim, setClaim] = useState<Claim | null>(null);
  const [activities, setActivities] = useState<ClaimActivity[]>([]);
  const [documents, setDocuments] = useState<ClaimDocument[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const loadClaimDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load claim details
      const { data: claimData, error: claimError } = await supabase
        .from('claims')
        .select('*')
        .eq('id', claimId)
        .single();

      if (claimError) throw claimError;
      setClaim(claimData);

      // Load claim activities
      const { data: activitiesData, error: activitiesError } = await supabase
        .from('claim_activities')
        .select(`
          *,
          users!agent_id (
            first_name,
            last_name,
            email
          )
        `)
        .eq('claim_id', claimId)
        .order('created_at', { ascending: false });

      if (activitiesError) {
        console.warn('Could not load activities:', activitiesError);
        setActivities([]);
      } else {
        setActivities(activitiesData || []);
      }

      // Load claim documents
      const { data: documentsData, error: documentsError } = await supabase
        .from('claim_documents')
        .select('*')
        .eq('claim_id', claimId)
        .order('created_at', { ascending: false });

      if (documentsError) {
        console.warn('Could not load documents:', documentsError);
        setDocuments([]);
      } else {
        setDocuments(documentsData || []);
      }

    } catch (err) {
      console.error('Error loading claim details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load claim details');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (claimId) {
      loadClaimDetails();
    }
  }, [claimId]);

  return {
    claim,
    activities,
    documents,
    loading,
    error,
    refreshClaim: loadClaimDetails
  };
}; 