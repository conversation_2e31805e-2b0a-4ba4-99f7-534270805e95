/**
 * Auto-Fix Authentication Script
 * This script automatically creates a demo user and logs them in
 * to resolve the UI component visibility issues
 */

(function() {
    console.log('🔧 Auto-Fix Authentication Script Starting...');
    
    // Check if we're already authenticated
    const existingUser = localStorage.getItem('arwa_user');
    if (existingUser) {
        try {
            const user = JSON.parse(existingUser);
            console.log(`✅ Already authenticated as: ${user.name} (${user.role})`);
            return;
        } catch (error) {
            console.warn('⚠️ Invalid user data found, clearing...');
            localStorage.removeItem('arwa_user');
        }
    }
    
    // Create demo admin user
    const demoUser = {
        id: 'demo-admin-001',
        email: '<EMAIL>',
        name: 'Demo Admin',
        role: 'admin',
        phone: '******-0123',
        active: true,
        companyName: 'Demo Company',
        plan: 'professional',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        lastLoginAt: new Date().toISOString(),
        permissions: [
            'claims:view_all',
            'claims:update_all',
            'claims:assign',
            'claims:delete',
            'claims:export',
            'claimants:view',
            'claimants:update',
            'claimants:create',
            'claimants:delete',
            'claimants:export',
            'activities:create',
            'activities:view_all',
            'activities:export',
            'documents:view',
            'documents:upload',
            'documents:generate',
            'documents:approve',
            'documents:delete',
            'batch:import',
            'batch:export',
            'batch:validate',
            'analytics:view_all',
            'analytics:export',
            'workflow:create',
            'workflow:manage',
            'workflow:execute',
            'users:view',
            'users:create',
            'users:update',
            'users:delete',
            'system:configure',
            'system:backup',
            'system:audit'
        ]
    };
    
    // Save the demo user
    localStorage.setItem('arwa_user', JSON.stringify(demoUser));
    console.log('✅ Demo user created and saved to localStorage');
    
    // Trigger React re-render by dispatching a storage event
    window.dispatchEvent(new StorageEvent('storage', {
        key: 'arwa_user',
        newValue: JSON.stringify(demoUser),
        oldValue: null,
        storageArea: localStorage
    }));
    
    console.log('🔄 Storage event dispatched to trigger React re-render');
    
    // Also try to trigger a custom event that the app might listen to
    window.dispatchEvent(new CustomEvent('authStateChanged', {
        detail: { user: demoUser, isAuthenticated: true }
    }));
    
    console.log('📡 Custom auth event dispatched');
    
    // Wait a moment then reload if React hasn't updated
    setTimeout(() => {
        const reactRoot = document.getElementById('root');
        if (reactRoot && reactRoot.children.length === 0) {
            console.log('🔄 React hasn\'t updated, reloading page...');
            window.location.reload();
        } else {
            console.log('✅ React appears to have updated successfully');
        }
    }, 2000);
    
    console.log('🎉 Auto-fix authentication completed!');
})();
