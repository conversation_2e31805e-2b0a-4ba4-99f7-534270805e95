import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  MoreHorizontal,
  Plus,
  Eye,
  Edit,
  Trash2,
  ChevronDown,
  Calendar,
  DollarSign,
  FileText,
  Users,
  TrendingUp,
  Clock,
  CheckCircle,
  AlertCircle,
  ArrowLeft
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { ClaimDetailView } from '@/features/claims/components/ClaimDetailView/ClaimDetailView';
import ClaimForm from './ClaimForm';
import { DashboardFilters } from '@/features/dashboard/components/DashboardFilters/DashboardFilters';
import { BatchOperations } from '@/features/dashboard/components/BatchOperations/BatchOperations';

// Types
interface Claim {
  id: string;
  property_id?: string;
  owner_name: string;
  amount: number;
  status: string;
  priority: string;
  state: string;
  assigned_agent_id?: string;
  created_at: string;
  updated_at: string;
  last_contact_date?: string;
  next_followup_date?: string;
}

interface DashboardStats {
  totalClaims: number;
  totalValue: number;
  activeAgents: number;
  pendingClaims: number;
  completedClaims: number;
  avgRecoveryTime: number;
}

export default function ClaimsDashboard() {
  // State management
  const [claims, setClaims] = useState<Claim[]>([]);
  const [filteredClaims, setFilteredClaims] = useState<Claim[]>([]);
  const [stats, setStats] = useState<DashboardStats>({
    totalClaims: 0,
    totalValue: 0,
    activeAgents: 0,
    pendingClaims: 0,
    completedClaims: 0,
    avgRecoveryTime: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [priorityFilter, setPriorityFilter] = useState('all');
  const [stateFilter, setStateFilter] = useState('all');

  // Navigation state
  const [selectedClaimId, setSelectedClaimId] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<'dashboard' | 'detail' | 'new'>('dashboard');

  // Batch operations state
  const [selectedClaimIds, setSelectedClaimIds] = useState<string[]>([]);
  const [isAllSelected, setIsAllSelected] = useState(false);

  // Load claims and statistics
  useEffect(() => {
    loadDashboardData();
  }, []);

  // Apply filters when search or filter criteria change
  useEffect(() => {
    applyFilters();
  }, [claims, searchTerm, statusFilter, priorityFilter, stateFilter]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load claims
      const { data: claimsData, error: claimsError } = await supabase
        .from('claims')
        .select('*')
        .order('created_at', { ascending: false });

      if (claimsError) throw claimsError;

      setClaims(claimsData || []);

      // Calculate dashboard statistics
      const totalClaims = claimsData?.length || 0;
      const totalValue = claimsData?.reduce((sum, claim) => sum + (claim.amount || 0), 0) || 0;
      const pendingClaims = claimsData?.filter(c => ['new', 'assigned', 'contacted', 'in_progress'].includes(c.status)).length || 0;
      const completedClaims = claimsData?.filter(c => c.status === 'completed').length || 0;

      setStats({
        totalClaims,
        totalValue,
        activeAgents: 0, // TODO: Calculate from users table
        pendingClaims,
        completedClaims,
        avgRecoveryTime: 0 // TODO: Calculate average completion time
      });

    } catch (err) {
      console.error('Error loading dashboard data:', err);
      setError(err instanceof Error ? err.message : 'Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const applyFilters = () => {
    let filtered = [...claims];

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(claim =>
        claim.owner_name.toLowerCase().includes(term) ||
        claim.property_id?.toLowerCase().includes(term) ||
        claim.state.toLowerCase().includes(term)
      );
    }

    // Status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(claim => claim.status === statusFilter);
    }

    // Priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(claim => claim.priority === priorityFilter);
    }

    // State filter
    if (stateFilter !== 'all') {
      filtered = filtered.filter(claim => claim.state === stateFilter);
    }

    setFilteredClaims(filtered);
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'new': 'bg-blue-100 text-blue-800',
      'assigned': 'bg-yellow-100 text-yellow-800',
      'contacted': 'bg-purple-100 text-purple-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'documents_requested': 'bg-indigo-100 text-indigo-800',
      'under_review': 'bg-cyan-100 text-cyan-800',
      'approved': 'bg-green-100 text-green-800',
      'completed': 'bg-emerald-100 text-emerald-800',
      'on_hold': 'bg-gray-100 text-gray-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'urgent': 'bg-red-100 text-red-800'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleClaimClick = (claimId: string) => {
    setSelectedClaimId(claimId);
    setCurrentView('detail');
  };

  const handleBackToDashboard = () => {
    setSelectedClaimId(null);
    setCurrentView('dashboard');
    loadDashboardData(); // Refresh data when returning
  };

  const handleNewClaim = () => {
    setCurrentView('new');
  };

  const handleCreateClaim = (claimId: string) => {
    setSelectedClaimId(claimId);
    setCurrentView('detail');
  };

  const handleClearFilters = () => {
    setSearchTerm('');
    setStatusFilter('all');
    setPriorityFilter('all');
    setStateFilter('all');
  };

  // Batch operations handlers
  const handleSelectClaim = (claimId: string, checked: boolean | 'indeterminate') => {
    const isChecked = checked === true;
    if (isChecked) {
      setSelectedClaimIds(prev => [...prev, claimId]);
    } else {
      setSelectedClaimIds(prev => prev.filter(id => id !== claimId));
    }
  };

  const handleSelectAll = (checked: boolean | 'indeterminate') => {
    const isChecked = checked === true;
    if (isChecked) {
      setSelectedClaimIds(filteredClaims.map(claim => claim.id));
    } else {
      setSelectedClaimIds([]);
    }
  };

  const handleClearSelection = () => {
    setSelectedClaimIds([]);
    setIsAllSelected(false);
  };

  const handleBatchUpdate = async (operation: string, value: string) => {
    try {
      // Batch operations implementation
      for (const claimId of selectedClaimIds) {
        switch (operation) {
          case 'update_status':
            await supabase
              .from('claims')
              .update({ status: value })
              .eq('id', claimId);
            break;
          case 'update_priority':
            await supabase
              .from('claims')
              .update({ priority: value })
              .eq('id', claimId);
            break;
          case 'quick_contacted':
            await supabase
              .from('claims')
              .update({ 
                status: 'contacted',
                last_contact_date: new Date().toISOString()
              })
              .eq('id', claimId);
            break;
          case 'quick_escalate':
            await supabase
              .from('claims')
              .update({ priority: 'urgent' })
              .eq('id', claimId);
            break;
          case 'quick_tomorrow':
            const tomorrow = new Date();
            tomorrow.setDate(tomorrow.getDate() + 1);
            await supabase
              .from('claims')
              .update({ next_followup_date: tomorrow.toISOString() })
              .eq('id', claimId);
            break;
          // Add more operations as needed
        }
      }
      
      // Refresh data and clear selection
      await loadDashboardData();
      handleClearSelection();
    } catch (error) {
      console.error('Batch operation failed:', error);
      throw error;
    }
  };

  // Show claim detail view
  if (currentView === 'detail' && selectedClaimId) {
    console.log('🎨 Rendering ClaimDetailView for:', selectedClaimId);
    return (
      <ClaimDetailView 
        claimId={selectedClaimId} 
        onBack={handleBackToDashboard}
      />
    );
  }

  // Show new claim form
  if (currentView === 'new') {
    return (
      <ClaimForm 
        onBack={handleBackToDashboard}
        onSave={handleCreateClaim}
      />
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading claims dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-semibold">Error Loading Dashboard</p>
            <p className="text-sm">{error}</p>
            <Button 
              variant="outline" 
              onClick={loadDashboardData} 
              className="mt-4"
            >
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Claims Dashboard</h1>
          <p className="text-gray-600">Manage and track asset recovery claims</p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadDashboardData}>
            <FileText className="h-4 w-4 mr-2" />
            Refresh
          </Button>
          <Button onClick={handleNewClaim}>
            <Plus className="h-4 w-4 mr-2" />
            New Claim
          </Button>
        </div>
      </div>

      {/* Dashboard Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Claims</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalClaims}</div>
            <p className="text-xs text-muted-foreground">
              Active recovery cases
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats.totalValue)}</div>
            <p className="text-xs text-muted-foreground">
              Combined claim amounts
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingClaims}</div>
            <p className="text-xs text-muted-foreground">
              Require attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.completedClaims}</div>
            <p className="text-xs text-muted-foreground">
              Successfully recovered
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Enhanced Filters */}
      <DashboardFilters
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        priorityFilter={priorityFilter}
        stateFilter={stateFilter}
        onSearchChange={setSearchTerm}
        onStatusChange={setStatusFilter}
        onPriorityChange={setPriorityFilter}
        onStateChange={setStateFilter}
        onClearFilters={handleClearFilters}
        totalResults={claims.length}
        filteredResults={filteredClaims.length}
      />

      {/* Batch Operations */}
      <BatchOperations
        selectedClaimIds={selectedClaimIds}
        onClearSelection={handleClearSelection}
        onBatchUpdate={handleBatchUpdate}
        totalSelected={selectedClaimIds.length}
      />

      {/* Claims Table */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>Claims ({filteredClaims.length})</CardTitle>
              <CardDescription>Recent asset recovery claims with current status and details</CardDescription>
            </div>
            <Button onClick={handleNewClaim}>
              <Plus className="h-4 w-4 mr-2" />
              New Claim
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {filteredClaims.length === 0 ? (
            <div className="text-center py-8">
              <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No claims found matching your criteria</p>
              <Button variant="outline" className="mt-4" onClick={handleClearFilters}>
                Clear Filters
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium w-12">
                      <Checkbox
                        checked={isAllSelected}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="text-left py-3 px-4 font-medium">Claim ID</th>
                    <th className="text-left py-3 px-4 font-medium">Owner</th>
                    <th className="text-left py-3 px-4 font-medium">Amount</th>
                    <th className="text-left py-3 px-4 font-medium">Status</th>
                    <th className="text-left py-3 px-4 font-medium">Priority</th>
                    <th className="text-left py-3 px-4 font-medium">State</th>
                    <th className="text-left py-3 px-4 font-medium">Created</th>
                    <th className="text-left py-3 px-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredClaims.map((claim) => (
                    <tr key={claim.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <Checkbox
                          checked={selectedClaimIds.includes(claim.id)}
                          onCheckedChange={(checked) => handleSelectClaim(claim.id, checked)}
                        />
                      </td>
                      <td 
                        className="py-3 px-4 font-mono text-sm cursor-pointer" 
                        onClick={() => handleClaimClick(claim.id)}
                      >
                        {claim.property_id || claim.id.slice(0, 8)}
                      </td>
                      <td className="py-3 px-4">
                        <div className="font-medium">{claim.owner_name}</div>
                      </td>
                      <td className="py-3 px-4 font-medium">
                        {formatCurrency(claim.amount)}
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getStatusColor(claim.status)}>
                          {claim.status.replace('_', ' ')}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">
                        <Badge className={getPriorityColor(claim.priority)}>
                          {claim.priority}
                        </Badge>
                      </td>
                      <td className="py-3 px-4">{claim.state}</td>
                      <td className="py-3 px-4 text-sm text-gray-600">
                        {formatDate(claim.created_at)}
                      </td>
                      <td className="py-3 px-4">
                        <Button variant="ghost" size="sm" onClick={(e) => {
                          e.stopPropagation();
                          handleClaimClick(claim.id);
                        }}>
                          <Eye className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
} 