import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Users, 
  TrendingUp, 
  Globe, 
  Handshake,
  Award,
  DollarSign,
  BarChart3,
  Building2,
  UserPlus,
  Target,
  Star,
  Zap,
  Shield,
  Crown,
  Sparkles,
  ArrowUp,
  ArrowDown,
  Calendar,
  MapPin,
  Briefcase,
  Phone,
  Mail
} from 'lucide-react';
import { 
  PartnerEcosystem, 
  PartnerTier,
  PartnerManagement 
} from '@/types/global-platform';

interface PartnerEcosystemProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const PartnerEcosystemComponent: React.FC<PartnerEcosystemProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [ecosystemMetrics, setEcosystemMetrics] = useState({
    total_partners: 347,
    active_partners: 289,
    revenue_through_partners: 2847000,
    partner_growth_rate: 23.7,
    average_deal_size: 84500,
    top_performing_tier: 'Gold'
  });

  const partnerTiers = [
    {
      name: 'Bronze',
      count: 156,
      revenue_share: 15,
      color: 'bg-amber-600',
      requirements: ['$25K annual revenue', 'Basic certification'],
      benefits: ['15% revenue share', 'Basic support', 'Marketing materials']
    },
    {
      name: 'Silver', 
      count: 89,
      revenue_share: 20,
      color: 'bg-gray-400',
      requirements: ['$100K annual revenue', 'Advanced certification'],
      benefits: ['20% revenue share', 'Enhanced support', 'Co-marketing opportunities']
    },
    {
      name: 'Gold',
      count: 67,
      revenue_share: 25,
      color: 'bg-yellow-500',
      requirements: ['$500K annual revenue', 'Expert certification'],
      benefits: ['25% revenue share', 'Premium support', 'Joint solutions development']
    },
    {
      name: 'Platinum',
      count: 23,
      revenue_share: 30,
      color: 'bg-purple-600',
      requirements: ['$2M annual revenue', 'Strategic alignment'],
      benefits: ['30% revenue share', 'White-glove support', 'Executive relationship']
    },
    {
      name: 'Diamond',
      count: 12,
      revenue_share: 35,
      color: 'bg-blue-600',
      requirements: ['$10M annual revenue', 'Global presence'],
      benefits: ['35% revenue share', 'Dedicated CSM', 'Product roadmap input']
    }
  ];

  const partnerPrograms = [
    {
      type: 'Reseller Program',
      partners: 198,
      revenue_contribution: 45,
      description: 'White-label solutions with territory management',
      status: 'Growing',
      trend: 'up'
    },
    {
      type: 'MSP Program', 
      partners: 89,
      revenue_contribution: 32,
      description: 'Managed service providers with recurring revenue models',
      status: 'Stable',
      trend: 'stable'
    },
    {
      type: 'Integration Partners',
      partners: 34,
      revenue_contribution: 15,
      description: 'Technology integrations and API partnerships',
      status: 'Expanding',
      trend: 'up'
    },
    {
      type: 'Consulting Partners',
      partners: 26,
      revenue_contribution: 8,
      description: 'Professional services and implementation expertise',
      status: 'Emerging',
      trend: 'up'
    }
  ];

  const recentPartnerActivity = [
    {
      partner: 'TechFlow Solutions',
      tier: 'Gold',
      activity: 'Closed $250K deal',
      impact: '+$37.5K commission',
      timestamp: '2 hours ago',
      type: 'deal'
    },
    {
      partner: 'Global MSP Corp',
      tier: 'Platinum', 
      activity: 'Upgraded tier',
      impact: 'Increased revenue share to 30%',
      timestamp: '1 day ago',
      type: 'tier_upgrade'
    },
    {
      partner: 'DataSync Integrations',
      tier: 'Silver',
      activity: 'New integration launched',
      impact: 'Expanded marketplace offering',
      timestamp: '3 days ago',
      type: 'integration'
    },
    {
      partner: 'Apex Consulting',
      tier: 'Bronze',
      activity: 'Completed certification',
      impact: 'Enabled for advanced features',
      timestamp: '1 week ago',
      type: 'certification'
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Partners</p>
                <p className="text-2xl font-bold text-blue-600">{ecosystemMetrics.total_partners}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +{ecosystemMetrics.partner_growth_rate}% growth
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Partner Revenue</p>
                <p className="text-2xl font-bold text-green-600">
                  ${(ecosystemMetrics.revenue_through_partners / 1000000).toFixed(1)}M
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              62% of total revenue
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Avg Deal Size</p>
                <p className="text-2xl font-bold text-purple-600">
                  ${(ecosystemMetrics.average_deal_size / 1000).toFixed(0)}K
                </p>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +18% vs last quarter
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Partners</p>
                <p className="text-2xl font-bold text-orange-600">{ecosystemMetrics.active_partners}</p>
              </div>
              <Handshake className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              83% engagement rate
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Award className="h-5 w-5" />
              Partner Tier Distribution
            </CardTitle>
            <CardDescription>
              Partner count and revenue share by tier level
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {partnerTiers.map((tier, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <div className={`w-3 h-3 rounded-full ${tier.color}`}></div>
                    <span className="font-medium">{tier.name}</span>
                    <Badge variant="outline">{tier.count} partners</Badge>
                  </div>
                  <span className="text-sm font-medium">{tier.revenue_share}% share</span>
                </div>
                <Progress value={(tier.count / ecosystemMetrics.total_partners) * 100} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Briefcase className="h-5 w-5" />
              Partner Programs
            </CardTitle>
            <CardDescription>
              Performance across different partnership models
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {partnerPrograms.map((program, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-2">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{program.type}</h4>
                  <div className="flex items-center gap-2">
                    {program.trend === 'up' && <ArrowUp className="h-4 w-4 text-green-600" />}
                    {program.trend === 'stable' && <Target className="h-4 w-4 text-blue-600" />}
                    <Badge variant={program.status === 'Growing' ? 'default' : 'secondary'}>
                      {program.status}
                    </Badge>
                  </div>
                </div>
                <p className="text-sm text-gray-600">{program.description}</p>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Partners: </span>
                    <span className="font-medium">{program.partners}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Revenue: </span>
                    <span className="font-medium">{program.revenue_contribution}%</span>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderPartnerOnboarding = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <UserPlus className="h-5 w-5" />
            Partner Onboarding Pipeline
          </CardTitle>
          <CardDescription>
            Current applications and onboarding status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">47</div>
              <div className="text-sm text-gray-600">Applications</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">23</div>
              <div className="text-sm text-gray-600">In Review</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">12</div>
              <div className="text-sm text-gray-600">Onboarding</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">8</div>
              <div className="text-sm text-gray-600">Completed</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Application Process</CardTitle>
            <CardDescription>
              Streamlined partner application workflow
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { step: 'Initial Application', time: '5 minutes', status: 'automated' },
              { step: 'Document Verification', time: '2-3 days', status: 'review' },
              { step: 'Technical Assessment', time: '1 week', status: 'testing' },
              { step: 'Business Alignment', time: '3-5 days', status: 'interview' },
              { step: 'Contract & Onboarding', time: '1 week', status: 'finalization' }
            ].map((step, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{step.step}</div>
                  <div className="text-sm text-gray-600">Avg: {step.time}</div>
                </div>
                <Badge variant="outline">{step.status}</Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Tier Requirements</CardTitle>
            <CardDescription>
              Partner tier qualification criteria
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {partnerTiers.slice(0, 3).map((tier, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center gap-3 mb-2">
                  <div className={`w-3 h-3 rounded-full ${tier.color}`}></div>
                  <h4 className="font-medium">{tier.name} Tier</h4>
                </div>
                <div className="space-y-1">
                  {tier.requirements.map((req, reqIndex) => (
                    <div key={reqIndex} className="text-sm text-gray-600 flex items-center gap-2">
                      <Shield className="h-3 w-3" />
                      {req}
                    </div>
                  ))}
                </div>
                <div className="mt-2 pt-2 border-t">
                  <div className="text-sm text-green-600 font-medium">
                    {tier.revenue_share}% Revenue Share
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderRevenueSharing = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            Revenue Sharing Performance
          </CardTitle>
          <CardDescription>
            Partner commissions and payout analytics
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">$847K</div>
              <div className="text-sm text-gray-600">Total Payouts (YTD)</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">$156K</div>
              <div className="text-sm text-gray-600">Pending Payouts</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">22.3%</div>
              <div className="text-sm text-gray-600">Avg Commission Rate</div>
            </div>
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Top Earning Partners</CardTitle>
            <CardDescription>
              Highest performing partners by revenue generation
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { name: 'TechFlow Solutions', tier: 'Gold', revenue: 420000, commission: 105000 },
              { name: 'Global MSP Corp', tier: 'Platinum', revenue: 380000, commission: 114000 },
              { name: 'Enterprise Partners', tier: 'Diamond', revenue: 290000, commission: 101500 },
              { name: 'DataSync Integrations', tier: 'Silver', revenue: 180000, commission: 36000 },
              { name: 'CloudWorks LLC', tier: 'Gold', revenue: 150000, commission: 37500 }
            ].map((partner, index) => (
              <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                <div>
                  <div className="font-medium">{partner.name}</div>
                  <div className="text-sm text-gray-600">{partner.tier} Partner</div>
                </div>
                <div className="text-right">
                  <div className="font-medium text-green-600">
                    ${(partner.commission / 1000).toFixed(0)}K
                  </div>
                  <div className="text-sm text-gray-600">
                    ${(partner.revenue / 1000).toFixed(0)}K revenue
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Commission Structure</CardTitle>
            <CardDescription>
              Revenue sharing models by tier and program type
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { program: 'Reseller Program', base: 20, performance: 'Up to +5%', volume: 'Up to +3%' },
              { program: 'MSP Program', base: 25, performance: 'Up to +7%', volume: 'Up to +5%' },
              { program: 'Integration Partners', base: 15, performance: 'Up to +3%', volume: 'N/A' },
              { program: 'Consulting Partners', base: 30, performance: 'Up to +10%', volume: 'Up to +5%' }
            ].map((structure, index) => (
              <div key={index} className="border rounded-lg p-4">
                <h4 className="font-medium mb-3">{structure.program}</h4>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Base Rate</div>
                    <div className="font-medium">{structure.base}%</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Performance Bonus</div>
                    <div className="font-medium">{structure.performance}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Volume Bonus</div>
                    <div className="font-medium">{structure.volume}</div>
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderActivity = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Recent Partner Activity
          </CardTitle>
          <CardDescription>
            Latest partner achievements and milestones
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {recentPartnerActivity.map((activity, index) => (
            <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
              <div className="flex items-center gap-4">
                <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                  {activity.type === 'deal' && <DollarSign className="h-5 w-5 text-blue-600" />}
                  {activity.type === 'tier_upgrade' && <ArrowUp className="h-5 w-5 text-green-600" />}
                  {activity.type === 'integration' && <Zap className="h-5 w-5 text-purple-600" />}
                  {activity.type === 'certification' && <Award className="h-5 w-5 text-orange-600" />}
                </div>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{activity.partner}</span>
                    <Badge variant="outline">{activity.tier}</Badge>
                  </div>
                  <div className="text-sm text-gray-600">{activity.activity}</div>
                  <div className="text-sm text-green-600">{activity.impact}</div>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">{activity.timestamp}</div>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Partnership Opportunities</CardTitle>
            <CardDescription>
              Potential expansion and growth initiatives
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              {
                opportunity: 'Global Expansion Partners',
                market: 'European Markets',
                potential: '$2.3M ARR',
                status: 'Active'
              },
              {
                opportunity: 'Industry Specialists',
                market: 'Healthcare Vertical',
                potential: '$1.8M ARR',
                status: 'Planning'
              },
              {
                opportunity: 'Technology Integrations',
                market: 'ERP Platforms',
                potential: '$1.2M ARR',
                status: 'Negotiating'
              }
            ].map((opp, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{opp.opportunity}</h4>
                  <Badge variant={opp.status === 'Active' ? 'default' : 'secondary'}>
                    {opp.status}
                  </Badge>
                </div>
                <div className="text-sm text-gray-600 mb-2">{opp.market}</div>
                <div className="text-sm font-medium text-green-600">{opp.potential} potential</div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Partner Success Metrics</CardTitle>
            <CardDescription>
              Key performance indicators for partner ecosystem
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: 'Partner Satisfaction', value: 4.7, max: 5.0, unit: '/5.0' },
              { metric: 'Time to First Deal', value: 28, max: 45, unit: ' days' },
              { metric: 'Partner Retention Rate', value: 94, max: 100, unit: '%' },
              { metric: 'Revenue per Partner', value: 285, max: 400, unit: 'K' }
            ].map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{metric.metric}</span>
                  <span className="text-sm font-medium">
                    {metric.value}{metric.unit}
                  </span>
                </div>
                <Progress value={(metric.value / metric.max) * 100} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Partner Ecosystem</h2>
          <p className="text-gray-600">
            Manage global partner network and revenue sharing programs
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
            {userPlan === 'diamond' ? 'Diamond Ecosystem' : 'Platinum Ecosystem'}
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade to Diamond
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="onboarding">Onboarding</TabsTrigger>
          <TabsTrigger value="revenue">Revenue</TabsTrigger>
          <TabsTrigger value="activity">Activity</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="onboarding" className="space-y-6">
          {renderPartnerOnboarding()}
        </TabsContent>

        <TabsContent value="revenue" className="space-y-6">
          {renderRevenueSharing()}
        </TabsContent>

        <TabsContent value="activity" className="space-y-6">
          {renderActivity()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Crown className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unlock Global Partner Network</h3>
              <p className="text-gray-600 mb-4">
                Upgrade to Diamond for advanced partner management, global marketplace access, and strategic alliance capabilities
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 