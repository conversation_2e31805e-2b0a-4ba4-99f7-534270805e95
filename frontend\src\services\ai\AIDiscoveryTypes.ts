// Core AI Discovery Types - Extracted from aiDiscoveryEngine.ts

export interface DiscoveryResults {
  nameVariants: string[];
  businessEntityCheck: BusinessEntityResult;
  deathRecordCheck: DeathRecordResult;
  heirDiscovery: HeirDiscoveryResult[];
  socialMediaFingerprint: SocialMediaResult;
  phoneValidation: PhoneValidationResult;
  emailValidation: EmailValidationResult;
  addressValidation: AddressValidationResult;
  contactQuality: ContactQualityScore;
  discoveryConfidence: number;
  processingTime: number;
  discoveryStages: DiscoveryStage[];
  interpretation: DataInterpretationResult;
  multiSourceResults: MultiSourceResults;
  successProbability: number;
}

export interface DataInterpretationResult {
  entityClassification: EntityClassification;
  nameInterpretations: NameInterpretation[];
  addressAnalysis: AddressAnalysis;
  propertyTypeInsights: PropertyTypeInsights;
  discoveryStrategy: DiscoveryStrategy;
  probabilityAssessment: number;
}

export interface EntityClassification {
  type: 'individual' | 'business' | 'trust' | 'estate' | 'unknown';
  confidence: number;
  reasoning: string[];
  indicators: string[];
}

export interface NameInterpretation {
  format: 'FIRST_MIDDLE_LAST' | 'LAST_FIRST_MIDDLE' | 'BUSINESS_ENTITY' | 'ESTATE_TRUST';
  first?: string;
  middle?: string;
  last?: string;
  businessName?: string;
  confidence: number;
  searchVariants: string[];
}

export interface AddressAnalysis {
  confidence: number;
  ageEstimate: number;
  addressType: 'residential' | 'commercial' | 'po_box' | 'unknown';
  searchableAddress: string;
  qualityScore: number;
}

export interface PropertyTypeInsights {
  category: 'LIFE_INSURANCE' | 'EMPLOYMENT_BENEFITS' | 'BANKING_FINANCE' | 'OTHER';
  deathProbabilityIndicator: number;
  searchStrategy: string[];
}

export interface DiscoveryStrategy {
  primaryApproach: string;
  dataSourcePriority: string[];
  estimatedDuration: number;
  successProbability: number;
}

export interface DiscoveryStage {
  name: string;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  progress: number;
  timeElapsed: number;
  results?: any;
  message: string;
}

export interface MultiSourceResults {
  propertyRecords: PropertySearchResult[];
  voterRegistration: VoterSearchResult[];
  socialMediaProfiles: EnhancedSocialMediaResult[];
  professionalNetworks: ProfessionalNetworkResult[];
  businessRegistrations: BusinessRegistrationResult[];
  courtRecords: CourtRecordResult[];
  addressHistory: AddressHistoryResult[];
}

export interface BusinessEntityResult {
  isBusinessEntity: boolean;
  businessType: 'corporation' | 'llc' | 'partnership' | 'trust' | 'estate' | 'individual' | 'unknown';
  registrationState: string | null;
  businessStatus: 'active' | 'inactive' | 'dissolved' | 'unknown';
  officers: BusinessOfficer[];
  registeredAddress: string | null;
  confidence: number;
  registrationDate?: Date;
  ein?: string;
  businessDescription?: string;
  relatedEntities?: string[];
}

export interface BusinessOfficer {
  name: string;
  title: string;
  address: string | null;
  role: 'president' | 'secretary' | 'treasurer' | 'director' | 'member' | 'trustee' | 'other';
  appointmentDate?: Date;
  isActive?: boolean;
}

export interface DeathRecordResult {
  isDeceased: boolean;
  deathDate: Date | null;
  deathState: string | null;
  confidence: number;
  source: 'ssdi' | 'obituary' | 'public_record' | 'none';
  placeOfDeath?: string;
  causeOfDeath?: string;
  survivingFamily?: string[];
  obituaryUrl?: string;
}

export interface HeirDiscoveryResult {
  name: string;
  relationship: 'spouse' | 'child' | 'parent' | 'sibling' | 'heir' | 'executor' | 'unknown';
  contact: {
    phone: string | null;
    email: string | null;
    address: string | null;
  };
  confidence: number;
  source: string;
  age?: number;
  occupation?: string;
  legalStatus?: 'primary_heir' | 'secondary_heir' | 'executor' | 'beneficiary';
  verificationStatus?: 'verified' | 'probable' | 'possible';
}

export interface SocialMediaResult {
  platforms: SocialMediaPlatform[];
  overallPresence: 'active' | 'inactive' | 'none';
  lastActivity: Date | null;
  engagementScore: number;
  confidence: number;
  digitalFootprintScore?: number;
  reachabilityAssessment?: string;
}

export interface SocialMediaPlatform {
  platform: 'facebook' | 'linkedin' | 'twitter' | 'instagram' | 'other';
  profileUrl: string;
  verified: boolean;
  lastActivity: Date | null;
  followerCount: number | null;
  activityLevel: 'high' | 'medium' | 'low';
  displayName?: string;
  bio?: string;
  location?: string;
  employmentInfo?: string;
  contactable?: boolean;
}

export interface ContactQualityScore {
  overall: number;
  components: {
    contactability: number;
    recency: number;
    completeness: number;
    reliability: number;
    responsiveness: number;
  };
  recommendation: 'high_priority' | 'medium_priority' | 'low_priority' | 'skip';
  expectedResponseRate: number;
  contactMethod: 'phone' | 'email' | 'mail' | 'social_media';
  optimalContactTime: string;
  personalizedMessage: string;
}

export interface DiscoveryOptions {
  enableBusinessLookup: boolean;
  enableDeathRecordCheck: boolean;
  enableHeirDiscovery: boolean;
  enableSocialMediaSearch: boolean;
  enablePhoneValidation: boolean;
  enableEmailValidation: boolean;
  enableAddressValidation: boolean;
  maxProcessingTime: number;
  qualityThreshold: number;
  userPlan: 'bronze' | 'silver' | 'gold' | 'enterprise';
  enableProgressUpdates: boolean;
  onProgressUpdate?: (stage: DiscoveryStage) => void;
  enableAdvancedFeatures: boolean;
}

export interface AIDiscoveryConfig {
  enableCulturalPatterns: boolean;
  enablePhoneticMatching: boolean;
  enableFamilyConnections: boolean;
  enableAddressHistory: boolean;
  minimumConfidence: number;
  maxResults: number;
  timeoutMs: number;
}

// Additional result types
export interface PropertySearchResult {
  address: string;
  ownerName: string;
  matchConfidence: number;
  recordDate: Date;
  propertyValue: number;
  source: string;
}

export interface VoterSearchResult {
  name: string;
  address: string;
  party: string;
  registrationDate: Date;
  matchConfidence: number;
}

export interface EnhancedSocialMediaResult {
  platform: string;
  profileUrl: string;
  displayName: string;
  location?: string;
  employmentInfo?: string;
  lastActivity: Date;
  matchConfidence: number;
  verificationLevel: 'high' | 'medium' | 'low';
}

export interface ProfessionalNetworkResult {
  platform: 'linkedin' | 'other';
  name: string;
  title: string;
  company: string;
  location: string;
  profileUrl: string;
  matchConfidence: number;
}

export interface BusinessRegistrationResult {
  businessName: string;
  registrationState: string;
  status: string;
  officers: BusinessOfficer[];
  registrationDate: Date;
  matchConfidence: number;
}

export interface CourtRecordResult {
  caseType: 'probate' | 'civil' | 'family' | 'other';
  caseNumber: string;
  parties: string[];
  date: Date;
  status: string;
  matchConfidence: number;
}

export interface AddressHistoryResult {
  address: string;
  residencyPeriod: string;
  confidence: number;
  source: string;
}

export interface PhoneValidationResult {
  isValid: boolean;
  phoneNumber: string | null;
  type: 'landline' | 'mobile' | 'voip' | 'unknown';
  carrier: string | null;
  location: string | null;
  confidence: number;
  lastVerified: Date | null;
  lineConnected?: boolean;
  doNotCall?: boolean;
  businessHours?: string;
  alternateNumbers?: string[];
}

export interface EmailValidationResult {
  isValid: boolean;
  emailAddress: string | null;
  deliverable: boolean;
  domain: string | null;
  domainType: 'personal' | 'business' | 'disposable' | 'unknown';
  confidence: number;
  lastVerified: Date | null;
  mxRecordValid?: boolean;
  riskScore?: number;
  alternateEmails?: string[];
}

export interface AddressValidationResult {
  isValid: boolean;
  standardizedAddress: string | null;
  coordinates: { lat: number; lng: number } | null;
  addressType: 'residential' | 'commercial' | 'po_box' | 'unknown';
  deliverable: boolean;
  confidence: number;
  lastVerified: Date | null;
  occupancyStatus?: 'occupied' | 'vacant' | 'unknown';
  propertyValue?: number;
  neighborhoodData?: NeighborhoodData;
}

export interface NeighborhoodData {
  incomeLevel: 'high' | 'medium' | 'low';
  demographics: string;
  safetyScore: number;
}
