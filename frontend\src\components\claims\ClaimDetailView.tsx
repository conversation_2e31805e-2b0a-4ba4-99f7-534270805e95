import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  ArrowLeft,
  Edit3,
  Save,
  X,
  MapPin,
  User,
  Phone,
  Mail,
  MessageSquare,
  Edit2,
  MoreHorizontal,
  AlertCircle,
  FileText,
  History
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { DocumentManager } from '@/features/documents';
import DocumentUpload from "./DocumentUpload";
import DocumentViewer from "./DocumentViewer";

interface Claim {
  id: string;
  property_id?: string;
  owner_name: string;
  owner_first_name?: string;
  owner_last_name?: string;
  owner_business_name?: string;
  amount: number;
  property_type?: string;
  status: string;
  priority: string;
  assigned_agent_id?: string;
  state: string;
  holder_name?: string;
  holder_address?: string;
  holder_city?: string;
  holder_state?: string;
  holder_zip?: string;
  owner_address?: string;
  owner_city?: string;
  owner_state?: string;
  owner_zip?: string;
  report_date?: string;
  shares_reported?: number;
  securities_name?: string;
  cusip?: string;
  complexity_score: number;
  estimated_recovery_amount?: number;
  actual_recovery_amount?: number;
  commission_rate: number;
  commission_amount?: number;
  compliance_status: string;
  last_contact_date?: string;
  next_followup_date?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

interface ClaimActivity {
  id: string;
  claim_id: string;
  activity_type: string;
  title: string;
  description?: string;
  agent_id: string;
  outcome?: string;
  created_at: string;
  users?: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

interface Props {
  claimId: string;
  onBack: () => void;
}

export default function ClaimDetailView({ claimId, onBack }: Props) {
  const [claim, setClaim] = useState<Claim | null>(null);
  const [activities, setActivities] = useState<ClaimActivity[]>([]);
  const [loading, setLoading] = useState(true);
  const [isEditing, setIsEditing] = useState(false);
  const [editedClaim, setEditedClaim] = useState<Partial<Claim>>({});
  const [newNote, setNewNote] = useState('');
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadClaimDetails();
  }, [claimId]);

  const loadClaimDetails = async () => {
    try {
      setLoading(true);
      setError(null);

      // Load claim details
      const { data: claimData, error: claimError } = await supabase
        .from('claims')
        .select('*')
        .eq('id', claimId)
        .single();

      if (claimError) throw claimError;
      setClaim(claimData);
      setEditedClaim(claimData);

      // Load claim activities
      const { data: activitiesData, error: activitiesError } = await supabase
        .from('claim_activities')
        .select(`
          *,
          users!agent_id (
            first_name,
            last_name,
            email
          )
        `)
        .eq('claim_id', claimId)
        .order('created_at', { ascending: false });

      if (activitiesError) {
        console.warn('Could not load activities:', activitiesError);
        setActivities([]);
      } else {
        setActivities(activitiesData || []);
      }

    } catch (err) {
      console.error('Error loading claim details:', err);
      setError(err instanceof Error ? err.message : 'Failed to load claim details');
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    if (!claim) return;

    try {
      const { error: updateError } = await supabase
        .from('claims')
        .update({
          ...editedClaim,
          updated_at: new Date().toISOString()
        })
        .eq('id', claimId);

      if (updateError) throw updateError;

      // Add activity log
      const { error: activityError } = await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: 'current_user_id', // TODO: Get from auth context
          activity_type: 'status_change',
          title: 'Claim details updated',
          description: 'Claim information modified'
        });

      if (activityError) {
        console.warn('Could not log activity:', activityError);
      }

      setClaim({ ...claim, ...editedClaim });
      setIsEditing(false);
      loadClaimDetails(); // Refresh activities

    } catch (err) {
      console.error('Error saving claim:', err);
      setError(err instanceof Error ? err.message : 'Failed to save changes');
    }
  };

  const handleAddNote = async () => {
    if (!newNote.trim()) return;

    try {
      const { error: activityError } = await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: 'current_user_id', // TODO: Get from auth context
          activity_type: 'note',
          title: 'Note added',
          description: newNote
        });

      if (activityError) throw activityError;

      setNewNote('');
      loadClaimDetails(); // Refresh activities

    } catch (err) {
      console.error('Error adding note:', err);
      setError(err instanceof Error ? err.message : 'Failed to add note');
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      'new': 'bg-blue-100 text-blue-800',
      'assigned': 'bg-yellow-100 text-yellow-800',
      'contacted': 'bg-purple-100 text-purple-800',
      'in_progress': 'bg-orange-100 text-orange-800',
      'documents_requested': 'bg-indigo-100 text-indigo-800',
      'under_review': 'bg-cyan-100 text-cyan-800',
      'approved': 'bg-green-100 text-green-800',
      'completed': 'bg-emerald-100 text-emerald-800',
      'on_hold': 'bg-gray-100 text-gray-800',
      'cancelled': 'bg-red-100 text-red-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      'low': 'bg-green-100 text-green-800',
      'medium': 'bg-yellow-100 text-yellow-800',
      'high': 'bg-orange-100 text-orange-800',
      'urgent': 'bg-red-100 text-red-800'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading claim details...</p>
        </div>
      </div>
    );
  }

  if (error || !claim) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-semibold">Error Loading Claim</p>
            <p className="text-sm">{error || 'Claim not found'}</p>
            <Button variant="outline" onClick={onBack} className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900">
              Claim {claim.property_id || claim.id.slice(0, 8)}
            </h1>
            <p className="text-gray-600">{claim.owner_name}</p>
          </div>
        </div>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button variant="outline" onClick={() => setIsEditing(false)}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </Button>
            </>
          ) : (
            <Button onClick={() => setIsEditing(true)}>
              <Edit3 className="h-4 w-4 mr-2" />
              Edit Claim
            </Button>
          )}
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Claim Information */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Claim Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">Owner Name</label>
                  {isEditing ? (
                    <Input
                      value={editedClaim.owner_name || ''}
                      onChange={(e) => setEditedClaim({ ...editedClaim, owner_name: e.target.value })}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-lg font-semibold">{claim.owner_name}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Amount</label>
                  {isEditing ? (
                    <Input
                      type="number"
                      value={editedClaim.amount || ''}
                      onChange={(e) => setEditedClaim({ ...editedClaim, amount: parseFloat(e.target.value) })}
                      className="mt-1"
                    />
                  ) : (
                    <p className="text-lg font-semibold text-green-600">{formatCurrency(claim.amount)}</p>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Status</label>
                  {isEditing ? (
                    <Select value={editedClaim.status || ''} onValueChange={(value) => setEditedClaim({ ...editedClaim, status: value })}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="assigned">Assigned</SelectItem>
                        <SelectItem value="contacted">Contacted</SelectItem>
                        <SelectItem value="in_progress">In Progress</SelectItem>
                        <SelectItem value="documents_requested">Documents Requested</SelectItem>
                        <SelectItem value="under_review">Under Review</SelectItem>
                        <SelectItem value="approved">Approved</SelectItem>
                        <SelectItem value="completed">Completed</SelectItem>
                        <SelectItem value="on_hold">On Hold</SelectItem>
                        <SelectItem value="cancelled">Cancelled</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Badge className={getStatusColor(claim.status)}>
                      {claim.status.replace('_', ' ')}
                    </Badge>
                  )}
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-600">Priority</label>
                  {isEditing ? (
                    <Select value={editedClaim.priority || ''} onValueChange={(value) => setEditedClaim({ ...editedClaim, priority: value })}>
                      <SelectTrigger className="mt-1">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="low">Low</SelectItem>
                        <SelectItem value="medium">Medium</SelectItem>
                        <SelectItem value="high">High</SelectItem>
                        <SelectItem value="urgent">Urgent</SelectItem>
                      </SelectContent>
                    </Select>
                  ) : (
                    <Badge className={getPriorityColor(claim.priority)}>
                      {claim.priority}
                    </Badge>
                  )}
                </div>
              </div>

              <Separator />

              {/* Address Information */}
              <div>
                <h4 className="font-semibold mb-3 flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  Address Information
                </h4>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">Owner Address</label>
                    <p className="text-sm">
                      {claim.owner_address && (
                        <>
                          {claim.owner_address}<br />
                          {claim.owner_city}, {claim.owner_state} {claim.owner_zip}
                        </>
                      )}
                    </p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-600">Holder Information</label>
                    <p className="text-sm">
                      {claim.holder_name && (
                        <>
                          {claim.holder_name}<br />
                          {claim.holder_address && (
                            <>
                              {claim.holder_address}<br />
                              {claim.holder_city}, {claim.holder_state} {claim.holder_zip}
                            </>
                          )}
                        </>
                      )}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Activity Timeline */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <History className="h-5 w-5" />
                Activity Timeline
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Add Note */}
                <div className="flex gap-2">
                  <Textarea
                    placeholder="Add a note or update..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    className="flex-1"
                  />
                  <Button onClick={handleAddNote} disabled={!newNote.trim()}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Add Note
                  </Button>
                </div>

                <Separator />

                {/* Activity List */}
                <div className="space-y-4">
                  {activities.length === 0 ? (
                    <p className="text-gray-500 text-center py-4">No activities recorded yet</p>
                  ) : (
                    activities.map((activity) => (
                      <div key={activity.id} className="flex gap-3 p-3 border rounded-lg">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <MessageSquare className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between">
                            <p className="font-medium">{activity.title}</p>
                            <span className="text-xs text-gray-500">{formatDate(activity.created_at)}</span>
                          </div>
                          {activity.description && (
                            <p className="text-sm text-gray-600 mt-1">{activity.description}</p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">
                            by {activity.users ? `${activity.users.first_name} ${activity.users.last_name}`.trim() : activity.agent_id || 'System'}
                          </p>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Created</span>
                <span className="text-sm font-medium">{formatDate(claim.created_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Updated</span>
                <span className="text-sm font-medium">{formatDate(claim.updated_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Complexity Score</span>
                <span className="text-sm font-medium">{claim.complexity_score}/10</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Commission Rate</span>
                <span className="text-sm font-medium">{(claim.commission_rate * 100).toFixed(1)}%</span>
              </div>
            </CardContent>
          </Card>

          {/* Documents - Enhanced */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Documents
              </CardTitle>
              <CardDescription>
                Upload, organize, and manage claim documents with advanced features
              </CardDescription>
            </CardHeader>
            <CardContent className="p-0">
              <DocumentManager
                claimId={claimId}
              />
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Last Contact</label>
                <p className="text-sm">{claim.last_contact_date ? formatDate(claim.last_contact_date) : 'No contact recorded'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Next Follow-up</label>
                <p className="text-sm">{claim.next_followup_date ? formatDate(claim.next_followup_date) : 'Not scheduled'}</p>
              </div>
              <div className="flex gap-2">
                <Button size="sm" className="flex-1">
                  <Phone className="h-4 w-4 mr-2" />
                  Call
                </Button>
                <Button size="sm" variant="outline" className="flex-1">
                  <Mail className="h-4 w-4 mr-2" />
                  Email
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
} 