/**
 * Fast CSV Upload Component
 * Optimized for large files with real-time progress and worker-based processing
 */

import React, { useState, useCallback, useRef, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Upload,
  FileText,
  CheckCircle,
  AlertTriangle,
  Clock,
  Zap,
  Activity,
  Database,
  X,
  Play,
  Pause,
  AlertCircle,
  CheckCircle2,
  BarChart3,
  Settings,
  ChevronDown,
  ChevronUp
} from 'lucide-react';

import { 
  FastCSVProcessor, 
  CSVProgress, 
  CSVValidationResult, 
  ProcessedRecord,
  CSVHeaderInfo,
  CSVFieldMapping
} from '@/services/fastCSVProcessor';

// =============================================================================
// INTERFACES
// =============================================================================

interface FastCSVUploadProps {
  onDataProcessed: (data: ProcessedRecord[]) => void;
  onError: (error: string) => void;
  maxFileSize?: number; // in MB
}

interface UploadState {
  stage: 'idle' | 'headers' | 'mapping' | 'processing' | 'complete' | 'error';
  file: File | null;
  headerInfo: CSVHeaderInfo | null;
  fieldMapping: CSVFieldMapping | null;
  validation: CSVValidationResult | null;
  progress: CSVProgress | null;
  processedData: ProcessedRecord[] | null;
  error: string | null;
}

const FIELD_OPTIONS = [
  // Essential Claimant Information
  { value: 'name', label: 'Full Name', description: 'Complete claimant name' },
  { value: 'firstName', label: 'First Name', description: 'Given name' },
  { value: 'lastName', label: 'Last Name', description: 'Family/surname' },
  { value: 'businessName', label: 'Business/Entity Name', description: 'Company or organization name' },
  { value: 'ssn', label: 'SSN/Tax ID', description: 'Social Security or Tax ID number' },
  
  // Contact & Location Information
  { value: 'address', label: 'Street Address', description: 'Physical/mailing address' },
  { value: 'city', label: 'City', description: 'City name' },
  { value: 'state', label: 'State/Province', description: 'State abbreviation or name' },
  { value: 'zip', label: 'ZIP/Postal Code', description: 'Postal code' },
  { value: 'phone', label: 'Phone Number', description: 'Primary contact number' },
  { value: 'email', label: 'Email Address', description: 'Email contact' },
  
  // Critical Asset Information
  { value: 'assetAmount', label: 'Asset Value/Amount', description: 'Dollar amount or value' },
  { value: 'assetType', label: 'Asset Type', description: 'Bank account, insurance, stocks, etc.' },
  { value: 'propertyDescription', label: 'Asset Description', description: 'Detailed asset description' },
  { value: 'parcelNumber', label: 'Parcel/Property ID', description: 'Property identification number' },
  
  // Financial Institution Data
  { value: 'institutionName', label: 'Holding Institution', description: 'Bank, insurance company, etc.' },
  { value: 'accountNumber', label: 'Account Number', description: 'Account or policy number' },
  { value: 'checkNumber', label: 'Check Number', description: 'Uncashed check number' },
  { value: 'policyNumber', label: 'Policy Number', description: 'Insurance policy number' },
  
  // Important Dates
  { value: 'dateOfBirth', label: 'Date of Birth', description: 'Claimant birth date' },
  { value: 'reportDate', label: 'Report Date', description: 'Date reported to state' },
  { value: 'lastActivityDate', label: 'Last Activity Date', description: 'Last account activity' },
  { value: 'escheatDate', label: 'Escheat Date', description: 'Date property escheated' },
  
  // Administrative & Legal
  { value: 'reportingState', label: 'Reporting State', description: 'State holding the property' },
  { value: 'caseNumber', label: 'Case/Claim Number', description: 'Tracking number' },
  { value: 'ownerType', label: 'Owner Type', description: 'Individual, corporation, etc.' },
  { value: 'assetStatus', label: 'Property Status', description: 'Active, claimed, etc.' },
  
  // Additional Useful Fields
  { value: 'county', label: 'County', description: 'County information' },
  { value: 'relationshipToOwner', label: 'Relationship', description: 'Heir, executor, etc.' },
  { value: 'notes', label: 'Notes/Comments', description: 'Additional information' },
  
  // System Fields
  { value: 'reportId', label: 'Report ID', description: 'Internal report identifier' },
  { value: 'claimantId', label: 'Claimant ID', description: 'Claimant identifier' },
  { value: 'ignore', label: '⚪ Ignore Field', description: 'Skip this column' }
];

// =============================================================================
// MAIN COMPONENT
// =============================================================================

export const FastCSVUpload: React.FC<FastCSVUploadProps> = ({
  onDataProcessed,
  onError,
  maxFileSize = 500
}) => {
  const [state, setState] = useState<UploadState>({
    stage: 'idle',
    file: null,
    headerInfo: null,
    fieldMapping: null,
    validation: null,
    progress: null,
    processedData: null,
    error: null
  });

  const [isDragOver, setIsDragOver] = useState(false);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const processor = useMemo(() => new FastCSVProcessor(), []);

  const resetState = useCallback(() => {
    setState({
      stage: 'idle',
      file: null,
      headerInfo: null,
      fieldMapping: null,
      validation: null,
      progress: null,
      processedData: null,
      error: null
    });
  }, []);

  const handleFileSelect = useCallback(async (file: File) => {
    try {
      // Validate file size
      if (file.size > maxFileSize * 1024 * 1024) {
        throw new Error(`File size exceeds ${maxFileSize}MB limit`);
      }

      // Validate file type
      if (!file.name.toLowerCase().endsWith('.csv')) {
        throw new Error('Please select a CSV file');
      }

      setState(prev => ({ 
        ...prev, 
        stage: 'headers', 
        file, 
        error: null 
      }));

      // Read headers and sample data
      const headerInfo = await processor.readCSVHeaders(file);
      
      setState(prev => ({ 
        ...prev, 
        stage: 'mapping',
        headerInfo,
        fieldMapping: headerInfo.suggestedMapping
      }));

    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        stage: 'error', 
        error: (error as Error).message 
      }));
      onError((error as Error).message);
    }
  }, [maxFileSize, processor, onError]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      handleFileSelect(files[0]);
    }
  }, [handleFileSelect]);

  const handleFieldMappingChange = useCallback((header: string, fieldType: string) => {
    setState(prev => ({
      ...prev,
      fieldMapping: prev.fieldMapping ? {
        ...prev.fieldMapping,
        [header]: fieldType as any
      } : null
    }));
  }, []);

  const startProcessing = useCallback(async () => {
    if (!state.file || !state.fieldMapping) return;

    try {
      setState(prev => ({ ...prev, stage: 'processing' }));

      const data = await processor.processCSVWithMapping(
        state.file,
        state.fieldMapping,
        (progress) => {
          setState(prev => ({ ...prev, progress }));
        }
      );

      setState(prev => ({ 
        ...prev, 
        stage: 'complete',
        processedData: data
      }));

      onDataProcessed(data);

    } catch (error) {
      setState(prev => ({ 
        ...prev, 
        stage: 'error', 
        error: (error as Error).message 
      }));
      onError((error as Error).message);
    }
  }, [state.file, state.fieldMapping, processor, onDataProcessed, onError]);

  const renderFileUpload = () => (
    <div className="space-y-6">
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDrop={handleDrop}
        onDragOver={(e) => { e.preventDefault(); setIsDragOver(true); }}
        onDragLeave={() => setIsDragOver(false)}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <div className="text-lg font-medium text-gray-900 mb-2">
          Drop your CSV file here
        </div>
        <div className="text-sm text-gray-500 mb-4">
          or click to browse (max {maxFileSize}MB)
        </div>
        <input
          type="file"
          accept=".csv"
          onChange={(e) => e.target.files?.[0] && handleFileSelect(e.target.files[0])}
          className="hidden"
          id="csv-upload"
        />
        <label
          htmlFor="csv-upload"
          className="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 cursor-pointer"
        >
          Select CSV File
        </label>
      </div>

      <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="flex">
          <AlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
          <div className="text-sm">
            <p className="text-yellow-700 font-medium mb-1">Important:</p>
            <p className="text-yellow-600">
              After uploading, you'll be able to map CSV columns to the correct fields 
              before processing. This ensures accurate data extraction regardless of 
              the CSV format from different states.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderFieldMapping = () => {
    if (!state.headerInfo || !state.fieldMapping) return null;

    const mappingStats = Object.values(state.fieldMapping).reduce((acc, fieldType) => {
      acc[fieldType] = (acc[fieldType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-medium text-gray-900">Map CSV Fields</h3>
            <p className="text-sm text-gray-500 mt-1">
              Map each column to the correct field type. Preview shows first few rows.
            </p>
          </div>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="flex items-center text-sm text-blue-600 hover:text-blue-700"
          >
            Advanced Options
            {showAdvanced ? <ChevronUp className="ml-1 h-4 w-4" /> : <ChevronDown className="ml-1 h-4 w-4" />}
          </button>
        </div>

        {/* Mapping Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-green-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-green-700">👤 Claimant Info</div>
            <div className="text-lg font-bold text-green-900">
              {(mappingStats.name || 0) + (mappingStats.firstName || 0) + (mappingStats.lastName || 0) + 
               (mappingStats.businessName || 0) + (mappingStats.ssn || 0)}
            </div>
          </div>
          <div className="bg-blue-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-blue-700">🏢 Institution Data</div>
            <div className="text-lg font-bold text-blue-900">
              {(mappingStats.institutionName || 0) + (mappingStats.accountNumber || 0) + 
               (mappingStats.checkNumber || 0) + (mappingStats.policyNumber || 0)}
            </div>
          </div>
          <div className="bg-purple-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-purple-700">📍 Contact Info</div>
            <div className="text-lg font-bold text-purple-900">
              {(mappingStats.address || 0) + (mappingStats.city || 0) + (mappingStats.state || 0) + 
               (mappingStats.zip || 0) + (mappingStats.phone || 0) + (mappingStats.email || 0)}
            </div>
          </div>
          <div className="bg-orange-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-orange-700">💰 Asset Info</div>
            <div className="text-lg font-bold text-orange-900">
              {(mappingStats.assetAmount || 0) + (mappingStats.assetType || 0) + 
               (mappingStats.propertyDescription || 0) + (mappingStats.parcelNumber || 0)}
            </div>
          </div>
          <div className="bg-gray-50 p-3 rounded-lg">
            <div className="text-sm font-medium text-gray-700">📅 Dates & Other</div>
            <div className="text-lg font-bold text-gray-900">
              {(mappingStats.dateOfBirth || 0) + (mappingStats.reportDate || 0) + 
               (mappingStats.lastActivityDate || 0) + (mappingStats.escheatDate || 0) +
               (mappingStats.reportingState || 0) + (mappingStats.caseNumber || 0) +
               (mappingStats.ownerType || 0) + (mappingStats.assetStatus || 0) +
               (mappingStats.county || 0) + (mappingStats.relationshipToOwner || 0) +
               (mappingStats.notes || 0) + (mappingStats.reportId || 0) + (mappingStats.claimantId || 0)}
            </div>
          </div>
        </div>

        {/* Field Mapping Table */}
        <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    CSV Column
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Sample Data
                  </th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Map To Field
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {state.headerInfo.headers.map((header, index) => (
                  <tr key={header} className="hover:bg-gray-50">
                    <td className="px-4 py-3 text-sm font-medium text-gray-900">
                      {header}
                    </td>
                    <td className="px-4 py-3 text-sm text-gray-500">
                      <div className="max-w-xs">
                        {state.headerInfo!.sampleData.map((row, rowIndex) => (
                          <div key={rowIndex} className="truncate">
                            {row[index] || '(empty)'}
                          </div>
                        )).slice(0, 3)}
                      </div>
                    </td>
                    <td className="px-4 py-3">
                      <select
                        value={state.fieldMapping![header]}
                        onChange={(e) => handleFieldMappingChange(header, e.target.value)}
                        className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="ignore">Ignore this field</option>
                        
                        <optgroup label="👤 Claimant/Person Information">
                          <option value="name">Full Name (Person Owed Assets)</option>
                          <option value="firstName">First Name</option>
                          <option value="lastName">Last Name</option>
                          <option value="businessName">Business/Entity Name</option>
                          <option value="ssn">SSN/Tax ID</option>
                        </optgroup>
                        
                        <optgroup label="🏢 Financial Institution Data">
                          <option value="institutionName">Institution Name (Holding Assets)</option>
                          <option value="accountNumber">Account Number</option>
                          <option value="checkNumber">Check Number</option>
                          <option value="policyNumber">Policy Number</option>
                        </optgroup>
                        
                        <optgroup label="📍 Contact & Location Information">
                          <option value="address">Street Address</option>
                          <option value="city">City</option>
                          <option value="state">State/Province</option>
                          <option value="zip">ZIP/Postal Code</option>
                          <option value="phone">Phone Number</option>
                          <option value="email">Email Address</option>
                        </optgroup>
                        
                        <optgroup label="💰 Asset Information">
                          <option value="assetAmount">Asset Value/Amount</option>
                          <option value="assetType">Asset Type</option>
                          <option value="propertyDescription">Asset Description</option>
                          <option value="parcelNumber">Parcel/Property ID</option>
                        </optgroup>
                        
                        <optgroup label="📅 Important Dates">
                          <option value="dateOfBirth">Date of Birth</option>
                          <option value="reportDate">Report Date</option>
                          <option value="lastActivityDate">Last Activity Date</option>
                          <option value="escheatDate">Escheat Date</option>
                        </optgroup>
                        
                        <optgroup label="📋 Administrative & Legal">
                          <option value="reportingState">Reporting State</option>
                          <option value="caseNumber">Case Number</option>
                          <option value="ownerType">Owner Type</option>
                          <option value="assetStatus">Asset Status</option>
                        </optgroup>
                        
                        <optgroup label="📝 Additional Fields">
                          <option value="county">County</option>
                          <option value="relationshipToOwner">Relationship to Owner</option>
                          <option value="notes">Notes/Comments</option>
                          <option value="reportId">Report ID</option>
                          <option value="claimantId">Claimant ID</option>
                        </optgroup>
                      </select>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Advanced Options */}
        {showAdvanced && (
          <div className="bg-gray-50 p-4 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-3">Advanced Processing Options</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Chunk Size (records per batch)
                </label>
                <input
                  type="number"
                  className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  defaultValue={5000}
                  min={1000}
                  max={20000}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Memory Limit (MB)
                </label>
                <input
                  type="number"
                  className="block w-full text-sm border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  defaultValue={1024}
                  min={512}
                  max={4096}
                />
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex justify-between">
          <button
            onClick={resetState}
            className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50"
          >
            Cancel
          </button>
          <button
            onClick={startProcessing}
            className="px-6 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Start Processing ({state.headerInfo.headers.length} columns)
          </button>
        </div>
      </div>
    );
  };

  const renderProcessing = () => {
    if (!state.progress) return null;

    const { stage, progress, recordsProcessed, currentSpeed, memoryUsage, errors } = state.progress;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <Clock className="mx-auto h-12 w-12 text-blue-500 mb-4 animate-spin" />
          <h3 className="text-lg font-medium text-gray-900">Processing CSV File</h3>
          <p className="text-sm text-gray-500 mt-1">
            Stage: {stage} • {recordsProcessed.toLocaleString()} records processed
          </p>
        </div>

        <div className="space-y-4">
          <div>
            <div className="flex justify-between text-sm mb-1">
              <span className="font-medium text-gray-700">Overall Progress</span>
              <span className="text-gray-500">{progress.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${progress}%` }}
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-gray-700">Processing Speed</div>
              <div className="text-lg font-bold text-gray-900">
                {currentSpeed.toLocaleString()} rec/sec
              </div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-gray-700">Memory Usage</div>
              <div className="text-lg font-bold text-gray-900">
                {memoryUsage} MB
              </div>
            </div>
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm font-medium text-gray-700">Records Found</div>
              <div className="text-lg font-bold text-gray-900">
                {recordsProcessed.toLocaleString()}
              </div>
            </div>
          </div>

          {errors.length > 0 && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex">
                <AlertCircle className="h-5 w-5 text-yellow-400 mt-0.5 mr-3" />
                <div>
                  <h4 className="text-sm font-medium text-yellow-800">Processing Warnings</h4>
                  <div className="mt-1 text-sm text-yellow-700">
                    {errors.map((error, index) => (
                      <div key={index}>{error}</div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  const renderComplete = () => {
    if (!state.processedData) return null;

    return (
      <div className="space-y-6">
        <div className="text-center">
          <CheckCircle2 className="mx-auto h-12 w-12 text-green-500 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Processing Complete!</h3>
          <p className="text-sm text-gray-500 mt-1">
            Successfully processed {state.processedData.length.toLocaleString()} records
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-green-50 p-4 rounded-lg">
            <Database className="h-8 w-8 text-green-600 mb-2" />
            <div className="text-2xl font-bold text-green-900">
              {state.processedData.length.toLocaleString()}
            </div>
            <div className="text-sm text-green-700">Total Records</div>
          </div>
          <div className="bg-blue-50 p-4 rounded-lg">
            <BarChart3 className="h-8 w-8 text-blue-600 mb-2" />
            <div className="text-2xl font-bold text-blue-900">
              {state.processedData.filter(r => r.name && r.name !== 'Unknown').length.toLocaleString()}
            </div>
            <div className="text-sm text-blue-700">Named Records</div>
          </div>
          <div className="bg-purple-50 p-4 rounded-lg">
            <Settings className="h-8 w-8 text-purple-600 mb-2" />
            <div className="text-2xl font-bold text-purple-900">
              {state.processedData.filter(r => r.phones.length > 0 || r.emails.length > 0).length.toLocaleString()}
            </div>
            <div className="text-sm text-purple-700">With Contact Info</div>
          </div>
        </div>

        <button
          onClick={resetState}
          className="w-full px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700"
        >
          Process Another File
        </button>
      </div>
    );
  };

  const renderError = () => (
    <div className="text-center space-y-4">
      <AlertCircle className="mx-auto h-12 w-12 text-red-500" />
      <div>
        <h3 className="text-lg font-medium text-gray-900">Processing Failed</h3>
        <p className="text-sm text-red-600 mt-1">{state.error}</p>
      </div>
      <button
        onClick={resetState}
        className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700"
      >
        Try Again
      </button>
    </div>
  );

  return (
    <div className="max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        {state.stage === 'idle' && renderFileUpload()}
        {state.stage === 'headers' && (
          <div className="text-center">
            <Clock className="mx-auto h-8 w-8 text-blue-500 animate-spin mb-4" />
            <p className="text-sm text-gray-600">Reading CSV headers...</p>
          </div>
        )}
        {state.stage === 'mapping' && renderFieldMapping()}
        {state.stage === 'processing' && renderProcessing()}
        {state.stage === 'complete' && renderComplete()}
        {state.stage === 'error' && renderError()}
      </div>
    </div>
  );
}; 