// Multi-Factor Authentication Service for AssetHunterPro
// Implements TOTP-based MFA with backup codes and security monitoring

import { supabase } from '@/lib/supabase'
import { FEATURE_FLAGS } from '@/config/features'

export interface MFAFactor {
  id: string
  type: 'totp'
  friendly_name: string
  status: 'verified' | 'unverified'
  created_at: string
  updated_at: string
}

export interface MFAEnrollment {
  id: string
  type: 'totp'
  totp: {
    qr_code: string
    secret: string
    uri: string
  }
}

export interface MFAChallenge {
  id: string
  type: 'totp'
  expires_at: number
}

export interface MFAVerification {
  access_token: string
  refresh_token: string
  user: any
}

export interface BackupCode {
  code: string
  used: boolean
  used_at?: string
}

export interface MFASettings {
  enabled: boolean
  required: boolean
  backup_codes: BackupCode[]
  recovery_email?: string
  last_verified?: string
}

export class MFAService {
  private static instance: MFAService
  private userId: string | null = null

  static getInstance(): MFAService {
    if (!MFAService.instance) {
      MFAService.instance = new MFAService()
    }
    return MFAService.instance
  }

  setUserId(userId: string) {
    this.userId = userId
  }

  /**
   * Enroll user in MFA (TOTP)
   */
  async enrollMFA(friendlyName: string = 'AssetHunterPro MFA'): Promise<{ data: MFAEnrollment | null; error: any }> {
    try {
      const { data, error } = await supabase.auth.mfa.enroll({
        factorType: 'totp',
        friendlyName
      })

      if (error) {
        console.error('❌ MFA enrollment error:', error)
        return { data: null, error }
      }

      // Store MFA settings in user profile
      await this.updateMFASettings({
        enabled: true,
        required: false,
        backup_codes: await this.generateBackupCodes(),
        last_verified: new Date().toISOString()
      })

      console.log('✅ MFA enrollment successful:', data)
      return { data, error: null }
    } catch (error) {
      console.error('❌ MFA enrollment failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Verify MFA enrollment with TOTP code
   */
  async verifyMFAEnrollment(factorId: string, code: string): Promise<{ error: any }> {
    try {
      const { data, error } = await supabase.auth.mfa.verify({
        factorId,
        challengeId: '', // Not needed for enrollment verification
        code
      })

      if (error) {
        console.error('❌ MFA verification error:', error)
        return { error }
      }

      // Update MFA settings to mark as verified
      await this.updateMFASettings({
        enabled: true,
        required: true,
        backup_codes: await this.generateBackupCodes(),
        last_verified: new Date().toISOString()
      })

      console.log('✅ MFA verification successful')
      return { error: null }
    } catch (error) {
      console.error('❌ MFA verification failed:', error)
      return { error }
    }
  }

  /**
   * Challenge MFA (request verification)
   */
  async challengeMFA(factorId: string): Promise<{ data: MFAChallenge | null; error: any }> {
    try {
      const { data, error } = await supabase.auth.mfa.challenge({
        factorId
      })

      if (error) {
        console.error('❌ MFA challenge error:', error)
        return { data: null, error }
      }

      console.log('✅ MFA challenge created')
      return { data, error: null }
    } catch (error) {
      console.error('❌ MFA challenge failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Verify MFA challenge with TOTP code
   */
  async verifyMFA(factorId: string, challengeId: string, code: string): Promise<{ data: MFAVerification | null; error: any }> {
    try {
      const { data, error } = await supabase.auth.mfa.verify({
        factorId,
        challengeId,
        code
      })

      if (error) {
        console.error('❌ MFA verification error:', error)
        return { data: null, error }
      }

      // Update last verified timestamp
      await this.updateMFASettings({
        last_verified: new Date().toISOString()
      })

      console.log('✅ MFA verification successful')
      return { data, error: null }
    } catch (error) {
      console.error('❌ MFA verification failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get user's MFA factors
   */
  async getMFAFactors(): Promise<{ data: MFAFactor[]; error: any }> {
    try {
      const { data, error } = await supabase.auth.mfa.listFactors()

      if (error) {
        console.error('❌ Error fetching MFA factors:', error)
        return { data: [], error }
      }

      return { data: data.totp || [], error: null }
    } catch (error) {
      console.error('❌ Failed to fetch MFA factors:', error)
      return { data: [], error }
    }
  }

  /**
   * Unenroll from MFA
   */
  async unenrollMFA(factorId: string): Promise<{ error: any }> {
    try {
      const { error } = await supabase.auth.mfa.unenroll({
        factorId
      })

      if (error) {
        console.error('❌ MFA unenrollment error:', error)
        return { error }
      }

      // Update MFA settings
      await this.updateMFASettings({
        enabled: false,
        required: false,
        backup_codes: [],
        last_verified: undefined
      })

      console.log('✅ MFA unenrollment successful')
      return { error: null }
    } catch (error) {
      console.error('❌ MFA unenrollment failed:', error)
      return { error }
    }
  }

  /**
   * Generate backup codes for account recovery
   */
  async generateBackupCodes(): Promise<BackupCode[]> {
    const codes: BackupCode[] = []
    
    for (let i = 0; i < 10; i++) {
      codes.push({
        code: this.generateRandomCode(),
        used: false
      })
    }

    return codes
  }

  /**
   * Verify backup code
   */
  async verifyBackupCode(code: string): Promise<{ success: boolean; error?: string }> {
    try {
      const settings = await this.getMFASettings()
      
      if (!settings.backup_codes) {
        return { success: false, error: 'No backup codes available' }
      }

      const backupCode = settings.backup_codes.find(bc => bc.code === code && !bc.used)
      
      if (!backupCode) {
        return { success: false, error: 'Invalid or already used backup code' }
      }

      // Mark code as used
      backupCode.used = true
      backupCode.used_at = new Date().toISOString()

      await this.updateMFASettings({
        backup_codes: settings.backup_codes,
        last_verified: new Date().toISOString()
      })

      return { success: true }
    } catch (error) {
      console.error('❌ Backup code verification failed:', error)
      return { success: false, error: 'Verification failed' }
    }
  }

  /**
   * Get MFA settings for user
   */
  async getMFASettings(): Promise<MFASettings> {
    try {
      if (!this.userId) {
        throw new Error('User ID not set')
      }

      const { data, error } = await supabase
        .from('user_mfa_settings')
        .select('*')
        .eq('user_id', this.userId)
        .single()

      if (error && error.code !== 'PGRST116') { // Not found error
        console.error('❌ Error fetching MFA settings:', error)
      }

      return data?.settings || {
        enabled: false,
        required: false,
        backup_codes: []
      }
    } catch (error) {
      console.error('❌ Failed to fetch MFA settings:', error)
      return {
        enabled: false,
        required: false,
        backup_codes: []
      }
    }
  }

  /**
   * Update MFA settings for user
   */
  private async updateMFASettings(settings: Partial<MFASettings>): Promise<void> {
    try {
      if (!this.userId) {
        throw new Error('User ID not set')
      }

      const currentSettings = await this.getMFASettings()
      const updatedSettings = { ...currentSettings, ...settings }

      const { error } = await supabase
        .from('user_mfa_settings')
        .upsert({
          user_id: this.userId,
          settings: updatedSettings,
          updated_at: new Date().toISOString()
        })

      if (error) {
        console.error('❌ Error updating MFA settings:', error)
        throw error
      }
    } catch (error) {
      console.error('❌ Failed to update MFA settings:', error)
      throw error
    }
  }

  /**
   * Generate random backup code
   */
  private generateRandomCode(): string {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'
    let result = ''
    for (let i = 0; i < 8; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length))
    }
    return result
  }


}

export const mfaService = MFAService.getInstance()
