import { useState, useEffect } from 'react';
import { <PERSON>, CardH<PERSON>er, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Plug, 
  FileText, 
  Phone, 
  Users, 
  Settings, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  TrendingUp, 
  Shield, 
  Zap, 
  Globe, 
  Mail, 
  MessageSquare, 
  Database, 
  BarChart, 
  Calendar, 
  CreditCard, 
  Building, 
  Link,
  Webhook,
  Key,
  RotateCcw
} from 'lucide-react';
import { IntegrationFramework, AvailableIntegration, ActiveIntegration } from '@/types/integrations';
import { usePricingLogic } from '@/hooks/usePricingLogic';

interface BasicIntegrationFrameworkProps {
  onUpgrade?: () => void;
}

export default function BasicIntegrationFramework({ onUpgrade }: BasicIntegrationFrameworkProps) {
  const { currentPlan, subscription } = usePricingLogic();
  const [activeTab, setActiveTab] = useState('overview');
  const [integrationFramework, setIntegrationFramework] = useState<IntegrationFramework | null>(null);
  const [availableIntegrations, setAvailableIntegrations] = useState<AvailableIntegration[]>([]);
  const [activeIntegrations, setActiveIntegrations] = useState<ActiveIntegration[]>([]);

  // Mock data - would come from API in real implementation
  useEffect(() => {
    const mockAvailableIntegrations: AvailableIntegration[] = [
      {
        integration_id: 'docusign',
        name: 'DocuSign',
        category: 'document',
        provider: 'DocuSign Inc.',
        description: 'Electronic signature and document workflow automation',
        logo_url: '/integrations/docusign-logo.png',
        pricing_tier_required: 'bronze',
        features: [
          {
            feature_name: 'E-Signature',
            description: 'Secure electronic signatures for claim documents',
            data_types: ['documents', 'signatures'],
            sync_direction: 'bidirectional',
            real_time_support: true,
            batch_support: true,
            webhooks_supported: true,
            rate_limited: true
          }
        ],
        setup_complexity: 'moderate',
        setup_time_estimate: '15-30 minutes',
        documentation_url: 'https://developers.docusign.com',
        support_level: 'email',
        api_version: 'v2.1',
        rate_limits: {
          requests_per_minute: 100,
          requests_per_hour: 1000,
          requests_per_day: 10000,
          burst_limit: 200,
          concurrent_connections: 10
        },
        data_residency: ['US', 'EU'],
        compliance_certifications: ['SOC2', 'GDPR', 'HIPAA']
      },
      {
        integration_id: 'twilio',
        name: 'Twilio',
        category: 'communication',
        provider: 'Twilio Inc.',
        description: 'Cloud communications platform for voice, SMS, and video',
        logo_url: '/integrations/twilio-logo.png',
        pricing_tier_required: 'silver',
        features: [
          {
            feature_name: 'Voice Calls',
            description: 'Make and receive voice calls with call tracking',
            data_types: ['call_logs', 'recordings'],
            sync_direction: 'bidirectional',
            real_time_support: true,
            batch_support: false,
            webhooks_supported: true,
            rate_limited: true
          }
        ],
        setup_complexity: 'easy',
        setup_time_estimate: '10-15 minutes',
        documentation_url: 'https://www.twilio.com/docs',
        support_level: 'email',
        api_version: '2010-04-01',
        rate_limits: {
          requests_per_minute: 3600,
          requests_per_hour: 30000,
          requests_per_day: 500000,
          burst_limit: 100,
          concurrent_connections: 50
        },
        data_residency: ['US', 'EU', 'APAC'],
        compliance_certifications: ['SOC2', 'GDPR', 'HIPAA', 'PCI DSS']
      },
      {
        integration_id: 'salesforce',
        name: 'Salesforce',
        category: 'crm',
        provider: 'Salesforce.com',
        description: 'World\'s #1 CRM platform for customer relationship management',
        logo_url: '/integrations/salesforce-logo.png',
        pricing_tier_required: 'gold',
        features: [
          {
            feature_name: 'Contact Sync',
            description: 'Bidirectional sync of contacts and leads',
            data_types: ['contacts', 'leads', 'opportunities'],
            sync_direction: 'bidirectional',
            real_time_support: true,
            batch_support: true,
            webhooks_supported: true,
            rate_limited: true
          }
        ],
        setup_complexity: 'complex',
        setup_time_estimate: '45-60 minutes',
        documentation_url: 'https://developer.salesforce.com',
        support_level: 'priority',
        api_version: 'v58.0',
        rate_limits: {
          requests_per_minute: 100,
          requests_per_hour: 1000,
          requests_per_day: 15000,
          burst_limit: 200,
          concurrent_connections: 25
        },
        data_residency: ['US', 'EU', 'APAC'],
        compliance_certifications: ['SOC2', 'GDPR', 'HIPAA', 'ISO 27001']
      }
    ];

    const mockActiveIntegrations: ActiveIntegration[] = [
      {
        integration_id: 'docusign',
        name: 'DocuSign',
        status: 'connected',
        connected_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 30 days ago
        last_sync: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
        next_sync: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
        sync_frequency: 'daily',
        configuration: {
          authentication: {
            auth_type: 'oauth2',
            credentials: {},
            token_expiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
            scopes: ['signature', 'extended'],
            auto_refresh: true
          },
          data_mapping: {},
          sync_settings: {
            batch_size: 100,
            parallel_processing: true,
            max_concurrent_operations: 5,
            transaction_support: true,
            rollback_on_error: true,
            progress_reporting: true
          },
          field_mappings: [],
          filters: [],
          transformations: [],
          error_handling: {
            retry_strategy: 'exponential_backoff',
            max_retries: 3,
            retry_delay_seconds: 30,
            error_notifications: true,
            dead_letter_queue: true,
            fallback_actions: []
          }
        },
        health_status: {
          overall_health: 'healthy',
          last_health_check: new Date(Date.now() - 10 * 60 * 1000),
          response_time_ms: 245,
          uptime_percentage: 99.8,
          error_rate_percentage: 0.2,
          data_quality_score: 96.5,
          alerts: []
        },
        usage_metrics: {
          total_api_calls: 1247,
          successful_calls: 1244,
          failed_calls: 3,
          average_response_time: 245,
          data_transferred_mb: 156.7,
          records_synced: 89,
          last_reset_date: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
          quota_usage: [
            {
              quota_type: 'api_calls',
              used: 1247,
              limit: 10000,
              reset_period: 'daily',
              next_reset: new Date(Date.now() + 12 * 60 * 60 * 1000)
            }
          ]
        },
        error_history: [],
        data_flow: {
          inbound_data: {
            total_records: 45,
            successful_records: 45,
            failed_records: 0,
            average_processing_time: 1.2,
            last_successful_sync: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          outbound_data: {
            total_records: 44,
            successful_records: 44,
            failed_records: 0,
            average_processing_time: 0.8,
            last_successful_sync: new Date(Date.now() - 2 * 60 * 60 * 1000)
          },
          total_records: 89,
          sync_conflicts: 0,
          data_quality_issues: 2
        }
      }
    ];

    setAvailableIntegrations(mockAvailableIntegrations);
    setActiveIntegrations(mockActiveIntegrations);
  }, []);

  const hasAccess = currentPlan && ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasAdvancedIntegrations = currentPlan && ['silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasPremiumIntegrations = currentPlan && ['gold', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasEnterpriseIntegrations = currentPlan && ['topaz', 'ruby', 'diamond'].includes(currentPlan.id);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'connected': return 'text-green-600 bg-green-100';
      case 'syncing': return 'text-blue-600 bg-blue-100';
      case 'error': return 'text-red-600 bg-red-100';
      case 'pending': return 'text-yellow-600 bg-yellow-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  const getHealthColor = (health: string) => {
    switch (health) {
      case 'healthy': return 'text-green-600';
      case 'degraded': return 'text-yellow-600';
      case 'unhealthy': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'document': return FileText;
      case 'communication': return Phone;
      case 'crm': return Users;
      case 'accounting': return CreditCard;
      case 'legal': return Building;
      case 'productivity': return Calendar;
      case 'analytics': return BarChart;
      default: return Plug;
    }
  };

  if (!hasAccess) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <Plug className="h-16 w-16 mx-auto text-gray-400" />
          <h3 className="text-xl font-semibold">Basic Integration Framework</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Connect AssetHunterPro with your essential business tools including DocuSign, phone systems, and CRM platforms.
          </p>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 mb-3">
              <strong>Included in Bronze and above:</strong>
            </p>
            <ul className="text-sm text-gray-600 space-y-1 text-left max-w-md mx-auto">
              <li>• DocuSign e-signature integration</li>
              <li>• Basic phone system connectivity</li>
              <li>• Essential CRM syncing</li>
              <li>• API management and monitoring</li>
              <li>• Real-time data synchronization</li>
            </ul>
          </div>
          <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
            Upgrade to Access Integrations
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Integration Framework Overview Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Plug className="h-6 w-6 text-blue-600" />
              <CardTitle>Integration Framework Dashboard</CardTitle>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-500">Active Integrations</div>
                <div className="text-2xl font-bold text-blue-600">{activeIntegrations.length}</div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">Health Score</div>
                <div className="text-2xl font-bold text-green-600">98.5%</div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-gray-500">Connected</div>
                  <div className="text-xl font-semibold">{activeIntegrations.filter(i => i.status === 'connected').length}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <RotateCcw className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-gray-500">Syncing Daily</div>
                  <div className="text-xl font-semibold">{activeIntegrations.filter(i => i.sync_frequency === 'daily').length}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-gray-500">API Calls Today</div>
                  <div className="text-xl font-semibold">2,847</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-gray-500">Error Rate</div>
                  <div className="text-xl font-semibold text-green-600">0.1%</div>
                </div>
              </div>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="active">Active</TabsTrigger>
              <TabsTrigger value="available">Available</TabsTrigger>
              <TabsTrigger value="docusign">DocuSign</TabsTrigger>
              <TabsTrigger value="phone">Phone</TabsTrigger>
              <TabsTrigger value="monitoring">Monitoring</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Integration Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Overall Health</span>
                      <span className="text-sm text-gray-600">98.5%</span>
                    </div>
                    <Progress value={98.5} className="h-2 bg-green-500" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Sync Success Rate</span>
                      <span className="text-sm text-gray-600">99.8%</span>
                    </div>
                    <Progress value={99.8} className="h-2 bg-green-500" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">API Response Time</span>
                      <span className="text-sm text-gray-600">245ms avg</span>
                    </div>
                    <Progress value={75} className="h-2 bg-blue-500" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Integration Activity</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-green-600" />
                        <span className="text-sm">DocuSign: 12 documents signed</span>
                      </div>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Phone className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">Twilio: 47 calls processed</span>
                      </div>
                      <span className="text-xs text-gray-500">4 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Users className="h-4 w-4 text-purple-600" />
                        <span className="text-sm">CRM: 23 contacts synced</span>
                      </div>
                      <span className="text-xs text-gray-500">6 hours ago</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="active" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Active Integrations</h3>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Manage All
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {activeIntegrations.map((integration) => (
                  <Card key={integration.integration_id} className="p-4">
                    <div className="flex items-center justify-between mb-3">
                      <div className="flex items-center gap-2">
                        <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                          <Plug className="h-4 w-4 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">{integration.name}</h4>
                          <div className="text-xs text-gray-500">
                            Connected {integration.connected_date.toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <Badge className={getStatusColor(integration.status)}>
                        {integration.status}
                      </Badge>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Health:</span>
                        <span className={`font-medium ${getHealthColor(integration.health_status.overall_health)}`}>
                          {integration.health_status.overall_health.toUpperCase()}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Uptime:</span>
                        <span className="font-medium">{integration.health_status.uptime_percentage}%</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Records Synced:</span>
                        <span className="font-medium">{integration.data_flow.total_records}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Last Sync:</span>
                        <span className="font-medium">{integration.last_sync.toLocaleTimeString()}</span>
                      </div>
                    </div>

                    <div className="mt-3 pt-3 border-t">
                      <Button size="sm" variant="outline" className="w-full">
                        Configure
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="available" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Available Integrations</h3>
                <div className="flex items-center gap-2">
                  {!hasEnterpriseIntegrations && (
                    <Badge variant="outline" className="text-orange-600">
                      Enterprise integrations require Topaz+ plan
                    </Badge>
                  )}
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableIntegrations.map((integration) => {
                  const IconComponent = getCategoryIcon(integration.category);
                  const canAccess = hasAccess && (
                    integration.pricing_tier_required === 'bronze' ||
                    (integration.pricing_tier_required === 'silver' && hasAdvancedIntegrations) ||
                    (integration.pricing_tier_required === 'gold' && hasPremiumIntegrations)
                  );

                  return (
                    <Card key={integration.integration_id} className="p-4">
                      <div className="flex items-center gap-2 mb-3">
                        <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                          <IconComponent className="h-4 w-4 text-gray-600" />
                        </div>
                        <div>
                          <h4 className="font-semibold">{integration.name}</h4>
                          <div className="text-xs text-gray-500 capitalize">
                            {integration.category} • {integration.provider}
                          </div>
                        </div>
                      </div>
                      
                      <p className="text-sm text-gray-600 mb-3">
                        {integration.description}
                      </p>

                      <div className="space-y-2 text-xs">
                        <div className="flex justify-between">
                          <span className="text-gray-500">Setup:</span>
                          <span className={`font-medium ${
                            integration.setup_complexity === 'easy' ? 'text-green-600' :
                            integration.setup_complexity === 'moderate' ? 'text-yellow-600' :
                            'text-red-600'
                          }`}>
                            {integration.setup_complexity}
                          </span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Time:</span>
                          <span className="font-medium">{integration.setup_time_estimate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-500">Plan Required:</span>
                          <Badge variant="outline" className="text-xs">
                            {integration.pricing_tier_required}+
                          </Badge>
                        </div>
                      </div>

                      <div className="mt-3 pt-3 border-t">
                        <Button 
                          size="sm" 
                          variant={canAccess ? "default" : "outline"} 
                          className="w-full"
                          disabled={!canAccess}
                          onClick={canAccess ? undefined : onUpgrade}
                        >
                          {canAccess ? 'Connect' : 'Upgrade Required'}
                        </Button>
                      </div>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            <TabsContent value="docusign" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">DocuSign Integration</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="default" className="bg-green-100 text-green-800">
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Connected
                  </Badge>
                </div>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <FileText className="h-5 w-5" />
                      E-Signature Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-2xl font-bold text-blue-600">47</div>
                        <div className="text-sm text-gray-600">Documents Sent</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-2xl font-bold text-green-600">42</div>
                        <div className="text-sm text-gray-600">Completed</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Completion Rate</span>
                      <span className="text-sm text-gray-600">89.4%</span>
                    </div>
                    <Progress value={89.4} className="h-2 bg-green-500" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Avg. Completion Time</span>
                      <span className="text-sm text-gray-600">2.3 days</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Template Library</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Asset Recovery Agreement</div>
                        <div className="text-xs text-gray-500">Used 23 times</div>
                      </div>
                      <Button size="sm" variant="outline">Use</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Authorization Form</div>
                        <div className="text-xs text-gray-500">Used 15 times</div>
                      </div>
                      <Button size="sm" variant="outline">Use</Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium">Payment Receipt</div>
                        <div className="text-xs text-gray-500">Used 9 times</div>
                      </div>
                      <Button size="sm" variant="outline">Use</Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="phone" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Phone System Integration</h3>
                {!hasAdvancedIntegrations && (
                  <Badge variant="outline" className="text-orange-600">
                    Advanced phone features require Silver+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Phone className="h-5 w-5 text-blue-600" />
                    <h4 className="font-semibold">Call Volume</h4>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-1">247</div>
                  <p className="text-sm text-gray-600">
                    Calls today (+12% vs yesterday)
                  </p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Clock className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold">Avg. Duration</h4>
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-1">4:32</div>
                  <p className="text-sm text-gray-600">
                    Minutes per call
                  </p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <TrendingUp className="h-5 w-5 text-purple-600" />
                    <h4 className="font-semibold">Conversion</h4>
                  </div>
                  <div className="text-2xl font-bold text-purple-600 mb-1">23.7%</div>
                  <p className="text-sm text-gray-600">
                    Call to claim rate
                  </p>
                </Card>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Available Providers</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-red-100 rounded-full flex items-center justify-center">
                          <Phone className="h-3 w-3 text-red-600" />
                        </div>
                        <div>
                          <div className="font-medium">Twilio</div>
                          <div className="text-xs text-gray-500">Cloud communications</div>
                        </div>
                      </div>
                      <Button size="sm" variant={hasAdvancedIntegrations ? "default" : "outline"} disabled={!hasAdvancedIntegrations}>
                        {hasAdvancedIntegrations ? 'Connect' : 'Silver+ Required'}
                      </Button>
                    </div>
                    <div className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center gap-2">
                        <div className="w-6 h-6 bg-blue-100 rounded-full flex items-center justify-center">
                          <Phone className="h-3 w-3 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">RingCentral</div>
                          <div className="text-xs text-gray-500">Unified communications</div>
                        </div>
                      </div>
                      <Button size="sm" variant={hasPremiumIntegrations ? "default" : "outline"} disabled={!hasPremiumIntegrations}>
                        {hasPremiumIntegrations ? 'Connect' : 'Gold+ Required'}
                      </Button>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Phone Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">Call Recording</span>
                      <Badge variant={hasAdvancedIntegrations ? 'default' : 'secondary'}>
                        {hasAdvancedIntegrations ? 'Available' : 'Silver+ Required'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">SMS Integration</span>
                      <Badge variant={hasAdvancedIntegrations ? 'default' : 'secondary'}>
                        {hasAdvancedIntegrations ? 'Available' : 'Silver+ Required'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">Call Analytics</span>
                      <Badge variant={hasPremiumIntegrations ? 'default' : 'secondary'}>
                        {hasPremiumIntegrations ? 'Available' : 'Gold+ Required'}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">Auto-Dialer</span>
                      <Badge variant={hasEnterpriseIntegrations ? 'default' : 'secondary'}>
                        {hasEnterpriseIntegrations ? 'Available' : 'Topaz+ Required'}
                      </Badge>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="monitoring" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Integration Monitoring</h3>
                <Button size="sm" variant="outline">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Refresh All
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">API Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Response Time</span>
                        <span className="font-medium">245ms avg</span>
                      </div>
                      <Progress value={75} className="h-2" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Success Rate</span>
                        <span className="font-medium">99.8%</span>
                      </div>
                      <Progress value={99.8} className="h-2 bg-green-500" />
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Uptime</span>
                        <span className="font-medium">99.9%</span>
                      </div>
                      <Progress value={99.9} className="h-2 bg-green-500" />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">System Health</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="text-lg font-bold text-green-600">All Good</div>
                        <div className="text-xs text-gray-600">System Status</div>
                      </div>
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">0</div>
                        <div className="text-xs text-gray-600">Active Alerts</div>
                      </div>
                    </div>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Last Health Check:</span>
                        <span className="font-medium">2 minutes ago</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Data Quality Score:</span>
                        <span className="font-medium">96.5%</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Sync Conflicts:</span>
                        <span className="font-medium">0</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upgrade Prompt for Limited Features */}
      {!hasPremiumIntegrations && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Plug className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Unlock Premium Integrations</h3>
                  <p className="text-sm text-gray-600">
                    Upgrade to Gold plan for Salesforce CRM, advanced phone systems, enterprise security, and priority support.
                  </p>
                </div>
              </div>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Gold
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 