import React from 'react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Search, Filter, X, Zap } from 'lucide-react';

interface DashboardFiltersProps {
  searchTerm: string;
  statusFilter: string;
  priorityFilter: string;
  stateFilter: string;
  onSearchChange: (value: string) => void;
  onStatusChange: (value: string) => void;
  onPriorityChange: (value: string) => void;
  onStateChange: (value: string) => void;
  onClearFilters: () => void;
  totalResults: number;
  filteredResults: number;
}

// Quick filter presets for instant productivity
const QUICK_FILTERS = [
  {
    label: 'My Today',
    filters: { status: 'assigned', priority: 'high' },
    icon: '⚡',
    description: 'High priority claims assigned to me'
  },
  {
    label: 'Need Follow-up',
    filters: { status: 'contacted' },
    icon: '📞',
    description: 'Claims that need follow-up calls'
  },
  {
    label: 'New Claims',
    filters: { status: 'new' },
    icon: '🆕',
    description: 'Unassigned new claims'
  },
  {
    label: 'Urgent',
    filters: { priority: 'urgent' },
    icon: '🚨',
    description: 'All urgent priority claims'
  },
  {
    label: 'Ready to Close',
    filters: { status: 'approved' },
    icon: '✅',
    description: 'Claims ready for completion'
  }
] as const;

export const DashboardFilters: React.FC<DashboardFiltersProps> = ({
  searchTerm,
  statusFilter,
  priorityFilter,
  stateFilter,
  onSearchChange,
  onStatusChange,
  onPriorityChange,
  onStateChange,
  onClearFilters,
  totalResults,
  filteredResults
}) => {
  const hasActiveFilters = searchTerm || statusFilter !== 'all' || priorityFilter !== 'all' || stateFilter !== 'all';

  const handleQuickFilter = (filters: Record<string, string | undefined>) => {
    // Clear existing filters first
    onClearFilters();
    
    // Apply quick filter
    setTimeout(() => {
      if (filters.status) onStatusChange(filters.status);
      if (filters.priority) onPriorityChange(filters.priority);
    }, 50);
  };

  return (
    <div className="space-y-4">
      {/* Quick Actions Bar */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div className="flex items-center gap-2 mb-3">
          <Zap className="h-4 w-4 text-blue-600" />
          <h3 className="font-medium text-blue-900">Quick Filters</h3>
          <Badge variant="secondary" className="ml-auto">
            {filteredResults} of {totalResults}
          </Badge>
        </div>
        
        <div className="grid grid-cols-2 md:grid-cols-5 gap-2">
          {QUICK_FILTERS.map((filter, index) => (
            <Button
              key={index}
              size="sm"
              variant="outline"
              onClick={() => handleQuickFilter(filter.filters)}
              className="justify-start h-auto p-3 hover:bg-blue-100 hover:border-blue-300"
              title={filter.description}
            >
              <div className="text-left">
                <div className="flex items-center gap-2">
                  <span>{filter.icon}</span>
                  <span className="font-medium text-xs">{filter.label}</span>
                </div>
              </div>
            </Button>
          ))}
        </div>
      </div>

      {/* Traditional Filters */}
      <div className="flex flex-col md:flex-row gap-4 items-end">
        {/* Search */}
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search by owner name, property ID, or state..."
              value={searchTerm}
              onChange={(e) => onSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>
        </div>

        {/* Status Filter */}
        <div className="w-full md:w-48">
          <Select value={statusFilter} onValueChange={onStatusChange}>
            <SelectTrigger>
              <SelectValue placeholder="All Statuses" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Statuses</SelectItem>
              <SelectItem value="new">New</SelectItem>
              <SelectItem value="assigned">Assigned</SelectItem>
              <SelectItem value="contacted">Contacted</SelectItem>
              <SelectItem value="in_progress">In Progress</SelectItem>
              <SelectItem value="documents_requested">Docs Requested</SelectItem>
              <SelectItem value="under_review">Under Review</SelectItem>
              <SelectItem value="approved">Approved</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="on_hold">On Hold</SelectItem>
              <SelectItem value="cancelled">Cancelled</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Priority Filter */}
        <div className="w-full md:w-40">
          <Select value={priorityFilter} onValueChange={onPriorityChange}>
            <SelectTrigger>
              <SelectValue placeholder="All Priorities" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Priorities</SelectItem>
              <SelectItem value="low">Low</SelectItem>
              <SelectItem value="medium">Medium</SelectItem>
              <SelectItem value="high">High</SelectItem>
              <SelectItem value="urgent">Urgent</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* State Filter */}
        <div className="w-full md:w-32">
          <Select value={stateFilter} onValueChange={onStateChange}>
            <SelectTrigger>
              <SelectValue placeholder="All States" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All States</SelectItem>
              <SelectItem value="CA">California</SelectItem>
              <SelectItem value="NY">New York</SelectItem>
              <SelectItem value="TX">Texas</SelectItem>
              <SelectItem value="FL">Florida</SelectItem>
              <SelectItem value="IL">Illinois</SelectItem>
              {/* Add more states as needed */}
            </SelectContent>
          </Select>
        </div>

        {/* Clear Filters */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            size="sm"
            onClick={onClearFilters}
            className="whitespace-nowrap"
          >
            <X className="h-4 w-4 mr-2" />
            Clear
          </Button>
        )}
      </div>

      {/* Active Filters Summary */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2">
          {searchTerm && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Search: "{searchTerm}"
              <button onClick={() => onSearchChange('')} className="ml-1 hover:bg-gray-300 rounded">
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {statusFilter !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Status: {statusFilter}
              <button onClick={() => onStatusChange('all')} className="ml-1 hover:bg-gray-300 rounded">
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {priorityFilter !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              Priority: {priorityFilter}
              <button onClick={() => onPriorityChange('all')} className="ml-1 hover:bg-gray-300 rounded">
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
          {stateFilter !== 'all' && (
            <Badge variant="secondary" className="flex items-center gap-1">
              State: {stateFilter}
              <button onClick={() => onStateChange('all')} className="ml-1 hover:bg-gray-300 rounded">
                <X className="h-3 w-3" />
              </button>
            </Badge>
          )}
        </div>
      )}
    </div>
  );
}; 