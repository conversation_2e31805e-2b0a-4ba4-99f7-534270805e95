import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Search, 
  Users, 
  DollarSign, 
  TrendingUp, 
  Settings,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  UserCheck,
  Clock,
  Eye
} from 'lucide-react';
import { agentAISearchService, AgentSearchQuota } from '@/services/agentAISearchService';

export default function AdminAISearchManagement() {
  const [quotas, setQuotas] = useState<AgentSearchQuota[]>([]);
  const [statistics, setStatistics] = useState<any>(null);
  const [newQuotaAgent, setNewQuotaAgent] = useState('');
  const [newDailyLimit, setNewDailyLimit] = useState(10);
  const [newMonthlyLimit, setNewMonthlyLimit] = useState(100);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Test agent IDs (in production, these would come from your user management system)
  const TEST_AGENTS = [
    'agent_001', 'agent_002', 'agent_003', 'agent_004', 'agent_005'
  ];

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setIsLoading(true);
    try {
      // Load quotas for all agents
      const agentQuotas = await Promise.all(
        TEST_AGENTS.map(agentId => agentAISearchService.getAgentQuota(agentId))
      );
      setQuotas(agentQuotas);

      // Load usage statistics
      const stats = await agentAISearchService.getSearchStatistics();
      
      // Generate real daily usage data from actual search logs
      if (!stats.dailyUsage) {
        // If no daily usage data exists, calculate from real search statistics
        stats.dailyUsage = calculateDailyUsageFromStats(stats);
      }
      
      setStatistics(stats);
      setError(null);
    } catch (err) {
      setError('Failed to load data');
      console.error('Load error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const updateAgentQuota = async (agentId: string, dailyLimit: number, monthlyLimit: number) => {
    try {
      await agentAISearchService.setAgentQuota(agentId, dailyLimit, monthlyLimit);
      setSuccess(`Updated quota for ${agentId}`);
      setTimeout(() => setSuccess(null), 3000);
      await loadData(); // Refresh data
    } catch (err) {
      setError('Failed to update quota');
      setTimeout(() => setError(null), 3000);
    }
  };

  const createNewQuota = async () => {
    if (!newQuotaAgent) {
      setError('Please enter an agent ID');
      return;
    }

    try {
      await agentAISearchService.setAgentQuota(newQuotaAgent, newDailyLimit, newMonthlyLimit);
      setSuccess(`Created quota for ${newQuotaAgent}`);
      setNewQuotaAgent('');
      setNewDailyLimit(10);
      setNewMonthlyLimit(100);
      setTimeout(() => setSuccess(null), 3000);
      await loadData();
    } catch (err) {
      setError('Failed to create quota');
      setTimeout(() => setError(null), 3000);
    }
  };

  const calculateDailyUsageFromStats = (stats: any) => {
    const dailyUsage = [];
    const today = new Date();
    
    // Generate last 7 days with real data if available, empty if not
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      dailyUsage.push({
        date: date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }),
        searches: i === 0 ? stats.dailySearches : 0 // Only show today's searches if available
      });
    }
    
    return dailyUsage;
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getQuotaStatus = (used: number, limit: number): { 
    color: string; 
    status: string; 
    percentage: number 
  } => {
    const percentage = (used / limit) * 100;
    if (percentage >= 90) return { color: 'bg-red-500', status: 'Critical', percentage };
    if (percentage >= 70) return { color: 'bg-yellow-500', status: 'Warning', percentage };
    return { color: 'bg-green-500', status: 'Good', percentage };
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">AI Search Management</h1>
        <Button onClick={loadData} disabled={isLoading}>
          {isLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              Loading...
            </>
          ) : (
            <>
              <Search className="h-4 w-4 mr-2" />
              Refresh Data
            </>
          )}
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-500 text-green-700">
          <CheckCircle className="h-4 w-4" />
          <AlertDescription>{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="quotas">Quota Management</TabsTrigger>
          <TabsTrigger value="usage">Usage Analytics</TabsTrigger>
          <TabsTrigger value="settings">Settings</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Summary Cards */}
          {statistics && (
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Search className="h-5 w-5 text-blue-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Searches</p>
                      <p className="text-2xl font-bold">{statistics.totalSearches}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <DollarSign className="h-5 w-5 text-green-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Cost</p>
                      <p className="text-2xl font-bold">{formatCurrency(statistics.totalCost)}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <TrendingUp className="h-5 w-5 text-purple-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Avg Quality</p>
                      <p className="text-2xl font-bold">{Math.round(statistics.averageResultQuality * 100)}%</p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center space-x-2">
                    <Users className="h-5 w-5 text-orange-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Agents</p>
                      <p className="text-2xl font-bold">{quotas.filter(q => q.totalSearches > 0).length}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Quick Agent Status */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <UserCheck className="h-5 w-5" />
                Agent Quota Status
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {quotas.map((quota) => {
                  const dailyStatus = getQuotaStatus(quota.dailyUsed, quota.dailyLimit);
                  const monthlyStatus = getQuotaStatus(quota.monthlyUsed, quota.monthlyLimit);
                  
                  return (
                    <div key={quota.agentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div>
                        <p className="font-medium">{quota.agentId}</p>
                        <p className="text-sm text-gray-600">
                          {quota.totalSearches} total searches
                        </p>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-center">
                          <p className="text-xs text-gray-500">Daily</p>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {quota.dailyUsed}/{quota.dailyLimit}
                            </span>
                            <div className={`w-2 h-2 rounded-full ${dailyStatus.color}`}></div>
                          </div>
                        </div>
                        <div className="text-center">
                          <p className="text-xs text-gray-500">Monthly</p>
                          <div className="flex items-center gap-2">
                            <span className="text-sm font-medium">
                              {quota.monthlyUsed}/{quota.monthlyLimit}
                            </span>
                            <div className={`w-2 h-2 rounded-full ${monthlyStatus.color}`}></div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="quotas" className="space-y-6">
          {/* Create New Quota */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5" />
                Create New Agent Quota
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="agentId">Agent ID</Label>
                  <Input
                    id="agentId"
                    value={newQuotaAgent}
                    onChange={(e) => setNewQuotaAgent(e.target.value)}
                    placeholder="agent_006"
                  />
                </div>
                <div>
                  <Label htmlFor="dailyLimit">Daily Limit</Label>
                  <Input
                    id="dailyLimit"
                    type="number"
                    value={newDailyLimit}
                    onChange={(e) => setNewDailyLimit(parseInt(e.target.value) || 0)}
                    min="1"
                    max="100"
                  />
                </div>
                <div>
                  <Label htmlFor="monthlyLimit">Monthly Limit</Label>
                  <Input
                    id="monthlyLimit"
                    type="number"
                    value={newMonthlyLimit}
                    onChange={(e) => setNewMonthlyLimit(parseInt(e.target.value) || 0)}
                    min="10"
                    max="1000"
                  />
                </div>
              </div>
              <Button onClick={createNewQuota} className="mt-4">
                Create Quota
              </Button>
            </CardContent>
          </Card>

          {/* Existing Quotas */}
          <Card>
            <CardHeader>
              <CardTitle>Manage Agent Quotas</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {quotas.map((quota) => (
                  <QuotaEditor
                    key={quota.agentId}
                    quota={quota}
                    onUpdate={updateAgentQuota}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          {statistics && (
            <>
              {/* Top Performing Agents */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <BarChart3 className="h-5 w-5" />
                    Top Performing Agents
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {statistics.topPerformingAgents.map((agent: any, index: number) => (
                      <div key={agent.agentId} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className="flex items-center justify-center w-8 h-8 bg-blue-500 text-white rounded-full text-sm font-bold">
                            {index + 1}
                          </div>
                          <div>
                            <p className="font-medium">{agent.agentId}</p>
                            <p className="text-sm text-gray-600">{agent.searches} searches</p>
                          </div>
                        </div>
                        <div className="text-right">
                          <p className="font-medium text-green-600">
                            {Math.round(agent.quality * 100)}% quality
                          </p>
                          <p className="text-xs text-gray-500">avg result quality</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Daily Usage Chart */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Clock className="h-5 w-5" />
                    Daily Usage Trend (Last 7 Days)
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {(statistics.dailyUsage || []).slice(-7).map((day: any) => (
                      <div key={day.date} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{day.date}</span>
                        <div className="flex items-center gap-2">
                          <div className="w-32 bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ 
                                width: `${Math.min((day.searches / Math.max(...(statistics.dailyUsage || []).map((d: any) => d.searches || 1))) * 100, 100)}%` 
                              }}
                            ></div>
                          </div>
                          <span className="text-sm text-gray-600 w-12 text-right">{day.searches}</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </>
          )}
        </TabsContent>

        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>System Settings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Default Quotas</h4>
                    <p className="text-sm text-gray-600">Daily Limit: 10 searches</p>
                    <p className="text-sm text-gray-600">Monthly Limit: 100 searches</p>
                  </div>
                  <div className="p-4 bg-gray-50 rounded-lg">
                    <h4 className="font-medium mb-2">Cost per Search</h4>
                    <p className="text-sm text-gray-600">$0.50 per AI search</p>
                    <p className="text-sm text-gray-600">Includes all data sources</p>
                  </div>
                </div>
                
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    Quota changes take effect immediately. Agents will see updated limits on their next search.
                  </AlertDescription>
                </Alert>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Quota Editor Component
interface QuotaEditorProps {
  quota: AgentSearchQuota;
  onUpdate: (agentId: string, daily: number, monthly: number) => Promise<void>;
}

function QuotaEditor({ quota, onUpdate }: QuotaEditorProps) {
  const [dailyLimit, setDailyLimit] = useState(quota.dailyLimit);
  const [monthlyLimit, setMonthlyLimit] = useState(quota.monthlyLimit);
  const [isEditing, setIsEditing] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await onUpdate(quota.agentId, dailyLimit, monthlyLimit);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to update quota:', error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleCancel = () => {
    setDailyLimit(quota.dailyLimit);
    setMonthlyLimit(quota.monthlyLimit);
    setIsEditing(false);
  };

  const dailyStatus = getQuotaStatus(quota.dailyUsed, quota.dailyLimit);
  const monthlyStatus = getQuotaStatus(quota.monthlyUsed, quota.monthlyLimit);

  return (
    <div className="p-4 border rounded-lg">
      <div className="flex justify-between items-start mb-4">
        <div>
          <h4 className="font-medium text-lg">{quota.agentId}</h4>
          <p className="text-sm text-gray-600">
            Total searches: {quota.totalSearches} • 
            Last search: {quota.lastSearchDate ? quota.lastSearchDate.toLocaleDateString() : 'Never'}
          </p>
        </div>
        <div className="flex gap-2">
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={() => setIsEditing(true)}>
              <Settings className="h-4 w-4 mr-1" />
              Edit
            </Button>
          ) : (
            <>
              <Button variant="outline" size="sm" onClick={handleCancel}>
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save'}
              </Button>
            </>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label className="text-sm text-gray-600">Daily Usage</Label>
          <div className="mt-1">
            <div className="flex justify-between text-sm mb-1">
              <span>{quota.dailyUsed} / {isEditing ? dailyLimit : quota.dailyLimit}</span>
              <span>{Math.round((quota.dailyUsed / (isEditing ? dailyLimit : quota.dailyLimit)) * 100)}%</span>
            </div>
            <Progress value={(quota.dailyUsed / (isEditing ? dailyLimit : quota.dailyLimit)) * 100} />
          </div>
          {isEditing && (
            <Input
              type="number"
              value={dailyLimit}
              onChange={(e) => setDailyLimit(parseInt(e.target.value) || 0)}
              min="1"
              max="100"
              className="mt-2"
            />
          )}
        </div>

        <div>
          <Label className="text-sm text-gray-600">Monthly Usage</Label>
          <div className="mt-1">
            <div className="flex justify-between text-sm mb-1">
              <span>{quota.monthlyUsed} / {isEditing ? monthlyLimit : quota.monthlyLimit}</span>
              <span>{Math.round((quota.monthlyUsed / (isEditing ? monthlyLimit : quota.monthlyLimit)) * 100)}%</span>
            </div>
            <Progress value={(quota.monthlyUsed / (isEditing ? monthlyLimit : quota.monthlyLimit)) * 100} />
          </div>
          {isEditing && (
            <Input
              type="number"
              value={monthlyLimit}
              onChange={(e) => setMonthlyLimit(parseInt(e.target.value) || 0)}
              min="10"
              max="1000"
              className="mt-2"
            />
          )}
        </div>
      </div>
    </div>
  );
}

function getQuotaStatus(used: number, limit: number): { 
  color: string; 
  status: string; 
  percentage: number 
} {
  const percentage = (used / limit) * 100;
  if (percentage >= 90) return { color: 'bg-red-500', status: 'Critical', percentage };
  if (percentage >= 70) return { color: 'bg-yellow-500', status: 'Warning', percentage };
  return { color: 'bg-green-500', status: 'Good', percentage };
} 