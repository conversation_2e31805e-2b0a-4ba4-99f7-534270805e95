import React from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { JuniorAgentDashboard } from '../JuniorAgentDashboard/JuniorAgentDashboard';
import { SeniorAgentDashboard } from '../SeniorAgentDashboard/SeniorAgentDashboard';
import { AdminDashboard } from '../AdminDashboard/AdminDashboard';
import { ContractorDashboard } from '../ContractorDashboard/ContractorDashboard';
import { ComplianceDashboard } from '../ComplianceDashboard/ComplianceDashboard';
import { FinanceDashboard } from '../FinanceDashboard/FinanceDashboard';

export const MainDashboard: React.FC = () => {
  const { user } = useAuth();

  if (!user) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading...</div>
      </div>
    );
  }

  // Route to appropriate dashboard based on user role
  switch (user.role) {
    case 'junior_agent':
      return <JuniorAgentDashboard />;
    
    case 'senior_agent':
      return <SeniorAgentDashboard />;
    
    case 'admin':
      return <AdminDashboard />;
    
    case 'contractor':
      return <ContractorDashboard />;
    
    case 'compliance':
      return <ComplianceDashboard />;
    
    case 'finance':
      return <FinanceDashboard />;
    
    default:
      // Fallback to junior agent dashboard for unknown roles
      return <JuniorAgentDashboard />;
  }
}; 