import { useState } from 'react';
import { supabase } from '@/lib/supabase';
import { Claim, ClaimFormData } from '../types/claim.types';

export interface UseClaimMutationsResult {
  isUpdating: boolean;
  isAddingNote: boolean;
  updateClaim: (claimId: string, data: ClaimFormData) => Promise<void>;
  addNote: (claimId: string, note: string) => Promise<void>;
}

export const useClaimMutations = (onSuccess?: () => void): UseClaimMutationsResult => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [isAddingNote, setIsAddingNote] = useState(false);

  const updateClaim = async (claimId: string, data: ClaimFormData) => {
    try {
      setIsUpdating(true);

      const { error: updateError } = await supabase
        .from('claims')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', claimId);

      if (updateError) throw updateError;

      // Add activity log
      const { error: activityError } = await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: 'current_user_id', // TODO: Get from auth context
          activity_type: 'status_change',
          title: 'Claim details updated',
          description: 'Claim information modified'
        });

      if (activityError) {
        console.warn('Could not log activity:', activityError);
      }

      onSuccess?.();
    } catch (error) {
      console.error('Error updating claim:', error);
      throw error;
    } finally {
      setIsUpdating(false);
    }
  };

  const addNote = async (claimId: string, note: string) => {
    if (!note.trim()) return;

    try {
      setIsAddingNote(true);

      const { error: activityError } = await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: 'current_user_id', // TODO: Get from auth context
          activity_type: 'note',
          title: 'Note added',
          description: note
        });

      if (activityError) throw activityError;

      onSuccess?.();
    } catch (error) {
      console.error('Error adding note:', error);
      throw error;
    } finally {
      setIsAddingNote(false);
    }
  };

  return {
    isUpdating,
    isAddingNote,
    updateClaim,
    addNote
  };
}; 