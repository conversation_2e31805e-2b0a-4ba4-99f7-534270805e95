import { Button } from '@/components/ui/button';
import { 
  Download, 
  Eye, 
  FileText, 
  File,
  Image as ImageIcon,
  ExternalLink
} from 'lucide-react';

interface DocumentViewerProps {
  document: {
    id: string;
    file_name: string;
    category: string;
    file_size?: number;
    file_url?: string;
    created_at: string;
  };
  onView?: () => void;
}

export default function DocumentViewer({ document: doc, onView }: DocumentViewerProps) {
  const getFileIcon = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return <ImageIcon className="h-4 w-4 text-blue-500" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <FileText className="h-4 w-4 text-red-500" />;
    }
    return <File className="h-4 w-4 text-gray-500" />;
  };

  const handleDownload = async () => {
    if (!doc.file_url) return;

    try {
      // Create a temporary link to download the file
      const link = document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.file_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download error:', error);
    }
  };

  const handleView = () => {
    onView?.();
  };

  const handleOpenInNewTab = () => {
    if (doc.file_url) {
      window.open(doc.file_url, '_blank');
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  return (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 transition-colors">
      <div className="flex items-center gap-3 flex-1 min-w-0">
        {getFileIcon(doc.file_name)}
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{doc.file_name}</p>
          <div className="flex items-center gap-2 text-xs text-gray-500">
            <span className="capitalize">{doc.category}</span>
            <span>•</span>
            <span>{formatFileSize(doc.file_size)}</span>
            <span>•</span>
            <span>{formatDate(doc.created_at)}</span>
          </div>
        </div>
      </div>
      
      <div className="flex gap-1 ml-2">
        <Button size="sm" variant="ghost" onClick={handleView} title="View Document">
          <Eye className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="ghost" onClick={handleOpenInNewTab} title="Open in New Tab">
          <ExternalLink className="h-4 w-4" />
        </Button>
        <Button size="sm" variant="ghost" onClick={handleDownload} title="Download Document">
          <Download className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
} 