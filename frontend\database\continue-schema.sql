-- AssetHunterPro - Database Schema Continuation
-- This script creates only the missing tables and components

-- Enable UUID extension (safe to run multiple times)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================
-- MISSING CORE TABLES
-- ===================================

-- Main claims table (MISSING)
CREATE TABLE claims (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    property_id VARCHAR(100),
    owner_name VARCHAR(255) NOT NULL,
    owner_first_name VARCHAR(100),
    owner_last_name VARCHAR(100), 
    owner_business_name VARCHAR(255),
    amount DECIMAL(12,2) NOT NULL,
    property_type VARCHAR(100),
    status VARCHAR(50) NOT NULL DEFAULT 'new' CHECK (status IN ('new', 'assigned', 'contacted', 'in_progress', 'documents_requested', 'under_review', 'approved', 'completed', 'on_hold', 'cancelled')),
    priority VARCHAR(20) NOT NULL DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
    assigned_agent_id UUID REFERENCES users(id),
    state VARCHAR(2) NOT NULL,
    
    -- Holder information
    holder_name VARCHAR(255),
    holder_address TEXT,
    holder_city VARCHAR(100),
    holder_state VARCHAR(2),
    holder_zip VARCHAR(20),
    
    -- Owner address information
    owner_address TEXT,
    owner_city VARCHAR(100),
    owner_state VARCHAR(2),
    owner_zip VARCHAR(20),
    
    -- Additional metadata
    report_date DATE,
    shares_reported INTEGER,
    securities_name VARCHAR(255),
    cusip VARCHAR(20),
    
    -- Processing information
    import_batch_id UUID REFERENCES import_batches(id),
    complexity_score INTEGER DEFAULT 1,
    estimated_recovery_amount DECIMAL(12,2),
    actual_recovery_amount DECIMAL(12,2),
    commission_rate DECIMAL(5,4) DEFAULT 0.25,
    commission_amount DECIMAL(12,2),
    
    -- Compliance and audit
    compliance_status VARCHAR(50) DEFAULT 'pending',
    last_contact_date DATE,
    next_followup_date DATE,
    
    -- Timestamps
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- Batch record details (MISSING)
CREATE TABLE batch_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    batch_id UUID NOT NULL REFERENCES import_batches(id) ON DELETE CASCADE,
    record_index INTEGER NOT NULL,
    raw_data JSONB NOT NULL,
    processed_data JSONB,
    validation_errors JSONB DEFAULT '[]',
    status VARCHAR(20) NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'valid', 'invalid', 'duplicate')),
    claim_id UUID REFERENCES claims(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(batch_id, record_index)
);

-- Contact methods for claimants (MISSING)
CREATE TABLE claim_contacts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    contact_type VARCHAR(20) NOT NULL CHECK (contact_type IN ('phone', 'email', 'address')),
    contact_value VARCHAR(500) NOT NULL,
    label VARCHAR(50),
    is_primary BOOLEAN DEFAULT false,
    is_valid BOOLEAN DEFAULT true,
    last_verified_at TIMESTAMPTZ,
    verification_notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activity timeline for all claim interactions (MISSING)
CREATE TABLE claim_activities (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    agent_id UUID NOT NULL REFERENCES users(id),
    activity_type VARCHAR(50) NOT NULL CHECK (activity_type IN ('call', 'email', 'sms', 'note', 'status_change', 'document_upload', 'payment', 'other')),
    outcome VARCHAR(50),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    contact_method_id UUID REFERENCES claim_contacts(id),
    scheduled_followup_at TIMESTAMPTZ,
    duration_minutes INTEGER,
    attachments JSONB DEFAULT '[]',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Document management (MISSING)
CREATE TABLE claim_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    file_name VARCHAR(500) NOT NULL,
    file_size BIGINT,
    file_type VARCHAR(100),
    category VARCHAR(100) NOT NULL CHECK (category IN ('id_documents', 'contracts', 'correspondence', 'state_forms', 'signatures', 'other')),
    description TEXT,
    file_path VARCHAR(1000),
    file_url VARCHAR(1000),
    uploaded_by UUID NOT NULL REFERENCES users(id),
    permissions VARCHAR(50) DEFAULT 'internal' CHECK (permissions IN ('internal', 'shareable')),
    is_signed BOOLEAN DEFAULT false,
    signature_status VARCHAR(50),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- State mapping templates (MISSING - but already exists in supabase.ts types)
CREATE TABLE state_mapping_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    state_code VARCHAR(2) NOT NULL,
    template_name VARCHAR(255) NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT false,
    field_mappings JSONB NOT NULL DEFAULT '{}',
    data_transformations JSONB,
    validation_rules JSONB,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(state_code, template_name)
);

-- Standard field definitions (MISSING)
CREATE TABLE standard_fields (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    field_name VARCHAR(100) UNIQUE NOT NULL,
    field_type VARCHAR(20) NOT NULL CHECK (field_type IN ('string', 'number', 'date', 'boolean')),
    is_required BOOLEAN DEFAULT false,
    description TEXT,
    validation_pattern VARCHAR(500),
    example_values TEXT[],
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Upload session tracking (MISSING)
CREATE TABLE upload_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    status VARCHAR(20) NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'cancelled')),
    files_uploaded INTEGER DEFAULT 0,
    total_file_size BIGINT DEFAULT 0,
    session_data JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    completed_at TIMESTAMPTZ
);

-- User permissions and roles (MISSING)
CREATE TABLE permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role VARCHAR(50) NOT NULL,
    resource VARCHAR(100) NOT NULL,
    actions_allowed TEXT[] NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Business rules configuration (MISSING)
CREATE TABLE business_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    rule_name VARCHAR(100) UNIQUE NOT NULL,
    description TEXT,
    rule_type VARCHAR(50) NOT NULL CHECK (rule_type IN ('validation', 'assignment', 'commission', 'workflow')),
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    is_active BOOLEAN DEFAULT true,
    priority INTEGER DEFAULT 100,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Performance metrics tracking (MISSING)
CREATE TABLE performance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES users(id),
    period_start DATE NOT NULL,
    period_end DATE NOT NULL,
    claims_assigned INTEGER DEFAULT 0,
    claims_contacted INTEGER DEFAULT 0,
    claims_completed INTEGER DEFAULT 0,
    total_recovery_amount DECIMAL(12,2) DEFAULT 0,
    avg_response_time INTERVAL,
    avg_completion_time INTERVAL,
    contact_success_rate DECIMAL(5,4),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(agent_id, period_start, period_end)
);

-- ===================================
-- INDEXES FOR PERFORMANCE
-- ===================================

-- Core claim indexes
CREATE INDEX idx_claims_assigned_agent ON claims(assigned_agent_id);
CREATE INDEX idx_claims_status ON claims(status);
CREATE INDEX idx_claims_state ON claims(state);
CREATE INDEX idx_claims_priority ON claims(priority);
CREATE INDEX idx_claims_created_at ON claims(created_at);
CREATE INDEX idx_claims_next_followup ON claims(next_followup_date);
CREATE INDEX idx_claims_owner_name ON claims(owner_name);
CREATE INDEX idx_claims_import_batch ON claims(import_batch_id);

-- Activity indexes
CREATE INDEX idx_activities_claim_id ON claim_activities(claim_id);
CREATE INDEX idx_activities_agent_id ON claim_activities(agent_id);
CREATE INDEX idx_activities_created_at ON claim_activities(created_at);
CREATE INDEX idx_activities_followup ON claim_activities(scheduled_followup_at);

-- Contact indexes
CREATE INDEX idx_contacts_claim_id ON claim_contacts(claim_id);
CREATE INDEX idx_contacts_type_value ON claim_contacts(contact_type, contact_value);

-- Batch processing indexes
CREATE INDEX idx_batch_records_batch_id ON batch_records(batch_id);
CREATE INDEX idx_batch_records_status ON batch_records(status);

-- Performance indexes
CREATE INDEX idx_performance_agent_id ON performance_metrics(agent_id);
CREATE INDEX idx_performance_period ON performance_metrics(period_start, period_end);

-- ===================================
-- INITIAL DATA
-- ===================================

-- Standard field definitions
INSERT INTO standard_fields (field_name, field_type, is_required, description, validation_pattern, example_values) VALUES
('owner_name', 'string', true, 'Full name of property owner', '^[a-zA-Z\s\.,\-'']+$', ARRAY['John Smith', 'ABC Company LLC']),
('amount', 'number', true, 'Property amount/value', '^\d+(\.\d{2})?$', ARRAY['1000.00', '25000.50']),
('state', 'string', true, 'State abbreviation', '^[A-Z]{2}$', ARRAY['CA', 'TX', 'NY']),
('property_id', 'string', false, 'Unique property identifier', '^[A-Z0-9\-]+$', ARRAY['PROP-001', 'CA-2023-12345']),
('report_date', 'date', false, 'Date property was reported', '^\d{4}-\d{2}-\d{2}$', ARRAY['2023-01-15', '2024-03-20']),
('holder_name', 'string', false, 'Name of current holder', '^[a-zA-Z\s\.,\-'']+$', ARRAY['State Treasury', 'Bank of America']),
('owner_address', 'string', false, 'Owner street address', NULL, ARRAY['123 Main St', '456 Oak Avenue Apt 2B']),
('property_type', 'string', false, 'Type of unclaimed property', NULL, ARRAY['Bank Account', 'Safe Deposit Box', 'Insurance Policy']);

-- Default permissions
INSERT INTO permissions (role, resource, actions_allowed) VALUES
('junior_agent', 'claims', ARRAY['read', 'update_status', 'add_activity', 'upload_document']),
('junior_agent', 'contacts', ARRAY['read', 'create', 'update']),
('senior_agent', 'claims', ARRAY['read', 'create', 'update', 'assign', 'approve']),
('senior_agent', 'agents', ARRAY['read', 'assign_claims']),
('senior_agent', 'reports', ARRAY['read', 'export']),
('admin', 'all', ARRAY['*']),
('compliance', 'claims', ARRAY['read', 'audit']),
('compliance', 'compliance_logs', ARRAY['read', 'create']),
('finance', 'payments', ARRAY['read', 'create', 'update']),
('finance', 'invoices', ARRAY['read', 'create', 'update']);

-- Basic business rules
INSERT INTO business_rules (rule_name, description, rule_type, conditions, actions, created_by) VALUES
('Auto_Assign_High_Value', 'Automatically assign high-value claims to senior agents', 'assignment', 
 '{"amount": {"operator": ">", "value": 50000}}', 
 '{"assign_to_role": "senior_agent", "set_priority": "high"}', NULL),
('Commission_Rate_Default', 'Set default commission rate based on claim amount', 'commission',
 '{"amount": {"operator": "<=", "value": 10000}}',
 '{"commission_rate": 0.25}', NULL),
('Commission_Rate_Large', 'Reduced commission rate for large claims', 'commission',
 '{"amount": {"operator": ">", "value": 100000}}',
 '{"commission_rate": 0.15}', NULL);

-- Add team foreign key constraint to users (now that teams table exists)
ALTER TABLE users ADD CONSTRAINT fk_users_team_id FOREIGN KEY (team_id) REFERENCES teams(id);

-- Success message
SELECT 'Database schema continuation completed successfully!' as message; 
