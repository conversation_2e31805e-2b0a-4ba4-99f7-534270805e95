import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  Eye, 
  AlertTriangle, 
  CheckCircle,
  FileText,
  Clock,
  Scale,
  Lock,
  Download,
  Upload,
  Filter,
  Search,
  Globe,
  Database,
  Mail,
  Phone,
  Users,
  Settings,
  BarChart3,
  Calendar,
  Flag,
  Archive
} from 'lucide-react';

interface ComplianceMetrics {
  totalAudits: number;
  pendingReviews: number;
  complianceScore: number;
  violationsCount: number;
  stateCompliance: number;
  privacyRequests: number;
  dataRetentionAlerts: number;
  trainingCompliance: number;
}

interface ComplianceAlert {
  id: string;
  type: 'violation' | 'audit_required' | 'data_retention' | 'privacy_request' | 'license_expiry';
  title: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  dueDate: string;
  state?: string;
  regulation: string;
  resolved: boolean;
}

interface AuditRecord {
  id: string;
  type: 'internal' | 'external' | 'regulatory' | 'privacy';
  title: string;
  auditor: string;
  status: 'scheduled' | 'in_progress' | 'completed' | 'failed';
  startDate: string;
  endDate?: string;
  findings: number;
  riskLevel: 'low' | 'medium' | 'high';
}

interface PrivacyRequest {
  id: string;
  type: 'gdpr_deletion' | 'ccpa_access' | 'data_portability' | 'opt_out';
  requestor: string;
  email: string;
  status: 'pending' | 'in_progress' | 'completed' | 'rejected';
  requestDate: string;
  dueDate: string;
  priority: 'normal' | 'urgent';
}

interface StateCompliance {
  state: string;
  complianceScore: number;
  lastReview: string;
  nextAudit: string;
  issues: number;
  status: 'compliant' | 'warning' | 'violation';
}

export const ComplianceDashboard: React.FC = () => {
  const [metrics] = useState<ComplianceMetrics>({
    totalAudits: 127,
    pendingReviews: 23,
    complianceScore: 94,
    violationsCount: 3,
    stateCompliance: 92,
    privacyRequests: 18,
    dataRetentionAlerts: 7,
    trainingCompliance: 87
  });

  const [alerts] = useState<ComplianceAlert[]>([
    {
      id: '1',
      type: 'license_expiry',
      title: 'License Expiry Warning',
      description: 'Florida recovery license expires in 30 days',
      severity: 'high',
      dueDate: '2025-01-12',
      state: 'Florida',
      regulation: 'FL Asset Recovery License',
      resolved: false
    },
    {
      id: '2',
      type: 'privacy_request',
      title: 'GDPR Data Deletion Request',
      description: 'User requested complete data deletion under GDPR Article 17',
      severity: 'medium',
      dueDate: '2024-12-19',
      regulation: 'GDPR Article 17',
      resolved: false
    },
    {
      id: '3',
      type: 'data_retention',
      title: 'Data Retention Alert',
      description: 'Claims from 2017 exceed 7-year retention policy',
      severity: 'medium',
      dueDate: '2024-12-31',
      regulation: 'Internal Data Policy',
      resolved: false
    }
  ]);

  const [auditRecords] = useState<AuditRecord[]>([
    {
      id: '1',
      type: 'regulatory',
      title: 'Texas State Compliance Audit',
      auditor: 'TX Dept of Banking',
      status: 'in_progress',
      startDate: '2024-12-01',
      findings: 2,
      riskLevel: 'low'
    },
    {
      id: '2',
      type: 'privacy',
      title: 'GDPR Compliance Review',
      auditor: 'Internal Compliance Team',
      status: 'completed',
      startDate: '2024-11-15',
      endDate: '2024-11-30',
      findings: 5,
      riskLevel: 'medium'
    },
    {
      id: '3',
      type: 'internal',
      title: 'Q4 Internal Security Audit',
      auditor: 'Security Team',
      status: 'scheduled',
      startDate: '2024-12-20',
      findings: 0,
      riskLevel: 'low'
    }
  ]);

  const [privacyRequests] = useState<PrivacyRequest[]>([
    {
      id: '1',
      type: 'gdpr_deletion',
      requestor: 'John Smith',
      email: '<EMAIL>',
      status: 'pending',
      requestDate: '2024-12-10',
      dueDate: '2024-12-19',
      priority: 'normal'
    },
    {
      id: '2',
      type: 'ccpa_access',
      requestor: 'Mary Johnson',
      email: '<EMAIL>',
      status: 'in_progress',
      requestDate: '2024-12-08',
      dueDate: '2024-12-18',
      priority: 'urgent'
    }
  ]);

  const [stateCompliance] = useState<StateCompliance[]>([
    {
      state: 'Texas',
      complianceScore: 98,
      lastReview: '2024-12-01',
      nextAudit: '2025-06-01',
      issues: 0,
      status: 'compliant'
    },
    {
      state: 'Florida',
      complianceScore: 85,
      lastReview: '2024-11-15',
      nextAudit: '2025-01-12',
      issues: 2,
      status: 'warning'
    },
    {
      state: 'California',
      complianceScore: 96,
      lastReview: '2024-11-30',
      nextAudit: '2025-05-30',
      issues: 1,
      status: 'compliant'
    }
  ]);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'in_progress': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'scheduled': return 'bg-purple-100 text-purple-800';
      case 'rejected': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getComplianceStatusColor = (status: string) => {
    switch (status) {
      case 'compliant': return 'bg-green-100 text-green-800';
      case 'warning': return 'bg-yellow-100 text-yellow-800';
      case 'violation': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'violation': return <AlertTriangle className="h-4 w-4 text-red-600" />;
      case 'audit_required': return <Eye className="h-4 w-4 text-blue-600" />;
      case 'data_retention': return <Database className="h-4 w-4 text-purple-600" />;
      case 'privacy_request': return <Lock className="h-4 w-4 text-orange-600" />;
      case 'license_expiry': return <Calendar className="h-4 w-4 text-yellow-600" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  const formatDaysUntil = (dateString: string) => {
    const now = new Date();
    const targetDate = new Date(dateString);
    const diffTime = targetDate.getTime() - now.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'Overdue';
    if (diffDays === 0) return 'Due today';
    if (diffDays === 1) return 'Due tomorrow';
    return `${diffDays} days`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Compliance Dashboard</h1>
          <p className="text-gray-600">Monitor regulatory compliance, privacy requests, and audit activities</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Search className="h-4 w-4 mr-2" />
            Search Records
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Shield className="h-4 w-4 mr-2" />
            Run Audit
          </Button>
        </div>
      </div>

      {/* Key Compliance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Compliance Score */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Compliance Score</CardTitle>
            <Shield className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{metrics.complianceScore}%</div>
            <div className="mt-2">
              <Progress value={metrics.complianceScore} className="h-2" />
            </div>
            <p className="text-sm text-green-600 mt-1">Excellent compliance</p>
          </CardContent>
        </Card>

        {/* Pending Reviews */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Reviews</CardTitle>
            <Eye className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{metrics.pendingReviews}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                <span className="text-gray-600">Require attention</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Privacy Requests */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Privacy Requests</CardTitle>
            <Lock className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{metrics.privacyRequests}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-purple-500 rounded-full mr-1"></div>
                <span className="text-gray-600">This quarter</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Violations */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Violations</CardTitle>
            <AlertTriangle className="h-5 w-5 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{metrics.violationsCount}</div>
            <div className="flex items-center mt-2">
              {metrics.violationsCount > 0 ? (
                <>
                  <AlertTriangle className="h-4 w-4 text-red-500 mr-1" />
                  <p className="text-sm text-red-600">Requires immediate action</p>
                </>
              ) : (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500 mr-1" />
                  <p className="text-sm text-green-600">No active violations</p>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="alerts" className="space-y-6">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="alerts">Compliance Alerts</TabsTrigger>
          <TabsTrigger value="audits">Audit Management</TabsTrigger>
          <TabsTrigger value="privacy">Privacy Requests</TabsTrigger>
          <TabsTrigger value="states">State Compliance</TabsTrigger>
          <TabsTrigger value="reporting">Reporting</TabsTrigger>
        </TabsList>

        {/* Compliance Alerts Tab */}
        <TabsContent value="alerts" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
                  Active Compliance Alerts
                </div>
                <Badge variant="destructive">{alerts.filter(alert => !alert.resolved).length}</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {alerts.map((alert) => (
                  <div key={alert.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-gray-100 rounded-lg">
                        {getAlertIcon(alert.type)}
                      </div>
                      <div>
                        <div className="font-medium text-sm">{alert.title}</div>
                        <div className="text-xs text-gray-500">
                          {alert.description}
                          {alert.state && ` • ${alert.state}`}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          {alert.regulation} • Due: {formatDate(alert.dueDate)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Badge className={`text-xs ${getSeverityColor(alert.severity)}`}>
                        {alert.severity}
                      </Badge>
                      <span className="text-xs text-gray-500">{formatDaysUntil(alert.dueDate)}</span>
                      <Button size="sm" variant="outline">
                        Resolve
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Management Tab */}
        <TabsContent value="audits" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Audit Schedule */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                  Audit Schedule
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {auditRecords.map((audit) => (
                    <div key={audit.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{audit.title}</div>
                        <div className="text-xs text-gray-500">
                          {audit.auditor} • {formatDate(audit.startDate)}
                          {audit.endDate && ` - ${formatDate(audit.endDate)}`}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={`text-xs ${getStatusColor(audit.status)}`}>
                          {audit.status.replace('_', ' ')}
                        </Badge>
                        {audit.findings > 0 && (
                          <Badge variant="outline" className="text-xs">
                            {audit.findings} findings
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                <Button variant="outline" className="w-full mt-4">
                  <Eye className="h-4 w-4 mr-2" />
                  View All Audits
                </Button>
              </CardContent>
            </Card>

            {/* Audit Statistics */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-green-600" />
                  Audit Statistics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="text-center p-3 bg-blue-50 rounded-lg">
                    <div className="text-2xl font-bold text-blue-600">{metrics.totalAudits}</div>
                    <div className="text-sm text-blue-700">Total Audits Conducted</div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="text-lg font-bold text-green-600">89%</div>
                      <div className="text-xs text-green-700">Pass Rate</div>
                    </div>
                    <div className="text-center p-3 bg-yellow-50 rounded-lg">
                      <div className="text-lg font-bold text-yellow-600">3</div>
                      <div className="text-xs text-yellow-700">In Progress</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Privacy Requests Tab */}
        <TabsContent value="privacy" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Lock className="h-5 w-5 mr-2 text-purple-600" />
                  Privacy Requests
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button size="sm">
                    <Upload className="h-4 w-4 mr-2" />
                    New Request
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {privacyRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-3">
                      <div className="p-2 bg-purple-100 rounded-lg">
                        <Lock className="h-4 w-4 text-purple-600" />
                      </div>
                      <div>
                        <div className="font-medium text-sm">
                          {request.type.replace('_', ' ').toUpperCase()} Request
                        </div>
                        <div className="text-xs text-gray-500">
                          {request.requestor} • {request.email}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Requested: {formatDate(request.requestDate)} • Due: {formatDate(request.dueDate)}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      {request.priority === 'urgent' && (
                        <Badge variant="destructive" className="text-xs">
                          URGENT
                        </Badge>
                      )}
                      <Badge className={`text-xs ${getStatusColor(request.status)}`}>
                        {request.status.replace('_', ' ')}
                      </Badge>
                      <span className="text-xs text-gray-500">{formatDaysUntil(request.dueDate)}</span>
                      <Button size="sm" variant="outline">
                        Process
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* State Compliance Tab */}
        <TabsContent value="states" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <Flag className="h-5 w-5 mr-2 text-blue-600" />
                  State Compliance Overview
                </div>
                <Badge variant="outline">{stateCompliance.length} States</Badge>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stateCompliance.map((state) => (
                  <div key={state.state} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold">
                        {state.state.substring(0, 2)}
                      </div>
                      <div>
                        <div className="font-medium">{state.state}</div>
                        <div className="text-sm text-gray-500">
                          Last review: {formatDate(state.lastReview)}
                        </div>
                        <div className="text-sm text-gray-500">
                          Next audit: {formatDate(state.nextAudit)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{state.complianceScore}%</div>
                        <div className="text-xs text-gray-500">Compliance</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-orange-600">{state.issues}</div>
                        <div className="text-xs text-gray-500">Issues</div>
                      </div>
                      <Badge className={`text-xs ${getComplianceStatusColor(state.status)}`}>
                        {state.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Reporting Tab */}
        <TabsContent value="reporting" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Compliance Reports */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-green-600" />
                  Compliance Reports
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button variant="outline" className="w-full justify-start">
                  <Download className="h-4 w-4 mr-2" />
                  Annual Compliance Report
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <BarChart3 className="h-4 w-4 mr-2" />
                  State Compliance Summary
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Lock className="h-4 w-4 mr-2" />
                  Privacy Impact Assessment
                </Button>
                <Button variant="outline" className="w-full justify-start">
                  <Eye className="h-4 w-4 mr-2" />
                  Audit Trail Report
                </Button>
              </CardContent>
            </Card>

            {/* Regulatory Filings */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Archive className="h-5 w-5 mr-2 text-purple-600" />
                  Regulatory Filings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-medium text-green-900">Q4 2024 Filing</div>
                    <div className="text-sm text-green-700">Submitted on time</div>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <div className="font-medium text-yellow-900">Q1 2025 Filing</div>
                    <div className="text-sm text-yellow-700">Due January 31, 2025</div>
                  </div>
                  <Button className="w-full">
                    <Upload className="h-4 w-4 mr-2" />
                    Prepare New Filing
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 