export interface Claim {
  id: string;
  property_id?: string;
  owner_name: string;
  owner_first_name?: string;
  owner_last_name?: string;
  owner_business_name?: string;
  owner_email?: string;
  amount: number;
  property_type?: string;
  status: string;
  priority: string;
  assigned_agent_id?: string;
  state: string;
  holder_name?: string;
  holder_address?: string;
  holder_city?: string;
  holder_state?: string;
  holder_zip?: string;
  owner_address?: string;
  owner_city?: string;
  owner_state?: string;
  owner_zip?: string;
  report_date?: string;
  shares_reported?: number;
  securities_name?: string;
  cusip?: string;
  complexity_score: number;
  estimated_recovery_amount?: number;
  actual_recovery_amount?: number;
  commission_rate: number;
  commission_amount?: number;
  compliance_status: string;
  last_contact_date?: string;
  next_followup_date?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
}

export interface ClaimActivity {
  id: string;
  claim_id: string;
  activity_type: string;
  title: string;
  description?: string;
  agent_id: string;
  outcome?: string;
  created_at: string;
  users?: {
    first_name: string;
    last_name: string;
    email: string;
  };
}

export interface ClaimDocument {
  id: string;
  claim_id: string;
  file_name: string;
  category: string;
  file_size?: number;
  uploaded_by: string;
  created_at: string;
  file_url?: string;
}

export type ClaimStatus = 
  | 'new' 
  | 'assigned' 
  | 'contacted' 
  | 'in_progress' 
  | 'documents_requested' 
  | 'under_review' 
  | 'approved' 
  | 'completed' 
  | 'on_hold' 
  | 'cancelled';

export type ClaimPriority = 'low' | 'medium' | 'high' | 'urgent';

export interface ClaimFormData extends Partial<Claim> {
  // Contact fields
  primary_phone?: string;
  primary_email?: string;
  
  // Additional fields
  description?: string;
}

export interface ClaimDetailViewProps {
  claimId: string;
  onBack: () => void;
} 