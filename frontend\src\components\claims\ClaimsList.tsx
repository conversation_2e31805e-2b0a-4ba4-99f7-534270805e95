import React, { useState, useEffect, useMemo } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { usePermissions } from '@/contexts/AuthContext'
import { 
  Search,
  Eye,
  Phone,
  Mail,
  Upload,
  Download,
  FileText,
  ArrowUpDown,
  Edit,
  Filter,
  MoreHorizontal,
  DollarSign,
  User,
  MapPin,
  Calendar
} from 'lucide-react'
import { SupabaseService, UIClaimData } from '@/services/supabaseService'

// Types for our claims data
interface Claim {
  id: string
  claimNumber: string
  state: string
  amountReported: number
  amountNet: number
  status: string
  claimantName: string
  assignedAgent: string
  lastActivity: string
  createdAt: string
  priority: string
}

const supabaseService = new SupabaseService()

const getStatusVariant = (status: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (status) {
    case 'new': return 'default';
    case 'contacted': return 'secondary';
    case 'contract_sent': return 'outline';
    case 'signed': return 'secondary';
    case 'filed': return 'secondary';
    case 'paid': return 'secondary';
    case 'closed': return 'outline';
    default: return 'default';
  }
};

const getPriorityVariant = (priority: string): "default" | "secondary" | "destructive" | "outline" => {
  switch (priority) {
    case 'high': return 'destructive';
    case 'medium': return 'outline';
    case 'low': return 'secondary';
    default: return 'default';
  }
};

export default function ClaimsList() {
  const [claims, setClaims] = useState<Claim[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('all')
  const [filterState, setFilterState] = useState('all')
  const [sortBy, setSortBy] = useState<string>('lastActivity')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const { user, hasPermission } = usePermissions()

  useEffect(() => {
    loadClaims()
  }, [filterStatus, filterState, searchTerm])

  const loadClaims = async () => {
    setIsLoading(true)
    setError(null)
    
    try {
      const filters = {
        status: filterStatus !== 'all' ? filterStatus : undefined,
        state: filterState !== 'all' ? filterState : undefined,
        searchTerm: searchTerm || undefined
      }

      const claimsData = await supabaseService.getClaims(filters)
      
      // Transform UIClaimData to Claim format
      const transformedClaims: Claim[] = claimsData.map(claim => ({
        id: claim.id,
        claimNumber: claim.claimNumber,
        state: claim.state,
        amountReported: claim.amountReported,
        amountNet: claim.amountNet,
        status: claim.status,
        claimantName: claim.claimantName,
        assignedAgent: claim.assignedAgent,
        lastActivity: claim.lastActivity,
        createdAt: claim.createdAt,
        priority: claim.priority
      }))
      
      setClaims(transformedClaims)
    } catch (err) {
      console.error('❌ Failed to load claims:', err)
      setError('Failed to load claims. Please check your database connection.')
    } finally {
      setIsLoading(false)
    }
  }

  // Filter and sort claims based on user permissions
  const filteredClaims = useMemo(() => {
    let filtered = claims;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(claim => 
        claim.claimantName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        claim.claimNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        claim.state.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply status filter
    if (filterStatus !== 'all') {
      filtered = filtered.filter(claim => claim.status === filterStatus);
    }

    // Apply state filter
    if (filterState !== 'all') {
      filtered = filtered.filter(claim => claim.state === filterState);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      let aValue = a[sortBy as keyof Claim];
      let bValue = b[sortBy as keyof Claim];
      
      if (typeof aValue === 'string') aValue = aValue.toLowerCase();
      if (typeof bValue === 'string') bValue = bValue.toLowerCase();
      
      if (sortOrder === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });

    return filtered;
  }, [claims, searchTerm, filterStatus, filterState, sortBy, sortOrder]);

  const handleSort = (field: string) => {
    if (sortBy === field) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(field)
      setSortOrder('desc')
    }
  }

  const handleStatusChange = (claimId: string, newStatus: string) => {
    setClaims(prevClaims => 
      prevClaims.map(claim => 
        claim.id === claimId 
          ? { ...claim, status: newStatus as Claim['status'], lastActivity: new Date().toISOString().split('T')[0] }
          : claim
      )
    )
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">
            {hasPermission('claims:view_all') ? 'All Claims' : 'My Claims'}
          </h2>
          <p className="text-gray-600">
            {hasPermission('claims:view_all') 
              ? 'Manage and track all claims in the system'
              : 'Manage and track your assigned claims'
            }
          </p>
        </div>
        <div className="flex items-center space-x-2">
          {/* Role-based action buttons */}
          {hasPermission('batch:import') && (
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Batch
            </Button>
          )}
          {hasPermission('claims:export') && (
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          )}
          {hasPermission('claims:update_own') && (
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              New Claim
            </Button>
          )}
        </div>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Filters & Search</CardTitle>
          <CardDescription>
            Filter and search through {hasPermission('claims:view_all') ? 'all' : 'your'} claims to find what you need
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            {/* Search */}
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-gray-600" />
              <Input
                placeholder="Search claims, claimants, or agents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Status Filter */}
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Statuses</SelectItem>
                <SelectItem value="new">New</SelectItem>
                <SelectItem value="contacted">Contacted</SelectItem>
                <SelectItem value="contract_sent">Contract Sent</SelectItem>
                <SelectItem value="signed">Signed</SelectItem>
                <SelectItem value="filed">Filed</SelectItem>
                <SelectItem value="paid">Paid</SelectItem>
                <SelectItem value="closed">Closed</SelectItem>
              </SelectContent>
            </Select>

            {/* State Filter */}
            <Select value={filterState} onValueChange={setFilterState}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by state" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All States</SelectItem>
                <SelectItem value="CA">California</SelectItem>
                <SelectItem value="TX">Texas</SelectItem>
                <SelectItem value="FL">Florida</SelectItem>
                <SelectItem value="NY">New York</SelectItem>
                <SelectItem value="IL">Illinois</SelectItem>
              </SelectContent>
            </Select>

            {/* Sort */}
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger>
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="lastActivity">Last Activity</SelectItem>
                <SelectItem value="amountReported">Amount</SelectItem>
                <SelectItem value="createdAt">Date Created</SelectItem>
                <SelectItem value="claimantName">Claimant Name</SelectItem>
                <SelectItem value="status">Status</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Claims Table */}
      <Card>
        <CardHeader>
          <CardTitle>Claims ({filteredClaims.length})</CardTitle>
          <CardDescription>
            {hasPermission('claims:view_all') 
              ? 'All claims in the system and their current status'
              : 'Your assigned claims and their current status'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>
                  <Button 
                    variant="ghost" 
                    onClick={() => handleSort('claimNumber')}
                    className="h-auto p-0 font-medium"
                  >
                    Claim #
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Claimant</TableHead>
                <TableHead>
                  <Button 
                    variant="ghost" 
                    onClick={() => handleSort('state')}
                    className="h-auto p-0 font-medium"
                  >
                    State
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>
                  <Button 
                    variant="ghost" 
                    onClick={() => handleSort('amountReported')}
                    className="h-auto p-0 font-medium"
                  >
                    Amount
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Status</TableHead>
                <TableHead>
                  <Button 
                    variant="ghost" 
                    onClick={() => handleSort('lastActivity')}
                    className="h-auto p-0 font-medium"
                  >
                    Last Activity
                    <ArrowUpDown className="ml-2 h-4 w-4" />
                  </Button>
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredClaims.map((claim) => (
                <TableRow key={claim.id} className="hover:bg-gray-50">
                  <TableCell className="font-medium">
                    <div className="flex items-center space-x-2">
                      <span>{claim.claimNumber}</span>
                      <Badge variant={getPriorityVariant(claim.priority)} className="text-xs">
                        {claim.priority}
                      </Badge>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{claim.claimantName}</div>
                      <div className="text-sm text-gray-600">
                        Agent: {claim.assignedAgent}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline">{claim.state}</Badge>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{formatCurrency(claim.amountReported)}</div>
                      <div className="text-sm text-gray-600">
                        Net: {formatCurrency(claim.amountNet)}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Select 
                      value={claim.status} 
                      onValueChange={(value) => handleStatusChange(claim.id, value)}
                    >
                      <SelectTrigger className="w-auto">
                        <Badge variant={getStatusVariant(claim.status)}>
                          {claim.status.replace('_', ' ')}
                        </Badge>
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="new">New</SelectItem>
                        <SelectItem value="contacted">Contacted</SelectItem>
                        <SelectItem value="contract_sent">Contract Sent</SelectItem>
                        <SelectItem value="signed">Signed</SelectItem>
                        <SelectItem value="filed">Filed</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="closed">Closed</SelectItem>
                      </SelectContent>
                    </Select>
                  </TableCell>
                  <TableCell>
                    <div className="text-sm">
                      {formatDate(claim.lastActivity)}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="flex space-x-2">
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Phone className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Mail className="h-4 w-4" />
                      </Button>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* No claims found message */}
      <div className="text-center py-8">
        <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">No claims found</h3>
        <p className="text-gray-600">
          {searchTerm || filterStatus !== 'all' || filterState !== 'all' 
            ? 'Try adjusting your filters to see more results.'
            : 'Get started by creating your first claim.'
          }
        </p>
      </div>
    </div>
  )
} 