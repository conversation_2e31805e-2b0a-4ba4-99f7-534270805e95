import React, { useState, useEffect } from 'react';
import { 
  TrendingUp, TrendingDown, Users, DollarSign, Target, Clock,
  Award, AlertTriangle, CheckCircle, BarChart3, PieChart, Activity,
  Calendar, MapPin, Briefcase, Star, ArrowUpRight, ArrowDownRight
} from 'lucide-react';
import { AdministratorState } from '../types';

interface ExecutiveDashboardProps {
  adminState: AdministratorState;
}

export const ExecutiveDashboard: React.FC<ExecutiveDashboardProps> = ({ adminState }) => {
  const [selectedPeriod, setSelectedPeriod] = useState<'7d' | '30d' | '90d' | '1y'>('30d');
  const [kpiData, setKpiData] = useState<KPIData | null>(null);
  const [performanceData, setPerformanceData] = useState<PerformanceData | null>(null);

  useEffect(() => {
    // Mock comprehensive KPI data
    const mockKPIData: KPIData = {
      revenue: {
        current: 2847500,
        previous: 2615200,
        target: 3000000,
        trend: 'up',
        changePercent: 8.9,
        breakdown: {
          newBusiness: 1523400,
          renewals: 892100,
          upsells: 432000
        }
      },
      leads: {
        total: 15847,
        qualified: 8932,
        converted: 1243,
        conversionRate: 13.9,
        trend: 'up',
        sourceBreakdown: {
          csv_imports: 9234,
          api_feeds: 4123,
          manual_entry: 2490
        }
      },
      teams: {
        totalTeams: 12,
        totalAgents: 47,
        productivity: 87.3,
        satisfaction: 4.6,
        topPerformers: [
          { name: 'Southeast Region', score: 94.2, change: 2.1 },
          { name: 'Midwest Division', score: 91.8, change: 1.8 },
          { name: 'West Coast Team', score: 89.5, change: -0.3 }
        ]
      },
      operations: {
        systemUptime: 99.98,
        responseTime: 142,
        errorRate: 0.02,
        dataQuality: 94.2,
        processingSpeed: 1847, // records per hour
        storageEfficiency: 78.3
      },
      financial: {
        costPerLead: 23.50,
        revenuePerAgent: 60563,
        marginPercent: 68.7,
        operatingCosts: 1247800,
        roi: 228.4,
        projectedGrowth: 24.7
      }
    };

    const mockPerformanceData: PerformanceData = {
      revenueChart: generateMockChartData(30, 80000, 120000),
      leadChart: generateMockChartData(30, 400, 600),
      conversionChart: generateMockChartData(30, 10, 18),
      teamPerformance: [
        { team: 'Southeast', current: 94.2, target: 90, trend: 'up' },
        { team: 'Midwest', current: 91.8, target: 90, trend: 'up' },
        { team: 'West Coast', current: 89.5, target: 90, trend: 'stable' },
        { team: 'Northeast', current: 87.3, target: 90, trend: 'down' }
      ],
      geographicData: [
        { state: 'GA', value: 523400, leads: 2234, color: '#059669' },
        { state: 'TX', value: 487200, leads: 1987, color: '#0d9488' },
        { state: 'FL', value: 398500, leads: 1643, color: '#0891b2' },
        { state: 'CA', value: 387600, leads: 1521, color: '#0284c7' },
        { state: 'NY', value: 343200, leads: 1398, color: '#2563eb' }
      ]
    };

    setKpiData(mockKPIData);
    setPerformanceData(mockPerformanceData);
  }, [selectedPeriod]);

  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const formatPercent = (value: number) => `${value.toFixed(1)}%`;

  const formatNumber = (value: number) => value.toLocaleString();

  if (!kpiData || !performanceData) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header with Period Selector */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Executive Dashboard</h2>
          <p className="text-gray-600">Strategic overview and key performance indicators</p>
        </div>
        <div className="flex space-x-2">
          {(['7d', '30d', '90d', '1y'] as const).map(period => (
            <button
              key={period}
              onClick={() => setSelectedPeriod(period)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                selectedPeriod === period
                  ? 'bg-blue-600 text-white'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border border-gray-300'
              }`}
            >
              {period === '7d' ? 'Last 7 Days' :
               period === '30d' ? 'Last 30 Days' :
               period === '90d' ? 'Last 90 Days' : 'Last Year'}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Revenue Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <DollarSign className="w-8 h-8 text-green-600" />
              <h3 className="text-sm font-medium text-gray-900">Total Revenue</h3>
            </div>
            <div className={`flex items-center space-x-1 ${
              kpiData.revenue.trend === 'up' ? 'text-green-600' : 'text-red-600'
            }`}>
              {kpiData.revenue.trend === 'up' ? 
                <ArrowUpRight className="w-4 h-4" /> : 
                <ArrowDownRight className="w-4 h-4" />
              }
              <span className="text-sm font-medium">
                {formatPercent(kpiData.revenue.changePercent)}
              </span>
            </div>
          </div>
          <div className="mt-4">
            <p className="text-3xl font-bold text-gray-900">
              {formatCurrency(kpiData.revenue.current)}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              Target: {formatCurrency(kpiData.revenue.target)}
            </p>
            <div className="mt-3 bg-gray-200 rounded-full h-2">
              <div 
                className="bg-green-600 h-2 rounded-full"
                style={{ 
                  width: `${Math.min((kpiData.revenue.current / kpiData.revenue.target) * 100, 100)}%` 
                }}
              ></div>
            </div>
          </div>
        </div>

        {/* Active Leads Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="w-8 h-8 text-blue-600" />
              <h3 className="text-sm font-medium text-gray-900">Active Leads</h3>
            </div>
            <div className="text-blue-600">
              <ArrowUpRight className="w-4 h-4" />
            </div>
          </div>
          <div className="mt-4">
            <p className="text-3xl font-bold text-gray-900">
              {formatNumber(kpiData.leads.total)}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {formatNumber(kpiData.leads.qualified)} qualified
            </p>
            <div className="mt-3 flex items-center space-x-4">
              <div className="flex-1">
                <div className="flex justify-between text-xs text-gray-600 mb-1">
                  <span>Conversion Rate</span>
                  <span>{formatPercent(kpiData.leads.conversionRate)}</span>
                </div>
                <div className="bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-600 h-2 rounded-full"
                    style={{ width: `${kpiData.leads.conversionRate * 5}%` }}
                  ></div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Team Performance Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Users className="w-8 h-8 text-purple-600" />
              <h3 className="text-sm font-medium text-gray-900">Team Performance</h3>
            </div>
            <div className="text-purple-600">
              <TrendingUp className="w-4 h-4" />
            </div>
          </div>
          <div className="mt-4">
            <p className="text-3xl font-bold text-gray-900">
              {formatPercent(kpiData.teams.productivity)}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              {kpiData.teams.totalAgents} agents across {kpiData.teams.totalTeams} teams
            </p>
            <div className="mt-3">
              <div className="flex items-center space-x-2">
                <Star className="w-4 h-4 text-yellow-500" />
                <span className="text-sm font-medium">
                  Satisfaction: {kpiData.teams.satisfaction}/5.0
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* System Health Card */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Activity className="w-8 h-8 text-emerald-600" />
              <h3 className="text-sm font-medium text-gray-900">System Health</h3>
            </div>
            <div className="text-emerald-600">
              <CheckCircle className="w-4 h-4" />
            </div>
          </div>
          <div className="mt-4">
            <p className="text-3xl font-bold text-gray-900">
              {formatPercent(kpiData.operations.systemUptime)}
            </p>
            <p className="text-sm text-gray-600 mt-1">
              Uptime • {kpiData.operations.responseTime}ms avg response
            </p>
            <div className="mt-3 space-y-2">
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Data Quality</span>
                <span className="font-medium">{formatPercent(kpiData.operations.dataQuality)}</span>
              </div>
              <div className="flex justify-between text-xs">
                <span className="text-gray-600">Error Rate</span>
                <span className="font-medium">{formatPercent(kpiData.operations.errorRate)}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Revenue and Performance Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Revenue Trend Chart */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Revenue Trend</h3>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="text-sm text-gray-600">Revenue</span>
              </div>
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                <span className="text-sm text-gray-600">Target</span>
              </div>
            </div>
          </div>
          <div className="h-64">
            <RevenueChart data={performanceData.revenueChart} />
          </div>
        </div>

        {/* Team Performance Comparison */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Team Performance</h3>
            <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
              View Details →
            </button>
          </div>
          <div className="space-y-4">
            {performanceData.teamPerformance.map((team, index) => (
              <div key={team.team} className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${
                    index === 0 ? 'bg-green-500' :
                    index === 1 ? 'bg-blue-500' :
                    index === 2 ? 'bg-yellow-500' : 'bg-gray-400'
                  }`}></div>
                  <span className="font-medium text-gray-900">{team.team}</span>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      {formatPercent(team.current)}
                    </p>
                    <p className="text-xs text-gray-500">
                      Target: {formatPercent(team.target)}
                    </p>
                  </div>
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center ${
                    team.trend === 'up' ? 'bg-green-100 text-green-600' :
                    team.trend === 'down' ? 'bg-red-100 text-red-600' :
                    'bg-gray-100 text-gray-600'
                  }`}>
                    {team.trend === 'up' ? <ArrowUpRight className="w-3 h-3" /> :
                     team.trend === 'down' ? <ArrowDownRight className="w-3 h-3" /> :
                     <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
                    }
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Geographic Performance and Financial Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Geographic Performance */}
        <div className="lg:col-span-2 bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <div className="flex items-center justify-between mb-6">
            <h3 className="text-lg font-semibold text-gray-900">Geographic Performance</h3>
            <div className="flex items-center space-x-2">
              <MapPin className="w-4 h-4 text-gray-400" />
              <span className="text-sm text-gray-600">Top 5 States</span>
            </div>
          </div>
          <div className="space-y-4">
            {performanceData.geographicData.map((item, index) => (
              <div key={item.state} className="flex items-center justify-between">
                <div className="flex items-center space-x-4">
                  <div className="flex items-center justify-center w-8 h-8 bg-gray-100 rounded-lg">
                    <span className="text-sm font-medium text-gray-700">
                      {index + 1}
                    </span>
                  </div>
                  <div>
                    <p className="font-medium text-gray-900">{item.state}</p>
                    <p className="text-sm text-gray-600">{formatNumber(item.leads)} leads</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-semibold text-gray-900">{formatCurrency(item.value)}</p>
                  <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className="h-2 rounded-full"
                      style={{ 
                        width: `${(item.value / performanceData.geographicData[0].value) * 100}%`,
                        backgroundColor: item.color 
                      }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Financial Overview */}
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-6">Financial Overview</h3>
          <div className="space-y-6">
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Cost per Lead</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(kpiData.financial.costPerLead)}
                </span>
              </div>
            </div>
            
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Revenue per Agent</span>
                <span className="font-semibold text-gray-900">
                  {formatCurrency(kpiData.financial.revenuePerAgent)}
                </span>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">Profit Margin</span>
                <span className="font-semibold text-green-600">
                  {formatPercent(kpiData.financial.marginPercent)}
                </span>
              </div>
            </div>

            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm text-gray-600">ROI</span>
                <span className="font-semibold text-green-600">
                  {formatPercent(kpiData.financial.roi)}
                </span>
              </div>
            </div>

            <div className="pt-4 border-t border-gray-200">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-900">Projected Growth</span>
                <div className="flex items-center space-x-1 text-green-600">
                  <TrendingUp className="w-4 h-4" />
                  <span className="font-semibold">
                    {formatPercent(kpiData.financial.projectedGrowth)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Mock chart component (would be replaced with actual chart library)
const RevenueChart: React.FC<{ data: ChartDataPoint[] }> = ({ data }) => (
  <div className="w-full h-full flex items-end justify-between space-x-1">
    {data.map((point, index) => (
      <div key={index} className="flex-1 flex flex-col items-center">
        <div 
          className="w-full bg-green-500 rounded-t"
          style={{ height: `${(point.value / Math.max(...data.map(d => d.value))) * 100}%` }}
        ></div>
      </div>
    ))}
  </div>
);

// Helper function to generate mock chart data
function generateMockChartData(days: number, min: number, max: number): ChartDataPoint[] {
  const data: ChartDataPoint[] = [];
  for (let i = 0; i < days; i++) {
    data.push({
      date: new Date(Date.now() - (days - i) * 24 * 60 * 60 * 1000),
      value: Math.floor(Math.random() * (max - min) + min)
    });
  }
  return data;
}

// Type definitions
interface KPIData {
  revenue: {
    current: number;
    previous: number;
    target: number;
    trend: 'up' | 'down';
    changePercent: number;
    breakdown: {
      newBusiness: number;
      renewals: number;
      upsells: number;
    };
  };
  leads: {
    total: number;
    qualified: number;
    converted: number;
    conversionRate: number;
    trend: 'up' | 'down';
    sourceBreakdown: {
      csv_imports: number;
      api_feeds: number;
      manual_entry: number;
    };
  };
  teams: {
    totalTeams: number;
    totalAgents: number;
    productivity: number;
    satisfaction: number;
    topPerformers: Array<{
      name: string;
      score: number;
      change: number;
    }>;
  };
  operations: {
    systemUptime: number;
    responseTime: number;
    errorRate: number;
    dataQuality: number;
    processingSpeed: number;
    storageEfficiency: number;
  };
  financial: {
    costPerLead: number;
    revenuePerAgent: number;
    marginPercent: number;
    operatingCosts: number;
    roi: number;
    projectedGrowth: number;
  };
}

interface PerformanceData {
  revenueChart: ChartDataPoint[];
  leadChart: ChartDataPoint[];
  conversionChart: ChartDataPoint[];
  teamPerformance: Array<{
    team: string;
    current: number;
    target: number;
    trend: 'up' | 'down' | 'stable';
  }>;
  geographicData: Array<{
    state: string;
    value: number;
    leads: number;
    color: string;
  }>;
}

interface ChartDataPoint {
  date: Date;
  value: number;
}