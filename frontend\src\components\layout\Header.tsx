import { useAuth } from '@/contexts/AuthContext'
import { useTheme } from '@/contexts/ThemeContext'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  Menu, 
  Bell, 
  Search, 
  Command,
  Moon,
  Sun,
  User
} from 'lucide-react'

interface HeaderProps {
  onSidebarToggle: () => void
  onCommandPaletteOpen: () => void
}

export function Header({ onSidebarToggle, onCommandPaletteOpen }: HeaderProps) {
  const { user } = useAuth()
  const { actualTheme, toggleTheme } = useTheme()

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3">
      <div className="flex items-center justify-between">
        {/* Left side */}
        <div className="flex items-center space-x-4">
          {/* Sidebar toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onSidebarToggle}
            className="lg:hidden"
          >
            <Menu className="h-5 w-5" />
          </Button>

          {/* Search trigger */}
          <Button
            variant="outline"
            size="sm"
            onClick={onCommandPaletteOpen}
            className="hidden sm:flex items-center space-x-2 text-gray-500 dark:text-gray-400 min-w-[200px] justify-start"
          >
            <Search className="h-4 w-4" />
            <span>Search...</span>
            <div className="ml-auto flex items-center space-x-1">
              <kbd className="px-1.5 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 rounded">
                <Command className="h-3 w-3" />
              </kbd>
              <kbd className="px-1.5 py-0.5 text-xs bg-gray-100 dark:bg-gray-700 rounded">K</kbd>
            </div>
          </Button>

          {/* Mobile search */}
          <Button
            variant="ghost"
            size="sm"
            onClick={onCommandPaletteOpen}
            className="sm:hidden"
          >
            <Search className="h-5 w-5" />
          </Button>
        </div>

        {/* Right side */}
        <div className="flex items-center space-x-3">
          {/* Theme toggle */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleTheme}
            className="hidden sm:flex"
          >
            {actualTheme === 'dark' ? (
              <Sun className="h-5 w-5" />
            ) : (
              <Moon className="h-5 w-5" />
            )}
          </Button>

          {/* Notifications */}
          <Button variant="ghost" size="sm" className="relative">
            <Bell className="h-5 w-5" />
            <Badge 
              variant="destructive" 
              className="absolute -top-1 -right-1 h-5 w-5 p-0 flex items-center justify-center text-xs"
            >
              3
            </Badge>
          </Button>

          {/* User menu */}
          <div className="flex items-center space-x-2">
            <div className="hidden sm:block text-right">
              <p className="text-sm font-medium text-gray-900 dark:text-gray-100">
                {user?.email?.split('@')[0]}
              </p>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {user?.role}
              </p>
            </div>
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
              {user?.email?.charAt(0).toUpperCase() || <User className="h-4 w-4" />}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}
