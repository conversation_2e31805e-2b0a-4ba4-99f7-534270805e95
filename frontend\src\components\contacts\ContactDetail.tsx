import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { 
  User, Phone, Mail, MapPin, Calendar, DollarSign, FileText, 
  Upload, Plus, Edit, Save, X, Users, Clock, AlertCircle,
  CheckCircle, PhoneCall, MessageSquare, Paperclip,
  Target, Flag, UserPlus, Building
} from 'lucide-react'

// Simple tabs component
interface TabsProps {
  defaultValue: string
  children: React.ReactNode
  className?: string
}

interface TabsListProps {
  children: React.ReactNode
  className?: string
}

interface TabsTriggerProps {
  value: string
  children: React.ReactNode
  className?: string
}

interface TabsContentProps {
  value: string
  children: React.ReactNode
  className?: string
}

function Tabs({ defaultValue, children, className }: TabsProps) {
  const [activeTab, setActiveTab] = useState(defaultValue)
  
  return (
    <div className={className}>
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child)) {
          return React.cloneElement(child, { activeTab, setActiveTab } as any)
        }
        return child
      })}
    </div>
  )
}

function TabsList({ children, className }: TabsListProps & { activeTab?: string; setActiveTab?: (value: string) => void }) {
  return (
    <div className={`flex border-b ${className || ''}`}>
      {children}
    </div>
  )
}

function TabsTrigger({ value, children, className, ...props }: TabsTriggerProps & { activeTab?: string; setActiveTab?: (value: string) => void }) {
  const { activeTab, setActiveTab } = props as any
  const isActive = activeTab === value
  
  return (
    <button
      className={`px-4 py-2 text-sm font-medium border-b-2 transition-colors ${
        isActive 
          ? 'border-blue-500 text-blue-600' 
          : 'border-transparent text-gray-500 hover:text-gray-700'
      } ${className || ''}`}
      onClick={() => setActiveTab?.(value)}
    >
      {children}
    </button>
  )
}

function TabsContent({ value, children, className, ...props }: TabsContentProps & { activeTab?: string }) {
  const { activeTab } = props as any
  
  if (activeTab !== value) return null
  
  return (
    <div className={className}>
      {children}
    </div>
  )
}

// Interfaces for the contact detail component
interface AssetRecoveryRecord {
  id: string
  first_name: string
  last_name: string
  middle_name?: string
  primary_email?: string
  primary_phone?: string
  primary_address?: string
  city?: string
  state_name?: string
  zip_code?: string
  amount: number
  property_id?: string
  status: string
  priority: string
  assigned_to?: string
  team_id?: string
  contact_attempts: number
  last_contact_attempt?: string
  next_follow_up?: string
  investigation_status: string
  custom_fields?: any
}

interface ContactMethod {
  id: string
  type: 'phone' | 'email' | 'address' | 'social_media' | 'other'
  subtype?: string
  value: string
  label?: string
  is_verified: boolean
  is_primary: boolean
  contact_attempts: number
  is_valid: boolean
  notes?: string
}

interface RelatedContact {
  id: string
  first_name: string
  last_name: string
  relationship: string
  email?: string
  phone?: string
  address?: string
  notes?: string
  is_primary_contact: boolean
}

interface InvestigationNote {
  id: string
  created_by: string
  created_at: string
  note_type: 'general' | 'contact_attempt' | 'research' | 'legal' | 'internal'
  content: string
  is_important: boolean
  contact_method?: string
  contact_result?: string
  next_action?: string
  tags?: string[]
}

interface InvestigationTask {
  id: string
  title: string
  description?: string
  task_type: 'contact' | 'research' | 'legal' | 'document' | 'follow_up'
  priority: 'low' | 'medium' | 'high' | 'urgent'
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled'
  due_date?: string
  assigned_to: string
  completed_at?: string
}

interface Document {
  id: string
  file_name: string
  original_name: string
  file_size: number
  document_type: string
  description?: string
  is_sensitive: boolean
  uploaded_by: string
  created_at: string
}

interface ContactDetailProps {
  recordId: string
  onClose: () => void
}

export function ContactDetail({ recordId, onClose }: ContactDetailProps) {
  const [record, setRecord] = useState<AssetRecoveryRecord | null>(null)
  const [contactMethods, setContactMethods] = useState<ContactMethod[]>([])
  const [relatedContacts, setRelatedContacts] = useState<RelatedContact[]>([])
  const [notes, setNotes] = useState<InvestigationNote[]>([])
  const [tasks, setTasks] = useState<InvestigationTask[]>([])
  const [documents, setDocuments] = useState<Document[]>([])
  const [loading, setLoading] = useState(true)

  // Form states
  const [newNote, setNewNote] = useState('')
  const [newNoteType, setNewNoteType] = useState<InvestigationNote['note_type']>('general')
  const [newContactMethod, setNewContactMethod] = useState<Partial<ContactMethod>>({})
  const [newRelatedContact, setNewRelatedContact] = useState<Partial<RelatedContact>>({})
  const [newTask, setNewTask] = useState<Partial<InvestigationTask>>({})
  const [editingRecord, setEditingRecord] = useState(false)

  useEffect(() => {
    loadRecordData()
  }, [recordId])

  const loadRecordData = async () => {
    setLoading(true)
    try {
      // Mock data for demonstration
      setRecord({
        id: recordId,
        first_name: 'John',
        last_name: 'Smith',
        middle_name: 'Robert',
        primary_email: '<EMAIL>',
        primary_phone: '************',
        primary_address: '123 Main Street',
        city: 'Springfield',
        state_name: 'Illinois',
        zip_code: '62701',
        amount: 1500.00,
        property_id: 'PROP-2024-001',
        status: 'in_progress',
        priority: 'medium',
        contact_attempts: 2,
        investigation_status: 'active'
      })

      setContactMethods([
        {
          id: '1',
          type: 'phone',
          subtype: 'work',
          value: '************',
          label: 'Work Phone',
          is_verified: false,
          is_primary: false,
          contact_attempts: 1,
          is_valid: true,
          notes: 'Voicemail system'
        }
      ])

      setRelatedContacts([
        {
          id: '1',
          first_name: 'Jane',
          last_name: 'Smith',
          relationship: 'spouse',
          email: '<EMAIL>',
          phone: '************',
          is_primary_contact: true,
          notes: 'Helpful and cooperative'
        }
      ])

      setNotes([
        {
          id: '1',
          created_by: 'agent1',
          created_at: new Date().toISOString(),
          note_type: 'contact_attempt',
          content: 'Called primary number - reached voicemail. Left detailed message with callback number.',
          is_important: false,
          contact_method: 'phone',
          contact_result: 'voicemail',
          next_action: 'follow_up'
        }
      ])

      setTasks([
        {
          id: '1',
          title: 'Follow-up phone call',
          description: 'Call back after voicemail left yesterday',
          task_type: 'contact',
          priority: 'medium',
          status: 'pending',
          due_date: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(),
          assigned_to: 'agent1'
        }
      ])

    } catch (error) {
      console.error('Failed to load record data:', error)
    } finally {
      setLoading(false)
    }
  }

  const addNote = async () => {
    if (!newNote.trim()) return
    
    const note: InvestigationNote = {
      id: Date.now().toString(),
      created_by: 'current_user',
      created_at: new Date().toISOString(),
      note_type: newNoteType,
      content: newNote,
      is_important: false
    }

    setNotes(prev => [note, ...prev])
    setNewNote('')
    setNewNoteType('general')
  }

  const addContactMethod = async () => {
    if (!newContactMethod.type || !newContactMethod.value) return

    const contactMethod: ContactMethod = {
      id: Date.now().toString(),
      type: newContactMethod.type!,
      subtype: newContactMethod.subtype,
      value: newContactMethod.value!,
      label: newContactMethod.label,
      is_verified: false,
      is_primary: false,
      contact_attempts: 0,
      is_valid: true
    }

    setContactMethods(prev => [...prev, contactMethod])
    setNewContactMethod({})
  }

  const addRelatedContact = async () => {
    if (!newRelatedContact.first_name || !newRelatedContact.last_name) return

    const relatedContact: RelatedContact = {
      id: Date.now().toString(),
      first_name: newRelatedContact.first_name!,
      last_name: newRelatedContact.last_name!,
      relationship: newRelatedContact.relationship || 'other',
      email: newRelatedContact.email,
      phone: newRelatedContact.phone,
      address: newRelatedContact.address,
      notes: newRelatedContact.notes,
      is_primary_contact: false
    }

    setRelatedContacts(prev => [...prev, relatedContact])
    setNewRelatedContact({})
  }

  const addTask = async () => {
    if (!newTask.title) return

    const task: InvestigationTask = {
      id: Date.now().toString(),
      title: newTask.title!,
      description: newTask.description,
      task_type: newTask.task_type || 'contact',
      priority: newTask.priority || 'medium',
      status: 'pending',
      assigned_to: 'current_user',
      due_date: newTask.due_date
    }

    setTasks(prev => [...prev, task])
    setNewTask({})
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!record) {
    return (
      <div className="text-center p-8">
        <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
        <h3 className="text-lg font-semibold">Record Not Found</h3>
        <p className="text-gray-600">The requested record could not be loaded.</p>
      </div>
    )
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'new': return 'bg-blue-100 text-blue-800'
      case 'in_progress': return 'bg-yellow-100 text-yellow-800'
      case 'contacted': return 'bg-green-100 text-green-800'
      case 'resolved': return 'bg-purple-100 text-purple-800'
      case 'closed': return 'bg-gray-100 text-gray-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'low': return 'bg-gray-100 text-gray-800'
      case 'medium': return 'bg-blue-100 text-blue-800'
      case 'high': return 'bg-orange-100 text-orange-800'
      case 'urgent': return 'bg-red-100 text-red-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }
  
  return (
    <div className="max-w-7xl mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold">
            {record.first_name} {record.middle_name} {record.last_name}
          </h1>
          <p className="text-gray-600">Property ID: {record.property_id}</p>
          <div className="flex items-center space-x-2 mt-2">
            <Badge className={getStatusColor(record.status)}>
              {record.status.replace('_', ' ').toUpperCase()}
            </Badge>
            <Badge className={getPriorityColor(record.priority)}>
              {record.priority.toUpperCase()} PRIORITY
            </Badge>
            <Badge variant="outline">
              <DollarSign className="h-3 w-3 mr-1" />
              ${record.amount.toLocaleString()}
            </Badge>
          </div>
        </div>
        <Button variant="outline" onClick={onClose}>
          <X className="h-4 w-4 mr-2" />
          Close
        </Button>
      </div>

      {/* Main Content */}
      <div className="bg-white rounded-lg border p-6">
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList className="grid w-full grid-cols-6">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="contacts">Contact Methods</TabsTrigger>
            <TabsTrigger value="family">Related Contacts</TabsTrigger>
            <TabsTrigger value="notes">Notes</TabsTrigger>
            <TabsTrigger value="tasks">Tasks</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Personal Information */}
              <div className="border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <User className="h-5 w-5 mr-2" />
                  Personal Information
                </h3>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="text-sm font-medium text-gray-600">First Name</label>
                      <p className="font-medium">{record.first_name}</p>
                    </div>
                    <div>
                      <label className="text-sm font-medium text-gray-600">Last Name</label>
                      <p className="font-medium">{record.last_name}</p>
                    </div>
                  </div>
                  {record.middle_name && (
                    <div>
                      <label className="text-sm font-medium text-gray-600">Middle Name</label>
                      <p className="font-medium">{record.middle_name}</p>
                    </div>
                  )}
                </div>
              </div>

              {/* Contact Information */}
              <div className="border rounded-lg p-6">
                <h3 className="text-lg font-semibold mb-4 flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  Primary Contact
                </h3>
                <div className="space-y-3">
                  {record.primary_email && (
                    <div className="flex items-center space-x-2">
                      <Mail className="h-4 w-4 text-gray-500" />
                      <span>{record.primary_email}</span>
                    </div>
                  )}
                  {record.primary_phone && (
                    <div className="flex items-center space-x-2">
                      <Phone className="h-4 w-4 text-gray-500" />
                      <span>{record.primary_phone}</span>
                    </div>
                  )}
                  {record.primary_address && (
                    <div className="flex items-start space-x-2">
                      <MapPin className="h-4 w-4 text-gray-500 mt-1" />
                      <div>
                        <p>{record.primary_address}</p>
                        <p>{record.city}, {record.state_name} {record.zip_code}</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="contacts" className="space-y-4">
            <div className="border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <Phone className="h-5 w-5 mr-2" />
                  Contact Methods
                </h3>
                <Button size="sm" onClick={() => setNewContactMethod({ type: 'phone' })}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Contact Method
                </Button>
              </div>
              
              {/* Contact Methods List */}
              <div className="space-y-3">
                {contactMethods.map(method => (
                  <div key={method.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">{method.type}</Badge>
                          {method.subtype && <Badge variant="secondary">{method.subtype}</Badge>}
                          {method.is_primary && <Badge className="bg-green-100 text-green-800">Primary</Badge>}
                          {method.is_verified && <CheckCircle className="h-4 w-4 text-green-500" />}
                        </div>
                        <p className="font-medium mt-1">{method.value}</p>
                        {method.label && <p className="text-sm text-gray-600">{method.label}</p>}
                        {method.notes && <p className="text-sm text-gray-600 mt-1">{method.notes}</p>}
                        <p className="text-xs text-gray-500 mt-1">
                          Contact attempts: {method.contact_attempts}
                        </p>
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <PhoneCall className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="family" className="space-y-4">
            <div className="border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Related Contacts & Family
                </h3>
                <Button size="sm" onClick={() => setNewRelatedContact({ first_name: '', last_name: '' })}>
                  <UserPlus className="h-4 w-4 mr-2" />
                  Add Related Contact
                </Button>
              </div>
              
              {/* Related Contacts List */}
              <div className="space-y-3">
                {relatedContacts.map(contact => (
                  <div key={contact.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">
                            {contact.first_name} {contact.last_name}
                          </h4>
                          <Badge variant="outline">{contact.relationship}</Badge>
                          {contact.is_primary_contact && (
                            <Badge className="bg-blue-100 text-blue-800">Primary Contact</Badge>
                          )}
                        </div>
                        {contact.email && (
                          <p className="text-sm text-gray-600 mt-1">📧 {contact.email}</p>
                        )}
                        {contact.phone && (
                          <p className="text-sm text-gray-600">📞 {contact.phone}</p>
                        )}
                        {contact.address && (
                          <p className="text-sm text-gray-600">📍 {contact.address}</p>
                        )}
                        {contact.notes && (
                          <p className="text-sm text-gray-600 mt-2 italic">{contact.notes}</p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <PhoneCall className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="notes" className="space-y-4">
            <div className="border rounded-lg p-6">
              <h3 className="text-lg font-semibold mb-4 flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Investigation Notes
              </h3>
              
              {/* Add New Note */}
              <div className="border rounded-lg p-4 mb-4 bg-gray-50">
                <div className="space-y-3">
                  <div className="flex space-x-3">
                    <select 
                      value={newNoteType} 
                      onChange={(e) => setNewNoteType(e.target.value as InvestigationNote['note_type'])}
                      className="border rounded px-3 py-2"
                    >
                      <option value="general">General Note</option>
                      <option value="contact_attempt">Contact Attempt</option>
                      <option value="research">Research</option>
                      <option value="legal">Legal</option>
                      <option value="internal">Internal</option>
                    </select>
                  </div>
                  <Textarea
                    placeholder="Enter note content..."
                    value={newNote}
                    onChange={(e) => setNewNote(e.target.value)}
                    rows={3}
                  />
                  <Button onClick={addNote} disabled={!newNote.trim()}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Note
                  </Button>
                </div>
              </div>

              {/* Notes List */}
              <div className="space-y-3">
                {notes.map(note => (
                  <div key={note.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline">{note.note_type.replace('_', ' ')}</Badge>
                        {note.is_important && (
                          <Badge className="bg-red-100 text-red-800">Important</Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-500">
                        {new Date(note.created_at).toLocaleString()}
                      </p>
                    </div>
                    <p className="text-gray-800">{note.content}</p>
                    {note.contact_result && (
                      <div className="mt-2 flex items-center space-x-4 text-sm text-gray-600">
                        <span>Result: {note.contact_result}</span>
                        {note.next_action && <span>Next: {note.next_action}</span>}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="tasks" className="space-y-4">
            <div className="border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <CheckCircle className="h-5 w-5 mr-2" />
                  Investigation Tasks
                </h3>
                <Button size="sm" onClick={() => setNewTask({ title: '' })}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Task
                </Button>
              </div>
              
              {/* Tasks List */}
              <div className="space-y-3">
                {tasks.map(task => (
                  <div key={task.id} className="border rounded-lg p-4">
                    <div className="flex justify-between items-start">
                      <div>
                        <div className="flex items-center space-x-2">
                          <h4 className="font-medium">{task.title}</h4>
                          <Badge variant="outline">{task.task_type}</Badge>
                          <Badge className={getPriorityColor(task.priority)}>
                            {task.priority}
                          </Badge>
                          <Badge variant={task.status === 'completed' ? 'default' : 'secondary'}>
                            {task.status}
                          </Badge>
                        </div>
                        {task.description && (
                          <p className="text-sm text-gray-600 mt-1">{task.description}</p>
                        )}
                        {task.due_date && (
                          <p className="text-sm text-gray-500 mt-1">
                            Due: {new Date(task.due_date).toLocaleString()}
                          </p>
                        )}
                      </div>
                      <div className="flex space-x-2">
                        <Button size="sm" variant="outline">
                          <CheckCircle className="h-4 w-4" />
                        </Button>
                        <Button size="sm" variant="outline">
                          <Edit className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="documents" className="space-y-4">
            <div className="border rounded-lg p-6">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <Paperclip className="h-5 w-5 mr-2" />
                  Documents & Attachments
                </h3>
                <Button size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Upload Document
                </Button>
              </div>
              
              {documents.length === 0 ? (
                <div className="text-center py-8">
                  <Paperclip className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h4 className="text-lg font-medium text-gray-900">No documents uploaded</h4>
                  <p className="text-gray-600">Upload documents related to this investigation.</p>
                </div>
              ) : (
                <div className="space-y-3">
                  {documents.map(doc => (
                    <div key={doc.id} className="border rounded-lg p-4">
                      <div className="flex justify-between items-start">
                        <div>
                          <h4 className="font-medium">{doc.original_name}</h4>
                          <p className="text-sm text-gray-600">{doc.document_type}</p>
                          {doc.description && (
                            <p className="text-sm text-gray-600 mt-1">{doc.description}</p>
                          )}
                          <p className="text-xs text-gray-500 mt-1">
                            {(doc.file_size / 1024).toFixed(1)} KB • {new Date(doc.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            View
                          </Button>
                          <Button size="sm" variant="outline">
                            Download
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}