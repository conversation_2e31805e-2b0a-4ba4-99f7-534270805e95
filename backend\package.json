{"name": "backend", "version": "1.0.0", "description": "Asset Recovery Web Application Backend API", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["asset-recovery", "api", "fastify"], "author": "", "license": "ISC", "dependencies": {"@fastify/cors": "^10.0.1", "@fastify/jwt": "^9.0.1", "@fastify/multipart": "^9.0.3", "@supabase/supabase-js": "^2.49.8", "@types/uuid": "^10.0.0", "csv-parser": "^3.2.0", "dotenv": "^16.4.7", "fastify": "^5.2.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.15.21", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "typescript": "^5.8.3"}}