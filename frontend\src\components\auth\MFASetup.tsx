import React, { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Smartphone, 
  Key, 
  CheckCircle, 
  AlertTriangle, 
  Copy, 
  Download,
  QrCode,
  RefreshCw
} from 'lucide-react'
import { mfaService, type MFAFactor, type BackupCode } from '@/services/mfaService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

interface MFASetupProps {
  onComplete?: () => void
  onCancel?: () => void
  required?: boolean
}

export const MFASetup: React.FC<MFASetupProps> = ({ 
  onComplete, 
  onCancel, 
  required = false 
}) => {
  const { user } = useAuth()
  const [currentStep, setCurrentStep] = useState<'setup' | 'verify' | 'backup' | 'complete'>('setup')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // MFA enrollment data
  const [factorId, setFactorId] = useState<string>('')
  const [qrCode, setQrCode] = useState<string>('')
  const [secret, setSecret] = useState<string>('')
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState<BackupCode[]>([])
  
  // Existing factors
  const [existingFactors, setExistingFactors] = useState<MFAFactor[]>([])

  useEffect(() => {
    if (user) {
      mfaService.setUserId(user.id)
      loadExistingFactors()
    }
  }, [user])

  const loadExistingFactors = async () => {
    try {
      const { data, error } = await mfaService.getMFAFactors()
      if (error) {
        console.error('Error loading MFA factors:', error)
        return
      }
      
      setExistingFactors(data)
      
      // If user already has MFA enabled, show complete state
      if (data.length > 0 && data.some(f => f.status === 'verified')) {
        setCurrentStep('complete')
      }
    } catch (error) {
      console.error('Failed to load MFA factors:', error)
    }
  }

  const startMFAEnrollment = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await mfaService.enrollMFA('AssetHunterPro MFA')
      
      if (error) {
        setError(error.message || 'Failed to start MFA enrollment')
        return
      }
      
      if (data) {
        setFactorId(data.id)
        setQrCode(data.totp.qr_code)
        setSecret(data.totp.secret)
        setCurrentStep('verify')
      }
    } catch (error) {
      setError('Failed to start MFA enrollment')
      console.error('MFA enrollment error:', error)
    } finally {
      setLoading(false)
    }
  }

  const verifyMFASetup = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      const { error } = await mfaService.verifyMFAEnrollment(factorId, verificationCode)
      
      if (error) {
        setError(error.message || 'Invalid verification code')
        return
      }
      
      // Generate backup codes
      const codes = await mfaService.generateBackupCodes()
      setBackupCodes(codes)
      setCurrentStep('backup')
      
      toast({
        title: "MFA Enabled Successfully",
        description: "Your account is now protected with multi-factor authentication.",
      })
    } catch (error) {
      setError('Verification failed')
      console.error('MFA verification error:', error)
    } finally {
      setLoading(false)
    }
  }

  const completeMFASetup = () => {
    setCurrentStep('complete')
    if (onComplete) {
      onComplete()
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    toast({
      title: "Copied to clipboard",
      description: "The text has been copied to your clipboard.",
    })
  }

  const downloadBackupCodes = () => {
    const codesText = backupCodes.map(code => code.code).join('\n')
    const blob = new Blob([codesText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'assethunterpro-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Backup codes downloaded",
      description: "Save these codes in a secure location.",
    })
  }

  const removeMFA = async (factorId: string) => {
    setLoading(true)
    
    try {
      const { error } = await mfaService.unenrollMFA(factorId)
      
      if (error) {
        setError(error.message || 'Failed to remove MFA')
        return
      }
      
      await loadExistingFactors()
      setCurrentStep('setup')
      
      toast({
        title: "MFA Removed",
        description: "Multi-factor authentication has been disabled for your account.",
      })
    } catch (error) {
      setError('Failed to remove MFA')
      console.error('MFA removal error:', error)
    } finally {
      setLoading(false)
    }
  }

  if (currentStep === 'complete' && existingFactors.length > 0) {
    return (
      <Card className="w-full max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4">
            <CheckCircle className="h-6 w-6 text-green-600" />
          </div>
          <CardTitle>MFA is Active</CardTitle>
          <CardDescription>
            Your account is protected with multi-factor authentication
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {existingFactors.map((factor) => (
            <div key={factor.id} className="flex items-center justify-between p-3 border rounded-lg">
              <div className="flex items-center space-x-3">
                <Smartphone className="h-5 w-5 text-gray-500" />
                <div>
                  <div className="font-medium">{factor.friendly_name}</div>
                  <div className="text-sm text-gray-500">
                    Added {new Date(factor.created_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Badge variant={factor.status === 'verified' ? 'default' : 'secondary'}>
                  {factor.status}
                </Badge>
                {!required && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => removeMFA(factor.id)}
                    disabled={loading}
                  >
                    Remove
                  </Button>
                )}
              </div>
            </div>
          ))}
          
          {!required && (
            <Button
              variant="outline"
              className="w-full"
              onClick={() => setCurrentStep('setup')}
            >
              Add Another Device
            </Button>
          )}
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle>
          {required ? 'Enable Multi-Factor Authentication' : 'Secure Your Account'}
        </CardTitle>
        <CardDescription>
          {required 
            ? 'MFA is required for your account. Please set it up to continue.'
            : 'Add an extra layer of security to your AssetHunterPro account'
          }
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs value={currentStep} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="setup" disabled={currentStep !== 'setup'}>Setup</TabsTrigger>
            <TabsTrigger value="verify" disabled={currentStep !== 'verify'}>Verify</TabsTrigger>
            <TabsTrigger value="backup" disabled={currentStep !== 'backup'}>Backup</TabsTrigger>
            <TabsTrigger value="complete" disabled={currentStep !== 'complete'}>Complete</TabsTrigger>
          </TabsList>

          <TabsContent value="setup" className="space-y-4">
            <div className="text-center space-y-4">
              <div className="space-y-2">
                <h3 className="font-medium">Step 1: Install an Authenticator App</h3>
                <p className="text-sm text-gray-600">
                  Download an authenticator app like Google Authenticator, Authy, or 1Password.
                </p>
              </div>
              
              <Button 
                onClick={startMFAEnrollment} 
                disabled={loading}
                className="w-full"
              >
                {loading && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                Continue Setup
              </Button>
              
              {!required && onCancel && (
                <Button variant="outline" onClick={onCancel} className="w-full">
                  Cancel
                </Button>
              )}
            </div>
          </TabsContent>

          <TabsContent value="verify" className="space-y-4">
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="font-medium mb-2">Step 2: Scan QR Code</h3>
                <p className="text-sm text-gray-600 mb-4">
                  Scan this QR code with your authenticator app
                </p>
                
                {qrCode && (
                  <div className="flex justify-center mb-4">
                    <img src={qrCode} alt="MFA QR Code" className="border rounded-lg" />
                  </div>
                )}
                
                <div className="text-xs text-gray-500 mb-4">
                  Can't scan? Enter this code manually:
                  <div className="font-mono bg-gray-100 p-2 rounded mt-1 flex items-center justify-between">
                    <span>{secret}</span>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => copyToClipboard(secret)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Enter verification code</label>
                <Input
                  type="text"
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                />
              </div>
              
              <Button 
                onClick={verifyMFASetup} 
                disabled={loading || verificationCode.length !== 6}
                className="w-full"
              >
                {loading && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                Verify & Enable MFA
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="backup" className="space-y-4">
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="font-medium mb-2">Step 3: Save Backup Codes</h3>
                <p className="text-sm text-gray-600">
                  Save these backup codes in a secure location. You can use them to access your account if you lose your device.
                </p>
              </div>
              
              <div className="bg-gray-50 p-4 rounded-lg">
                <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                  {backupCodes.map((code, index) => (
                    <div key={index} className="text-center py-1">
                      {code.code}
                    </div>
                  ))}
                </div>
              </div>
              
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  onClick={() => copyToClipboard(backupCodes.map(c => c.code).join('\n'))}
                  className="flex-1"
                >
                  <Copy className="mr-2 h-4 w-4" />
                  Copy Codes
                </Button>
                <Button
                  variant="outline"
                  onClick={downloadBackupCodes}
                  className="flex-1"
                >
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </Button>
              </div>
              
              <Button onClick={completeMFASetup} className="w-full">
                I've Saved My Backup Codes
              </Button>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
