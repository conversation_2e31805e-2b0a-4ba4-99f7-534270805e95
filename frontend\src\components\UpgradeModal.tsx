import React from 'react';
import { X, <PERSON>, Check, ArrowRight, Zap, Users, BarChart3, Shield } from 'lucide-react';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ValidationResult, User, UserPlan } from '../services/planEnforcementService';

interface UpgradeModalProps {
  validation: ValidationResult;
  currentUser: User;
  onClose: () => void;
  onUpgrade: (planId: string) => void;
}

export const UpgradeModal: React.FC<UpgradeModalProps> = ({
  validation,
  currentUser,
  onClose,
  onUpgrade
}) => {
  const handleUpgrade = (planId: string) => {
    onUpgrade(planId);
    onClose();
  };

  // Plan features mapping
  const planFeatures = {
    bronze: {
      icon: Shield,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200',
      features: [
        '5,000 records',
        '1 state',
        'CSV import',
        'AI discovery',
        'Compliance reports',
        'Email support'
      ]
    },
    silver: {
      icon: Users,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      features: [
        '25,000 records',
        '5 states',
        'Batch processing',
        'Team collaboration',
        'Advanced analytics',
        'Priority support'
      ]
    },
    gold: {
      icon: BarChart3,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      features: [
        '100,000 records',
        'All 50 states',
        'White label',
        'API access',
        'Custom integrations',
        'Dedicated support'
      ]
    },
    enterprise: {
      icon: Zap,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200',
      features: [
        'Unlimited records',
        'All states',
        'Custom features',
        'Enterprise SSO',
        'SLA guarantee',
        'On-premise option'
      ]
    }
  };

  const getFeatureIcon = (planName: string) => {
    const plan = planFeatures[planName as keyof typeof planFeatures];
    return plan ? plan.icon : Shield;
  };

  const getPlanColor = (planName: string) => {
    const plan = planFeatures[planName as keyof typeof planFeatures];
    return plan?.color || 'text-gray-600';
  };

  const getPlanBgColor = (planName: string) => {
    const plan = planFeatures[planName as keyof typeof planFeatures];
    return plan?.bgColor || 'bg-gray-50';
  };

  const getPlanBorderColor = (planName: string) => {
    const plan = planFeatures[planName as keyof typeof planFeatures];
    return plan?.borderColor || 'border-gray-200';
  };

  const getPlanFeatures = (planName: string) => {
    const plan = planFeatures[planName as keyof typeof planFeatures];
    return plan?.features || [];
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-xl">
            <Star className="w-6 h-6 text-yellow-500" />
            Upgrade Required
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Current Issue */}
          <div className="p-4 bg-red-50 rounded-lg border border-red-200">
            <div className="flex items-start gap-3">
              <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                <X className="w-4 h-4 text-red-600" />
              </div>
              <div>
                <h3 className="font-semibold text-red-900">Upload Blocked</h3>
                <p className="text-red-700 mt-1">{validation.error}</p>
                {validation.upgradePrompt && (
                  <p className="text-red-600 text-sm mt-2">{validation.upgradePrompt}</p>
                )}
              </div>
            </div>
          </div>

          {/* Current Plan Status */}
          <div className="p-4 bg-gray-50 rounded-lg">
            <h3 className="font-semibold text-gray-900 mb-2">Current Plan Status</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center">
                <p className="text-lg font-bold text-gray-700">
                  {currentUser.planId.toUpperCase()}
                </p>
                <p className="text-sm text-gray-600">Current Plan</p>
              </div>
              {validation.usageInfo && (
                <>
                  <div className="text-center">
                    <p className="text-lg font-bold text-blue-600">
                      {validation.usageInfo.currentRecords.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">Records Used</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-orange-600">
                      {validation.usageInfo.currentStates}
                    </p>
                    <p className="text-sm text-gray-600">States Active</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-purple-600">
                      {validation.usageInfo.utilizationPercentage.toFixed(1)}%
                    </p>
                    <p className="text-sm text-gray-600">Utilization</p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Recommended Plan */}
          {validation.recommendedPlan && (
            <div>
              <h3 className="font-semibold text-gray-900 mb-4">
                🎯 Recommended Solution
              </h3>
              <Card className={`border-2 ${getPlanBorderColor(validation.recommendedPlan.name)} ${getPlanBgColor(validation.recommendedPlan.name)}`}>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {React.createElement(getFeatureIcon(validation.recommendedPlan.name), {
                        className: `w-6 h-6 ${getPlanColor(validation.recommendedPlan.name)}`
                      })}
                      <span className="text-xl font-bold">
                        {validation.recommendedPlan.name.toUpperCase()} PLAN
                      </span>
                      <Badge variant="outline" className="ml-2">
                        Recommended
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-2xl font-bold">${validation.recommendedPlan.price}</p>
                      <p className="text-sm text-gray-600">/month</p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 mb-4">{validation.recommendedPlan.description}</p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                    {getPlanFeatures(validation.recommendedPlan.name).map((feature, index) => (
                      <div key={index} className="flex items-center gap-2">
                        <Check className="w-4 h-4 text-green-600" />
                        <span className="text-sm">{feature}</span>
                      </div>
                    ))}
                  </div>

                  <div className="flex gap-3">
                    <Button 
                      onClick={() => handleUpgrade(validation.recommendedPlan!.id)}
                      className="flex-1"
                      size="lg"
                    >
                      Upgrade Now
                      <ArrowRight className="w-4 h-4 ml-2" />
                    </Button>
                    <Button 
                      variant="outline" 
                      onClick={onClose}
                      size="lg"
                    >
                      Maybe Later
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* All Plans Comparison */}
          <div>
            <h3 className="font-semibold text-gray-900 mb-4">
              📊 Compare All Plans
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {(['bronze', 'silver', 'gold', 'enterprise'] as const).map((planName) => {
                const PlanIcon = getFeatureIcon(planName);
                const isRecommended = validation.recommendedPlan?.name === planName;
                const isCurrent = currentUser.planId === planName;
                
                return (
                  <Card 
                    key={planName}
                    className={`relative ${
                      isRecommended 
                        ? `border-2 ${getPlanBorderColor(planName)}` 
                        : isCurrent
                        ? 'border-2 border-gray-400'
                        : 'border border-gray-200'
                    } ${isRecommended ? getPlanBgColor(planName) : ''}`}
                  >
                    {isRecommended && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge className="bg-yellow-500 text-yellow-900">
                          Recommended
                        </Badge>
                      </div>
                    )}
                    {isCurrent && (
                      <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                        <Badge variant="outline">
                          Current Plan
                        </Badge>
                      </div>
                    )}
                    
                    <CardHeader className="text-center pb-2">
                      <div className={`w-12 h-12 mx-auto rounded-full flex items-center justify-center ${getPlanBgColor(planName)}`}>
                        <PlanIcon className={`w-6 h-6 ${getPlanColor(planName)}`} />
                      </div>
                      <CardTitle className="text-lg">
                        {planName.toUpperCase()}
                      </CardTitle>
                      <div>
                        <p className="text-2xl font-bold">
                          ${planName === 'bronze' ? 49 : planName === 'silver' ? 149 : planName === 'gold' ? 349 : 999}
                        </p>
                        <p className="text-sm text-gray-600">/month</p>
                      </div>
                    </CardHeader>
                    
                    <CardContent className="pt-2">
                      <div className="space-y-2 mb-4">
                        {getPlanFeatures(planName).slice(0, 4).map((feature, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <Check className="w-3 h-3 text-green-600" />
                            <span className="text-xs">{feature}</span>
                          </div>
                        ))}
                      </div>
                      
                      <Button 
                        variant={isRecommended ? "default" : "outline"}
                        size="sm"
                        className="w-full"
                        onClick={() => handleUpgrade(planName)}
                        disabled={isCurrent}
                      >
                        {isCurrent ? 'Current Plan' : 'Select Plan'}
                      </Button>
                    </CardContent>
                  </Card>
                );
              })}
            </div>
          </div>

          {/* Benefits Highlight */}
          <div className="p-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg border border-blue-200">
            <h3 className="font-semibold text-blue-900 mb-3">
              🚀 Why Upgrade Now?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-blue-100 rounded-full flex items-center justify-center mb-2">
                  <Zap className="w-6 h-6 text-blue-600" />
                </div>
                <h4 className="font-medium text-blue-900">Instant Access</h4>
                <p className="text-sm text-blue-700">Upload your file immediately</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-purple-100 rounded-full flex items-center justify-center mb-2">
                  <BarChart3 className="w-6 h-6 text-purple-600" />
                </div>
                <h4 className="font-medium text-purple-900">Scale Your Business</h4>
                <p className="text-sm text-purple-700">Handle larger volumes efficiently</p>
              </div>
              <div className="text-center">
                <div className="w-12 h-12 mx-auto bg-green-100 rounded-full flex items-center justify-center mb-2">
                  <Users className="w-6 h-6 text-green-600" />
                </div>
                <h4 className="font-medium text-green-900">Team Features</h4>
                <p className="text-sm text-green-700">Collaborate with your team</p>
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 