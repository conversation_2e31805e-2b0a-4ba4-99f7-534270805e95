import React, { useState, useEffect } from 'react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { 
  Upload, 
  Search, 
  Filter, 
  Eye, 
  Download, 
  Share2, 
  Edit, 
  Trash2, 
  Clock, 
  User, 
  Tag, 
  FileText, 
  Image, 
  FileArchive, 
  AlertCircle,
  CheckCircle,
  MoreHorizontal,
  FolderOpen,
  StarIcon,
  Archive
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { DocumentUploadModal } from './DocumentUploadModal';
import { DocumentViewer } from './DocumentViewer';
import { DocumentVersionHistory } from './DocumentVersionHistory';

interface Document {
  id: string;
  claim_id: string;
  file_name: string;
  original_name: string;
  category: string;
  subcategory?: string;
  file_size: number;
  file_type: string;
  file_url?: string;
  description?: string;
  tags: string[];
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'archived';
  version: number;
  parent_document_id?: string;
  uploaded_by: string;
  reviewed_by?: string;
  approved_by?: string;
  is_confidential: boolean;
  expiry_date?: string;
  permissions: 'internal' | 'shareable' | 'public';
  created_at: string;
  updated_at: string;
  uploader_name?: string;
  reviewer_name?: string;
  approver_name?: string;
}

interface DocumentManagerProps {
  claimId: string;
  onDocumentUpdate?: () => void;
}

const DOCUMENT_CATEGORIES = [
  {
    value: 'id_documents',
    label: 'ID Documents',
    subcategories: ['drivers_license', 'passport', 'ssn_card', 'birth_certificate', 'other_id']
  },
  {
    value: 'contracts',
    label: 'Contracts & Agreements',
    subcategories: ['service_agreement', 'nda', 'power_of_attorney', 'authorization', 'settlement']
  },
  {
    value: 'correspondence',
    label: 'Correspondence',
    subcategories: ['email', 'letter', 'notice', 'demand_letter', 'response']
  },
  {
    value: 'state_forms',
    label: 'State Forms',
    subcategories: ['unclaimed_property', 'tax_forms', 'regulatory', 'compliance', 'filing']
  },
  {
    value: 'signatures',
    label: 'Signatures & Authorization',
    subcategories: ['wet_signature', 'digital_signature', 'authorization_form', 'consent']
  },
  {
    value: 'financial',
    label: 'Financial Documents',
    subcategories: ['bank_statements', 'tax_returns', 'w2', '1099', 'invoice', 'receipt']
  },
  {
    value: 'legal',
    label: 'Legal Documents',
    subcategories: ['court_filing', 'judgment', 'lien', 'lawsuit', 'settlement_agreement']
  },
  {
    value: 'other',
    label: 'Other Documents',
    subcategories: ['photo', 'screenshot', 'report', 'analysis', 'misc']
  }
];

const STATUS_CONFIG = {
  draft: { color: 'gray', label: 'Draft' },
  pending_review: { color: 'yellow', label: 'Pending Review' },
  approved: { color: 'green', label: 'Approved' },
  rejected: { color: 'red', label: 'Rejected' },
  archived: { color: 'blue', label: 'Archived' }
};

export const DocumentManager: React.FC<DocumentManagerProps> = ({ claimId, onDocumentUpdate }) => {
  const { user } = useAuth();
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [showDocumentViewer, setShowDocumentViewer] = useState(false);
  const [showVersionHistory, setShowVersionHistory] = useState(false);

  // Load documents
  useEffect(() => {
    loadDocuments();
  }, [claimId]);

  // Filter and sort documents
  useEffect(() => {
    let filtered = [...documents];

    // Search filter
    if (searchQuery) {
      filtered = filtered.filter(doc => 
        doc.file_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    // Category filter
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(doc => doc.category === selectedCategory);
    }

    // Status filter
    if (selectedStatus !== 'all') {
      filtered = filtered.filter(doc => doc.status === selectedStatus);
    }

    // Sort
    filtered.sort((a, b) => {
      let aValue = a[sortBy as keyof Document];
      let bValue = b[sortBy as keyof Document];

      // Handle undefined/null values
      if (aValue == null && bValue == null) return 0;
      if (aValue == null) return sortOrder === 'asc' ? -1 : 1;
      if (bValue == null) return sortOrder === 'asc' ? 1 : -1;

      if (aValue < bValue) return sortOrder === 'asc' ? -1 : 1;
      if (aValue > bValue) return sortOrder === 'asc' ? 1 : -1;
      return 0;
    });

    setFilteredDocuments(filtered);
  }, [documents, searchQuery, selectedCategory, selectedStatus, sortBy, sortOrder]);

  const loadDocuments = async () => {
    try {
      setLoading(true);
      const { data, error } = await supabase
        .from('claim_documents')
        .select(`
          *,
          uploader:uploaded_by(name),
          reviewer:reviewed_by(name),
          approver:approved_by(name)
        `)
        .eq('claim_id', claimId)
        .order('created_at', { ascending: false });

      if (error) throw error;

      const documentsWithNames = data?.map(doc => ({
        ...doc,
        tags: doc.tags || [],
        uploader_name: doc.uploader?.name,
        reviewer_name: doc.reviewer?.name,
        approver_name: doc.approver?.name
      })) || [];

      setDocuments(documentsWithNames);
    } catch (error) {
      console.error('Error loading documents:', error);
    } finally {
      setLoading(false);
    }
  };

  const getFileIcon = (fileType: string, size: string = 'h-5 w-5') => {
    if (fileType.startsWith('image/')) return <Image className={`${size} text-blue-500`} />;
    if (fileType === 'application/pdf') return <FileText className={`${size} text-red-500`} />;
    if (fileType.includes('zip') || fileType.includes('archive')) return <FileArchive className={`${size} text-orange-500`} />;
    return <FileText className={`${size} text-gray-500`} />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDocumentAction = async (action: string, document: Document) => {
    switch (action) {
      case 'view':
        setSelectedDocument(document);
        setShowDocumentViewer(true);
        break;
      case 'download':
        if (document.file_url) {
          window.open(document.file_url, '_blank');
        }
        break;
      case 'versions':
        setSelectedDocument(document);
        setShowVersionHistory(true);
        break;
      case 'approve':
        await updateDocumentStatus(document.id, 'approved');
        break;
      case 'reject':
        await updateDocumentStatus(document.id, 'rejected');
        break;
      case 'archive':
        await updateDocumentStatus(document.id, 'archived');
        break;
      default:
        break;
    }
  };

  const updateDocumentStatus = async (documentId: string, status: string) => {
    try {
      const { error } = await supabase
        .from('claim_documents')
        .update({ 
          status,
          reviewed_by: user?.id,
          updated_at: new Date().toISOString()
        })
        .eq('id', documentId);

      if (error) throw error;
      await loadDocuments();
    } catch (error) {
      console.error('Error updating document status:', error);
    }
  };

  const handleUploadComplete = () => {
    loadDocuments();
    setShowUploadModal(false);
    onDocumentUpdate?.();
  };

  const DocumentCard: React.FC<{ document: Document }> = ({ document }) => (
    <Card className="hover:shadow-md transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            {getFileIcon(document.file_type, 'h-8 w-8')}
            <div className="flex-1 min-w-0">
              <h3 className="font-medium text-sm truncate">{document.original_name}</h3>
              <p className="text-xs text-gray-500 mt-1">
                {formatFileSize(document.file_size)} • {formatDate(document.created_at)}
              </p>
              {document.description && (
                <p className="text-xs text-gray-600 mt-1 line-clamp-2">{document.description}</p>
              )}
              <div className="flex items-center gap-2 mt-2">
                <Badge variant={STATUS_CONFIG[document.status].color as any}>
                  {STATUS_CONFIG[document.status].label}
                </Badge>
                {document.is_confidential && (
                  <Badge variant="secondary">Confidential</Badge>
                )}
                {document.version > 1 && (
                  <Badge variant="outline">v{document.version}</Badge>
                )}
              </div>
              {document.tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {document.tags.slice(0, 3).map((tag, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                  {document.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{document.tags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuItem onClick={() => handleDocumentAction('view', document)}>
                <Eye className="h-4 w-4 mr-2" />
                View
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDocumentAction('download', document)}>
                <Download className="h-4 w-4 mr-2" />
                Download
              </DropdownMenuItem>
              <DropdownMenuItem onClick={() => handleDocumentAction('versions', document)}>
                <Clock className="h-4 w-4 mr-2" />
                Version History
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              {document.status === 'pending_review' && (
                <>
                  <DropdownMenuItem onClick={() => handleDocumentAction('approve', document)}>
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Approve
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleDocumentAction('reject', document)}>
                    <AlertCircle className="h-4 w-4 mr-2" />
                    Reject
                  </DropdownMenuItem>
                </>
              )}
              <DropdownMenuItem onClick={() => handleDocumentAction('archive', document)}>
                <Archive className="h-4 w-4 mr-2" />
                Archive
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Management</h2>
          <p className="text-gray-600">
            Manage all documents for this claim with advanced organization and workflow features.
          </p>
        </div>
        <Button onClick={() => setShowUploadModal(true)}>
          <Upload className="h-4 w-4 mr-2" />
          Upload Documents
        </Button>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search documents, descriptions, tags..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {DOCUMENT_CATEGORIES.map(category => (
                  <SelectItem key={category.value} value={category.value}>
                    {category.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={selectedStatus} onValueChange={setSelectedStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                {Object.entries(STATUS_CONFIG).map(([key, config]) => (
                  <SelectItem key={key} value={key}>
                    {config.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={`${sortBy}-${sortOrder}`} onValueChange={(value) => {
              const [field, order] = value.split('-');
              setSortBy(field);
              setSortOrder(order as 'asc' | 'desc');
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Sort" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="created_at-desc">Newest First</SelectItem>
                <SelectItem value="created_at-asc">Oldest First</SelectItem>
                <SelectItem value="file_name-asc">Name A-Z</SelectItem>
                <SelectItem value="file_name-desc">Name Z-A</SelectItem>
                <SelectItem value="file_size-desc">Largest First</SelectItem>
                <SelectItem value="file_size-asc">Smallest First</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Document Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">{documents.length}</div>
            <div className="text-sm text-gray-600">Total Documents</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-yellow-600">
              {documents.filter(d => d.status === 'pending_review').length}
            </div>
            <div className="text-sm text-gray-600">Pending Review</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {documents.filter(d => d.status === 'approved').length}
            </div>
            <div className="text-sm text-gray-600">Approved</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold">
              {formatFileSize(documents.reduce((sum, doc) => sum + doc.file_size, 0))}
            </div>
            <div className="text-sm text-gray-600">Total Size</div>
          </CardContent>
        </Card>
      </div>

      {/* Document List */}
      <div className="space-y-4">
        {loading ? (
          <div className="text-center py-8">Loading documents...</div>
        ) : filteredDocuments.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <FolderOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No documents found</h3>
              <p className="text-gray-600 mb-4">
                {documents.length === 0 
                  ? "Upload your first document to get started."
                  : "Try adjusting your search or filter criteria."}
              </p>
              {documents.length === 0 && (
                <Button onClick={() => setShowUploadModal(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload First Document
                </Button>
              )}
            </CardContent>
          </Card>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredDocuments.map((document) => (
              <DocumentCard key={document.id} document={document} />
            ))}
          </div>
        )}
      </div>

      {/* Modals */}
      {showUploadModal && (
        <DocumentUploadModal
          claimId={claimId}
          onClose={() => setShowUploadModal(false)}
          onUploadComplete={handleUploadComplete}
        />
      )}

      {showDocumentViewer && selectedDocument && (
        <DocumentViewer
          document={selectedDocument}
          onClose={() => setShowDocumentViewer(false)}
        />
      )}

      {showVersionHistory && selectedDocument && (
        <DocumentVersionHistory
          document={selectedDocument}
          onClose={() => setShowVersionHistory(false)}
        />
      )}
    </div>
  );
};

export default DocumentManager; 