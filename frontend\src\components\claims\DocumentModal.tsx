import { useState, useEffect } from 'react';
import { Button } from "@/components/ui/button";
import { 
  X, 
  ChevronLeft, 
  ChevronRight,
  Download,
  ZoomIn,
  ZoomOut,
  RotateCw,
  FileText,
  File,
  ExternalLink
} from 'lucide-react';

interface Document {
  id: string;
  file_name: string;
  category: string;
  file_size?: number;
  file_url?: string;
  created_at: string;
}

interface DocumentModalProps {
  documents: Document[];
  initialDocumentIndex: number;
  isOpen: boolean;
  onClose: () => void;
}

export default function DocumentModal({ documents, initialDocumentIndex, isOpen, onClose }: DocumentModalProps) {
  const [currentIndex, setCurrentIndex] = useState(initialDocumentIndex);
  const [zoom, setZoom] = useState(100);
  const [rotation, setRotation] = useState(0);

  useEffect(() => {
    setCurrentIndex(initialDocumentIndex);
  }, [initialDocumentIndex]);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (!isOpen) return;
      
      switch (e.key) {
        case 'Escape':
          onClose();
          break;
        case 'ArrowLeft':
          goToPrevious();
          break;
        case 'ArrowRight':
          goToNext();
          break;
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, currentIndex]);

  if (!isOpen || documents.length === 0) return null;

  const currentDocument = documents[currentIndex];
  
  const goToPrevious = () => {
    setCurrentIndex((prev: number) => prev > 0 ? prev - 1 : documents.length - 1);
    resetView();
  };

  const goToNext = () => {
    setCurrentIndex((prev: number) => prev < documents.length - 1 ? prev + 1 : 0);
    resetView();
  };

  const resetView = () => {
    setZoom(100);
    setRotation(0);
  };

  const handleZoomIn = () => setZoom((prev: number) => Math.min(prev + 25, 300));
  const handleZoomOut = () => setZoom((prev: number) => Math.max(prev - 25, 25));
  const handleRotate = () => setRotation((prev: number) => (prev + 90) % 360);

  const handleDownload = async () => {
    if (!currentDocument.file_url) return;

    try {
      const link = document.createElement('a');
      link.href = currentDocument.file_url;
      link.download = currentDocument.file_name;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Download error:', error);
    }
  };

  const handleOpenInNewTab = () => {
    if (currentDocument.file_url) {
      window.open(currentDocument.file_url, '_blank');
    }
  };

  const getFileType = (fileName: string) => {
    const extension = fileName.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension || '')) {
      return 'image';
    }
    if (['pdf'].includes(extension || '')) {
      return 'pdf';
    }
    return 'other';
  };

  const renderDocumentContent = () => {
    const fileType = getFileType(currentDocument.file_name);
    
    if (!currentDocument.file_url) {
      return (
        <div className="flex items-center justify-center h-full text-gray-500">
          <div className="text-center">
            <File className="h-16 w-16 mx-auto mb-4" />
            <p>Document URL not available</p>
          </div>
        </div>
      );
    }

    const style = {
      transform: `scale(${zoom / 100}) rotate(${rotation}deg)`,
      transformOrigin: 'center center',
      transition: 'transform 0.2s ease-in-out'
    };

    switch (fileType) {
      case 'image':
        return (
          <div className="flex items-center justify-center h-full overflow-auto">
            <img
              src={currentDocument.file_url}
              alt={currentDocument.file_name}
              style={style}
              className="max-w-full max-h-full object-contain"
            />
          </div>
        );
      
      case 'pdf':
        return (
          <div className="h-full overflow-auto">
            <iframe
              src={currentDocument.file_url}
              title={currentDocument.file_name}
              className="w-full h-full border-none"
              style={{
                transform: `scale(${zoom / 100})`,
                transformOrigin: 'top left',
                width: `${100 / (zoom / 100)}%`,
                height: `${100 / (zoom / 100)}%`
              }}
            />
          </div>
        );
      
      default:
        return (
          <div className="flex items-center justify-center h-full text-gray-500">
            <div className="text-center">
              <FileText className="h-16 w-16 mx-auto mb-4" />
              <p className="mb-4">Cannot preview this file type</p>
              <Button onClick={handleOpenInNewTab} variant="outline">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in New Tab
              </Button>
            </div>
          </div>
        );
    }
  };

  const formatFileSize = (bytes?: number) => {
    if (!bytes) return 'Unknown size';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-90 z-50 flex flex-col">
      {/* Header */}
      <div className="bg-white border-b px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h3 className="font-semibold text-lg truncate max-w-md">
            {currentDocument.file_name}
          </h3>
          <div className="text-sm text-gray-500">
            {currentIndex + 1} of {documents.length}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Zoom Controls */}
          <Button size="sm" variant="outline" onClick={handleZoomOut} disabled={zoom <= 25}>
            <ZoomOut className="h-4 w-4" />
          </Button>
          <span className="text-sm font-medium min-w-[3rem] text-center">{zoom}%</span>
          <Button size="sm" variant="outline" onClick={handleZoomIn} disabled={zoom >= 300}>
            <ZoomIn className="h-4 w-4" />
          </Button>
          
          {/* Rotate (for images) */}
          {getFileType(currentDocument.file_name) === 'image' && (
            <Button size="sm" variant="outline" onClick={handleRotate}>
              <RotateCw className="h-4 w-4" />
            </Button>
          )}
          
          {/* Download */}
          <Button size="sm" variant="outline" onClick={handleDownload}>
            <Download className="h-4 w-4" />
          </Button>
          
          {/* Open in new tab */}
          <Button size="sm" variant="outline" onClick={handleOpenInNewTab}>
            <ExternalLink className="h-4 w-4" />
          </Button>
          
          {/* Close */}
          <Button size="sm" variant="outline" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Content Area */}
      <div className="flex-1 flex">
        {/* Navigation - Previous */}
        {documents.length > 1 && (
          <div className="flex items-center justify-center w-16 bg-black bg-opacity-20">
            <Button
              size="lg"
              variant="ghost"
              onClick={goToPrevious}
              className="text-white hover:bg-white hover:bg-opacity-20 h-full w-full rounded-none"
            >
              <ChevronLeft className="h-8 w-8" />
            </Button>
          </div>
        )}

        {/* Document Display */}
        <div className="flex-1 bg-gray-100">
          {renderDocumentContent()}
        </div>

        {/* Navigation - Next */}
        {documents.length > 1 && (
          <div className="flex items-center justify-center w-16 bg-black bg-opacity-20">
            <Button
              size="lg"
              variant="ghost"
              onClick={goToNext}
              className="text-white hover:bg-white hover:bg-opacity-20 h-full w-full rounded-none"
            >
              <ChevronRight className="h-8 w-8" />
            </Button>
          </div>
        )}
      </div>

      {/* Footer with document info */}
      <div className="bg-white border-t px-4 py-2">
        <div className="flex items-center justify-between text-sm text-gray-600">
          <div className="flex items-center gap-4">
            <span className="capitalize font-medium">{currentDocument.category}</span>
            <span>{formatFileSize(currentDocument.file_size)}</span>
            <span>Uploaded: {formatDate(currentDocument.created_at)}</span>
          </div>
          
          {documents.length > 1 && (
            <div className="text-xs text-gray-500">
              Use arrow keys or buttons to navigate • Press ESC to close
            </div>
          )}
        </div>
      </div>
    </div>
  );
} 