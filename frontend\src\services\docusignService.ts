// SignRequest Integration Service for AssetHunterPro
// Handles electronic signature workflows for legal documents

import { FEATURE_FLAGS } from '@/config/features'

export interface SignRequestConfig {
  apiToken: string
  baseUrl: string
  webhookUrl: string
}

export interface DocumentData {
  id: string
  name: string
  content: string | Uint8Array
  contentType: string
}

export interface Signer {
  email: string
  name: string
  recipientId: string
  routingOrder: number
  tabs?: SignerTabs
}

export interface SignerTabs {
  signHereTabs?: SignHereTab[]
  dateSignedTabs?: DateSignedTab[]
  textTabs?: TextTab[]
}

export interface SignHereTab {
  anchorString?: string
  anchorXOffset?: string
  anchorYOffset?: string
  xPosition?: string
  yPosition?: string
  pageNumber?: string
}

export interface DateSignedTab {
  anchorString?: string
  anchorXOffset?: string
  anchorYOffset?: string
  xPosition?: string
  yPosition?: string
  pageNumber?: string
}

export interface TextTab {
  anchorString?: string
  anchorXOffset?: string
  anchorYOffset?: string
  xPosition?: string
  yPosition?: string
  pageNumber?: string
  value?: string
  required?: boolean
}

export interface EnvelopeRequest {
  documents: DocumentData[]
  signers: Signer[]
  subject: string
  message: string
  status: 'created' | 'sent'
  claimId?: string
  claimantId?: string
}

export interface EnvelopeResponse {
  envelopeId: string
  status: string
  statusDateTime: string
  uri: string
}

export interface EnvelopeStatus {
  envelopeId: string
  status: 'created' | 'sent' | 'delivered' | 'signed' | 'completed' | 'declined' | 'voided'
  statusDateTime: string
  documentsUri?: string
  recipientsUri?: string
  documents?: DocumentStatus[]
  recipients?: RecipientStatus[]
}

export interface DocumentStatus {
  documentId: string
  name: string
  type: string
  uri: string
}

export interface RecipientStatus {
  recipientId: string
  email: string
  name: string
  status: string
  signedDateTime?: string
  deliveredDateTime?: string
}

export interface SigningUrl {
  url: string
  recipientId: string
}

export class DocuSignService {
  private static instance: DocuSignService
  private config: DocuSignConfig | null = null
  private accessToken: string | null = null

  static getInstance(): DocuSignService {
    if (!DocuSignService.instance) {
      DocuSignService.instance = new DocuSignService()
    }
    return DocuSignService.instance
  }

  /**
   * Initialize DocuSign service with configuration
   */
  initialize(config: DocuSignConfig) {
    this.config = config
    console.log('📝 DocuSign service initialized')
  }

  /**
   * Set access token for API calls
   */
  setAccessToken(token: string) {
    this.accessToken = token
  }

  /**
   * Create and send envelope for signature
   */
  async createEnvelope(request: EnvelopeRequest): Promise<{ data: EnvelopeResponse | null; error: any }> {
    try {
      if (!this.config || !this.accessToken) {
        throw new Error('DocuSign service not properly configured')
      }

      const envelopeDefinition = {
        emailSubject: request.subject,
        emailMessage: request.message,
        status: request.status,
        documents: request.documents.map((doc, index) => ({
          documentId: (index + 1).toString(),
          name: doc.name,
          documentBase64: typeof doc.content === 'string' ? doc.content : this.arrayBufferToBase64(doc.content),
          fileExtension: this.getFileExtension(doc.name)
        })),
        recipients: {
          signers: request.signers.map(signer => ({
            email: signer.email,
            name: signer.name,
            recipientId: signer.recipientId,
            routingOrder: signer.routingOrder.toString(),
            tabs: signer.tabs || this.getDefaultTabs()
          }))
        }
      }

      const response = await fetch(`${this.config.baseUrl}/v2.1/accounts/${this.config.accountId}/envelopes`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${this.accessToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(envelopeDefinition)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`DocuSign API error: ${errorData.message || response.statusText}`)
      }

      const data = await response.json()
      
      // Store envelope metadata in database
      await this.storeEnvelopeMetadata(data.envelopeId, request)

      console.log('✅ DocuSign envelope created:', data.envelopeId)
      return { data, error: null }
    } catch (error) {
      console.error('❌ DocuSign envelope creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get envelope status and details
   */
  async getEnvelopeStatus(envelopeId: string): Promise<{ data: EnvelopeStatus | null; error: any }> {
    try {
      if (!this.config || !this.accessToken) {
        throw new Error('DocuSign service not properly configured')
      }

      const response = await fetch(
        `${this.config.baseUrl}/v2.1/accounts/${this.config.accountId}/envelopes/${envelopeId}`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          }
        }
      )

      if (!response.ok) {
        throw new Error(`DocuSign API error: ${response.statusText}`)
      }

      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      console.error('❌ DocuSign status check failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get recipient signing URL
   */
  async getSigningUrl(envelopeId: string, recipientId: string, returnUrl?: string): Promise<{ data: SigningUrl | null; error: any }> {
    try {
      if (!this.config || !this.accessToken) {
        throw new Error('DocuSign service not properly configured')
      }

      const requestBody = {
        returnUrl: returnUrl || this.config.redirectUrl,
        authenticationMethod: 'none',
        email: true,
        userName: 'Signer'
      }

      const response = await fetch(
        `${this.config.baseUrl}/v2.1/accounts/${this.config.accountId}/envelopes/${envelopeId}/views/recipient`,
        {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(requestBody)
        }
      )

      if (!response.ok) {
        throw new Error(`DocuSign API error: ${response.statusText}`)
      }

      const data = await response.json()
      return { 
        data: { 
          url: data.url, 
          recipientId 
        }, 
        error: null 
      }
    } catch (error) {
      console.error('❌ DocuSign signing URL generation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Download completed documents
   */
  async downloadDocuments(envelopeId: string): Promise<{ data: Uint8Array | null; error: any }> {
    try {
      if (!this.config || !this.accessToken) {
        throw new Error('DocuSign service not properly configured')
      }

      const response = await fetch(
        `${this.config.baseUrl}/v2.1/accounts/${this.config.accountId}/envelopes/${envelopeId}/documents/combined`,
        {
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Accept': 'application/pdf'
          }
        }
      )

      if (!response.ok) {
        throw new Error(`DocuSign API error: ${response.statusText}`)
      }

      const data = await response.arrayBuffer()
      return { data: new Uint8Array(data), error: null }
    } catch (error) {
      console.error('❌ DocuSign document download failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Void an envelope
   */
  async voidEnvelope(envelopeId: string, reason: string): Promise<{ error: any }> {
    try {
      if (!this.config || !this.accessToken) {
        throw new Error('DocuSign service not properly configured')
      }

      const response = await fetch(
        `${this.config.baseUrl}/v2.1/accounts/${this.config.accountId}/envelopes/${envelopeId}`,
        {
          method: 'PUT',
          headers: {
            'Authorization': `Bearer ${this.accessToken}`,
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            status: 'voided',
            voidedReason: reason
          })
        }
      )

      if (!response.ok) {
        throw new Error(`DocuSign API error: ${response.statusText}`)
      }

      console.log('✅ DocuSign envelope voided:', envelopeId)
      return { error: null }
    } catch (error) {
      console.error('❌ DocuSign envelope void failed:', error)
      return { error }
    }
  }

  // Helper methods
  private getDefaultTabs(): SignerTabs {
    return {
      signHereTabs: [
        {
          anchorString: '/sn1/',
          anchorXOffset: '20',
          anchorYOffset: '10'
        }
      ],
      dateSignedTabs: [
        {
          anchorString: '/ds1/',
          anchorXOffset: '20',
          anchorYOffset: '10'
        }
      ]
    }
  }

  private getFileExtension(filename: string): string {
    return filename.split('.').pop()?.toLowerCase() || 'pdf'
  }

  private arrayBufferToBase64(buffer: Uint8Array): string {
    let binary = ''
    const bytes = new Uint8Array(buffer)
    for (let i = 0; i < bytes.byteLength; i++) {
      binary += String.fromCharCode(bytes[i])
    }
    return btoa(binary)
  }

  private async storeEnvelopeMetadata(envelopeId: string, request: EnvelopeRequest) {
    // Store envelope metadata in database for tracking
    // This would integrate with your database service
    console.log('📝 Storing envelope metadata:', { envelopeId, claimId: request.claimId })
  }


}

export const docuSignService = DocuSignService.getInstance()
