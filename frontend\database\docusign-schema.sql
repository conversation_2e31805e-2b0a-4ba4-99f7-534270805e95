-- DocuSign Integration Schema for AssetHunterPro
-- This schema supports electronic signature workflows and document management

-- Enable UUID extension (safe to run multiple times)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===================================
-- DOCUMENT TEMPLATES TABLE
-- ===================================

-- Store reusable document templates
CREATE TABLE IF NOT EXISTS document_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    template_type VARCHAR(50) NOT NULL CHECK (template_type IN (
        'asset_recovery_agreement',
        'power_of_attorney',
        'claim_assignment',
        'release_form',
        'settlement_agreement'
    )),
    content_url TEXT, -- URL to template file
    content_base64 TEXT, -- Base64 encoded template content
    placeholders JSONB DEFAULT '[]', -- Array of placeholder fields
    signature_fields JSONB DEFAULT '[]', -- Array of signature field definitions
    is_active BOOLEAN DEFAULT true,
    version INTEGER DEFAULT 1,
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Index for faster template lookups
CREATE INDEX IF NOT EXISTS idx_document_templates_type ON document_templates(template_type);
CREATE INDEX IF NOT EXISTS idx_document_templates_active ON document_templates(is_active);

-- ===================================
-- DOCUSIGN ENVELOPES TABLE
-- ===================================

-- Track DocuSign envelopes and their status
CREATE TABLE IF NOT EXISTS docusign_envelopes (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    envelope_id VARCHAR(255) UNIQUE NOT NULL, -- DocuSign envelope ID
    claim_id UUID REFERENCES claims(id) ON DELETE CASCADE,
    claimant_id UUID REFERENCES claimants(id) ON DELETE SET NULL,
    template_id UUID REFERENCES document_templates(id) ON DELETE SET NULL,
    
    -- Envelope details
    subject VARCHAR(500) NOT NULL,
    message TEXT,
    status VARCHAR(50) NOT NULL CHECK (status IN (
        'created',
        'sent',
        'delivered',
        'signed',
        'completed',
        'declined',
        'voided',
        'expired'
    )),
    
    -- Metadata
    envelope_uri TEXT,
    documents_uri TEXT,
    recipients_uri TEXT,
    
    -- Timestamps
    sent_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    voided_at TIMESTAMPTZ,
    expires_at TIMESTAMPTZ,
    
    -- Audit fields
    created_by UUID NOT NULL REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for envelope queries
CREATE INDEX IF NOT EXISTS idx_docusign_envelopes_envelope_id ON docusign_envelopes(envelope_id);
CREATE INDEX IF NOT EXISTS idx_docusign_envelopes_claim_id ON docusign_envelopes(claim_id);
CREATE INDEX IF NOT EXISTS idx_docusign_envelopes_status ON docusign_envelopes(status);
CREATE INDEX IF NOT EXISTS idx_docusign_envelopes_created_at ON docusign_envelopes(created_at);

-- ===================================
-- ENVELOPE DOCUMENTS TABLE
-- ===================================

-- Track individual documents within envelopes
CREATE TABLE IF NOT EXISTS envelope_documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    envelope_id UUID NOT NULL REFERENCES docusign_envelopes(id) ON DELETE CASCADE,
    document_id VARCHAR(50) NOT NULL, -- DocuSign document ID within envelope
    name VARCHAR(255) NOT NULL,
    type VARCHAR(50) DEFAULT 'content',
    order_index INTEGER DEFAULT 1,
    
    -- Document content
    original_content_url TEXT,
    signed_content_url TEXT,
    content_type VARCHAR(100) DEFAULT 'application/pdf',
    
    -- Status
    is_signed BOOLEAN DEFAULT false,
    signed_at TIMESTAMPTZ,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique document per envelope
    UNIQUE(envelope_id, document_id)
);

-- Index for document lookups
CREATE INDEX IF NOT EXISTS idx_envelope_documents_envelope_id ON envelope_documents(envelope_id);

-- ===================================
-- ENVELOPE RECIPIENTS TABLE
-- ===================================

-- Track recipients (signers) for each envelope
CREATE TABLE IF NOT EXISTS envelope_recipients (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    envelope_id UUID NOT NULL REFERENCES docusign_envelopes(id) ON DELETE CASCADE,
    recipient_id VARCHAR(50) NOT NULL, -- DocuSign recipient ID
    
    -- Recipient details
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255) NOT NULL,
    role VARCHAR(50) DEFAULT 'signer' CHECK (role IN ('signer', 'carbon_copy', 'certified_delivery')),
    routing_order INTEGER DEFAULT 1,
    
    -- Status tracking
    status VARCHAR(50) NOT NULL DEFAULT 'created' CHECK (status IN (
        'created',
        'sent',
        'delivered',
        'signed',
        'declined',
        'completed',
        'fax_pending',
        'auto_responded'
    )),
    
    -- Timestamps
    sent_at TIMESTAMPTZ,
    delivered_at TIMESTAMPTZ,
    signed_at TIMESTAMPTZ,
    declined_at TIMESTAMPTZ,
    
    -- Signing details
    signing_url TEXT,
    decline_reason TEXT,
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    
    -- Ensure unique recipient per envelope
    UNIQUE(envelope_id, recipient_id)
);

-- Indexes for recipient queries
CREATE INDEX IF NOT EXISTS idx_envelope_recipients_envelope_id ON envelope_recipients(envelope_id);
CREATE INDEX IF NOT EXISTS idx_envelope_recipients_email ON envelope_recipients(email);
CREATE INDEX IF NOT EXISTS idx_envelope_recipients_status ON envelope_recipients(status);

-- ===================================
-- SIGNATURE EVENTS TABLE
-- ===================================

-- Audit trail for signature events
CREATE TABLE IF NOT EXISTS signature_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    envelope_id UUID NOT NULL REFERENCES docusign_envelopes(id) ON DELETE CASCADE,
    recipient_id UUID REFERENCES envelope_recipients(id) ON DELETE SET NULL,
    
    event_type VARCHAR(50) NOT NULL CHECK (event_type IN (
        'envelope_sent',
        'envelope_delivered',
        'envelope_completed',
        'envelope_declined',
        'envelope_voided',
        'recipient_signed',
        'recipient_declined',
        'document_viewed',
        'signing_started',
        'signing_completed'
    )),
    
    -- Event details
    description TEXT,
    ip_address INET,
    user_agent TEXT,
    geolocation JSONB,
    
    -- Metadata
    metadata JSONB DEFAULT '{}',
    
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for event queries
CREATE INDEX IF NOT EXISTS idx_signature_events_envelope_id ON signature_events(envelope_id);
CREATE INDEX IF NOT EXISTS idx_signature_events_event_type ON signature_events(event_type);
CREATE INDEX IF NOT EXISTS idx_signature_events_created_at ON signature_events(created_at);

-- ===================================
-- DOCUSIGN CONFIGURATION TABLE
-- ===================================

-- Store DocuSign API configuration
CREATE TABLE IF NOT EXISTS docusign_config (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    environment VARCHAR(20) NOT NULL CHECK (environment IN ('sandbox', 'production')),
    integration_key VARCHAR(255) NOT NULL,
    user_id VARCHAR(255) NOT NULL,
    account_id VARCHAR(255) NOT NULL,
    base_url VARCHAR(255) NOT NULL,
    redirect_url VARCHAR(255) NOT NULL,
    
    -- OAuth settings
    private_key TEXT, -- RSA private key for JWT
    access_token TEXT,
    refresh_token TEXT,
    token_expires_at TIMESTAMPTZ,
    
    -- Settings
    is_active BOOLEAN DEFAULT true,
    webhook_url TEXT,
    webhook_secret VARCHAR(255),
    
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- FUNCTIONS AND TRIGGERS
-- ===================================

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_docusign_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_document_templates_updated_at 
    BEFORE UPDATE ON document_templates
    FOR EACH ROW 
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_docusign_envelopes_updated_at 
    BEFORE UPDATE ON docusign_envelopes
    FOR EACH ROW 
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_envelope_documents_updated_at 
    BEFORE UPDATE ON envelope_documents
    FOR EACH ROW 
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_envelope_recipients_updated_at 
    BEFORE UPDATE ON envelope_recipients
    FOR EACH ROW 
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_docusign_config_updated_at 
    BEFORE UPDATE ON docusign_config
    FOR EACH ROW 
    EXECUTE FUNCTION update_docusign_updated_at_column();

-- ===================================
-- HELPER FUNCTIONS
-- ===================================

-- Function to get envelope status summary
CREATE OR REPLACE FUNCTION get_envelope_status_summary(envelope_uuid UUID)
RETURNS JSONB AS $$
DECLARE
    envelope_record RECORD;
    recipients_summary JSONB;
    documents_summary JSONB;
BEGIN
    -- Get envelope details
    SELECT * INTO envelope_record FROM docusign_envelopes WHERE id = envelope_uuid;
    
    IF envelope_record IS NULL THEN
        RETURN jsonb_build_object('error', 'Envelope not found');
    END IF;
    
    -- Get recipients summary
    SELECT jsonb_agg(
        jsonb_build_object(
            'name', name,
            'email', email,
            'status', status,
            'signed_at', signed_at
        )
    ) INTO recipients_summary
    FROM envelope_recipients 
    WHERE envelope_id = envelope_uuid;
    
    -- Get documents summary
    SELECT jsonb_agg(
        jsonb_build_object(
            'name', name,
            'is_signed', is_signed,
            'signed_at', signed_at
        )
    ) INTO documents_summary
    FROM envelope_documents 
    WHERE envelope_id = envelope_uuid;
    
    RETURN jsonb_build_object(
        'envelope_id', envelope_record.envelope_id,
        'status', envelope_record.status,
        'subject', envelope_record.subject,
        'sent_at', envelope_record.sent_at,
        'completed_at', envelope_record.completed_at,
        'recipients', COALESCE(recipients_summary, '[]'::jsonb),
        'documents', COALESCE(documents_summary, '[]'::jsonb)
    );
END;
$$ LANGUAGE plpgsql;

-- Function to log signature events
CREATE OR REPLACE FUNCTION log_signature_event(
    envelope_uuid UUID,
    recipient_uuid UUID,
    event_type_param VARCHAR(50),
    description_param TEXT DEFAULT NULL,
    ip_address_param INET DEFAULT NULL,
    metadata_param JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
    event_id UUID;
BEGIN
    INSERT INTO signature_events (
        envelope_id,
        recipient_id,
        event_type,
        description,
        ip_address,
        metadata
    ) VALUES (
        envelope_uuid,
        recipient_uuid,
        event_type_param,
        description_param,
        ip_address_param,
        metadata_param
    ) RETURNING id INTO event_id;
    
    RETURN event_id;
END;
$$ LANGUAGE plpgsql;

-- ===================================
-- ROW LEVEL SECURITY (RLS)
-- ===================================

-- Enable RLS on sensitive tables
ALTER TABLE docusign_envelopes ENABLE ROW LEVEL SECURITY;
ALTER TABLE envelope_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE envelope_recipients ENABLE ROW LEVEL SECURITY;
ALTER TABLE signature_events ENABLE ROW LEVEL SECURITY;

-- Users can access envelopes for their claims
CREATE POLICY "Users can access their claim envelopes" ON docusign_envelopes
    FOR ALL USING (
        claim_id IN (
            SELECT id FROM claims 
            WHERE assigned_agent_id = auth.uid()
            OR created_by = auth.uid()
        )
    );

-- Admins can access all envelopes
CREATE POLICY "Admins can access all envelopes" ON docusign_envelopes
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users 
            WHERE id = auth.uid() 
            AND role = 'admin'
        )
    );

-- Similar policies for related tables
CREATE POLICY "Users can access envelope documents" ON envelope_documents
    FOR ALL USING (
        envelope_id IN (
            SELECT id FROM docusign_envelopes
            WHERE claim_id IN (
                SELECT id FROM claims 
                WHERE assigned_agent_id = auth.uid()
                OR created_by = auth.uid()
            )
        )
    );

-- ===================================
-- SAMPLE DATA (for development)
-- ===================================

-- Insert default document templates
INSERT INTO document_templates (name, description, template_type, placeholders, signature_fields, created_by) 
VALUES 
    (
        'Standard Asset Recovery Agreement',
        'Standard agreement for asset recovery services',
        'asset_recovery_agreement',
        '[{"name": "claimant_name", "type": "text"}, {"name": "property_address", "type": "text"}, {"name": "amount", "type": "currency"}]'::jsonb,
        '[{"name": "claimant_signature", "anchor": "/sn1/"}, {"name": "date_signed", "anchor": "/ds1/"}]'::jsonb,
        (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
    ),
    (
        'Power of Attorney Form',
        'Legal power of attorney for asset recovery',
        'power_of_attorney',
        '[{"name": "claimant_name", "type": "text"}, {"name": "agent_name", "type": "text"}]'::jsonb,
        '[{"name": "claimant_signature", "anchor": "/sn1/"}, {"name": "witness_signature", "anchor": "/sn2/"}]'::jsonb,
        (SELECT id FROM users WHERE role = 'admin' LIMIT 1)
    )
ON CONFLICT DO NOTHING;

-- Insert sample DocuSign configuration (sandbox)
INSERT INTO docusign_config (
    environment,
    integration_key,
    user_id,
    account_id,
    base_url,
    redirect_url,
    webhook_url
) VALUES (
    'sandbox',
    'your-integration-key-here',
    'your-user-id-here',
    'your-account-id-here',
    'https://demo.docusign.net/restapi',
    'http://localhost:3005/docusign/callback',
    'http://localhost:3005/api/docusign/webhook'
) ON CONFLICT DO NOTHING;

-- ===================================
-- COMMENTS
-- ===================================

COMMENT ON TABLE document_templates IS 'Reusable document templates for electronic signatures';
COMMENT ON TABLE docusign_envelopes IS 'DocuSign envelope tracking and status';
COMMENT ON TABLE envelope_documents IS 'Individual documents within DocuSign envelopes';
COMMENT ON TABLE envelope_recipients IS 'Recipients and signers for DocuSign envelopes';
COMMENT ON TABLE signature_events IS 'Audit trail for signature-related events';
COMMENT ON TABLE docusign_config IS 'DocuSign API configuration and credentials';

COMMENT ON FUNCTION get_envelope_status_summary(UUID) IS 'Returns comprehensive status summary for an envelope';
COMMENT ON FUNCTION log_signature_event(UUID, UUID, VARCHAR, TEXT, INET, JSONB) IS 'Logs signature events for audit purposes';

-- ===================================
-- STRIPE INTEGRATION SCHEMA
-- ===================================

-- Stripe customers table
CREATE TABLE IF NOT EXISTS stripe_customers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_customer_id VARCHAR(255) UNIQUE NOT NULL,
    email VARCHAR(255) NOT NULL,
    name VARCHAR(255),
    phone VARCHAR(50),
    address JSONB,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),

    UNIQUE(user_id)
);

-- Stripe subscriptions table
CREATE TABLE IF NOT EXISTS stripe_subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_subscription_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    status VARCHAR(50) NOT NULL CHECK (status IN (
        'active', 'past_due', 'canceled', 'incomplete', 'trialing', 'unpaid'
    )),
    plan_id VARCHAR(255) NOT NULL,
    plan_name VARCHAR(255),
    amount INTEGER NOT NULL, -- in cents
    currency VARCHAR(3) DEFAULT 'usd',
    interval_type VARCHAR(20) CHECK (interval_type IN ('month', 'year')),
    current_period_start TIMESTAMPTZ,
    current_period_end TIMESTAMPTZ,
    trial_start TIMESTAMPTZ,
    trial_end TIMESTAMPTZ,
    cancel_at_period_end BOOLEAN DEFAULT false,
    canceled_at TIMESTAMPTZ,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Commission payments table
CREATE TABLE IF NOT EXISTS commission_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    agent_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    claim_id UUID NOT NULL REFERENCES claims(id) ON DELETE CASCADE,
    amount DECIMAL(12,2) NOT NULL,
    percentage DECIMAL(5,2) NOT NULL,
    status VARCHAR(50) NOT NULL DEFAULT 'pending' CHECK (status IN (
        'pending', 'processing', 'paid', 'failed', 'canceled'
    )),
    stripe_payment_intent_id VARCHAR(255),
    payment_method VARCHAR(50) DEFAULT 'ach' CHECK (payment_method IN ('ach', 'wire', 'check')),
    paid_at TIMESTAMPTZ,
    failed_at TIMESTAMPTZ,
    failure_reason TEXT,
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Payment methods table
CREATE TABLE IF NOT EXISTS stripe_payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_payment_method_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    type VARCHAR(50) NOT NULL,
    card_brand VARCHAR(50),
    card_last4 VARCHAR(4),
    card_exp_month INTEGER,
    card_exp_year INTEGER,
    billing_details JSONB,
    is_default BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Invoices table
CREATE TABLE IF NOT EXISTS stripe_invoices (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    stripe_invoice_id VARCHAR(255) UNIQUE NOT NULL,
    stripe_customer_id VARCHAR(255) NOT NULL,
    stripe_subscription_id VARCHAR(255),
    amount_due INTEGER NOT NULL,
    amount_paid INTEGER DEFAULT 0,
    currency VARCHAR(3) DEFAULT 'usd',
    status VARCHAR(50) NOT NULL CHECK (status IN (
        'draft', 'open', 'paid', 'void', 'uncollectible'
    )),
    due_date TIMESTAMPTZ,
    paid_at TIMESTAMPTZ,
    invoice_pdf TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Stripe webhook events table
CREATE TABLE IF NOT EXISTS stripe_webhook_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    stripe_event_id VARCHAR(255) UNIQUE NOT NULL,
    event_type VARCHAR(100) NOT NULL,
    processed BOOLEAN DEFAULT false,
    processed_at TIMESTAMPTZ,
    data JSONB NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for Stripe tables
CREATE INDEX IF NOT EXISTS idx_stripe_customers_user_id ON stripe_customers(user_id);
CREATE INDEX IF NOT EXISTS idx_stripe_customers_stripe_id ON stripe_customers(stripe_customer_id);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_user_id ON stripe_subscriptions(user_id);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_stripe_id ON stripe_subscriptions(stripe_subscription_id);
CREATE INDEX IF NOT EXISTS idx_stripe_subscriptions_status ON stripe_subscriptions(status);
CREATE INDEX IF NOT EXISTS idx_commission_payments_agent_id ON commission_payments(agent_id);
CREATE INDEX IF NOT EXISTS idx_commission_payments_claim_id ON commission_payments(claim_id);
CREATE INDEX IF NOT EXISTS idx_commission_payments_status ON commission_payments(status);
CREATE INDEX IF NOT EXISTS idx_stripe_payment_methods_user_id ON stripe_payment_methods(user_id);
CREATE INDEX IF NOT EXISTS idx_stripe_invoices_user_id ON stripe_invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_stripe_webhook_events_type ON stripe_webhook_events(event_type);
CREATE INDEX IF NOT EXISTS idx_stripe_webhook_events_processed ON stripe_webhook_events(processed);

-- Triggers for Stripe tables
CREATE TRIGGER update_stripe_customers_updated_at
    BEFORE UPDATE ON stripe_customers
    FOR EACH ROW
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_stripe_subscriptions_updated_at
    BEFORE UPDATE ON stripe_subscriptions
    FOR EACH ROW
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_commission_payments_updated_at
    BEFORE UPDATE ON commission_payments
    FOR EACH ROW
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_stripe_payment_methods_updated_at
    BEFORE UPDATE ON stripe_payment_methods
    FOR EACH ROW
    EXECUTE FUNCTION update_docusign_updated_at_column();

CREATE TRIGGER update_stripe_invoices_updated_at
    BEFORE UPDATE ON stripe_invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_docusign_updated_at_column();

-- RLS policies for Stripe tables
ALTER TABLE stripe_customers ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE commission_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE stripe_invoices ENABLE ROW LEVEL SECURITY;

-- Users can access their own Stripe data
CREATE POLICY "Users can access their own Stripe customer data" ON stripe_customers
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can access their own subscription data" ON stripe_subscriptions
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can access their own payment methods" ON stripe_payment_methods
    FOR ALL USING (user_id = auth.uid());

CREATE POLICY "Users can access their own invoices" ON stripe_invoices
    FOR ALL USING (user_id = auth.uid());

-- Agents can view their own commissions
CREATE POLICY "Agents can view their own commissions" ON commission_payments
    FOR SELECT USING (agent_id = auth.uid());

-- Finance and admin can manage all commissions
CREATE POLICY "Finance and admin can manage commissions" ON commission_payments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM users
            WHERE id = auth.uid()
            AND role IN ('admin', 'finance')
        )
    );

-- Helper functions for Stripe integration
CREATE OR REPLACE FUNCTION get_user_subscription_status(user_id_param UUID)
RETURNS JSONB AS $$
DECLARE
    subscription_record RECORD;
BEGIN
    SELECT * INTO subscription_record
    FROM stripe_subscriptions
    WHERE user_id = user_id_param
    AND status IN ('active', 'trialing')
    ORDER BY created_at DESC
    LIMIT 1;

    IF subscription_record IS NULL THEN
        RETURN jsonb_build_object(
            'has_subscription', false,
            'status', 'none'
        );
    END IF;

    RETURN jsonb_build_object(
        'has_subscription', true,
        'status', subscription_record.status,
        'plan_name', subscription_record.plan_name,
        'amount', subscription_record.amount,
        'current_period_end', subscription_record.current_period_end,
        'cancel_at_period_end', subscription_record.cancel_at_period_end
    );
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION calculate_agent_commissions(agent_id_param UUID, start_date DATE DEFAULT NULL, end_date DATE DEFAULT NULL)
RETURNS JSONB AS $$
DECLARE
    total_earned DECIMAL(12,2) := 0;
    total_pending DECIMAL(12,2) := 0;
    total_paid DECIMAL(12,2) := 0;
    commission_count INTEGER := 0;
BEGIN
    SELECT
        COUNT(*),
        COALESCE(SUM(amount), 0),
        COALESCE(SUM(CASE WHEN status = 'pending' THEN amount ELSE 0 END), 0),
        COALESCE(SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END), 0)
    INTO commission_count, total_earned, total_pending, total_paid
    FROM commission_payments
    WHERE agent_id = agent_id_param
    AND (start_date IS NULL OR created_at::date >= start_date)
    AND (end_date IS NULL OR created_at::date <= end_date);

    RETURN jsonb_build_object(
        'total_earned', total_earned,
        'total_pending', total_pending,
        'total_paid', total_paid,
        'commission_count', commission_count
    );
END;
$$ LANGUAGE plpgsql;

-- Comments for Stripe tables
COMMENT ON TABLE stripe_customers IS 'Stripe customer records linked to users';
COMMENT ON TABLE stripe_subscriptions IS 'User subscription data from Stripe';
COMMENT ON TABLE commission_payments IS 'Agent commission payments and tracking';
COMMENT ON TABLE stripe_payment_methods IS 'User payment methods stored in Stripe';
COMMENT ON TABLE stripe_invoices IS 'Invoice records from Stripe';
COMMENT ON TABLE stripe_webhook_events IS 'Stripe webhook events for processing';

COMMENT ON FUNCTION get_user_subscription_status(UUID) IS 'Returns subscription status for a user';
COMMENT ON FUNCTION calculate_agent_commissions(UUID, DATE, DATE) IS 'Calculates commission totals for an agent';
