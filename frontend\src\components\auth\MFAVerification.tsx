import React, { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Smartphone, 
  Key, 
  AlertTriangle, 
  RefreshCw,
  ArrowLeft
} from 'lucide-react'
import { mfaService, type MFAFactor, type MFAChallenge } from '@/services/mfaService'

interface MFAVerificationProps {
  userId: string
  onSuccess: (tokens: { access_token: string; refresh_token: string }) => void
  onCancel: () => void
  onBackupCodeUsed?: () => void
}

export const MFAVerification: React.FC<MFAVerificationProps> = ({
  userId,
  onSuccess,
  onCancel,
  onBackupCodeUsed
}) => {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCode, setBackupCode] = useState('')
  const [activeTab, setActiveTab] = useState<'totp' | 'backup'>('totp')
  
  // MFA data
  const [factors, setFactors] = useState<MFAFactor[]>([])
  const [currentChallenge, setCurrentChallenge] = useState<MFAChallenge | null>(null)
  const [selectedFactor, setSelectedFactor] = useState<MFAFactor | null>(null)

  useEffect(() => {
    if (userId) {
      mfaService.setUserId(userId)
      loadMFAFactors()
    }
  }, [userId])

  const loadMFAFactors = async () => {
    try {
      const { data, error } = await mfaService.getMFAFactors()
      
      if (error) {
        setError('Failed to load authentication factors')
        return
      }
      
      setFactors(data)
      
      // Auto-select the first verified factor
      const verifiedFactor = data.find(f => f.status === 'verified')
      if (verifiedFactor) {
        setSelectedFactor(verifiedFactor)
        await createChallenge(verifiedFactor.id)
      }
    } catch (error) {
      setError('Failed to load authentication factors')
      console.error('Error loading MFA factors:', error)
    }
  }

  const createChallenge = async (factorId: string) => {
    try {
      const { data, error } = await mfaService.challengeMFA(factorId)
      
      if (error) {
        setError('Failed to create authentication challenge')
        return
      }
      
      setCurrentChallenge(data)
    } catch (error) {
      setError('Failed to create authentication challenge')
      console.error('Error creating MFA challenge:', error)
    }
  }

  const verifyTOTP = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      setError('Please enter a 6-digit verification code')
      return
    }
    
    if (!selectedFactor || !currentChallenge) {
      setError('Authentication not properly initialized')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      const { data, error } = await mfaService.verifyMFA(
        selectedFactor.id,
        currentChallenge.id,
        verificationCode
      )
      
      if (error) {
        setError(error.message || 'Invalid verification code')
        return
      }
      
      if (data) {
        onSuccess({
          access_token: data.access_token,
          refresh_token: data.refresh_token
        })
      }
    } catch (error) {
      setError('Verification failed')
      console.error('MFA verification error:', error)
    } finally {
      setLoading(false)
    }
  }

  const verifyBackupCodeHandler = async () => {
    if (!backupCode || backupCode.length < 6) {
      setError('Please enter a valid backup code')
      return
    }
    
    setLoading(true)
    setError(null)
    
    try {
      const { success, error } = await mfaService.verifyBackupCode(backupCode)
      
      if (!success) {
        setError(error || 'Invalid backup code')
        return
      }
      
      // Backup code verification successful
      if (onBackupCodeUsed) {
        onBackupCodeUsed()
      }
      
      // For demo purposes, we'll simulate successful authentication
      onSuccess({
        access_token: 'backup-access-token',
        refresh_token: 'backup-refresh-token'
      })
    } catch (error) {
      setError('Backup code verification failed')
      console.error('Backup code verification error:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleKeyPress = (e: React.KeyboardEvent, action: () => void) => {
    if (e.key === 'Enter') {
      action()
    }
  }

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4">
          <Shield className="h-6 w-6 text-blue-600" />
        </div>
        <CardTitle>Two-Factor Authentication</CardTitle>
        <CardDescription>
          Enter your authentication code to continue
        </CardDescription>
      </CardHeader>
      
      <CardContent>
        {error && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'totp' | 'backup')}>
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="totp" className="flex items-center space-x-2">
              <Smartphone className="h-4 w-4" />
              <span>Authenticator</span>
            </TabsTrigger>
            <TabsTrigger value="backup" className="flex items-center space-x-2">
              <Key className="h-4 w-4" />
              <span>Backup Code</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="totp" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Open your authenticator app and enter the 6-digit code
                </p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Verification Code</label>
                <Input
                  type="text"
                  placeholder="000000"
                  value={verificationCode}
                  onChange={(e) => setVerificationCode(e.target.value.replace(/\D/g, '').slice(0, 6))}
                  onKeyPress={(e) => handleKeyPress(e, verifyTOTP)}
                  maxLength={6}
                  className="text-center text-lg tracking-widest"
                  autoFocus
                />
              </div>
              
              <Button 
                onClick={verifyTOTP} 
                disabled={loading || verificationCode.length !== 6}
                className="w-full"
              >
                {loading && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                Verify Code
              </Button>
            </div>
          </TabsContent>

          <TabsContent value="backup" className="space-y-4 mt-4">
            <div className="space-y-4">
              <div className="text-center">
                <p className="text-sm text-gray-600">
                  Enter one of your backup codes if you don't have access to your authenticator app
                </p>
              </div>
              
              <div className="space-y-2">
                <label className="text-sm font-medium">Backup Code</label>
                <Input
                  type="text"
                  placeholder="XXXXXXXX"
                  value={backupCode}
                  onChange={(e) => setBackupCode(e.target.value.toUpperCase().replace(/[^A-Z0-9]/g, '').slice(0, 8))}
                  onKeyPress={(e) => handleKeyPress(e, verifyBackupCodeHandler)}
                  maxLength={8}
                  className="text-center text-lg tracking-widest font-mono"
                />
              </div>
              
              <Button 
                onClick={verifyBackupCodeHandler} 
                disabled={loading || backupCode.length < 6}
                className="w-full"
              >
                {loading && <RefreshCw className="mr-2 h-4 w-4 animate-spin" />}
                Verify Backup Code
              </Button>
              
              <Alert>
                <AlertTriangle className="h-4 w-4" />
                <AlertDescription className="text-xs">
                  Each backup code can only be used once. Make sure to generate new codes after using this one.
                </AlertDescription>
              </Alert>
            </div>
          </TabsContent>
        </Tabs>

        <div className="mt-6 pt-4 border-t">
          <Button 
            variant="outline" 
            onClick={onCancel}
            className="w-full"
            disabled={loading}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Login
          </Button>
        </div>

        {/* Factor selection (if multiple factors) */}
        {factors.length > 1 && (
          <div className="mt-4 pt-4 border-t">
            <label className="text-sm font-medium mb-2 block">Authentication Method</label>
            <div className="space-y-2">
              {factors.map((factor) => (
                <button
                  key={factor.id}
                  onClick={() => {
                    setSelectedFactor(factor)
                    createChallenge(factor.id)
                  }}
                  className={`w-full p-3 text-left border rounded-lg transition-colors ${
                    selectedFactor?.id === factor.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-center space-x-3">
                    <Smartphone className="h-5 w-5 text-gray-500" />
                    <div>
                      <div className="font-medium">{factor.friendly_name}</div>
                      <div className="text-sm text-gray-500">
                        Added {new Date(factor.created_at).toLocaleDateString()}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
