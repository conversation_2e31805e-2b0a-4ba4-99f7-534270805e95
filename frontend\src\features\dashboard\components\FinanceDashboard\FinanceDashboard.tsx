import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  DollarSign, 
  TrendingUp, 
  TrendingDown,
  CreditCard,
  Banknote,
  Calculator,
  PieChart,
  BarChart3,
  Download,
  FileText,
  Calendar,
  Filter,
  AlertCircle,
  CheckCircle,
  Clock,
  Receipt,
  Users,
  Target
} from 'lucide-react';

interface FinancialMetrics {
  totalRevenue: number;
  monthlyRevenue: number;
  revenueGrowth: number;
  totalCommissions: number;
  pendingPayments: number;
  paidCommissions: number;
  avgDealSize: number;
  recoveryRate: number;
}

interface CommissionData {
  id: string;
  agentName: string;
  claimId: string;
  claimantName: string;
  recoveryAmount: number;
  commissionRate: number;
  commissionAmount: number;
  status: 'pending' | 'approved' | 'paid' | 'on_hold';
  dateEarned: string;
  datePaid?: string;
}

interface RevenueByMonth {
  month: string;
  revenue: number;
  commissions: number;
  netProfit: number;
}

interface PaymentAlert {
  id: string;
  type: 'overdue_commission' | 'large_payment' | 'payment_failed' | 'approval_needed';
  description: string;
  amount: number;
  agentName?: string;
  daysOverdue?: number;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export const FinanceDashboard: React.FC = () => {
  const [metrics] = useState<FinancialMetrics>({
    totalRevenue: 3450000,
    monthlyRevenue: 425000,
    revenueGrowth: 18.5,
    totalCommissions: 862500,
    pendingPayments: 125000,
    paidCommissions: 737500,
    avgDealSize: 15500,
    recoveryRate: 78.5
  });

  const [commissions] = useState<CommissionData[]>([
    {
      id: '1',
      agentName: 'Sarah Johnson',
      claimId: 'CL-2024-001',
      claimantName: 'John Smith',
      recoveryAmount: 45000,
      commissionRate: 25,
      commissionAmount: 11250,
      status: 'pending',
      dateEarned: '2024-12-10',
    },
    {
      id: '2',
      agentName: 'Mike Chen',
      claimId: 'CL-2024-034',
      claimantName: 'Corporate LLC',
      recoveryAmount: 85000,
      commissionRate: 30,
      commissionAmount: 25500,
      status: 'approved',
      dateEarned: '2024-12-08',
    },
    {
      id: '3',
      agentName: 'Emily Davis',
      claimId: 'CL-2024-067',
      claimantName: 'Mary Wilson',
      recoveryAmount: 28000,
      commissionRate: 25,
      commissionAmount: 7000,
      status: 'paid',
      dateEarned: '2024-12-05',
      datePaid: '2024-12-12'
    }
  ]);

  const [revenueData] = useState<RevenueByMonth[]>([
    { month: 'Aug', revenue: 380000, commissions: 95000, netProfit: 285000 },
    { month: 'Sep', revenue: 420000, commissions: 105000, netProfit: 315000 },
    { month: 'Oct', revenue: 395000, commissions: 98750, netProfit: 296250 },
    { month: 'Nov', revenue: 465000, commissions: 116250, netProfit: 348750 },
    { month: 'Dec', revenue: 425000, commissions: 106250, netProfit: 318750 }
  ]);

  const [paymentAlerts] = useState<PaymentAlert[]>([
    {
      id: '1',
      type: 'overdue_commission',
      description: 'Commission payment overdue',
      amount: 15000,
      agentName: 'David Rodriguez',
      daysOverdue: 7,
      priority: 'high'
    },
    {
      id: '2',
      type: 'large_payment',
      description: 'Large recovery requiring approval',
      amount: 95000,
      agentName: 'Sarah Johnson',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'approval_needed',
      description: 'Commission rate adjustment pending',
      amount: 8500,
      agentName: 'Emily Davis',
      priority: 'medium'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'paid': return 'bg-green-100 text-green-800';
      case 'approved': return 'bg-blue-100 text-blue-800';
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'on_hold': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'overdue_commission': return <Clock className="h-4 w-4 text-red-600" />;
      case 'large_payment': return <DollarSign className="h-4 w-4 text-green-600" />;
      case 'payment_failed': return <AlertCircle className="h-4 w-4 text-red-600" />;
      case 'approval_needed': return <CheckCircle className="h-4 w-4 text-blue-600" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    });
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Financial Dashboard</h1>
          <p className="text-gray-600">Revenue tracking, commission management, and financial analytics</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Calendar className="h-4 w-4 mr-2" />
            Date Range
          </Button>
          <Button variant="outline">
            <Download className="h-4 w-4 mr-2" />
            Export Report
          </Button>
          <Button>
            <Calculator className="h-4 w-4 mr-2" />
            Calculate Commissions
          </Button>
        </div>
      </div>

      {/* Key Financial Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Revenue */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
            <DollarSign className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.totalRevenue)}</div>
            <div className="flex items-center mt-2">
              <TrendingUp className="h-4 w-4 text-green-500 mr-1" />
              <p className="text-sm text-green-600">+{metrics.revenueGrowth}% this quarter</p>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Revenue */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Monthly Revenue</CardTitle>
            <BarChart3 className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.monthlyRevenue)}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                <span className="text-gray-600">This month</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Commissions */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Commissions</CardTitle>
            <CreditCard className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.totalCommissions)}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{formatCurrency(metrics.paidCommissions)} Paid</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-yellow-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{formatCurrency(metrics.pendingPayments)} Pending</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Average Deal Size */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Avg Deal Size</CardTitle>
            <Target className="h-5 w-5 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{formatCurrency(metrics.avgDealSize)}</div>
            <div className="mt-2">
              <Progress value={metrics.recoveryRate} className="h-2" />
            </div>
            <p className="text-sm text-gray-600 mt-1">{metrics.recoveryRate}% recovery rate</p>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Tabs */}
      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview">Revenue Overview</TabsTrigger>
          <TabsTrigger value="commissions">Commissions</TabsTrigger>
          <TabsTrigger value="payments">Payments</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
        </TabsList>

        {/* Revenue Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Trend Chart */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                  Revenue Trends
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {revenueData.map((data, index) => (
                    <div key={data.month} className="flex items-center justify-between">
                      <div className="w-16 text-sm font-medium">{data.month}</div>
                      <div className="flex-1 mx-4">
                        <div className="flex space-x-2">
                          <div className="flex-1">
                            <div className="h-6 bg-blue-200 rounded relative">
                              <div 
                                className="h-full bg-blue-600 rounded"
                                style={{ width: `${(data.revenue / 500000) * 100}%` }}
                              ></div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div className="w-24 text-right">
                        <div className="text-sm font-medium">{formatCurrency(data.revenue)}</div>
                        <div className="text-xs text-gray-500">Revenue</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Payment Alerts */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center">
                    <AlertCircle className="h-5 w-5 mr-2 text-orange-600" />
                    Payment Alerts
                  </div>
                  <Badge variant="destructive">{paymentAlerts.length}</Badge>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {paymentAlerts.map((alert) => (
                    <div key={alert.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-center space-x-3">
                        <div className="p-2 bg-gray-100 rounded-lg">
                          {getAlertIcon(alert.type)}
                        </div>
                        <div>
                          <div className="font-medium text-sm">{alert.description}</div>
                          <div className="text-xs text-gray-500">
                            {alert.agentName && `${alert.agentName} • `}
                            {formatCurrency(alert.amount)}
                            {alert.daysOverdue && ` • ${alert.daysOverdue} days overdue`}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge className={`text-xs ${getPriorityColor(alert.priority)}`}>
                          {alert.priority}
                        </Badge>
                        <Button size="sm" variant="outline">
                          Review
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Commissions Tab */}
        <TabsContent value="commissions" className="space-y-6">
          <Card className="border-0 shadow-lg">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center">
                  <CreditCard className="h-5 w-5 mr-2 text-purple-600" />
                  Commission Tracking
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4 mr-2" />
                    Filter
                  </Button>
                  <Button size="sm">
                    <Calculator className="h-4 w-4 mr-2" />
                    Calculate All
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {commissions.map((commission) => (
                  <div key={commission.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full flex items-center justify-center text-white font-medium">
                        {commission.agentName.split(' ').map(n => n[0]).join('')}
                      </div>
                      <div>
                        <div className="font-medium">{commission.agentName}</div>
                        <div className="text-sm text-gray-500">
                          {commission.claimantName} • {commission.claimId}
                        </div>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-6 text-center">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{formatCurrency(commission.recoveryAmount)}</div>
                        <div className="text-xs text-gray-500">Recovery Amount</div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-blue-600">{commission.commissionRate}%</div>
                        <div className="text-xs text-gray-500">Commission Rate</div>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-green-600">{formatCurrency(commission.commissionAmount)}</div>
                        <div className="text-xs text-gray-500">Commission Amount</div>
                      </div>
                      <div>
                        <Badge className={`text-xs ${getStatusColor(commission.status)}`}>
                          {commission.status.replace('_', ' ')}
                        </Badge>
                        <div className="text-xs text-gray-500 mt-1">
                          {formatDate(commission.dateEarned)}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Payments Tab */}
        <TabsContent value="payments" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Payment Processing */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Banknote className="h-5 w-5 mr-2 text-green-600" />
                  Payment Processing
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <div className="font-medium text-green-900">Ready for Payment</div>
                      <div className="text-sm text-green-700">3 approved commissions</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-900">{formatCurrency(45750)}</div>
                      <Button size="sm" className="bg-green-600 hover:bg-green-700 mt-2">
                        Process Payment
                      </Button>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <div className="font-medium text-yellow-900">Pending Approval</div>
                      <div className="text-sm text-yellow-700">5 commissions awaiting review</div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-yellow-900">{formatCurrency(79250)}</div>
                      <Button size="sm" variant="outline" className="mt-2">
                        Review Queue
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Payment History */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Receipt className="h-5 w-5 mr-2 text-blue-600" />
                  Recent Payments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {commissions.filter(c => c.status === 'paid').map((payment) => (
                    <div key={payment.id} className="flex items-center justify-between p-3 border rounded-lg">
                      <div>
                        <div className="font-medium text-sm">{payment.agentName}</div>
                        <div className="text-xs text-gray-500">
                          {payment.claimId} • Paid {payment.datePaid && formatDate(payment.datePaid)}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="font-medium text-green-600">{formatCurrency(payment.commissionAmount)}</div>
                        <Badge className="text-xs bg-green-100 text-green-800">Paid</Badge>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Revenue Analytics */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2 text-purple-600" />
                  Revenue Breakdown
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="h-64 flex items-center justify-center text-gray-500">
                  <div className="text-center">
                    <PieChart className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                    <p>Revenue breakdown chart</p>
                    <p className="text-sm mt-2">By state, agent, claim type</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Performance Metrics */}
            <Card className="border-0 shadow-lg">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-orange-600" />
                  Performance Metrics
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium">Recovery Rate</span>
                    <span className="text-sm text-gray-600">{metrics.recoveryRate}%</span>
                  </div>
                  <Progress value={metrics.recoveryRate} className="h-2" />
                  
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-sm font-medium">Commission Efficiency</span>
                    <span className="text-sm text-gray-600">85%</span>
                  </div>
                  <Progress value={85} className="h-2" />
                  
                  <div className="flex items-center justify-between mt-4">
                    <span className="text-sm font-medium">Payment Turnaround</span>
                    <span className="text-sm text-gray-600">92%</span>
                  </div>
                  <Progress value={92} className="h-2" />
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 