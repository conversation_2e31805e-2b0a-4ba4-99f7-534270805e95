import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Settings, 
  Key, 
  Globe, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Save,
  TestTube,
  ExternalLink,
  RefreshCw
} from 'lucide-react'
import { docuSignService, type DocuSignConfig } from '@/services/docusignService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

export const DocuSignConfigComponent: React.FC = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Configuration state
  const [config, setConfig] = useState<DocuSignConfig>({
    integrationKey: '',
    userId: '',
    accountId: '',
    baseUrl: 'https://demo.docusign.net/restapi',
    redirectUrl: 'http://localhost:3005/docusign/callback'
  })
  
  const [environment, setEnvironment] = useState<'sandbox' | 'production'>('sandbox')
  const [webhookUrl, setWebhookUrl] = useState('http://localhost:3005/api/docusign/webhook')
  const [isConfigured, setIsConfigured] = useState(false)

  useEffect(() => {
    loadConfiguration()
  }, [])

  const loadConfiguration = async () => {
    setLoading(true)
    
    try {
      // In a real implementation, this would load from your database
      // For now, we'll check if configuration exists in localStorage
      const savedConfig = localStorage.getItem('docusign_config')
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig)
        setConfig(parsedConfig)
        setEnvironment(parsedConfig.environment || 'sandbox')
        setWebhookUrl(parsedConfig.webhookUrl || webhookUrl)
        setIsConfigured(true)
        
        // Initialize the service
        docuSignService.initialize(parsedConfig)
      }
    } catch (error) {
      console.error('Error loading DocuSign configuration:', error)
      setError('Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  const saveConfiguration = async () => {
    setLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate required fields
      if (!config.integrationKey || !config.userId || !config.accountId) {
        setError('Please fill in all required fields')
        return
      }

      // Update base URL based on environment
      const updatedConfig = {
        ...config,
        baseUrl: environment === 'production' 
          ? 'https://www.docusign.net/restapi'
          : 'https://demo.docusign.net/restapi',
        environment,
        webhookUrl
      }

      // Save to localStorage (in real app, save to database)
      localStorage.setItem('docusign_config', JSON.stringify(updatedConfig))
      
      // Initialize the service
      docuSignService.initialize(updatedConfig)
      
      setConfig(updatedConfig)
      setIsConfigured(true)
      setSuccess('Configuration saved successfully')
      
      toast({
        title: "Configuration Saved",
        description: "DocuSign integration has been configured successfully.",
      })
    } catch (error) {
      setError('Failed to save configuration')
      console.error('Error saving DocuSign configuration:', error)
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    setTesting(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Test the DocuSign connection by creating a test envelope
      const testEnvelope = {
        documents: [{
          id: 'test-doc',
          name: 'Test Document.pdf',
          content: 'JVBERi0xLjQKJcOkw7zDtsO8CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNC=', // Minimal PDF
          contentType: 'application/pdf'
        }],
        signers: [{
          email: user?.email || '<EMAIL>',
          name: user?.name || 'Test User',
          recipientId: '1',
          routingOrder: 1
        }],
        subject: 'DocuSign Connection Test',
        message: 'This is a test envelope to verify DocuSign integration.',
        status: 'created' as const
      }

      const { data, error } = await docuSignService.createEnvelope(testEnvelope)
      
      if (error) {
        setError(`Connection test failed: ${error.message}`)
        return
      }
      
      if (data) {
        setSuccess(`Connection test successful! Test envelope created: ${data.envelopeId}`)
        
        toast({
          title: "Connection Test Successful",
          description: "DocuSign integration is working correctly.",
        })
      }
    } catch (error) {
      setError('Connection test failed')
      console.error('DocuSign connection test error:', error)
    } finally {
      setTesting(false)
    }
  }

  const resetConfiguration = () => {
    setConfig({
      integrationKey: '',
      userId: '',
      accountId: '',
      baseUrl: 'https://demo.docusign.net/restapi',
      redirectUrl: 'http://localhost:3005/docusign/callback'
    })
    setEnvironment('sandbox')
    setWebhookUrl('http://localhost:3005/api/docusign/webhook')
    setIsConfigured(false)
    localStorage.removeItem('docusign_config')
    
    toast({
      title: "Configuration Reset",
      description: "DocuSign configuration has been cleared.",
    })
  }

  // Check if user has admin permissions
  if (user?.role !== 'admin') {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You need administrator privileges to configure DocuSign integration.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">DocuSign Configuration</h2>
          <p className="text-gray-600">Configure electronic signature integration</p>
        </div>
        <div className="flex items-center space-x-2">
          {isConfigured && (
            <Badge variant="default" className="flex items-center space-x-1">
              <CheckCircle className="h-3 w-3" />
              <span>Configured</span>
            </Badge>
          )}
        </div>
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="configuration" className="space-y-6">
        <TabsList>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        <TabsContent value="configuration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>API Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure your DocuSign API credentials and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Environment</label>
                  <select
                    value={environment}
                    onChange={(e) => setEnvironment(e.target.value as 'sandbox' | 'production')}
                    className="w-full p-2 border rounded-lg"
                  >
                    <option value="sandbox">Sandbox (Development)</option>
                    <option value="production">Production</option>
                  </select>
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Base URL</label>
                  <Input
                    value={config.baseUrl}
                    readOnly
                    className="bg-gray-50"
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Integration Key *</label>
                <Input
                  placeholder="Enter your DocuSign Integration Key"
                  value={config.integrationKey}
                  onChange={(e) => setConfig({ ...config, integrationKey: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Found in your DocuSign Developer Account under Apps & Keys
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">User ID *</label>
                  <Input
                    placeholder="Enter your DocuSign User ID"
                    value={config.userId}
                    onChange={(e) => setConfig({ ...config, userId: e.target.value })}
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Account ID *</label>
                  <Input
                    placeholder="Enter your DocuSign Account ID"
                    value={config.accountId}
                    onChange={(e) => setConfig({ ...config, accountId: e.target.value })}
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Redirect URL</label>
                <Input
                  placeholder="OAuth redirect URL"
                  value={config.redirectUrl}
                  onChange={(e) => setConfig({ ...config, redirectUrl: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  URL where users will be redirected after DocuSign authentication
                </p>
              </div>

              <div className="flex space-x-2">
                <Button 
                  onClick={saveConfiguration}
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Save Configuration
                </Button>
                <Button 
                  variant="outline"
                  onClick={resetConfiguration}
                  disabled={loading}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Setup Instructions</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">1</div>
                  <div>
                    <h4 className="font-medium">Create DocuSign Developer Account</h4>
                    <p className="text-sm text-gray-600">
                      Sign up for a free DocuSign Developer account at{' '}
                      <a href="https://developers.docusign.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        developers.docusign.com
                      </a>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                  <div>
                    <h4 className="font-medium">Create Integration</h4>
                    <p className="text-sm text-gray-600">
                      Create a new app integration and note down your Integration Key, User ID, and Account ID
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                  <div>
                    <h4 className="font-medium">Configure OAuth</h4>
                    <p className="text-sm text-gray-600">
                      Add the redirect URL to your DocuSign app's OAuth configuration
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                  <div>
                    <h4 className="font-medium">Test Integration</h4>
                    <p className="text-sm text-gray-600">
                      Use the testing tab to verify your configuration is working correctly
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TestTube className="h-5 w-5" />
                <span>Connection Testing</span>
              </CardTitle>
              <CardDescription>
                Test your DocuSign integration to ensure it's working correctly
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8">
                <TestTube className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Test DocuSign Connection</h3>
                <p className="text-gray-500 mb-6">
                  This will create a test envelope to verify your DocuSign integration is working properly.
                </p>
                
                <Button 
                  onClick={testConnection}
                  disabled={testing || !isConfigured}
                  size="lg"
                >
                  {testing ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <TestTube className="mr-2 h-4 w-4" />
                  )}
                  {testing ? 'Testing Connection...' : 'Test Connection'}
                </Button>
                
                {!isConfigured && (
                  <p className="text-sm text-red-600 mt-2">
                    Please configure DocuSign settings first
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Webhook Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure webhooks to receive real-time updates from DocuSign
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Webhook URL</label>
                <Input
                  placeholder="Enter webhook endpoint URL"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                />
                <p className="text-xs text-gray-500">
                  This URL will receive webhook notifications from DocuSign
                </p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Supported Events</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Envelope sent</li>
                  <li>• Envelope completed</li>
                  <li>• Recipient signed</li>
                  <li>• Envelope declined</li>
                  <li>• Envelope voided</li>
                </ul>
              </div>

              <Button variant="outline" className="w-full">
                <ExternalLink className="mr-2 h-4 w-4" />
                Configure in DocuSign Console
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
