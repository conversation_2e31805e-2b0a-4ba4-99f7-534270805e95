import { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Smartphone, 
  Download, 
  MapPin, 
  Camera, 
  Wifi, 
  WifiOff,
  RotateCcw,
  Battery,
  Navigation,
  FileText,
  Clock,
  CheckCircle,
  AlertTriangle,
  Settings,
  TrendingUp,
  Shield,
  Zap,
  Globe,
  Users,
  Upload,
  ScanLine,
  Route,
  Bell,
  Archive,
  Fingerprint
} from 'lucide-react';
import { MobileAppFeatures, SyncStatus, OfflineCapabilities, GpsTracking } from '@/types/mobile-app';
import { usePricingLogic } from '@/hooks/usePricingLogic';

interface EnhancedMobileAppProps {
  onUpgrade?: () => void;
}

export default function EnhancedMobileApp({ onUpgrade }: EnhancedMobileAppProps) {
  const { currentPlan, subscription } = usePricingLogic();
  const [activeTab, setActiveTab] = useState('overview');
  const [mobileFeatures, setMobileFeatures] = useState<MobileAppFeatures | null>(null);
  const [syncStatus, setSyncStatus] = useState<SyncStatus | null>(null);
  const [offlineCapabilities, setOfflineCapabilities] = useState<OfflineCapabilities | null>(null);

  // Mock data - would come from API in real implementation
  useEffect(() => {
    const mockSyncStatus: SyncStatus = {
      overall_status: 'synced',
      last_sync: new Date(Date.now() - 5 * 60 * 1000), // 5 minutes ago
      next_sync: new Date(Date.now() + 10 * 60 * 1000), // 10 minutes from now
      items_pending: 3,
      items_in_progress: 0,
      sync_progress_percentage: 100,
      estimated_completion: new Date(),
      connection_quality: 'excellent'
    };

    const mockOfflineCapabilities: OfflineCapabilities = {
      enabled: true,
      storage_limit_mb: 2048,
      current_usage_mb: 1567,
      offline_claims: [],
      cached_data: {
        claims_cached: 150,
        contacts_cached: 847,
        documents_cached: 1234,
        templates_cached: 45,
        last_full_sync: new Date(Date.now() - 2 * 60 * 60 * 1000),
        cache_expiry: new Date(Date.now() + 24 * 60 * 60 * 1000),
        auto_cleanup_enabled: true
      },
      sync_queue: [],
      conflict_resolution: {
        conflicts_detected: [],
        auto_resolution_enabled: true,
        manual_review_required: 0,
        resolution_strategy: 'server_wins'
      },
      offline_features: [
        {
          feature_name: 'Claim Management',
          available_offline: true,
          requires_sync: false,
          limitations: ['Cannot create new claims without sync'],
          data_requirements: ['Cached claim data', 'Contact information']
        },
        {
          feature_name: 'Document Scanning',
          available_offline: true,
          requires_sync: false,
          limitations: ['OCR processing limited without internet'],
          data_requirements: ['Camera permissions', 'Local storage']
        },
        {
          feature_name: 'GPS Tracking',
          available_offline: true,
          requires_sync: false,
          limitations: ['Location history sync delayed'],
          data_requirements: ['Location permissions', 'GPS enabled']
        }
      ]
    };

    setSyncStatus(mockSyncStatus);
    setOfflineCapabilities(mockOfflineCapabilities);
  }, []);

  const hasAccess = currentPlan && ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasAdvancedMobile = currentPlan && ['silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasPremiumMobile = currentPlan && ['gold', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasTeamMobile = currentPlan && ['topaz', 'ruby', 'diamond'].includes(currentPlan.id);

  const getConnectionColor = (quality: string) => {
    switch (quality) {
      case 'excellent': return 'text-green-600';
      case 'good': return 'text-blue-600';
      case 'poor': return 'text-yellow-600';
      default: return 'text-red-600';
    }
  };

  const getStorageUsageColor = (percentage: number) => {
    if (percentage < 70) return 'bg-green-500';
    if (percentage < 85) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  if (!hasAccess) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <Smartphone className="h-16 w-16 mx-auto text-gray-400" />
          <h3 className="text-xl font-semibold">Enhanced Mobile App</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Professional mobile tools for field work, including offline capabilities, GPS tracking, and document scanning.
          </p>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 mb-3">
              <strong>Included in Bronze and above:</strong>
            </p>
            <ul className="text-sm text-gray-600 space-y-1 text-left max-w-md mx-auto">
              <li>• Basic mobile app access</li>
              <li>• Offline claim management</li>
              <li>• Document scanning and OCR</li>
              <li>• GPS location tracking</li>
              <li>• Mobile-optimized workflows</li>
            </ul>
          </div>
          <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
            Upgrade to Access Mobile Features
          </Button>
        </div>
      </Card>
    );
  }

  const storagePercentage = offlineCapabilities ? 
    Math.round((offlineCapabilities.current_usage_mb / offlineCapabilities.storage_limit_mb) * 100) : 0;

  return (
    <div className="space-y-6">
      {/* Mobile App Overview Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Smartphone className="h-6 w-6 text-blue-600" />
              <CardTitle>Enhanced Mobile App Dashboard</CardTitle>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-500">App Status</div>
                <div className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm font-medium">Online & Synced</span>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-500">Connection</div>
                <div className={`text-sm font-semibold ${getConnectionColor(syncStatus?.connection_quality || 'offline')}`}>
                  {syncStatus?.connection_quality?.toUpperCase() || 'OFFLINE'}
                </div>
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-gray-500">Offline Claims</div>
                  <div className="text-xl font-semibold">{offlineCapabilities?.cached_data.claims_cached || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Upload className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-gray-500">Pending Sync</div>
                  <div className="text-xl font-semibold">{syncStatus?.items_pending || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <ScanLine className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-gray-500">Documents Scanned</div>
                  <div className="text-xl font-semibold">47</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-gray-500">GPS Tracking</div>
                  <div className="text-xl font-semibold text-green-600">Active</div>
                </div>
              </div>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-6">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="offline">Offline</TabsTrigger>
              <TabsTrigger value="scanning">Scanning</TabsTrigger>
              <TabsTrigger value="gps">GPS & Routes</TabsTrigger>
              <TabsTrigger value="workflows">Workflows</TabsTrigger>
              <TabsTrigger value="sync">Sync</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Mobile App Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Storage Usage</span>
                      <span className="text-sm text-gray-600">
                        {offlineCapabilities?.current_usage_mb || 0} / {offlineCapabilities?.storage_limit_mb || 0} MB
                      </span>
                    </div>
                    <Progress 
                      value={storagePercentage} 
                      className={`h-2 ${getStorageUsageColor(storagePercentage)}`}
                    />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Sync Efficiency</span>
                      <span className="text-sm text-gray-600">97.3%</span>
                    </div>
                    <Progress value={97.3} className="h-2 bg-green-500" />
                    
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Field Productivity</span>
                      <span className="text-sm text-gray-600">+23%</span>
                    </div>
                    <Progress value={85} className="h-2 bg-blue-500" />
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Today's Mobile Activity</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">7 claims updated offline</span>
                      </div>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Camera className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">12 documents scanned</span>
                      </div>
                      <span className="text-xs text-gray-500">4 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Route className="h-4 w-4 text-purple-600" />
                        <span className="text-sm">Route optimized: 45 min saved</span>
                      </div>
                      <span className="text-xs text-gray-500">6 hours ago</span>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="offline" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Offline Capabilities</h3>
                {!hasAdvancedMobile && (
                  <Badge variant="outline" className="text-orange-600">
                    Advanced offline features require Silver+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <WifiOff className="h-5 w-5" />
                      Offline Status
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Offline Mode</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        Enabled
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Auto-Sync</span>
                      <Badge variant="outline" className="bg-blue-50 text-blue-700">
                        <RotateCcw className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Conflict Resolution</span>
                      <Badge variant="outline" className="bg-purple-50 text-purple-700">
                        <Shield className="h-3 w-3 mr-1" />
                        Automatic
                      </Badge>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium mb-2">Cached Data</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Claims:</span>
                          <span className="font-medium">{offlineCapabilities?.cached_data.claims_cached}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Contacts:</span>
                          <span className="font-medium">{offlineCapabilities?.cached_data.contacts_cached}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Documents:</span>
                          <span className="font-medium">{offlineCapabilities?.cached_data.documents_cached}</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Offline Features</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {offlineCapabilities?.offline_features.map((feature, index) => (
                      <div key={index} className="p-3 border rounded-lg">
                        <div className="flex items-center justify-between mb-2">
                          <span className="font-medium">{feature.feature_name}</span>
                          <Badge variant={feature.available_offline ? 'default' : 'secondary'}>
                            {feature.available_offline ? 'Available' : 'Limited'}
                          </Badge>
                        </div>
                        {feature.limitations.length > 0 && (
                          <div className="text-xs text-gray-600">
                            <strong>Limitations:</strong> {feature.limitations.join(', ')}
                          </div>
                        )}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="scanning" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Document Scanning</h3>
                {!hasPremiumMobile && (
                  <Badge variant="outline" className="text-orange-600">
                    Advanced OCR requires Gold+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Camera className="h-5 w-5 text-blue-600" />
                    <h4 className="font-semibold">Scan Quality</h4>
                  </div>
                  <div className="text-2xl font-bold text-blue-600 mb-1">HD+</div>
                  <p className="text-sm text-gray-600">
                    Auto-enhanced scanning with perspective correction
                  </p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <FileText className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold">OCR Accuracy</h4>
                  </div>
                  <div className="text-2xl font-bold text-green-600 mb-1">96.8%</div>
                  <p className="text-sm text-gray-600">
                    Text recognition with auto-correction
                  </p>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Zap className="h-5 w-5 text-purple-600" />
                    <h4 className="font-semibold">Processing Speed</h4>
                  </div>
                  <div className="text-2xl font-bold text-purple-600 mb-1">2.3s</div>
                  <p className="text-sm text-gray-600">
                    Average scan-to-upload time
                  </p>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Scanning Features</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Auto-Enhancement</span>
                        <Badge variant="default">Enabled</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Multi-Page Detection</span>
                        <Badge variant="default">Enabled</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Auto-Categorization</span>
                        <Badge variant={hasPremiumMobile ? 'default' : 'secondary'}>
                          {hasPremiumMobile ? 'Enabled' : 'Upgrade Required'}
                        </Badge>
                      </div>
                    </div>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Batch Processing</span>
                        <Badge variant={hasAdvancedMobile ? 'default' : 'secondary'}>
                          {hasAdvancedMobile ? 'Enabled' : 'Silver+ Required'}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Cloud OCR</span>
                        <Badge variant="default">Enabled</Badge>
                      </div>
                      <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                        <span className="text-sm">Secure Encryption</span>
                        <Badge variant="default">AES-256</Badge>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="gps" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">GPS & Route Optimization</h3>
                {!hasTeamMobile && (
                  <Badge variant="outline" className="text-orange-600">
                    Team tracking requires Topaz+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <MapPin className="h-5 w-5" />
                      Location Services
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">GPS Tracking</span>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <MapPin className="h-3 w-3 mr-1" />
                        Active
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Accuracy</span>
                      <span className="text-sm font-medium">±3 meters</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Battery Optimization</span>
                      <Badge variant="outline" className="bg-green-50 text-green-700">
                        Efficient
                      </Badge>
                    </div>
                    
                    <div className="border-t pt-4">
                      <h4 className="font-medium mb-2">Today's Activity</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Distance Traveled:</span>
                          <span className="font-medium">127.3 miles</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Locations Visited:</span>
                          <span className="font-medium">8 stops</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Time Saved:</span>
                          <span className="font-medium text-green-600">45 minutes</span>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center gap-2">
                      <Route className="h-5 w-5" />
                      Route Optimization
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Efficiency Gained</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">+23%</div>
                      <div className="text-sm text-blue-700">vs. manual routing</div>
                    </div>
                    
                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Traffic Integration</span>
                        <Badge variant="default">Real-time</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Route Recalculation</span>
                        <Badge variant="default">Automatic</Badge>
                      </div>
                      <div className="flex items-center justify-between">
                        <span className="text-sm">Fuel Optimization</span>
                        <Badge variant={hasPremiumMobile ? 'default' : 'secondary'}>
                          {hasPremiumMobile ? 'Enabled' : 'Gold+ Required'}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="workflows" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Mobile Workflows</h3>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Customize Workflows
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold">Claim Visit</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Complete workflow for on-site claim verification and documentation
                  </p>
                  <div className="text-xs text-gray-500">
                    Average completion: 12 minutes
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <h4 className="font-semibold">Document Collection</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Guided workflow for scanning and categorizing claim documents
                  </p>
                  <div className="text-xs text-gray-500">
                    Average completion: 8 minutes
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Users className="h-5 w-5 text-purple-600" />
                    <h4 className="font-semibold">Contact Verification</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Step-by-step process for verifying claimant identity and information
                  </p>
                  <div className="text-xs text-gray-500">
                    Average completion: 15 minutes
                  </div>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="sync" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Sync Management</h3>
                <Button size="sm" variant="outline">
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Force Sync Now
                </Button>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Sync Status</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Overall Status</span>
                      <Badge variant="default" className="bg-green-100 text-green-800">
                        <CheckCircle className="h-3 w-3 mr-1" />
                        {syncStatus?.overall_status?.toUpperCase()}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Last Sync</span>
                      <span className="text-sm font-medium">
                        {syncStatus?.last_sync.toLocaleTimeString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Next Sync</span>
                      <span className="text-sm font-medium">
                        {syncStatus?.next_sync.toLocaleTimeString()}
                      </span>
                    </div>
                    
                    <div className="border-t pt-4">
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Sync Progress</span>
                        <span className="text-sm text-gray-600">
                          {syncStatus?.sync_progress_percentage}%
                        </span>
                      </div>
                      <Progress 
                        value={syncStatus?.sync_progress_percentage || 0} 
                        className="h-2"
                      />
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Data Usage</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Globe className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Today's Usage</span>
                      </div>
                      <div className="text-2xl font-bold text-blue-600">47.3 MB</div>
                      <div className="text-sm text-blue-700">Below daily limit</div>
                    </div>
                    
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span>Documents Synced:</span>
                        <span className="font-medium">23</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Claims Updated:</span>
                        <span className="font-medium">15</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Conflicts Resolved:</span>
                        <span className="font-medium">0</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upgrade Prompt for Limited Features */}
      {!hasPremiumMobile && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Smartphone className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Unlock Premium Mobile Features</h3>
                  <p className="text-sm text-gray-600">
                    Upgrade to Gold plan for advanced OCR, AI categorization, route optimization, and team tracking.
                  </p>
                </div>
              </div>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Gold
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 