import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Progress } from '@/components/ui/progress';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Download, 
  FileText, 
  FileSpreadsheet, 
  Database, 
  Image,
  Calendar,
  Filter,
  Settings,
  Clock,
  CheckCircle,
  AlertCircle,
  X
} from 'lucide-react';
import { useToast } from '@/components/ui/toast';

interface ExportFormat {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  extension: string;
  supportsFilters: boolean;
  supportsCustomFields: boolean;
}

interface ExportField {
  id: string;
  name: string;
  category: 'basic' | 'financial' | 'contact' | 'activity' | 'compliance';
  required: boolean;
  description?: string;
}

interface ExportOptions {
  format: string;
  dateRange: {
    start: string;
    end: string;
  };
  filters: {
    status: string[];
    assignee: string[];
    value: { min: number; max: number };
  };
  fields: string[];
  includeAttachments: boolean;
  groupBy?: string;
  sortBy: string;
  sortOrder: 'asc' | 'desc';
}

interface AdvancedExportProps {
  data: any[];
  title: string;
  onExport: (options: ExportOptions) => Promise<void>;
}

export const AdvancedExport: React.FC<AdvancedExportProps> = ({
  data,
  title,
  onExport
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState(0);
  const [options, setOptions] = useState<ExportOptions>({
    format: 'csv',
    dateRange: {
      start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
      end: new Date().toISOString().split('T')[0]
    },
    filters: {
      status: [],
      assignee: [],
      value: { min: 0, max: 1000000 }
    },
    fields: ['id', 'title', 'status', 'value', 'created_at'],
    includeAttachments: false,
    sortBy: 'created_at',
    sortOrder: 'desc'
  });

  const { success, error } = useToast();

  const formats: ExportFormat[] = [
    {
      id: 'csv',
      name: 'CSV',
      description: 'Comma-separated values for spreadsheet applications',
      icon: <FileSpreadsheet className="h-4 w-4" />,
      extension: 'csv',
      supportsFilters: true,
      supportsCustomFields: true
    },
    {
      id: 'excel',
      name: 'Excel',
      description: 'Microsoft Excel workbook with formatting',
      icon: <FileSpreadsheet className="h-4 w-4 text-green-600" />,
      extension: 'xlsx',
      supportsFilters: true,
      supportsCustomFields: true
    },
    {
      id: 'pdf',
      name: 'PDF Report',
      description: 'Formatted PDF report with charts and summary',
      icon: <FileText className="h-4 w-4 text-red-600" />,
      extension: 'pdf',
      supportsFilters: true,
      supportsCustomFields: false
    },
    {
      id: 'json',
      name: 'JSON',
      description: 'Machine-readable JSON format',
      icon: <Database className="h-4 w-4 text-blue-600" />,
      extension: 'json',
      supportsFilters: true,
      supportsCustomFields: true
    }
  ];

  const availableFields: ExportField[] = [
    // Basic Fields
    { id: 'id', name: 'ID', category: 'basic', required: true },
    { id: 'title', name: 'Title', category: 'basic', required: false },
    { id: 'status', name: 'Status', category: 'basic', required: false },
    { id: 'created_at', name: 'Created Date', category: 'basic', required: false },
    { id: 'updated_at', name: 'Updated Date', category: 'basic', required: false },
    
    // Financial Fields
    { id: 'value', name: 'Value', category: 'financial', required: false },
    { id: 'commission', name: 'Commission', category: 'financial', required: false },
    { id: 'expenses', name: 'Expenses', category: 'financial', required: false },
    
    // Contact Fields
    { id: 'claimant_name', name: 'Claimant Name', category: 'contact', required: false },
    { id: 'claimant_email', name: 'Claimant Email', category: 'contact', required: false },
    { id: 'claimant_phone', name: 'Claimant Phone', category: 'contact', required: false },
    
    // Activity Fields
    { id: 'last_activity', name: 'Last Activity', category: 'activity', required: false },
    { id: 'activity_count', name: 'Activity Count', category: 'activity', required: false },
    
    // Compliance Fields
    { id: 'compliance_status', name: 'Compliance Status', category: 'compliance', required: false },
    { id: 'audit_trail', name: 'Audit Trail', category: 'compliance', required: false }
  ];

  const handleExport = async () => {
    setIsExporting(true);
    setExportProgress(0);

    try {
      // Simulate progress
      const progressInterval = setInterval(() => {
        setExportProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval);
            return prev;
          }
          return prev + 10;
        });
      }, 200);

      await onExport(options);
      
      clearInterval(progressInterval);
      setExportProgress(100);
      
      setTimeout(() => {
        setIsExporting(false);
        setExportProgress(0);
        setIsOpen(false);
        success('Export completed successfully', `${title} exported to ${options.format.toUpperCase()}`);
      }, 500);

    } catch (err) {
      setIsExporting(false);
      setExportProgress(0);
      error('Export failed', 'Please try again or contact support');
    }
  };

  const updateField = (fieldId: string, checked: boolean) => {
    setOptions(prev => ({
      ...prev,
      fields: checked 
        ? [...prev.fields, fieldId]
        : prev.fields.filter(id => id !== fieldId)
    }));
  };

  const selectedFormat = formats.find(f => f.id === options.format);
  const fieldsByCategory = availableFields.reduce((acc, field) => {
    if (!acc[field.category]) acc[field.category] = [];
    acc[field.category].push(field);
    return acc;
  }, {} as Record<string, ExportField[]>);

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Download className="h-4 w-4" />
          Export Data
        </Button>
      </DialogTrigger>
      
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Download className="h-5 w-5" />
            Export {title}
            <Badge variant="outline">{data.length} records</Badge>
          </DialogTitle>
        </DialogHeader>

        {isExporting ? (
          <div className="py-8 text-center">
            <div className="mb-4">
              <div className="inline-flex items-center justify-center w-16 h-16 bg-blue-100 dark:bg-blue-900/20 rounded-full mb-4">
                <Download className="h-8 w-8 text-blue-600" />
              </div>
            </div>
            <h3 className="text-lg font-semibold mb-2">Preparing Export...</h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              Processing {data.length} records in {selectedFormat?.name} format
            </p>
            <div className="max-w-md mx-auto">
              <Progress value={exportProgress} className="mb-2" />
              <p className="text-sm text-gray-500">{exportProgress}% complete</p>
            </div>
          </div>
        ) : (
          <Tabs defaultValue="format" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="format">Format</TabsTrigger>
              <TabsTrigger value="fields">Fields</TabsTrigger>
              <TabsTrigger value="filters">Filters</TabsTrigger>
              <TabsTrigger value="options">Options</TabsTrigger>
            </TabsList>

            {/* Format Selection */}
            <TabsContent value="format" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {formats.map(format => (
                  <Card 
                    key={format.id}
                    className={`cursor-pointer transition-all ${
                      options.format === format.id 
                        ? 'ring-2 ring-blue-500 border-blue-500' 
                        : 'hover:border-gray-300'
                    }`}
                    onClick={() => setOptions(prev => ({ ...prev, format: format.id }))}
                  >
                    <CardHeader className="pb-3">
                      <div className="flex items-center gap-3">
                        {format.icon}
                        <div>
                          <CardTitle className="text-base">{format.name}</CardTitle>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {format.description}
                          </p>
                        </div>
                        {options.format === format.id && (
                          <CheckCircle className="h-5 w-5 text-blue-500 ml-auto" />
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <div className="flex gap-2">
                        {format.supportsFilters && (
                          <Badge variant="outline" className="text-xs">
                            <Filter className="h-3 w-3 mr-1" />
                            Filters
                          </Badge>
                        )}
                        {format.supportsCustomFields && (
                          <Badge variant="outline" className="text-xs">
                            <Settings className="h-3 w-3 mr-1" />
                            Custom Fields
                          </Badge>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </TabsContent>

            {/* Field Selection */}
            <TabsContent value="fields" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Select Fields to Export</h3>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setOptions(prev => ({ 
                      ...prev, 
                      fields: availableFields.map(f => f.id) 
                    }))}
                  >
                    Select All
                  </Button>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={() => setOptions(prev => ({ 
                      ...prev, 
                      fields: availableFields.filter(f => f.required).map(f => f.id) 
                    }))}
                  >
                    Required Only
                  </Button>
                </div>
              </div>

              {Object.entries(fieldsByCategory).map(([category, fields]) => (
                <Card key={category}>
                  <CardHeader className="pb-3">
                    <CardTitle className="text-base capitalize flex items-center justify-between">
                      {category} Fields
                      <Badge variant="outline">
                        {fields.filter(f => options.fields.includes(f.id)).length}/{fields.length}
                      </Badge>
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-3">
                    {fields.map(field => (
                      <div key={field.id} className="flex items-center space-x-2">
                        <Checkbox
                          id={field.id}
                          checked={options.fields.includes(field.id)}
                          onCheckedChange={(checked) => updateField(field.id, !!checked)}
                          disabled={field.required}
                        />
                        <Label 
                          htmlFor={field.id} 
                          className={`flex-1 text-sm ${field.required ? 'font-medium' : ''}`}
                        >
                          {field.name}
                          {field.required && (
                            <Badge variant="outline" className="ml-2 text-xs">Required</Badge>
                          )}
                        </Label>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              ))}
            </TabsContent>

            {/* Filters */}
            <TabsContent value="filters" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5" />
                    Date Range
                  </CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="start-date">Start Date</Label>
                    <Input
                      id="start-date"
                      type="date"
                      value={options.dateRange.start}
                      onChange={(e) => setOptions(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, start: e.target.value }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="end-date">End Date</Label>
                    <Input
                      id="end-date"
                      type="date"
                      value={options.dateRange.end}
                      onChange={(e) => setOptions(prev => ({
                        ...prev,
                        dateRange: { ...prev.dateRange, end: e.target.value }
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Value Range</CardTitle>
                </CardHeader>
                <CardContent className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="min-value">Minimum Value ($)</Label>
                    <Input
                      id="min-value"
                      type="number"
                      value={options.filters.value.min}
                      onChange={(e) => setOptions(prev => ({
                        ...prev,
                        filters: {
                          ...prev.filters,
                          value: { ...prev.filters.value, min: Number(e.target.value) }
                        }
                      }))}
                    />
                  </div>
                  <div>
                    <Label htmlFor="max-value">Maximum Value ($)</Label>
                    <Input
                      id="max-value"
                      type="number"
                      value={options.filters.value.max}
                      onChange={(e) => setOptions(prev => ({
                        ...prev,
                        filters: {
                          ...prev.filters,
                          value: { ...prev.filters.value, max: Number(e.target.value) }
                        }
                      }))}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Export Options */}
            <TabsContent value="options" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Export Options</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="sort-by">Sort By</Label>
                      <Select
                        value={options.sortBy}
                        onValueChange={(value) => setOptions(prev => ({ ...prev, sortBy: value }))}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="created_at">Created Date</SelectItem>
                          <SelectItem value="updated_at">Updated Date</SelectItem>
                          <SelectItem value="value">Value</SelectItem>
                          <SelectItem value="status">Status</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="sort-order">Sort Order</Label>
                      <Select
                        value={options.sortOrder}
                        onValueChange={(value: 'asc' | 'desc') => 
                          setOptions(prev => ({ ...prev, sortOrder: value }))
                        }
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="desc">Newest First</SelectItem>
                          <SelectItem value="asc">Oldest First</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="flex items-center space-x-2">
                    <Checkbox
                      id="include-attachments"
                      checked={options.includeAttachments}
                      onCheckedChange={(checked) => 
                        setOptions(prev => ({ ...prev, includeAttachments: !!checked }))
                      }
                    />
                    <Label htmlFor="include-attachments">
                      Include attachments (creates ZIP file)
                    </Label>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Export Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Format:</span>
                      <Badge>{selectedFormat?.name}</Badge>
                    </div>
                    <div className="flex justify-between">
                      <span>Records:</span>
                      <span>{data.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Fields:</span>
                      <span>{options.fields.length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Date Range:</span>
                      <span>{options.dateRange.start} to {options.dateRange.end}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        )}

        {!isExporting && (
          <div className="flex justify-between pt-4 border-t">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleExport} className="gap-2">
              <Download className="h-4 w-4" />
              Export {selectedFormat?.name}
            </Button>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}; 