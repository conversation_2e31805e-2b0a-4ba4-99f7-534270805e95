import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, Ta<PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  Brain, 
  TrendingUp, 
  Target, 
  Clock, 
  Star,
  Zap,
  AlertCircle,
  CheckCircle,
  Activity,
  BarChart3,
  Lightbulb,
  RefreshCw,
  Filter,
  ArrowUp,
  ArrowDown,
  Minus
} from 'lucide-react';
import { LeadScore, ContactQualityScore, OptimalTimingRecommendation } from '@/types/ai-scoring';
import { usePricingLogic } from '@/hooks/usePricingLogic';

interface AILeadScoringProps {
  onUpgrade?: () => void;
}

export default function AILeadScoring({ onUpgrade }: AILeadScoringProps) {
  const { currentPlan, subscription } = usePricingLogic();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [leadScores, setLeadScores] = useState<LeadScore[]>([]);
  const [isAnalyzing, setIsAnalyzing] = useState(false);

  // Mock data - would come from AI service in real implementation
  useEffect(() => {
    const mockLeadScores: LeadScore[] = [
      {
        claim_id: 'claim_001',
        overall_score: 87,
        success_probability: 0.82,
        estimated_recovery_amount: 15000,
        estimated_timeline_days: 45,
        risk_factors: [
          {
            factor_id: 'rf1',
            factor_name: 'Outdated Contact Information',
            category: 'contact',
            impact_score: -15,
            description: 'Phone number may be outdated based on age of data',
            mitigation_suggestions: ['Use skip tracing service', 'Try social media research'],
            severity: 'medium'
          }
        ],
        opportunity_factors: [
          {
            factor_id: 'of1',
            factor_name: 'High-Value Asset',
            category: 'financial',
            impact_score: 25,
            description: 'Asset value indicates strong recovery potential',
            optimization_suggestions: ['Prioritize contact attempts', 'Use premium communication methods'],
            potential: 'high'
          },
          {
            factor_id: 'of2',
            factor_name: 'Recent Escheatment',
            category: 'temporal',
            impact_score: 20,
            description: 'Recently escheated assets have higher success rates',
            optimization_suggestions: ['Fast-track processing', 'Use urgent messaging'],
            potential: 'high'
          }
        ],
        recommended_actions: [
          {
            action_id: 'action1',
            priority: 1,
            action_type: 'contact',
            title: 'Immediate Phone Contact',
            description: 'Call within next 2 hours for optimal response rate',
            expected_impact: 'high',
            estimated_effort: 0.5,
            estimated_cost: 0,
            automation_available: true,
            success_rate_improvement: 15
          },
          {
            action_id: 'action2',
            priority: 2,
            action_type: 'research',
            title: 'Skip Tracing Research',
            description: 'Update contact information using professional skip tracing',
            expected_impact: 'medium',
            estimated_effort: 1,
            estimated_cost: 25,
            automation_available: true,
            success_rate_improvement: 12
          }
        ],
        scoring_components: [
          {
            component_name: 'Asset Value Score',
            weight: 30,
            raw_score: 90,
            weighted_score: 27,
            factors_considered: ['Asset amount', 'Asset type', 'Market value'],
            explanation: 'High-value financial asset with strong market position'
          },
          {
            component_name: 'Contact Quality Score',
            weight: 25,
            raw_score: 75,
            weighted_score: 18.75,
            factors_considered: ['Phone validity', 'Address accuracy', 'Email status'],
            explanation: 'Good contact information with some verification needed'
          },
          {
            component_name: 'Timing Score',
            weight: 20,
            raw_score: 95,
            weighted_score: 19,
            factors_considered: ['Escheat date', 'Notification timing', 'Response window'],
            explanation: 'Optimal timing window for contact and recovery'
          },
          {
            component_name: 'Geographic Score',
            weight: 15,
            raw_score: 85,
            weighted_score: 12.75,
            factors_considered: ['State regulations', 'Local success rates', 'Processing complexity'],
            explanation: 'Favorable jurisdiction with good recovery history'
          },
          {
            component_name: 'Behavioral Score',
            weight: 10,
            raw_score: 80,
            weighted_score: 8,
            factors_considered: ['Historical response patterns', 'Demographic indicators'],
            explanation: 'Positive behavioral indicators for engagement'
          }
        ],
        last_calculated: new Date(),
        confidence_level: 0.89
      },
      {
        claim_id: 'claim_002',
        overall_score: 65,
        success_probability: 0.58,
        estimated_recovery_amount: 3500,
        estimated_timeline_days: 75,
        risk_factors: [
          {
            factor_id: 'rf2',
            factor_name: 'Low Asset Value',
            category: 'financial',
            impact_score: -20,
            description: 'Lower asset value may reduce engagement motivation',
            mitigation_suggestions: ['Emphasize ease of recovery', 'Bundle with other claims'],
            severity: 'medium'
          },
          {
            factor_id: 'rf3',
            factor_name: 'Complex State Rules',
            category: 'legal',
            impact_score: -10,
            description: 'State has complex recovery procedures',
            mitigation_suggestions: ['Use state-specific templates', 'Review compliance checklist'],
            severity: 'low'
          }
        ],
        opportunity_factors: [
          {
            factor_id: 'of3',
            factor_name: 'Verified Contact Info',
            category: 'contact',
            impact_score: 15,
            description: 'Contact information recently verified',
            optimization_suggestions: ['Use verified channels first'],
            potential: 'medium'
          }
        ],
        recommended_actions: [
          {
            action_id: 'action3',
            priority: 1,
            action_type: 'documentation',
            title: 'Prepare Documentation Package',
            description: 'Gather all required documents before initial contact',
            expected_impact: 'medium',
            estimated_effort: 2,
            estimated_cost: 0,
            automation_available: false,
            success_rate_improvement: 8
          }
        ],
        scoring_components: [
          {
            component_name: 'Asset Value Score',
            weight: 30,
            raw_score: 45,
            weighted_score: 13.5,
            factors_considered: ['Asset amount', 'Asset type'],
            explanation: 'Lower value asset requiring efficient processing'
          },
          {
            component_name: 'Contact Quality Score',
            weight: 25,
            raw_score: 85,
            weighted_score: 21.25,
            factors_considered: ['Recent verification', 'Multiple contact methods'],
            explanation: 'High-quality contact information recently verified'
          }
        ],
        last_calculated: new Date(),
        confidence_level: 0.75
      }
    ];

    setLeadScores(mockLeadScores);
  }, []);

  const hasAccess = currentPlan && ['gold', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasAdvancedAI = currentPlan && ['diamond'].includes(currentPlan.id);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getScoreBgColor = (score: number) => {
    if (score >= 80) return 'bg-green-100';
    if (score >= 60) return 'bg-yellow-100';
    return 'bg-red-100';
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <ArrowUp className="h-4 w-4 text-green-600" />;
      case 'down': return <ArrowDown className="h-4 w-4 text-red-600" />;
      default: return <Minus className="h-4 w-4 text-gray-600" />;
    }
  };

  const handleReanalyze = async () => {
    setIsAnalyzing(true);
    // Simulate AI analysis
    setTimeout(() => {
      setIsAnalyzing(false);
    }, 3000);
  };

  if (!hasAccess) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <Brain className="h-16 w-16 mx-auto text-gray-400" />
          <h3 className="text-xl font-semibold">AI Lead Scoring Engine</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Intelligent claim prioritization using machine learning to predict success probability and optimize recovery strategies.
          </p>
          <div className="bg-gradient-to-r from-purple-50 to-indigo-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 mb-3">
              <strong>Gold Plan Feature:</strong>
            </p>
            <ul className="text-sm text-gray-600 space-y-1 text-left max-w-md mx-auto">
              <li>• AI-powered success probability scoring</li>
              <li>• Intelligent claim prioritization</li>
              <li>• Predictive recovery amount estimation</li>
              <li>• Optimal timing recommendations</li>
              <li>• Contact quality assessment</li>
              <li>• Automated action suggestions</li>
            </ul>
          </div>
          <Button onClick={onUpgrade} className="bg-purple-600 hover:bg-purple-700">
            Upgrade to Gold for AI Features
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* AI Dashboard Overview */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-6 w-6 text-purple-600" />
              <CardTitle>AI Lead Scoring Dashboard</CardTitle>
            </div>
            <div className="flex items-center gap-2">
              <Button 
                size="sm" 
                variant="outline" 
                onClick={handleReanalyze}
                disabled={isAnalyzing}
              >
                {isAnalyzing ? (
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4 mr-2" />
                )}
                {isAnalyzing ? 'Analyzing...' : 'Reanalyze All'}
              </Button>
              <Button size="sm" variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filters
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Target className="h-5 w-5 text-purple-600" />
                <div>
                  <div className="text-sm text-gray-500">Avg Success Rate</div>
                  <div className="text-xl font-semibold">73.2%</div>
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    {getTrendIcon('up')}
                    <span>+5.3%</span>
                  </div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                <div>
                  <div className="text-sm text-gray-500">High-Score Claims</div>
                  <div className="text-xl font-semibold">24</div>
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    {getTrendIcon('up')}
                    <span>+12%</span>
                  </div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-gray-500">AI Confidence</div>
                  <div className="text-xl font-semibold">87.4%</div>
                  <div className="flex items-center gap-1 text-xs text-gray-600">
                    {getTrendIcon('stable')}
                    <span>Stable</span>
                  </div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-gray-500">Avg Timeline</div>
                  <div className="text-xl font-semibold">52 days</div>
                  <div className="flex items-center gap-1 text-xs text-green-600">
                    {getTrendIcon('down')}
                    <span>-8 days</span>
                  </div>
                </div>
              </div>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="dashboard">Dashboard</TabsTrigger>
              <TabsTrigger value="scores">Lead Scores</TabsTrigger>
              <TabsTrigger value="insights">AI Insights</TabsTrigger>
              <TabsTrigger value="models">Model Performance</TabsTrigger>
            </TabsList>

            <TabsContent value="dashboard" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Top Priority Claims</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    {leadScores.slice(0, 3).map((lead) => (
                      <div key={lead.claim_id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center gap-3">
                          <div className={`w-12 h-12 rounded-full flex items-center justify-center ${getScoreBgColor(lead.overall_score)}`}>
                            <span className={`font-bold ${getScoreColor(lead.overall_score)}`}>
                              {lead.overall_score}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium">{lead.claim_id}</div>
                            <div className="text-sm text-gray-600">
                              ${lead.estimated_recovery_amount.toLocaleString()} • {lead.estimated_timeline_days} days
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-green-600">
                            {Math.round(lead.success_probability * 100)}% success
                          </div>
                          <Button size="sm" variant="outline" className="mt-1">
                            View Details
                          </Button>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">AI Recommendations</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-start gap-3 p-3 bg-blue-50 rounded-lg">
                      <Lightbulb className="h-5 w-5 text-blue-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-blue-900">Optimize Contact Timing</div>
                        <div className="text-sm text-blue-700">
                          Call claims #001-#003 between 2-4 PM for 23% higher response rates
                        </div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg">
                      <CheckCircle className="h-5 w-5 text-green-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-green-900">Skip Tracing Opportunity</div>
                        <div className="text-sm text-green-700">
                          Update contact info for 6 claims to increase success by 18%
                        </div>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 bg-yellow-50 rounded-lg">
                      <AlertCircle className="h-5 w-5 text-yellow-600 mt-0.5" />
                      <div>
                        <div className="font-medium text-yellow-900">Resource Allocation</div>
                        <div className="text-sm text-yellow-700">
                          Focus on high-value claims this week for maximum ROI
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="scores" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Detailed Lead Scores</h3>
                <div className="flex items-center gap-2">
                  <Badge variant="outline">
                    {leadScores.length} claims analyzed
                  </Badge>
                </div>
              </div>
              
              <div className="space-y-4">
                {leadScores.map((lead) => (
                  <Card key={lead.claim_id} className="p-6">
                    <div className="flex items-start gap-6">
                      <div className="text-center">
                        <div className={`w-20 h-20 rounded-full flex items-center justify-center ${getScoreBgColor(lead.overall_score)} mb-2`}>
                          <span className={`text-2xl font-bold ${getScoreColor(lead.overall_score)}`}>
                            {lead.overall_score}
                          </span>
                        </div>
                        <div className="text-xs text-gray-500">Overall Score</div>
                      </div>
                      
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-4">
                          <h4 className="text-lg font-semibold">{lead.claim_id}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant="outline" className="text-green-600">
                              {Math.round(lead.success_probability * 100)}% Success Probability
                            </Badge>
                            <Badge variant="outline">
                              Confidence: {Math.round(lead.confidence_level * 100)}%
                            </Badge>
                          </div>
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="text-center p-3 bg-gray-50 rounded-lg">
                            <div className="text-lg font-semibold text-green-600">
                              ${lead.estimated_recovery_amount.toLocaleString()}
                            </div>
                            <div className="text-xs text-gray-600">Est. Recovery</div>
                          </div>
                          <div className="text-center p-3 bg-gray-50 rounded-lg">
                            <div className="text-lg font-semibold text-blue-600">
                              {lead.estimated_timeline_days} days
                            </div>
                            <div className="text-xs text-gray-600">Est. Timeline</div>
                          </div>
                          <div className="text-center p-3 bg-gray-50 rounded-lg">
                            <div className="text-lg font-semibold text-purple-600">
                              {lead.recommended_actions.length}
                            </div>
                            <div className="text-xs text-gray-600">Recommendations</div>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <div>
                            <h5 className="font-medium mb-2">Scoring Components</h5>
                            <div className="space-y-2">
                              {lead.scoring_components.map((component) => (
                                <div key={component.component_name} className="flex items-center justify-between">
                                  <div className="flex items-center gap-2">
                                    <span className="text-sm font-medium">{component.component_name}</span>
                                    <span className="text-xs text-gray-500">({component.weight}% weight)</span>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Progress value={component.raw_score} className="w-20" />
                                    <span className="text-sm font-medium w-8">{component.raw_score}</span>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>

                          <div>
                            <h5 className="font-medium mb-2">Top Recommendations</h5>
                            <div className="space-y-2">
                              {lead.recommended_actions.slice(0, 2).map((action) => (
                                <div key={action.action_id} className="flex items-center justify-between p-2 bg-blue-50 rounded">
                                  <div>
                                    <div className="text-sm font-medium">{action.title}</div>
                                    <div className="text-xs text-gray-600">{action.description}</div>
                                  </div>
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className="text-blue-600">
                                      +{action.success_rate_improvement}%
                                    </Badge>
                                    <Button size="sm" variant="outline">
                                      Execute
                                    </Button>
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="insights" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Performance Insights</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="p-4 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <TrendingUp className="h-4 w-4 text-green-600" />
                        <span className="font-medium text-green-900">Success Rate Improvement</span>
                      </div>
                      <p className="text-sm text-green-700">
                        Claims with AI scores above 75 have a 34% higher success rate than unscored claims.
                      </p>
                    </div>
                    <div className="p-4 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Clock className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Time Savings</span>
                      </div>
                      <p className="text-sm text-blue-700">
                        AI prioritization has reduced average claim resolution time by 18%.
                      </p>
                    </div>
                    <div className="p-4 bg-purple-50 rounded-lg">
                      <div className="flex items-center gap-2 mb-2">
                        <Brain className="h-4 w-4 text-purple-600" />
                        <span className="font-medium text-purple-900">Model Accuracy</span>
                      </div>
                      <p className="text-sm text-purple-700">
                        Current model accuracy is 87.4% and improving with each data point.
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Optimization Opportunities</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="w-2 h-16 bg-orange-500 rounded-full"></div>
                      <div className="flex-1">
                        <h5 className="font-medium">Contact Quality Enhancement</h5>
                        <p className="text-sm text-gray-600 mb-2">
                          Improving contact data quality could increase success rates by 12-15%.
                        </p>
                        <Button size="sm" variant="outline">
                          View Strategy
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="w-2 h-16 bg-blue-500 rounded-full"></div>
                      <div className="flex-1">
                        <h5 className="font-medium">Timing Optimization</h5>
                        <p className="text-sm text-gray-600 mb-2">
                          Calling at optimal times could boost response rates by 20%.
                        </p>
                        <Button size="sm" variant="outline">
                          Schedule Calls
                        </Button>
                      </div>
                    </div>
                    <div className="flex items-start gap-3 p-3 border rounded-lg">
                      <div className="w-2 h-16 bg-green-500 rounded-full"></div>
                      <div className="flex-1">
                        <h5 className="font-medium">Resource Reallocation</h5>
                        <p className="text-sm text-gray-600 mb-2">
                          Focus on high-score claims for 25% better ROI.
                        </p>
                        <Button size="sm" variant="outline">
                          Optimize Workflow
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="models" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Model Performance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Overall Accuracy</span>
                      <div className="flex items-center gap-2">
                        <Progress value={87} className="w-20" />
                        <span className="text-sm font-bold">87.4%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Precision</span>
                      <div className="flex items-center gap-2">
                        <Progress value={84} className="w-20" />
                        <span className="text-sm font-bold">84.2%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Recall</span>
                      <div className="flex items-center gap-2">
                        <Progress value={91} className="w-20" />
                        <span className="text-sm font-bold">91.1%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">F1 Score</span>
                      <div className="flex items-center gap-2">
                        <Progress value={87} className="w-20" />
                        <span className="text-sm font-bold">87.5%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Feature Importance</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Asset Value</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: '45%' }}></div>
                        </div>
                        <span className="text-xs w-8">45%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Contact Quality</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: '28%' }}></div>
                        </div>
                        <span className="text-xs w-8">28%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Timing Factors</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: '15%' }}></div>
                        </div>
                        <span className="text-xs w-8">15%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Geographic</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: '8%' }}></div>
                        </div>
                        <span className="text-xs w-8">8%</span>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Other</span>
                      <div className="flex items-center gap-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div className="bg-blue-600 h-2 rounded-full" style={{ width: '4%' }}></div>
                        </div>
                        <span className="text-xs w-8">4%</span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Model Training History</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">15,420</div>
                      <div className="text-sm text-gray-600">Training Records</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">2,847</div>
                      <div className="text-sm text-gray-600">This Month</div>
                    </div>
                    <div className="p-4 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">v2.3.1</div>
                      <div className="text-sm text-gray-600">Model Version</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upgrade Prompt for Enhanced Features */}
      {!hasAdvancedAI && (
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 border-purple-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                  <Brain className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Unlock Predictive Analytics</h3>
                  <p className="text-sm text-gray-600">
                    Upgrade to Diamond for advanced AI features including predictive analytics, market intelligence, and custom model training.
                  </p>
                </div>
              </div>
              <Button onClick={onUpgrade} className="bg-purple-600 hover:bg-purple-700">
                Upgrade to Diamond
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 