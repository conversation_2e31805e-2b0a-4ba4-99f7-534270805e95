import React, { useState } from 'react';
import { C<PERSON>mantResult, ClaimantResultCard } from './ClaimantResultCard';
import { ClaimantResultsManager } from './ClaimantResultsManager';

// Sample data that matches the format shown in the user's image
const generateSampleClaimantResults = (): ClaimantResult[] => {
  return [
    {
      discoveryId: '34302188',
      primaryName: '<PERSON>',
      nameVariations: ['<PERSON>', '<PERSON><PERSON> <PERSON>', '<PERSON><PERSON> <PERSON><PERSON>'],
      addresses: [
        {
          street: '765 Barclay St',
          city: 'San Diego',
          state: 'CA',
          zipCode: '92101',
          type: 'current',
          source: 'property_records',
          confidence: 0.85
        },
        {
          street: '1014 Cedar St, Apt 3',
          city: 'San Diego',
          state: 'CA',
          zipCode: '92101',
          type: 'previous',
          source: 'voter_registration',
          confidence: 0.75
        }
      ],
      phoneNumbers: [
        {
          number: '(*************',
          type: 'mobile',
          verified: false,
          source: 'public_records',
          confidence: 0.80
        }
      ],
      emailAddresses: [
        {
          email: '<EMAIL>',
          verified: false,
          source: 'social_media',
          confidence: 0.70
        }
      ],
      confidence: 0.75,
      qualityScore: 0.82,
      lastActivity: 'Property Record - May 2024',
      socialMediaFingerprints: [
        {
          platform: 'LinkedIn',
          profile: 'linkedin.com/in/deb-thompson',
          url: 'https://linkedin.com/in/deb-thompson'
        },
        {
          platform: 'Facebook',
          profile: 'Search: Deborah Thompson in San Diego, CA',
          url: 'https://facebook.com/search?q=Deborah+Thompson+San+Diego'
        }
      ],
      familyConnections: [
        {
          name: 'Michael Thompson',
          relationship: 'Spouse',
          contact: '(*************'
        },
        {
          name: 'Sarah Thompson',
          relationship: 'Daughter',
          contact: 'Potential address: San Diego, CA'
        }
      ],
      businessConnections: [
        {
          company: 'Thompson & Associates',
          role: 'Partner',
          contact: '(619) 555-0200'
        }
      ],
      validationStatus: 'pending',
      contactAttempts: []
    },
    {
      discoveryId: '34302189',
      primaryName: 'Robert James Miller',
      nameVariations: ['Bob Miller', 'R. J. Miller', 'Robert Miller'],
      addresses: [
        {
          street: '3421 Oak Avenue',
          city: 'Sacramento',
          state: 'CA',
          zipCode: '95816',
          type: 'current',
          source: 'property_records',
          confidence: 0.90
        }
      ],
      phoneNumbers: [
        {
          number: '(*************',
          type: 'home',
          verified: true,
          source: 'white_pages',
          confidence: 0.95
        },
        {
          number: '(*************',
          type: 'mobile',
          verified: false,
          source: 'social_media',
          confidence: 0.65
        }
      ],
      emailAddresses: [
        {
          email: '<EMAIL>',
          verified: true,
          source: 'business_records',
          confidence: 0.85
        }
      ],
      confidence: 0.88,
      qualityScore: 0.91,
      lastActivity: 'Business Registration - March 2024',
      socialMediaFingerprints: [
        {
          platform: 'LinkedIn',
          profile: 'linkedin.com/in/robert-miller-construction',
          url: 'https://linkedin.com/in/robert-miller-construction'
        }
      ],
      familyConnections: [
        {
          name: 'Jennifer Miller',
          relationship: 'Wife',
          contact: '(*************'
        }
      ],
      businessConnections: [
        {
          company: 'Miller Construction LLC',
          role: 'Owner',
          contact: '(*************'
        },
        {
          company: 'Sacramento Builders Association',
          role: 'Member',
          contact: '<EMAIL>'
        }
      ],
      validationStatus: 'in_progress',
      contactAttempts: [
        {
          method: 'email',
          timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
          status: 'sent',
          notes: 'Initial contact email sent'
        }
      ]
    },
    {
      discoveryId: '34302190',
      primaryName: 'Maria Elena Rodriguez',
      nameVariations: ['Maria Rodriguez', 'M. E. Rodriguez', 'Elena Rodriguez'],
      addresses: [
        {
          street: '892 Mission Street',
          city: 'Los Angeles',
          state: 'CA',
          zipCode: '90015',
          type: 'current',
          source: 'voter_registration',
          confidence: 0.70
        }
      ],
      phoneNumbers: [
        {
          number: '(*************',
          type: 'mobile',
          verified: false,
          source: 'skip_tracing',
          confidence: 0.60
        }
      ],
      emailAddresses: [],
      confidence: 0.62,
      qualityScore: 0.55,
      lastActivity: 'Voter Registration - January 2024',
      socialMediaFingerprints: [
        {
          platform: 'Facebook',
          profile: 'Search: Maria Rodriguez in Los Angeles, CA',
          url: 'https://facebook.com/search?q=Maria+Rodriguez+Los+Angeles'
        }
      ],
      familyConnections: [
        {
          name: 'Carlos Rodriguez',
          relationship: 'Potential Spouse',
          contact: 'Search in Los Angeles, CA'
        }
      ],
      businessConnections: [],
      validationStatus: 'pending',
      contactAttempts: []
    },
    {
      discoveryId: '34302191',
      primaryName: 'James Patrick O\'Connor',
      nameVariations: ['Jim O\'Connor', 'J. P. O\'Connor', 'Patrick O\'Connor'],
      addresses: [
        {
          street: '1567 Highland Drive',
          city: 'Fresno',
          state: 'CA',
          zipCode: '93705',
          type: 'current',
          source: 'property_records',
          confidence: 0.95
        },
        {
          street: '423 Pine Street',
          city: 'Fresno',
          state: 'CA',
          zipCode: '93701',
          type: 'previous',
          source: 'voter_registration',
          confidence: 0.80
        }
      ],
      phoneNumbers: [
        {
          number: '(*************',
          type: 'home',
          verified: true,
          source: 'property_records',
          confidence: 0.90
        }
      ],
      emailAddresses: [
        {
          email: '<EMAIL>',
          verified: true,
          source: 'business_records',
          confidence: 0.85
        }
      ],
      confidence: 0.92,
      qualityScore: 0.88,
      lastActivity: 'Property Purchase - June 2024',
      socialMediaFingerprints: [
        {
          platform: 'LinkedIn',
          profile: 'linkedin.com/in/james-oconnor-tech',
          url: 'https://linkedin.com/in/james-oconnor-tech'
        },
        {
          platform: 'Twitter',
          profile: '@jpoconnor_tech',
          url: 'https://twitter.com/jpoconnor_tech'
        }
      ],
      familyConnections: [
        {
          name: 'Margaret O\'Connor',
          relationship: 'Mother',
          contact: '(*************'
        }
      ],
      businessConnections: [
        {
          company: 'TechSolutions Inc.',
          role: 'Senior Developer',
          contact: '<EMAIL>'
        }
      ],
      validationStatus: 'validated',
      contactAttempts: [
        {
          method: 'phone',
          timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
          status: 'responded',
          notes: 'Confirmed identity and interest in claim'
        },
        {
          method: 'email',
          timestamp: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000), // 5 days ago
          status: 'responded',
          notes: 'Sent documentation package'
        }
      ]
    }
  ];
};

interface ClaimantResultsDemoProps {
  onClose?: () => void;
}

export const ClaimantResultsDemo: React.FC<ClaimantResultsDemoProps> = ({ onClose }) => {
  const [sampleResults] = useState<ClaimantResult[]>(generateSampleClaimantResults());
  const [updatedResults, setUpdatedResults] = useState<ClaimantResult[]>(sampleResults);

  const handleResultsUpdate = (newResults: ClaimantResult[]) => {
    setUpdatedResults(newResults);
  };

  const handleValidateClaimant = async (resultId: string, method: string, notes?: string) => {
    console.log(`🎯 Validating claimant ${resultId} via ${method}:`, notes);
    
    // Simulate validation process
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    const updated = updatedResults.map(result => {
      if (result.discoveryId === resultId) {
        return {
          ...result,
          validationStatus: 'in_progress' as const,
          contactAttempts: [
            ...(result.contactAttempts || []),
            {
              method,
              timestamp: new Date(),
              status: 'sent' as const,
              notes: notes || `Validation attempt via ${method}`
            }
          ]
        };
      }
      return result;
    });
    
    setUpdatedResults(updated);
  };

  const handleMarkInvalid = async (resultId: string, reason: string) => {
    console.log(`❌ Marking claimant ${resultId} as invalid:`, reason);
    
    const updated = updatedResults.map(result => {
      if (result.discoveryId === resultId) {
        return {
          ...result,
          validationStatus: 'invalid' as const
        };
      }
      return result;
    });
    
    setUpdatedResults(updated);
  };

  const handleContactAttempt = async (resultId: string, method: string, contact: string) => {
    console.log(`📞 Contact attempt for ${resultId} via ${method}:`, contact);
    
    const updated = updatedResults.map(result => {
      if (result.discoveryId === resultId) {
        return {
          ...result,
          contactAttempts: [
            ...(result.contactAttempts || []),
            {
              method,
              timestamp: new Date(),
              status: 'sent' as const,
              notes: `Contact attempted via ${method}: ${contact}`
            }
          ]
        };
      }
      return result;
    });
    
    setUpdatedResults(updated);
  };

  return (
    <div className="min-h-screen bg-gray-100 p-6">
      {/* Demo Header */}
      <div className="mb-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-3xl font-bold text-gray-900">🎯 AssetHunterPro Discovery Results</h1>
            <p className="text-gray-600 mt-2">
              Interactive demonstration of claimant discovery results with validation workflow
            </p>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"
            >
              Close Demo
            </button>
          )}
        </div>
        
        <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-md">
          <h3 className="font-medium text-blue-900 mb-2">💡 Demo Features</h3>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• <strong>Multiple Results:</strong> View and compare different claimant matches</li>
            <li>• <strong>Validation Workflow:</strong> Track contact attempts and validation status</li>
            <li>• <strong>Contact Options:</strong> Email, phone, mail, social media, family connections</li>
            <li>• <strong>Quality Scoring:</strong> Confidence levels and comprehensive quality metrics</li>
            <li>• <strong>Filtering & Sorting:</strong> Organize results by status, confidence, or activity</li>
          </ul>
        </div>
      </div>

      {/* Results Manager */}
      <ClaimantResultsManager
        results={updatedResults}
        searchId="DEMO_2024_001"
        onResultsUpdate={handleResultsUpdate}
      />

      {/* Demo Instructions */}
      <div className="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">🚀 Try These Actions</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div className="p-4 bg-green-50 border border-green-200 rounded-md">
            <h4 className="font-medium text-green-900">✅ Validate a Claimant</h4>
            <p className="text-sm text-green-800 mt-2">
              Click "Validate Claimant" on any result card and try different contact methods
            </p>
          </div>
          <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="font-medium text-blue-900">📞 Contact Options</h4>
            <p className="text-sm text-blue-800 mt-2">
              Click phone numbers, emails, or social media links to simulate contact attempts
            </p>
          </div>
          <div className="p-4 bg-purple-50 border border-purple-200 rounded-md">
            <h4 className="font-medium text-purple-900">🔍 Filter & Sort</h4>
            <p className="text-sm text-purple-800 mt-2">
              Use the filters above to organize results by validation status or confidence
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

// Export for testing
export { generateSampleClaimantResults }; 