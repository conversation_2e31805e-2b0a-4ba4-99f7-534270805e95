import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter 
} from '@/components/ui/dialog';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Shield, 
  Key, 
  Smartphone, 
  Lock, 
  Eye, 
  EyeOff,
  Clock,
  AlertTriangle,
  CheckCircle,
  Setting<PERSON>,
  <PERSON>,
  History,
  QrCode,
  RefreshCw,
  Download,
  Mail
} from 'lucide-react';

interface MFASetup {
  enabled: boolean;
  method: 'sms' | 'email' | 'authenticator';
  phoneNumber?: string;
  email?: string;
  backupCodes: string[];
}

interface SecurityQuestion {
  id: string;
  question: string;
  answer: string;
}

interface SessionInfo {
  id: string;
  device: string;
  location: string;
  lastActivity: string;
  current: boolean;
}

interface PasswordPolicy {
  minLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  prohibitReuse: number;
  maxAge: number;
}

interface EnhancedAuthProps {
  className?: string;
}

const defaultPasswordPolicy: PasswordPolicy = {
  minLength: 8,
  requireUppercase: true,
  requireLowercase: true,
  requireNumbers: true,
  requireSpecialChars: true,
  prohibitReuse: 5,
  maxAge: 90
};

const securityQuestions = [
  "What was the name of your first pet?",
  "What city were you born in?",
  "What was the make of your first car?",
  "What was your mother's maiden name?",
  "What was the name of your elementary school?",
  "What was your favorite food as a child?",
  "What was the name of your first boss?",
  "What was the model of your first mobile phone?"
];

export const EnhancedAuth: React.FC<EnhancedAuthProps> = ({ className }) => {
  const [activeTab, setActiveTab] = useState("overview");
  const [showPassword, setShowPassword] = useState(false);
  const [mfaSetup, setMfaSetup] = useState<MFASetup>({
    enabled: false,
    method: 'authenticator',
    backupCodes: []
  });
  const [securityQuestions2FA, setSecurityQuestions2FA] = useState<SecurityQuestion[]>([]);
  const [passwordPolicy, setPasswordPolicy] = useState<PasswordPolicy>(defaultPasswordPolicy);
  const [activeSessions, setActiveSessions] = useState<SessionInfo[]>([
    {
      id: '1',
      device: 'Chrome on Windows 11',
      location: 'New York, NY',
      lastActivity: '2024-01-15 14:30:00',
      current: true
    },
    {
      id: '2',
      device: 'Mobile Safari on iPhone',
      location: 'Los Angeles, CA',
      lastActivity: '2024-01-14 09:15:00',
      current: false
    }
  ]);
  const [loginAttempts, setLoginAttempts] = useState([
    { timestamp: '2024-01-15 14:30:00', ip: '*************', status: 'success', location: 'New York, NY' },
    { timestamp: '2024-01-15 08:45:00', ip: '*************', status: 'failed', location: 'Unknown' },
    { timestamp: '2024-01-14 16:22:00', ip: '*************', status: 'success', location: 'New York, NY' }
  ]);
  const [qrCodeVisible, setQrCodeVisible] = useState(false);

  const generateBackupCodes = () => {
    const codes = Array.from({ length: 10 }, () => 
      Math.random().toString(36).substr(2, 8).toUpperCase()
    );
    setMfaSetup(prev => ({ ...prev, backupCodes: codes }));
  };

  const downloadBackupCodes = () => {
    const content = `AssetHunterPro MFA Backup Codes\n\nGenerated: ${new Date().toISOString()}\n\n${mfaSetup.backupCodes.join('\n')}\n\nKeep these codes safe. Each code can only be used once.`;
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'assethunterpro-backup-codes.txt';
    a.click();
    URL.revokeObjectURL(url);
  };

  const terminateSession = (sessionId: string) => {
    setActiveSessions(prev => prev.filter(session => session.id !== sessionId));
  };

  const validatePassword = (password: string) => {
    const errors = [];
    if (password.length < passwordPolicy.minLength) {
      errors.push(`At least ${passwordPolicy.minLength} characters`);
    }
    if (passwordPolicy.requireUppercase && !/[A-Z]/.test(password)) {
      errors.push('At least one uppercase letter');
    }
    if (passwordPolicy.requireLowercase && !/[a-z]/.test(password)) {
      errors.push('At least one lowercase letter');
    }
    if (passwordPolicy.requireNumbers && !/\d/.test(password)) {
      errors.push('At least one number');
    }
    if (passwordPolicy.requireSpecialChars && !/[!@#$%^&*]/.test(password)) {
      errors.push('At least one special character');
    }
    return errors;
  };

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center space-x-3">
        <div className="p-2 bg-blue-100 rounded-lg">
          <Shield className="h-6 w-6 text-blue-600" />
        </div>
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Enhanced Security</h2>
          <p className="text-gray-600">Manage authentication, security settings, and access controls</p>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-5">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="mfa">2FA/MFA</TabsTrigger>
          <TabsTrigger value="password">Password Policy</TabsTrigger>
          <TabsTrigger value="sessions">Sessions</TabsTrigger>
          <TabsTrigger value="audit">Audit Log</TabsTrigger>
        </TabsList>

        {/* Security Overview */}
        <TabsContent value="overview" className="space-y-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Security Score</CardTitle>
                <Shield className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-green-600">85%</div>
                <p className="text-xs text-muted-foreground">
                  +5% from last month
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Active Sessions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{activeSessions.length}</div>
                <p className="text-xs text-muted-foreground">
                  Across all devices
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">MFA Status</CardTitle>
                <Key className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  <Badge variant={mfaSetup.enabled ? "default" : "destructive"}>
                    {mfaSetup.enabled ? "Enabled" : "Disabled"}
                  </Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {mfaSetup.enabled ? "Extra layer of security active" : "Recommended to enable"}
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Security Recommendations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <AlertTriangle className="h-5 w-5 text-orange-500" />
                <span>Security Recommendations</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {!mfaSetup.enabled && (
                <div className="flex items-center justify-between p-4 bg-orange-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Key className="h-5 w-5 text-orange-600" />
                    <div>
                      <h4 className="font-medium text-orange-900">Enable Multi-Factor Authentication</h4>
                      <p className="text-sm text-orange-700">Add an extra layer of security to your account</p>
                    </div>
                  </div>
                  <Button size="sm" onClick={() => setActiveTab("mfa")}>
                    Setup MFA
                  </Button>
                </div>
              )}

              <div className="flex items-center justify-between p-4 bg-blue-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <Clock className="h-5 w-5 text-blue-600" />
                  <div>
                    <h4 className="font-medium text-blue-900">Regular Password Updates</h4>
                    <p className="text-sm text-blue-700">Last changed 45 days ago</p>
                  </div>
                </div>
                <Button size="sm" variant="outline">
                  Change Password
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Multi-Factor Authentication */}
        <TabsContent value="mfa" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Smartphone className="h-5 w-5" />
                <span>Multi-Factor Authentication</span>
              </CardTitle>
              <CardDescription>
                Secure your account with an additional layer of protection
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-medium">Enable 2FA</h4>
                  <p className="text-sm text-gray-600">Require a second form of authentication</p>
                </div>
                <Switch 
                  checked={mfaSetup.enabled}
                  onCheckedChange={(checked) => setMfaSetup(prev => ({ ...prev, enabled: checked }))}
                />
              </div>

              {mfaSetup.enabled && (
                <>
                  <Separator />
                  
                  <div className="space-y-4">
                    <Label>Authentication Method</Label>
                    <Select 
                      value={mfaSetup.method} 
                      onValueChange={(value: 'sms' | 'email' | 'authenticator') => 
                        setMfaSetup(prev => ({ ...prev, method: value }))
                      }
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="authenticator">Authenticator App (Recommended)</SelectItem>
                        <SelectItem value="sms">SMS Text Message</SelectItem>
                        <SelectItem value="email">Email Verification</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  {mfaSetup.method === 'authenticator' && (
                    <div className="space-y-4">
                      <div className="p-4 bg-gray-50 rounded-lg">
                        <h4 className="font-medium mb-2">Setup Authenticator App</h4>
                        <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                          <li>Download an authenticator app (Google Authenticator, Authy, etc.)</li>
                          <li>Scan the QR code or enter the setup key manually</li>
                          <li>Enter the 6-digit code from your app to verify</li>
                        </ol>
                      </div>

                      <div className="flex justify-center">
                        <Button 
                          variant="outline" 
                          onClick={() => setQrCodeVisible(!qrCodeVisible)}
                          className="flex items-center space-x-2"
                        >
                          <QrCode className="h-4 w-4" />
                          <span>{qrCodeVisible ? 'Hide' : 'Show'} QR Code</span>
                        </Button>
                      </div>

                      {qrCodeVisible && (
                        <div className="flex flex-col items-center space-y-4 p-4 bg-white border rounded-lg">
                          <div className="w-48 h-48 bg-gray-200 rounded-lg flex items-center justify-center">
                            <QrCode className="h-24 w-24 text-gray-400" />
                          </div>
                          <p className="text-sm text-gray-600 text-center">
                            Setup Key: JBSWY3DPEHPK3PXP
                          </p>
                        </div>
                      )}
                    </div>
                  )}

                  {mfaSetup.method === 'sms' && (
                    <div className="space-y-4">
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input 
                        id="phone"
                        type="tel"
                        placeholder="+****************"
                        value={mfaSetup.phoneNumber || ''}
                        onChange={(e) => setMfaSetup(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      />
                    </div>
                  )}

                  {mfaSetup.method === 'email' && (
                    <div className="space-y-4">
                      <Label htmlFor="email">Email Address</Label>
                      <Input 
                        id="email"
                        type="email"
                        placeholder="<EMAIL>"
                        value={mfaSetup.email || ''}
                        onChange={(e) => setMfaSetup(prev => ({ ...prev, email: e.target.value }))}
                      />
                    </div>
                  )}

                  <Separator />

                  {/* Backup Codes */}
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">Backup Codes</h4>
                        <p className="text-sm text-gray-600">Use these codes if you lose access to your primary 2FA method</p>
                      </div>
                      <Button size="sm" variant="outline" onClick={generateBackupCodes}>
                        <RefreshCw className="h-4 w-4 mr-2" />
                        Generate Codes
                      </Button>
                    </div>

                    {mfaSetup.backupCodes.length > 0 && (
                      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h5 className="font-medium text-yellow-800">Your Backup Codes</h5>
                          <Button size="sm" variant="outline" onClick={downloadBackupCodes}>
                            <Download className="h-4 w-4 mr-2" />
                            Download
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 gap-2 font-mono text-sm">
                          {mfaSetup.backupCodes.map((code, index) => (
                            <div key={index} className="p-2 bg-white rounded border">
                              {code}
                            </div>
                          ))}
                        </div>
                        <p className="text-xs text-yellow-700 mt-3">
                          ⚠️ Save these codes in a secure location. Each code can only be used once.
                        </p>
                      </div>
                    )}
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Password Policy */}
        <TabsContent value="password" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Lock className="h-5 w-5" />
                <span>Password Policy</span>
              </CardTitle>
              <CardDescription>
                Configure password requirements for enhanced security
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid gap-6 md:grid-cols-2">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="minLength">Minimum Length</Label>
                    <Input 
                      id="minLength"
                      type="number"
                      min={6}
                      max={128}
                      value={passwordPolicy.minLength}
                      onChange={(e) => setPasswordPolicy(prev => ({ 
                        ...prev, 
                        minLength: parseInt(e.target.value) || 8 
                      }))}
                    />
                  </div>
                  
                  <div>
                    <Label htmlFor="maxAge">Password Age (days)</Label>
                    <Input 
                      id="maxAge"
                      type="number"
                      min={0}
                      max={365}
                      value={passwordPolicy.maxAge}
                      onChange={(e) => setPasswordPolicy(prev => ({ 
                        ...prev, 
                        maxAge: parseInt(e.target.value) || 90 
                      }))}
                    />
                  </div>

                  <div>
                    <Label htmlFor="prohibitReuse">Prohibit Reuse (last N passwords)</Label>
                    <Input 
                      id="prohibitReuse"
                      type="number"
                      min={0}
                      max={24}
                      value={passwordPolicy.prohibitReuse}
                      onChange={(e) => setPasswordPolicy(prev => ({ 
                        ...prev, 
                        prohibitReuse: parseInt(e.target.value) || 5 
                      }))}
                    />
                  </div>
                </div>

                <div className="space-y-4">
                  <h4 className="font-medium">Character Requirements</h4>
                  
                  <div className="space-y-3">
                    <div className="flex items-center justify-between">
                      <Label>Require Uppercase Letters</Label>
                      <Switch 
                        checked={passwordPolicy.requireUppercase}
                        onCheckedChange={(checked) => setPasswordPolicy(prev => ({ 
                          ...prev, 
                          requireUppercase: checked 
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Lowercase Letters</Label>
                      <Switch 
                        checked={passwordPolicy.requireLowercase}
                        onCheckedChange={(checked) => setPasswordPolicy(prev => ({ 
                          ...prev, 
                          requireLowercase: checked 
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Numbers</Label>
                      <Switch 
                        checked={passwordPolicy.requireNumbers}
                        onCheckedChange={(checked) => setPasswordPolicy(prev => ({ 
                          ...prev, 
                          requireNumbers: checked 
                        }))}
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <Label>Require Special Characters</Label>
                      <Switch 
                        checked={passwordPolicy.requireSpecialChars}
                        onCheckedChange={(checked) => setPasswordPolicy(prev => ({ 
                          ...prev, 
                          requireSpecialChars: checked 
                        }))}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <Separator />

              {/* Password Strength Test */}
              <div className="space-y-4">
                <h4 className="font-medium">Test Password Strength</h4>
                <div className="space-y-2">
                  <div className="relative">
                    <Input 
                      type={showPassword ? "text" : "password"}
                      placeholder="Enter a password to test..."
                      onChange={(e) => {
                        const errors = validatePassword(e.target.value);
                        // You could show validation results here
                      }}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-2">
                <Button variant="outline">Reset to Defaults</Button>
                <Button>Save Policy</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Active Sessions */}
        <TabsContent value="sessions" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Active Sessions</span>
              </CardTitle>
              <CardDescription>
                Manage your active login sessions across all devices
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {activeSessions.map((session) => (
                  <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="p-2 bg-blue-100 rounded-lg">
                        <Smartphone className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">{session.device}</span>
                          {session.current && (
                            <Badge variant="default" className="text-xs">Current</Badge>
                          )}
                        </div>
                        <p className="text-sm text-gray-600">{session.location}</p>
                        <p className="text-xs text-gray-500">Last active: {session.lastActivity}</p>
                      </div>
                    </div>
                    {!session.current && (
                      <Button 
                        size="sm" 
                        variant="destructive"
                        onClick={() => terminateSession(session.id)}
                      >
                        Terminate
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <Separator className="my-6" />

              <div className="flex justify-between items-center">
                <div>
                  <h4 className="font-medium">Security Actions</h4>
                  <p className="text-sm text-gray-600">Terminate all sessions or force password reset</p>
                </div>
                <div className="space-x-2">
                  <Button variant="outline">Terminate All Other Sessions</Button>
                  <Button variant="destructive">Force Password Reset</Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Log */}
        <TabsContent value="audit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <History className="h-5 w-5" />
                <span>Security Audit Log</span>
              </CardTitle>
              <CardDescription>
                Review recent login attempts and security events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {loginAttempts.map((attempt, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-lg ${
                        attempt.status === 'success' 
                          ? 'bg-green-100' 
                          : 'bg-red-100'
                      }`}>
                        {attempt.status === 'success' ? (
                          <CheckCircle className="h-5 w-5 text-green-600" />
                        ) : (
                          <AlertTriangle className="h-5 w-5 text-red-600" />
                        )}
                      </div>
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium">
                            {attempt.status === 'success' ? 'Successful Login' : 'Failed Login'}
                          </span>
                          <Badge variant={attempt.status === 'success' ? 'default' : 'destructive'}>
                            {attempt.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600">{attempt.location}</p>
                        <p className="text-xs text-gray-500">IP: {attempt.ip}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="text-sm font-medium">{attempt.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex justify-center mt-6">
                <Button variant="outline">Load More Events</Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 