<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>React App Diagnostic</title>
    <style>
        body {
            font-family: 'Courier New', monospace;
            margin: 20px;
            background: #1a1a1a;
            color: #00ff00;
        }
        .section {
            background: #2a2a2a;
            border: 1px solid #444;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .found { color: #00ff00; }
        .not-found { color: #ff4444; }
        .info { color: #ffaa00; }
        button {
            background: #333;
            color: #00ff00;
            border: 1px solid #555;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #444;
        }
        pre {
            background: #111;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <h1>🔍 React App Diagnostic Tool</h1>
    <p>This tool diagnoses why React app detection is failing</p>
    
    <button onclick="runDiagnostic()">🔍 Run Diagnostic</button>
    <button onclick="checkMainApp()">⚛️ Check Main App</button>
    <button onclick="clearResults()">🗑️ Clear</button>

    <div id="results"></div>

    <script>
        function log(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `section ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        async function runDiagnostic() {
            clearResults();
            log('<h2>🔍 React App Diagnostic Results</h2>', 'info');
            
            // Step 1: Check if we're on the right page
            log(`<h3>Page Information</h3>
                <p>Current URL: <span class="found">${window.location.href}</span></p>
                <p>Page title: <span class="found">${document.title}</span></p>
                <p>Document ready state: <span class="found">${document.readyState}</span></p>`, 'info');

            // Step 2: Check root element
            const root = document.getElementById('root');
            if (root) {
                log(`<h3>Root Element Analysis</h3>
                    <p>Root element found: <span class="found">✅ Yes</span></p>
                    <p>Has data-reactroot: <span class="${root.hasAttribute('data-reactroot') ? 'found' : 'not-found'}">${root.hasAttribute('data-reactroot') ? '✅ Yes' : '❌ No'}</span></p>
                    <p>Children count: <span class="found">${root.children.length}</span></p>
                    <p>Inner HTML length: <span class="found">${root.innerHTML.length} characters</span></p>
                    <p>Class names: <span class="found">${root.className || 'none'}</span></p>`, 'info');

                if (root.innerHTML.length > 0) {
                    log(`<h3>Root Content Preview</h3>
                        <pre>${root.innerHTML.substring(0, 500)}${root.innerHTML.length > 500 ? '...' : ''}</pre>`, 'info');
                }
            } else {
                log('<h3>Root Element Analysis</h3><p class="not-found">❌ Root element not found!</p>', 'not-found');
            }

            // Step 3: Check for React indicators
            log(`<h3>React Detection Methods</h3>
                <p>React DevTools: <span class="${window.__REACT_DEVTOOLS_GLOBAL_HOOK__ ? 'found' : 'not-found'}">${window.__REACT_DEVTOOLS_GLOBAL_HOOK__ ? '✅ Detected' : '❌ Not detected'}</span></p>
                <p>React on window: <span class="${window.React ? 'found' : 'not-found'}">${window.React ? '✅ Found' : '❌ Not found'}</span></p>
                <p>ReactDOM on window: <span class="${window.ReactDOM ? 'found' : 'not-found'}">${window.ReactDOM ? '✅ Found' : '❌ Not found'}</span></p>`, 'info');

            // Step 4: Check for common React patterns
            const reactPatterns = [
                { name: 'data-reactroot attribute', selector: '[data-reactroot]' },
                { name: 'React class names', selector: '[class*="react"]' },
                { name: 'React data attributes', selector: '[data-react*]' },
                { name: 'Common React components', selector: '[class*="App"], [class*="component"]' }
            ];

            let patternResults = '<h3>React Pattern Detection</h3>';
            reactPatterns.forEach(pattern => {
                const elements = document.querySelectorAll(pattern.selector);
                patternResults += `<p>${pattern.name}: <span class="${elements.length > 0 ? 'found' : 'not-found'}">${elements.length > 0 ? '✅ Found (' + elements.length + ')' : '❌ Not found'}</span></p>`;
            });
            log(patternResults, 'info');

            // Step 5: Check scripts
            const scripts = document.querySelectorAll('script');
            let scriptInfo = '<h3>Script Analysis</h3>';
            scriptInfo += `<p>Total scripts: <span class="found">${scripts.length}</span></p>`;
            
            Array.from(scripts).forEach((script, index) => {
                if (script.src) {
                    scriptInfo += `<p>Script ${index + 1}: <span class="found">${script.src}</span></p>`;
                } else if (script.textContent.length > 0) {
                    scriptInfo += `<p>Inline script ${index + 1}: <span class="found">${script.textContent.length} characters</span></p>`;
                }
            });
            log(scriptInfo, 'info');

            // Step 6: Wait and check again
            log('<h3>Waiting for React to Load...</h3><p>Checking again in 3 seconds...</p>', 'info');
            
            setTimeout(() => {
                const rootAfterWait = document.getElementById('root');
                if (rootAfterWait && rootAfterWait.children.length > 0) {
                    log(`<h3>After Wait - React Status</h3>
                        <p class="found">✅ React app loaded! Found ${rootAfterWait.children.length} child elements</p>`, 'found');
                } else {
                    log('<h3>After Wait - React Status</h3><p class="not-found">❌ React app still not loaded</p>', 'not-found');
                    
                    // Check for errors
                    log('<h3>Checking for Errors</h3>', 'info');
                    if (window.console && window.console.error) {
                        log('<p>Check browser console for JavaScript errors</p>', 'info');
                    }
                }
            }, 3000);
        }

        async function checkMainApp() {
            clearResults();
            log('<h2>⚛️ Main App Check</h2>', 'info');
            
            try {
                // Try to fetch the main app page
                const response = await fetch('/');
                const html = await response.text();
                
                log(`<h3>Main App Response</h3>
                    <p>Status: <span class="found">${response.status}</span></p>
                    <p>Content-Type: <span class="found">${response.headers.get('content-type')}</span></p>
                    <p>HTML length: <span class="found">${html.length} characters</span></p>`, 'info');

                // Check if HTML contains React indicators
                const hasRootDiv = html.includes('id="root"');
                const hasReactScript = html.includes('react') || html.includes('main.tsx') || html.includes('main.jsx');
                const hasViteScript = html.includes('vite') || html.includes('type="module"');

                log(`<h3>HTML Content Analysis</h3>
                    <p>Contains root div: <span class="${hasRootDiv ? 'found' : 'not-found'}">${hasRootDiv ? '✅ Yes' : '❌ No'}</span></p>
                    <p>Contains React script: <span class="${hasReactScript ? 'found' : 'not-found'}">${hasReactScript ? '✅ Yes' : '❌ No'}</span></p>
                    <p>Contains Vite script: <span class="${hasViteScript ? 'found' : 'not-found'}">${hasViteScript ? '✅ Yes' : '❌ No'}</span></p>`, 'info');

                // Show HTML preview
                log(`<h3>HTML Preview</h3>
                    <pre>${html.substring(0, 1000)}${html.length > 1000 ? '...' : ''}</pre>`, 'info');

            } catch (error) {
                log(`<h3>Main App Check Failed</h3>
                    <p class="not-found">Error: ${error.message}</p>`, 'not-found');
            }
        }

        // Auto-run diagnostic
        setTimeout(() => {
            log('<h2>🚀 Auto-Running Diagnostic</h2>', 'info');
            runDiagnostic();
        }, 1000);
    </script>
</body>
</html>
