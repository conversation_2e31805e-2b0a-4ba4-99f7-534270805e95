import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CreditCard, 
  Key, 
  Globe, 
  Shield, 
  CheckCircle, 
  AlertTriangle,
  Save,
  TestTube,
  ExternalLink,
  RefreshCw,
  DollarSign,
  Settings
} from 'lucide-react'
import { stripeService, type StripeConfig } from '@/services/stripeService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

export const StripeConfigComponent: React.FC = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [testing, setTesting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  
  // Configuration state
  const [config, setConfig] = useState<StripeConfig>({
    publishableKey: '',
    secretKey: '',
    webhookSecret: '',
    environment: 'test'
  })
  
  const [isConfigured, setIsConfigured] = useState(false)
  const [webhookUrl, setWebhookUrl] = useState('http://localhost:3005/api/stripe/webhook')

  // Subscription plans
  const [plans, setPlans] = useState([
    {
      id: 'price_starter',
      name: 'Starter',
      amount: 2900, // $29.00
      interval: 'month',
      features: ['Up to 100 claims/month', 'Basic support', '5GB storage']
    },
    {
      id: 'price_professional',
      name: 'Professional',
      amount: 9900, // $99.00
      interval: 'month',
      features: ['Unlimited claims', 'Priority support', '50GB storage', 'Advanced analytics']
    },
    {
      id: 'price_enterprise',
      name: 'Enterprise',
      amount: 29900, // $299.00
      interval: 'month',
      features: ['Unlimited everything', '24/7 support', 'Unlimited storage', 'Custom integrations']
    }
  ])

  useEffect(() => {
    loadConfiguration()
  }, [])

  const loadConfiguration = async () => {
    setLoading(true)
    
    try {
      // In a real implementation, this would load from your database
      // For now, we'll check if configuration exists in localStorage
      const savedConfig = localStorage.getItem('stripe_config')
      if (savedConfig) {
        const parsedConfig = JSON.parse(savedConfig)
        setConfig(parsedConfig)
        setIsConfigured(true)
        
        // Initialize the service
        await stripeService.initialize(parsedConfig)
      }
    } catch (error) {
      console.error('Error loading Stripe configuration:', error)
      setError('Failed to load configuration')
    } finally {
      setLoading(false)
    }
  }

  const saveConfiguration = async () => {
    setLoading(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Validate required fields
      if (!config.publishableKey || !config.secretKey) {
        setError('Please fill in all required fields')
        return
      }

      // Validate key formats
      if (!config.publishableKey.startsWith('pk_')) {
        setError('Invalid publishable key format')
        return
      }

      if (!config.secretKey.startsWith('sk_')) {
        setError('Invalid secret key format')
        return
      }

      // Save to localStorage (in real app, save to database)
      localStorage.setItem('stripe_config', JSON.stringify(config))
      
      // Initialize the service
      await stripeService.initialize(config)
      
      setIsConfigured(true)
      setSuccess('Configuration saved successfully')
      
      toast({
        title: "Configuration Saved",
        description: "Stripe integration has been configured successfully.",
      })
    } catch (error) {
      setError('Failed to save configuration')
      console.error('Error saving Stripe configuration:', error)
    } finally {
      setLoading(false)
    }
  }

  const testConnection = async () => {
    setTesting(true)
    setError(null)
    setSuccess(null)
    
    try {
      // Test the Stripe connection by creating a test customer
      const { data, error } = await stripeService.createCustomer({
        email: '<EMAIL>',
        name: 'Test Customer',
        metadata: { test: 'true' }
      })
      
      if (error) {
        setError(`Connection test failed: ${error.message}`)
        return
      }
      
      if (data) {
        setSuccess(`Connection test successful! Test customer created: ${data.id}`)
        
        toast({
          title: "Connection Test Successful",
          description: "Stripe integration is working correctly.",
        })
      }
    } catch (error) {
      setError('Connection test failed')
      console.error('Stripe connection test error:', error)
    } finally {
      setTesting(false)
    }
  }

  const resetConfiguration = () => {
    setConfig({
      publishableKey: '',
      secretKey: '',
      webhookSecret: '',
      environment: 'test'
    })
    setIsConfigured(false)
    localStorage.removeItem('stripe_config')
    
    toast({
      title: "Configuration Reset",
      description: "Stripe configuration has been cleared.",
    })
  }

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount / 100)
  }

  // Check if user has admin permissions
  if (user?.role !== 'admin') {
    return (
      <Alert>
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription>
          You need administrator privileges to configure Stripe integration.
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Stripe Configuration</h2>
          <p className="text-gray-600">Configure payment processing and billing</p>
        </div>
        <div className="flex items-center space-x-2">
          {isConfigured && (
            <Badge variant="default" className="flex items-center space-x-1">
              <CheckCircle className="h-3 w-3" />
              <span>Configured</span>
            </Badge>
          )}
        </div>
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert className="border-green-200 bg-green-50">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-green-700">{success}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="configuration" className="space-y-6">
        <TabsList>
          <TabsTrigger value="configuration">Configuration</TabsTrigger>
          <TabsTrigger value="testing">Testing</TabsTrigger>
          <TabsTrigger value="plans">Subscription Plans</TabsTrigger>
          <TabsTrigger value="webhooks">Webhooks</TabsTrigger>
        </TabsList>

        <TabsContent value="configuration" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <CreditCard className="h-5 w-5" />
                <span>API Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure your Stripe API keys and settings
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Environment</label>
                <select
                  value={config.environment}
                  onChange={(e) => setConfig({ ...config, environment: e.target.value as 'test' | 'live' })}
                  className="w-full p-2 border rounded-lg"
                >
                  <option value="test">Test Mode</option>
                  <option value="live">Live Mode</option>
                </select>
                <p className="text-xs text-gray-500">
                  Use test mode for development and live mode for production
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Publishable Key *</label>
                <Input
                  placeholder="pk_test_... or pk_live_..."
                  value={config.publishableKey}
                  onChange={(e) => setConfig({ ...config, publishableKey: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Your Stripe publishable key (safe to expose in frontend)
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Secret Key *</label>
                <Input
                  type="password"
                  placeholder="sk_test_... or sk_live_..."
                  value={config.secretKey}
                  onChange={(e) => setConfig({ ...config, secretKey: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Your Stripe secret key (keep this secure)
                </p>
              </div>

              <div className="space-y-2">
                <label className="text-sm font-medium">Webhook Secret</label>
                <Input
                  placeholder="whsec_..."
                  value={config.webhookSecret}
                  onChange={(e) => setConfig({ ...config, webhookSecret: e.target.value })}
                />
                <p className="text-xs text-gray-500">
                  Webhook endpoint secret for verifying webhook signatures
                </p>
              </div>

              <div className="flex space-x-2">
                <Button 
                  onClick={saveConfiguration}
                  disabled={loading}
                  className="flex-1"
                >
                  {loading ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <Save className="mr-2 h-4 w-4" />
                  )}
                  Save Configuration
                </Button>
                <Button 
                  variant="outline"
                  onClick={resetConfiguration}
                  disabled={loading}
                >
                  Reset
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Setup Instructions</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">1</div>
                  <div>
                    <h4 className="font-medium">Create Stripe Account</h4>
                    <p className="text-sm text-gray-600">
                      Sign up for a Stripe account at{' '}
                      <a href="https://stripe.com" target="_blank" rel="noopener noreferrer" className="text-blue-600 hover:underline">
                        stripe.com
                      </a>
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                  <div>
                    <h4 className="font-medium">Get API Keys</h4>
                    <p className="text-sm text-gray-600">
                      Navigate to Developers → API keys in your Stripe dashboard to get your keys
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                  <div>
                    <h4 className="font-medium">Configure Webhooks</h4>
                    <p className="text-sm text-gray-600">
                      Set up webhook endpoints to receive real-time payment notifications
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start space-x-3">
                  <div className="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">4</div>
                  <div>
                    <h4 className="font-medium">Test Integration</h4>
                    <p className="text-sm text-gray-600">
                      Use the testing tab to verify your configuration is working correctly
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="testing" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <TestTube className="h-5 w-5" />
                <span>Connection Testing</span>
              </CardTitle>
              <CardDescription>
                Test your Stripe integration to ensure it's working correctly
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8">
                <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Test Stripe Connection</h3>
                <p className="text-gray-500 mb-6">
                  This will create a test customer to verify your Stripe integration is working properly.
                </p>
                
                <Button 
                  onClick={testConnection}
                  disabled={testing || !isConfigured}
                  size="lg"
                >
                  {testing ? (
                    <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                  ) : (
                    <TestTube className="mr-2 h-4 w-4" />
                  )}
                  {testing ? 'Testing Connection...' : 'Test Connection'}
                </Button>
                
                {!isConfigured && (
                  <p className="text-sm text-red-600 mt-2">
                    Please configure Stripe settings first
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="plans" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <DollarSign className="h-5 w-5" />
                <span>Subscription Plans</span>
              </CardTitle>
              <CardDescription>
                Manage your subscription plans and pricing
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {plans.map((plan) => (
                  <div key={plan.id} className="border rounded-lg p-6">
                    <div className="text-center mb-4">
                      <h3 className="text-lg font-semibold">{plan.name}</h3>
                      <div className="text-3xl font-bold text-blue-600">
                        {formatCurrency(plan.amount)}
                      </div>
                      <div className="text-sm text-gray-500">per {plan.interval}</div>
                    </div>
                    
                    <ul className="space-y-2 mb-6">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center text-sm">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2" />
                          {feature}
                        </li>
                      ))}
                    </ul>
                    
                    <Button variant="outline" className="w-full">
                      <Settings className="h-4 w-4 mr-2" />
                      Configure
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="webhooks" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Webhook Configuration</span>
              </CardTitle>
              <CardDescription>
                Configure webhooks to receive real-time payment updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Webhook URL</label>
                <Input
                  placeholder="Enter webhook endpoint URL"
                  value={webhookUrl}
                  onChange={(e) => setWebhookUrl(e.target.value)}
                />
                <p className="text-xs text-gray-500">
                  This URL will receive webhook notifications from Stripe
                </p>
              </div>

              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-medium mb-2">Required Events</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• payment_intent.succeeded</li>
                  <li>• payment_intent.payment_failed</li>
                  <li>• customer.subscription.created</li>
                  <li>• customer.subscription.updated</li>
                  <li>• customer.subscription.deleted</li>
                  <li>• invoice.payment_succeeded</li>
                  <li>• invoice.payment_failed</li>
                </ul>
              </div>

              <Button variant="outline" className="w-full">
                <ExternalLink className="mr-2 h-4 w-4" />
                Configure in Stripe Dashboard
              </Button>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
