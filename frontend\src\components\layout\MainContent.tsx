import { ReactNode, Suspense } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Loader2 } from 'lucide-react'

interface MainContentProps {
  children: ReactNode
}

function LoadingFallback() {
  return (
    <div className="flex items-center justify-center min-h-[400px]">
      <div className="flex flex-col items-center space-y-4">
        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
        <p className="text-sm text-gray-600 dark:text-gray-400">Loading...</p>
      </div>
    </div>
  )
}

function ErrorBoundary({ children }: { children: ReactNode }) {
  return (
    <div className="min-h-[400px]">
      {children}
    </div>
  )
}

export function MainContent({ children }: MainContentProps) {
  return (
    <main className="p-6">
      <div className="max-w-7xl mx-auto">
        <ErrorBoundary>
          <Suspense fallback={<LoadingFallback />}>
            {children}
          </Suspense>
        </ErrorBoundary>
      </div>
    </main>
  )
}
