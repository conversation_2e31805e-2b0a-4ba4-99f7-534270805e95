import React, { useEffect, useState } from 'react';
import { TrendingUp, AlertTriangle, CheckCircle, ArrowUp, Users, Database, MapPin } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { PlanEnforcementEngine, User } from '../services/planEnforcementService';

interface UsageDashboardWidgetProps {
  currentUser: User;
  onUpgradeClick?: () => void;
}

interface UsageMetrics {
  recordUtilization: number;
  stateUtilization: number;
  warnings: string[];
}

export const UsageDashboardWidget: React.FC<UsageDashboardWidgetProps> = ({
  currentUser,
  onUpgradeClick
}) => {
  const [usageMetrics, setUsageMetrics] = useState<UsageMetrics | null>(null);
  const [planEnforcement] = useState(new PlanEnforcementEngine());

  useEffect(() => {
    const loadUsageMetrics = async () => {
      try {
        const metrics = await planEnforcement.checkUsageLimits(currentUser);
        setUsageMetrics(metrics);
      } catch (error) {
        console.error('Failed to load usage metrics:', error);
      }
    };

    loadUsageMetrics();
  }, [currentUser, planEnforcement]);

  const userPlan = planEnforcement.getUserPlan(currentUser);
  const upgradeOptions = planEnforcement.getUpgradeOptions(currentUser.planId);

  // Get utilization color based on percentage
  const getUtilizationColor = (percentage: number) => {
    if (percentage >= 90) return 'text-red-600';
    if (percentage >= 75) return 'text-yellow-600';
    return 'text-green-600';
  };

  const getUtilizationBgColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-100';
    if (percentage >= 75) return 'bg-yellow-100';
    return 'bg-green-100';
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 90) return 'bg-red-500';
    if (percentage >= 75) return 'bg-yellow-500';
    return 'bg-blue-500';
  };

  const formatNumber = (num: number) => {
    if (num === -1) return 'Unlimited';
    return num.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* Plan Overview Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Current Plan: {userPlan.name.toUpperCase()}
            <Badge variant="outline" className="ml-2">
              ${userPlan.price}/month
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">{userPlan.description}</p>
          
          {/* Plan Features */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-3 mb-4">
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.csvImport ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.csvImport ? 'text-gray-900' : 'text-gray-400'}`}>
                CSV Import
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.batchProcessing ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.batchProcessing ? 'text-gray-900' : 'text-gray-400'}`}>
                Batch Processing
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.aiDiscovery ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.aiDiscovery ? 'text-gray-900' : 'text-gray-400'}`}>
                AI Discovery
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.teamCollaboration ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.teamCollaboration ? 'text-gray-900' : 'text-gray-400'}`}>
                Team Collaboration
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.advancedAnalytics ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.advancedAnalytics ? 'text-gray-900' : 'text-gray-400'}`}>
                Advanced Analytics
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CheckCircle className={`w-4 h-4 ${userPlan.features.apiAccess ? 'text-green-600' : 'text-gray-400'}`} />
              <span className={`text-sm ${userPlan.features.apiAccess ? 'text-gray-900' : 'text-gray-400'}`}>
                API Access
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Usage Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Record Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="w-5 h-5" />
              Record Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {currentUser.currentUsage.recordCount.toLocaleString()} / {formatNumber(userPlan.maxRecords)}
                </span>
                <span className={`text-sm font-medium ${getUtilizationColor(usageMetrics?.recordUtilization || 0)}`}>
                  {usageMetrics?.recordUtilization.toFixed(1) || '0.0'}%
                </span>
              </div>
              
              <div className="relative">
                <Progress 
                  value={usageMetrics?.recordUtilization || 0} 
                  className="h-3"
                />
                <div 
                  className={`absolute top-0 left-0 h-3 rounded-full transition-all ${getProgressColor(usageMetrics?.recordUtilization || 0)}`}
                  style={{ width: `${Math.min(usageMetrics?.recordUtilization || 0, 100)}%` }}
                />
              </div>
              
              {usageMetrics && usageMetrics.recordUtilization >= 80 && (
                <div className={`p-3 rounded-lg ${getUtilizationBgColor(usageMetrics.recordUtilization)}`}>
                  <p className={`text-sm ${getUtilizationColor(usageMetrics.recordUtilization)}`}>
                    {usageMetrics.recordUtilization >= 90 
                      ? '⚠️ Near limit - consider upgrading' 
                      : '📊 Approaching limit'
                    }
                  </p>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* State Usage */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MapPin className="w-5 h-5" />
              State Usage
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-600">
                  {currentUser.currentUsage.activeStates.length} / {formatNumber(userPlan.maxStates)}
                </span>
                <span className={`text-sm font-medium ${getUtilizationColor(usageMetrics?.stateUtilization || 0)}`}>
                  {usageMetrics?.stateUtilization.toFixed(1) || '0.0'}%
                </span>
              </div>
              
              <div className="relative">
                <Progress 
                  value={usageMetrics?.stateUtilization || 0} 
                  className="h-3"
                />
                <div 
                  className={`absolute top-0 left-0 h-3 rounded-full transition-all ${getProgressColor(usageMetrics?.stateUtilization || 0)}`}
                  style={{ width: `${Math.min(usageMetrics?.stateUtilization || 0, 100)}%` }}
                />
              </div>
              
              {currentUser.currentUsage.activeStates.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {currentUser.currentUsage.activeStates.map((state, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {state.toUpperCase()}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Warnings and Recommendations */}
      {usageMetrics && usageMetrics.warnings.length > 0 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <div className="space-y-1">
              {usageMetrics.warnings.map((warning, index) => (
                <p key={index} className="text-sm">{warning}</p>
              ))}
            </div>
          </AlertDescription>
        </Alert>
      )}

      {/* Upgrade Recommendations */}
      {upgradeOptions.length > 0 && (usageMetrics?.recordUtilization || 0) >= 75 && (
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-900">
              <TrendingUp className="w-5 h-5" />
              Upgrade Recommendation
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-blue-800 mb-4">
              You're approaching your plan limits. Upgrade to unlock more capacity and features.
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
              {upgradeOptions.slice(0, 2).map((plan) => (
                <div key={plan.id} className="p-3 bg-white rounded-lg border border-blue-200">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-semibold text-blue-900">{plan.name.toUpperCase()}</h4>
                    <span className="text-sm font-bold text-blue-700">${plan.price}/mo</span>
                  </div>
                  <div className="text-xs text-blue-700 space-y-1">
                    <p>• {formatNumber(plan.maxRecords)} records</p>
                    <p>• {formatNumber(plan.maxStates)} states</p>
                    <p>• {plan.features.batchProcessing ? 'Batch processing' : 'Standard processing'}</p>
                  </div>
                </div>
              ))}
            </div>
            
            <Button 
              onClick={onUpgradeClick}
              className="w-full"
              size="lg"
            >
              <ArrowUp className="w-4 h-4 mr-2" />
              View Upgrade Options
            </Button>
          </CardContent>
        </Card>
      )}

      {/* Subscription Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="w-5 h-5" />
            Subscription Status
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <p className={`text-lg font-bold ${
                currentUser.subscription.status === 'active' ? 'text-green-600' : 
                currentUser.subscription.status === 'trial' ? 'text-blue-600' : 'text-red-600'
              }`}>
                {currentUser.subscription.status.toUpperCase()}
              </p>
              <p className="text-sm text-gray-600">Status</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-gray-700">
                {new Date(currentUser.subscription.currentPeriodEnd).toLocaleDateString()}
              </p>
              <p className="text-sm text-gray-600">Billing Cycle</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-purple-600">
                {currentUser.currentUsage.monthlyUploads}
              </p>
              <p className="text-sm text-gray-600">Monthly Uploads</p>
            </div>
            <div className="text-center">
              <p className="text-lg font-bold text-orange-600">
                {currentUser.licensedStates.length}
              </p>
              <p className="text-sm text-gray-600">Licensed States</p>
            </div>
          </div>
          
          {currentUser.subscription.status === 'trial' && currentUser.subscription.trialEnd && (
            <div className="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-200">
              <p className="text-sm text-yellow-800">
                ⏰ Trial ends on {new Date(currentUser.subscription.trialEnd).toLocaleDateString()}
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 