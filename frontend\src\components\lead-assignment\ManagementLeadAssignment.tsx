// Management Lead Assignment Dashboard
// Specialized interface for <PERSON><PERSON>, Senior Agent, and Compliance Officer roles

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter 
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  UserPlus, 
  Target, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Plus,
  Filter,
  Search,
  BarChart3,
  Zap,
  Brain,
  Settings,
  Eye,
  Edit,
  PhoneCall,
  Mail,
  FileText,
  Calendar,
  DollarSign,
  MapPin,
  Building,
  Star,
  Loader,
  RefreshCw,
  Shield,
  AlertCircle
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { FEATURE_FLAGS } from '@/config/features';
import { toast } from 'sonner';
import type {
  Agent,
  AssignmentCriteria,
  AssignmentRecommendation,
  WorkloadAnalysis,
  UserRole,
  ManagementRole,
  Lead,
  AssignmentAction
} from '@/types/lead-assignment';
import { LeadStatus } from '@/types/lead-assignment';
import { 
  ROLE_PERMISSIONS, 
  isManagementRole, 
  canAssignLeads,
  canBulkAssign,
  canViewAllLeads,
  getMaxAssignableLeads
} from '@/types/lead-assignment';
import { LeadAssignmentService } from '@/services/leadAssignmentService';
import { LeadTrackingService } from '@/services/leadTrackingService';

interface ManagementLeadAssignmentProps {
  leads?: Lead[];
  onAssignmentComplete?: (assignments: AssignmentAction[]) => void;
  className?: string;
}

export const ManagementLeadAssignment: React.FC<ManagementLeadAssignmentProps> = ({
  leads = [],
  onAssignmentComplete,
  className = ""
}) => {
  const { user } = useAuth();
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);
  const [assignmentRecommendations, setAssignmentRecommendations] = useState<AssignmentRecommendation[]>([]);
  const [workloadAnalysis, setWorkloadAnalysis] = useState<WorkloadAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [assignmentCriteria, setAssignmentCriteria] = useState<AssignmentCriteria>({});
  const [distributionMethod, setDistributionMethod] = useState<'round_robin' | 'workload_based' | 'expertise_based'>('workload_based');
  const [showAssignmentPreview, setShowAssignmentPreview] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterState, setFilterState] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');

  const assignmentService = useMemo(() => new LeadAssignmentService(), []);
  const trackingService = useMemo(() => new LeadTrackingService(), []);

  // Check if user is authorized for management functions
  const userRole = user?.role as UserRole;
  const isAuthorizedManagement = userRole && isManagementRole(userRole);
  const canUserAssignLeads = userRole && canAssignLeads(userRole);
  const canUserBulkAssign = userRole && canBulkAssign(userRole);
  const maxAssignable = userRole ? getMaxAssignableLeads(userRole) : 0;

  // Role-specific capabilities
  const isAdmin = userRole === 'admin';
  const isSeniorAgent = userRole === 'senior_agent';
  const isCompliance = userRole === 'compliance';

  useEffect(() => {
    if (isAuthorizedManagement && canUserAssignLeads) {
      loadInitialData();
    }
  }, [isAuthorizedManagement, canUserAssignLeads]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // Load available agents based on role permissions
      const agents = await loadAvailableAgents();
      setAvailableAgents(agents);

      // Load workload analysis
      const workload = await assignmentService.analyzeWorkloads();
      setWorkloadAnalysis(workload);

      console.log(`✅ Loaded ${agents.length} agents for ${userRole} management interface`);
    } catch (error) {
      console.error('Error loading assignment data:', error);
      toast.error('Failed to load assignment data');
    } finally {
      setIsLoading(false);
    }
  };

  const loadAvailableAgents = async (): Promise<Agent[]> => {
    // Role-based agent filtering
    const mockAgents: Agent[] = [
      {
        id: 'agent-1',
        name: 'John Smith',
        role: 'junior_agent',
        email: '<EMAIL>',
        activeLeads: 15,
        maxLeads: 25,
        stateExperience: ['CA', 'NV', 'AZ'],
        propertyTypeExperience: ['stocks', 'bonds'],
        successRate: 0.78,
        experienceLevel: 'junior',
        workloadScore: 60,
        isActive: true
      },
      {
        id: 'agent-2',
        name: 'Sarah Johnson',
        role: 'senior_agent',
        email: '<EMAIL>',
        activeLeads: 30,
        maxLeads: 40,
        stateExperience: ['TX', 'FL', 'NY'],
        propertyTypeExperience: ['real_estate', 'business_assets'],
        successRate: 0.92,
        experienceLevel: 'senior',
        workloadScore: 75,
        isActive: true
      },
      {
        id: 'agent-3',
        name: 'Mike Rodriguez',
        role: 'junior_agent',
        email: '<EMAIL>',
        activeLeads: 8,
        maxLeads: 20,
        stateExperience: ['CA', 'OR', 'WA'],
        propertyTypeExperience: ['insurance', 'retirement'],
        successRate: 0.85,
        experienceLevel: 'junior',
        workloadScore: 40,
        isActive: true
      },
      {
        id: 'agent-4',
        name: 'Lisa Chen',
        role: 'contractor',
        email: '<EMAIL>',
        activeLeads: 5,
        maxLeads: 15,
        stateExperience: ['NY', 'NJ', 'CT'],
        propertyTypeExperience: ['stocks', 'real_estate'],
        successRate: 0.88,
        experienceLevel: 'senior',
        workloadScore: 33,
        isActive: true
      }
    ];

    // Filter agents based on management role permissions
    if (isSeniorAgent) {
      // Senior agents can only assign to junior agents and contractors
      return mockAgents.filter(agent => 
        agent.role === 'junior_agent' || agent.role === 'contractor'
      );
    } else if (isCompliance) {
      // Compliance can see all agents for reassignment purposes
      return mockAgents;
    } else if (isAdmin) {
      // Admin can assign to anyone
      return mockAgents;
    }

    return mockAgents;
  };

  const handleLeadSelection = (leadId: string, selected: boolean) => {
    setSelectedLeads(prev => 
      selected 
        ? [...prev, leadId]
        : prev.filter(id => id !== leadId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    const filteredLeads = getFilteredLeads();
    setSelectedLeads(selected ? filteredLeads.map(lead => lead.id) : []);
  };

  const handleBulkAssignment = async () => {
    if (!canUserBulkAssign || selectedLeads.length === 0) {
      toast.error('Cannot perform bulk assignment');
      return;
    }

    // Check assignment limits
    if (maxAssignable > 0 && selectedLeads.length > maxAssignable) {
      toast.error(`Cannot assign more than ${maxAssignable} leads`);
      return;
    }

    setIsLoading(true);
    try {
      const assignments = await assignmentService.executeBulkAssignment(
        selectedLeads,
        assignmentCriteria,
        distributionMethod,
        user!.id
      );

      console.log(`✅ ${userRole} completed bulk assignment:`, assignments);
      toast.success(`Successfully assigned ${selectedLeads.length} leads`);
      onAssignmentComplete?.(assignments as any);
      setSelectedLeads([]);
      await loadInitialData(); // Refresh data
    } catch (error) {
      console.error('❌ Error executing bulk assignment:', error);
      toast.error('Failed to assign leads');
    } finally {
      setIsLoading(false);
    }
  };

  const handleIndividualAssignment = async (leadId: string, agentId: string) => {
    setIsLoading(true);
    try {
      const assignment = await assignmentService.assignLeadToAgent(
        leadId,
        agentId,
        user!.id,
        getAssignmentReason()
      );

      if (assignment) {
        console.log(`✅ ${userRole} completed individual assignment:`, assignment);
        toast.success('Lead assigned successfully');
        await loadInitialData(); // Refresh data
      }
    } catch (error) {
      console.error('❌ Error executing individual assignment:', error);
      toast.error('Failed to assign lead');
    } finally {
      setIsLoading(false);
    }
  };

  const getAssignmentReason = (): string => {
    if (isCompliance) {
      return 'Compliance reassignment';
    } else if (isSeniorAgent) {
      return 'Team assignment by senior agent';
    } else if (isAdmin) {
      return 'Administrative assignment';
    }
    return 'Management assignment';
  };

  const generateAIRecommendations = async () => {
    if (selectedLeads.length === 0) {
      toast.error('Please select leads first');
      return;
    }

    setIsLoading(true);
    try {
      const recommendations = await assignmentService.calculateOptimalAssignment(
        selectedLeads,
        availableAgents,
        { ...assignmentCriteria, agentExperienceLevel: undefined }
      );
      setAssignmentRecommendations(recommendations);
      setShowAssignmentPreview(true);
      toast.success('AI recommendations generated');
    } catch (error) {
      console.error('Error generating AI recommendations:', error);
      toast.error('Failed to generate recommendations');
    } finally {
      setIsLoading(false);
    }
  };

  const getFilteredLeads = () => {
    return leads.filter(lead => {
      const matchesSearch = lead.owner_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           lead.id.toLowerCase().includes(searchTerm.toLowerCase());
      const matchesState = filterState === 'all' || lead.state === filterState;
      const matchesStatus = filterStatus === 'all' || lead.status === filterStatus;
      
      return matchesSearch && matchesState && matchesStatus;
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusColor = (status: LeadStatus) => {
    const colors = {
      [LeadStatus.NEW]: 'bg-blue-100 text-blue-800',
      [LeadStatus.ASSIGNED]: 'bg-yellow-100 text-yellow-800',
      [LeadStatus.IN_PROGRESS]: 'bg-purple-100 text-purple-800',
      [LeadStatus.CONTACTED]: 'bg-green-100 text-green-800',
      [LeadStatus.COMPLETED]: 'bg-emerald-100 text-emerald-800',
      [LeadStatus.CLOSED]: 'bg-gray-100 text-gray-800'
    };
    return colors[status] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      urgent: 'bg-red-100 text-red-800',
      high: 'bg-orange-100 text-orange-800',
      medium: 'bg-yellow-100 text-yellow-800',
      low: 'bg-green-100 text-green-800'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  // Authorization check
  if (!isAuthorizedManagement) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600">You don't have permission to access lead assignment management.</p>
            <p className="text-sm text-gray-500 mt-2">
              This interface is restricted to Admin, Senior Agent, and Compliance roles.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!canUserAssignLeads) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Limited Access</h3>
            <p className="text-gray-600">Your {userRole} role has limited assignment capabilities.</p>
            <p className="text-sm text-gray-500 mt-2">
              Contact your administrator to request additional permissions.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const filteredLeads = getFilteredLeads();

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with Role-Specific Information */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 flex items-center">
            <Target className="h-6 w-6 mr-2 text-blue-600" />
            Lead Assignment Management
            <Badge className="ml-3 bg-blue-100 text-blue-800">
              {userRole === 'admin' ? 'Admin' : 
               userRole === 'senior_agent' ? 'Senior Agent' : 
               'Compliance'}
            </Badge>
          </h2>
          <p className="text-gray-600">
            {isAdmin && 'Full system lead assignment and management capabilities'}
            {isSeniorAgent && 'Team lead assignment and workload management'}
            {isCompliance && 'Compliance-based lead reassignment and oversight'}
          </p>
          {maxAssignable > 0 && (
            <p className="text-sm text-gray-500">
              Assignment limit: {maxAssignable} leads
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          {canUserBulkAssign && (
            <Button 
              onClick={generateAIRecommendations}
              disabled={selectedLeads.length === 0 || isLoading}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white"
            >
              <Brain className="h-4 w-4 mr-2" />
              AI Recommendations
            </Button>
          )}
          <Button 
            onClick={() => setShowAssignmentPreview(true)}
            disabled={selectedLeads.length === 0}
            variant="outline"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview Assignment
          </Button>
        </div>
      </div>

      <Tabs defaultValue="assignment" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="assignment">Lead Assignment</TabsTrigger>
          <TabsTrigger value="agents">Agent Workload</TabsTrigger>
          <TabsTrigger value="analytics">Assignment Analytics</TabsTrigger>
          <TabsTrigger value="settings">Assignment Rules</TabsTrigger>
        </TabsList>

        {/* Lead Assignment Tab */}
        <TabsContent value="assignment" className="space-y-6">
          {/* Assignment Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-blue-600" />
                Assignment Controls
                {selectedLeads.length > 0 && (
                  <Badge className="ml-3 bg-green-100 text-green-800">
                    {selectedLeads.length} selected
                  </Badge>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                {/* Search */}
                <div>
                  <Label className="text-sm font-medium">Search Leads</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search by name or ID..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* State Filter */}
                <div>
                  <Label className="text-sm font-medium">State Filter</Label>
                  <Select value={filterState} onValueChange={setFilterState}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All States</SelectItem>
                      <SelectItem value="CA">California</SelectItem>
                      <SelectItem value="TX">Texas</SelectItem>
                      <SelectItem value="FL">Florida</SelectItem>
                      <SelectItem value="NY">New York</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Distribution Method */}
                {canUserBulkAssign && (
                  <div>
                    <Label className="text-sm font-medium">Distribution Method</Label>
                    <Select value={distributionMethod} onValueChange={(value: any) => setDistributionMethod(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="round_robin">Round Robin</SelectItem>
                        <SelectItem value="workload_based">Workload Based</SelectItem>
                        <SelectItem value="expertise_based">Expertise Based</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Bulk Assignment Button */}
                {canUserBulkAssign && (
                  <div className="flex items-end">
                    <Button 
                      onClick={handleBulkAssignment}
                      disabled={selectedLeads.length === 0 || isLoading}
                      className="w-full bg-blue-600 hover:bg-blue-700"
                    >
                      {isLoading ? (
                        <Loader className="h-4 w-4 mr-2 animate-spin" />
                      ) : (
                        <Zap className="h-4 w-4 mr-2" />
                      )}
                      Assign Selected ({selectedLeads.length})
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Leads Table */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  Available Leads ({filteredLeads.length})
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    checked={selectedLeads.length === filteredLeads.length && filteredLeads.length > 0}
                    onCheckedChange={(checked) => handleSelectAll(!!checked)}
                  />
                  <span className="text-sm text-gray-600">Select All</span>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">Select</TableHead>
                      <TableHead>Lead Details</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>State</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Assigned To</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredLeads.map((lead) => (
                      <TableRow key={lead.id}>
                        <TableCell>
                          <Checkbox 
                            checked={selectedLeads.includes(lead.id)}
                            onCheckedChange={(checked) => handleLeadSelection(lead.id, !!checked)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{lead.owner_name}</div>
                            <div className="text-sm text-gray-500">{lead.property_type}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-green-600">
                            {formatCurrency(lead.amount)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{lead.state}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(lead.status)}>{lead.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getPriorityColor(lead.priority)}>{lead.priority}</Badge>
                        </TableCell>
                        <TableCell>
                          {lead.assigned_agent_id ? (
                            <span className="text-sm text-gray-600">
                              {availableAgents.find(a => a.id === lead.assigned_agent_id)?.name || 'Unknown Agent'}
                            </span>
                          ) : (
                            <span className="text-sm text-gray-400">Unassigned</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Select onValueChange={(agentId) => handleIndividualAssignment(lead.id, agentId)}>
                              <SelectTrigger className="w-32">
                                <SelectValue placeholder="Assign to..." />
                              </SelectTrigger>
                              <SelectContent>
                                {availableAgents.map((agent) => (
                                  <SelectItem key={agent.id} value={agent.id}>
                                    {agent.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agent Workload Tab */}
        <TabsContent value="agents" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2 text-blue-600" />
                Agent Workload Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {availableAgents.map((agent) => (
                  <Card key={agent.id} className="p-4">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium">{agent.name}</h4>
                      <Badge className={agent.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                        {agent.role}
                      </Badge>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Active Leads:</span>
                        <span>{agent.activeLeads}/{agent.maxLeads}</span>
                      </div>
                      <Progress 
                        value={(agent.activeLeads / agent.maxLeads) * 100} 
                        className="h-2"
                      />
                      <div className="flex justify-between text-sm">
                        <span>Success Rate:</span>
                        <span>{Math.round(agent.successRate * 100)}%</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span>Experience:</span>
                        <span>{agent.experienceLevel}</span>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2 text-blue-600" />
                Assignment Analytics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <div className="text-2xl font-bold text-blue-600">{leads.length}</div>
                  <div className="text-sm text-gray-600">Total Leads</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <div className="text-2xl font-bold text-green-600">
                    {leads.filter(l => l.assigned_agent_id).length}
                  </div>
                  <div className="text-sm text-gray-600">Assigned Leads</div>
                </div>
                <div className="text-center p-4 bg-yellow-50 rounded-lg">
                  <div className="text-2xl font-bold text-yellow-600">
                    {leads.filter(l => !l.assigned_agent_id).length}
                  </div>
                  <div className="text-sm text-gray-600">Unassigned Leads</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-blue-600" />
                Assignment Rules & Settings
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="font-medium mb-2">Role Permissions</h4>
                  <div className="text-sm text-gray-600 space-y-1">
                    <div>• Can assign leads: {canUserAssignLeads ? 'Yes' : 'No'}</div>
                    <div>• Can bulk assign: {canUserBulkAssign ? 'Yes' : 'No'}</div>
                    <div>• Max assignable: {maxAssignable === -1 ? 'Unlimited' : maxAssignable}</div>
                    <div>• View all leads: {canViewAllLeads(userRole) ? 'Yes' : 'No'}</div>
                  </div>
                </div>
                
                {isCompliance && (
                  <div className="bg-yellow-50 p-4 rounded-lg">
                    <h4 className="font-medium text-yellow-800 mb-2">Compliance Assignment Rules</h4>
                    <ul className="text-sm text-yellow-700 space-y-1">
                      <li>• Only reassign for compliance violations</li>
                      <li>• Document reason for each reassignment</li>
                      <li>• Maximum 10 leads per assignment action</li>
                      <li>• Audit trail automatically maintained</li>
                    </ul>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assignment Preview Dialog */}
      <Dialog open={showAssignmentPreview} onOpenChange={setShowAssignmentPreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Assignment Preview</DialogTitle>
          </DialogHeader>
          <div className="max-h-96 overflow-y-auto">
            {assignmentRecommendations.length > 0 ? (
              <div className="space-y-2">
                {assignmentRecommendations.map((rec) => (
                  <div key={rec.leadId} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <span className="font-medium">Lead {rec.leadId}</span>
                      <span className="text-gray-600 ml-2">→ {rec.recommendedAgent.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium">Confidence: {Math.round(rec.confidence * 100)}%</div>
                      <div className="text-xs text-gray-500">{rec.reasoning.join(', ')}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8 text-gray-500">
                No recommendations generated yet
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignmentPreview(false)}>
              Cancel
            </Button>
            {assignmentRecommendations.length > 0 && (
              <Button onClick={handleBulkAssignment} disabled={isLoading}>
                Execute Assignments
              </Button>
            )}
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 