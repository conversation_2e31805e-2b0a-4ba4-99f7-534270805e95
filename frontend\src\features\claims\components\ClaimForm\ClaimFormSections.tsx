import React from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { FormField, SelectField, TextareaField } from '@/components/ui/form-field';
import { User, MapPin, DollarSign, FileText, Building } from 'lucide-react';
import { ClaimFormData } from '../../types';
import { US_STATES, PROPERTY_TYPES, PRIORITY_LEVELS } from '@/constants/formOptions';
import { ValidationErrors } from '@/utils/validation/formValidation';

interface SectionProps {
  formData: ClaimFormData;
  errors: ValidationErrors;
  onChange: (field: keyof ClaimFormData, value: string) => void;
}

export const OwnerInformationSection: React.FC<SectionProps> = ({ formData, errors, onChange }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <User className="h-5 w-5" />
        Owner Information
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Owner Name"
          name="owner_name"
          value={formData.owner_name || ''}
          onChange={(value) => onChange('owner_name', value)}
          error={errors.owner_name}
          required
        />
        <FormField
          label="First Name"
          name="owner_first_name"
          value={formData.owner_first_name || ''}
          onChange={(value) => onChange('owner_first_name', value)}
          error={errors.owner_first_name}
        />
        <FormField
          label="Last Name"
          name="owner_last_name"
          value={formData.owner_last_name || ''}
          onChange={(value) => onChange('owner_last_name', value)}
          error={errors.owner_last_name}
        />
        <FormField
          label="Business Name"
          name="owner_business_name"
          value={formData.owner_business_name || ''}
          onChange={(value) => onChange('owner_business_name', value)}
          error={errors.owner_business_name}
        />
      </div>
    </CardContent>
  </Card>
);

export const PropertyInformationSection: React.FC<SectionProps> = ({ formData, errors, onChange }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <DollarSign className="h-5 w-5" />
        Property Information
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Property ID"
          name="property_id"
          value={formData.property_id || ''}
          onChange={(value) => onChange('property_id', value)}
          error={errors.property_id}
        />
        <FormField
          label="Amount"
          name="amount"
          type="number"
          value={formData.amount?.toString() || ''}
          onChange={(value) => onChange('amount', value)}
          error={errors.amount}
          required
        />
        <SelectField
          label="Property Type"
          name="property_type"
          value={formData.property_type || ''}
          onChange={(value) => onChange('property_type', value)}
          options={PROPERTY_TYPES}
          error={errors.property_type}
        />
        <SelectField
          label="Priority"
          name="priority"
          value={formData.priority || 'medium'}
          onChange={(value) => onChange('priority', value)}
          options={PRIORITY_LEVELS}
          error={errors.priority}
        />
        <FormField
          label="Securities Name"
          name="securities_name"
          value={formData.securities_name || ''}
          onChange={(value) => onChange('securities_name', value)}
          error={errors.securities_name}
        />
        <FormField
          label="CUSIP"
          name="cusip"
          value={formData.cusip || ''}
          onChange={(value) => onChange('cusip', value)}
          error={errors.cusip}
        />
        <FormField
          label="Shares Reported"
          name="shares_reported"
          type="number"
          value={formData.shares_reported?.toString() || ''}
          onChange={(value) => onChange('shares_reported', value)}
          error={errors.shares_reported}
        />
        <FormField
          label="Report Date"
          name="report_date"
          type="date"
          value={formData.report_date || ''}
          onChange={(value) => onChange('report_date', value)}
          error={errors.report_date}
        />
      </div>
    </CardContent>
  </Card>
);

export const AddressInformationSection: React.FC<SectionProps> = ({ formData, errors, onChange }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <MapPin className="h-5 w-5" />
        Address Information
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-6">
      {/* Owner Address */}
      <div>
        <h4 className="font-semibold mb-3">Owner Address</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Address"
            name="owner_address"
            value={formData.owner_address || ''}
            onChange={(value) => onChange('owner_address', value)}
            error={errors.owner_address}
            className="md:col-span-2"
          />
          <FormField
            label="City"
            name="owner_city"
            value={formData.owner_city || ''}
            onChange={(value) => onChange('owner_city', value)}
            error={errors.owner_city}
          />
          <SelectField
            label="State"
            name="owner_state"
            value={formData.owner_state || ''}
            onChange={(value) => onChange('owner_state', value)}
            options={US_STATES}
            error={errors.owner_state}
          />
          <FormField
            label="ZIP Code"
            name="owner_zip"
            value={formData.owner_zip || ''}
            onChange={(value) => onChange('owner_zip', value)}
            error={errors.owner_zip}
          />
        </div>
      </div>

      {/* Holder Address */}
      <div>
        <h4 className="font-semibold mb-3">Holder Address</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            label="Holder Name"
            name="holder_name"
            value={formData.holder_name || ''}
            onChange={(value) => onChange('holder_name', value)}
            error={errors.holder_name}
            className="md:col-span-2"
          />
          <FormField
            label="Address"
            name="holder_address"
            value={formData.holder_address || ''}
            onChange={(value) => onChange('holder_address', value)}
            error={errors.holder_address}
            className="md:col-span-2"
          />
          <FormField
            label="City"
            name="holder_city"
            value={formData.holder_city || ''}
            onChange={(value) => onChange('holder_city', value)}
            error={errors.holder_city}
          />
          <SelectField
            label="State"
            name="holder_state"
            value={formData.holder_state || ''}
            onChange={(value) => onChange('holder_state', value)}
            options={US_STATES}
            error={errors.holder_state}
          />
          <FormField
            label="ZIP Code"
            name="holder_zip"
            value={formData.holder_zip || ''}
            onChange={(value) => onChange('holder_zip', value)}
            error={errors.holder_zip}
          />
        </div>
      </div>
    </CardContent>
  </Card>
);

export const ContactInformationSection: React.FC<SectionProps> = ({ formData, errors, onChange }) => (
  <Card>
    <CardHeader>
      <CardTitle className="flex items-center gap-2">
        <FileText className="h-5 w-5" />
        Contact & Additional Information
      </CardTitle>
    </CardHeader>
    <CardContent className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField
          label="Primary Phone"
          name="primary_phone"
          type="tel"
          value={formData.primary_phone || ''}
          onChange={(value) => onChange('primary_phone', value)}
          error={errors.primary_phone}
        />
        <FormField
          label="Primary Email"
          name="primary_email"
          type="email"
          value={formData.primary_email || ''}
          onChange={(value) => onChange('primary_email', value)}
          error={errors.primary_email}
        />
        <SelectField
          label="State"
          name="state"
          value={formData.state || ''}
          onChange={(value) => onChange('state', value)}
          options={US_STATES}
          error={errors.state}
          required
        />
      </div>
      <TextareaField
        label="Description/Notes"
        name="description"
        value={formData.description || ''}
        onChange={(value) => onChange('description', value)}
        error={errors.description}
        placeholder="Additional notes or description..."
        rows={4}
      />
    </CardContent>
  </Card>
); 