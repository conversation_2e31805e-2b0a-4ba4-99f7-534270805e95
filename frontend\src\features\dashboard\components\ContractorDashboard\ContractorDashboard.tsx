import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  Phone, 
  Mail, 
  Calendar,
  Target,
  AlertCircle,
  MessageSquare,
  User,
  Activity,
  DollarSign,
  TrendingUp,
  Archive
} from 'lucide-react';

interface ContractorStats {
  assignedClaims: number;
  activeClaims: number;
  completedClaims: number;
  successRate: number;
  avgResponseTime: number;
  thisWeekActivities: number;
  pendingTasks: number;
}

interface AssignedClaim {
  id: string;
  claimId: string;
  claimantName: string;
  amount: number;
  status: 'new' | 'in_progress' | 'contacted' | 'pending_documents' | 'completed';
  priority: 'low' | 'medium' | 'high';
  assignedDate: string;
  lastActivity: string;
  daysActive: number;
}

interface ContractorActivity {
  id: string;
  type: 'call' | 'email' | 'note';
  claimId: string;
  claimantName: string;
  description: string;
  timestamp: string;
  outcome?: string;
}

interface ContractorTask {
  id: string;
  type: 'follow_up' | 'document_review' | 'call_scheduled';
  claimId: string;
  claimantName: string;
  description: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high';
}

export const ContractorDashboard: React.FC = () => {
  const [stats] = useState<ContractorStats>({
    assignedClaims: 25,
    activeClaims: 18,
    completedClaims: 7,
    successRate: 64,
    avgResponseTime: 3.2,
    thisWeekActivities: 42,
    pendingTasks: 6
  });

  const [assignedClaims] = useState<AssignedClaim[]>([
    {
      id: '1',
      claimId: 'CL-2024-089',
      claimantName: 'Jennifer Adams',
      amount: 12500,
      status: 'in_progress',
      priority: 'high',
      assignedDate: '2024-12-08',
      lastActivity: '2024-12-12T10:30:00Z',
      daysActive: 4
    },
    {
      id: '2',
      claimId: 'CL-2024-094',
      claimantName: 'Robert Chen',
      amount: 8750,
      status: 'contacted',
      priority: 'medium',
      assignedDate: '2024-12-09',
      lastActivity: '2024-12-11T16:45:00Z',
      daysActive: 3
    },
    {
      id: '3',
      claimId: 'CL-2024-102',
      claimantName: 'Lisa Thompson',
      amount: 15200,
      status: 'new',
      priority: 'high',
      assignedDate: '2024-12-12',
      lastActivity: '2024-12-12T09:00:00Z',
      daysActive: 1
    },
    {
      id: '4',
      claimId: 'CL-2024-087',
      claimantName: 'David Martinez',
      amount: 6300,
      status: 'pending_documents',
      priority: 'medium',
      assignedDate: '2024-12-05',
      lastActivity: '2024-12-10T14:20:00Z',
      daysActive: 7
    }
  ]);

  const [recentActivities] = useState<ContractorActivity[]>([
    {
      id: '1',
      type: 'call',
      claimId: 'CL-2024-089',
      claimantName: 'Jennifer Adams',
      description: 'Successful contact - claimant interested',
      timestamp: '2024-12-12T10:30:00Z',
      outcome: 'interested'
    },
    {
      id: '2',
      type: 'email',
      claimId: 'CL-2024-094',
      claimantName: 'Robert Chen',
      description: 'Sent information packet',
      timestamp: '2024-12-11T16:45:00Z'
    },
    {
      id: '3',
      type: 'note',
      claimId: 'CL-2024-087',
      claimantName: 'David Martinez',
      description: 'Updated contact information',
      timestamp: '2024-12-10T14:20:00Z'
    }
  ]);

  const [pendingTasks] = useState<ContractorTask[]>([
    {
      id: '1',
      type: 'follow_up',
      claimId: 'CL-2024-089',
      claimantName: 'Jennifer Adams',
      description: 'Follow up on engagement letter',
      dueDate: '2024-12-13T15:00:00Z',
      priority: 'high'
    },
    {
      id: '2',
      type: 'call_scheduled',
      claimId: 'CL-2024-102',
      claimantName: 'Lisa Thompson',
      description: 'Initial contact call',
      dueDate: '2024-12-13T11:00:00Z',
      priority: 'high'
    }
  ]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'bg-green-100 text-green-800';
      case 'contacted': return 'bg-blue-100 text-blue-800';
      case 'in_progress': return 'bg-yellow-100 text-yellow-800';
      case 'pending_documents': return 'bg-orange-100 text-orange-800';
      case 'new': return 'bg-purple-100 text-purple-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'call': return <Phone className="h-4 w-4" />;
      case 'email': return <Mail className="h-4 w-4" />;
      case 'note': return <MessageSquare className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'follow_up': return <Clock className="h-4 w-4" />;
      case 'call_scheduled': return <Phone className="h-4 w-4" />;
      case 'document_review': return <FileText className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const formatDueDate = (dueDate: string) => {
    const now = new Date();
    const date = new Date(dueDate);
    const diffInHours = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 0) return 'Overdue';
    if (diffInHours < 24) return `In ${diffInHours}h`;
    return `In ${Math.floor(diffInHours / 24)}d`;
  };

  const totalValue = assignedClaims.reduce((sum, claim) => sum + claim.amount, 0);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Assignments</h1>
          <p className="text-gray-600">Track your assigned claims and daily tasks</p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline">
            <Activity className="h-4 w-4 mr-2" />
            Log Activity
          </Button>
          <Button>
            <FileText className="h-4 w-4 mr-2" />
            View All Claims
          </Button>
        </div>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Assigned Claims */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Assigned Claims</CardTitle>
            <FileText className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.assignedClaims}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{stats.activeClaims} Active</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{stats.completedClaims} Done</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Total Value */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Value</CardTitle>
            <DollarSign className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{formatCurrency(totalValue)}</div>
            <p className="text-sm text-gray-600 mt-2">Assigned claims value</p>
          </CardContent>
        </Card>

        {/* Success Rate */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Target className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.successRate}%</div>
            <div className="mt-2">
              <Progress value={stats.successRate} className="h-2" />
            </div>
            <p className="text-sm text-gray-600 mt-1">This quarter</p>
          </CardContent>
        </Card>

        {/* Pending Tasks */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Tasks</CardTitle>
            <Clock className="h-5 w-5 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.pendingTasks}</div>
            <div className="flex items-center mt-2">
              <AlertCircle className="h-4 w-4 text-orange-500 mr-1" />
              <p className="text-sm text-orange-600">2 due today</p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Assigned Claims */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <FileText className="h-5 w-5 mr-2 text-blue-600" />
                My Assigned Claims
              </div>
              <Badge variant="outline">{assignedClaims.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {assignedClaims.map((claim) => (
                <div key={claim.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <User className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium text-sm">{claim.claimantName}</div>
                      <div className="text-xs text-gray-500">
                        {claim.claimId} • {formatCurrency(claim.amount)} • {claim.daysActive}d active
                      </div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={`text-xs ${getPriorityColor(claim.priority)}`}>
                      {claim.priority}
                    </Badge>
                    <Badge className={`text-xs ${getStatusColor(claim.status)}`}>
                      {claim.status.replace('_', ' ')}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Claims
            </Button>
          </CardContent>
        </Card>

        {/* Upcoming Tasks */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-purple-600" />
                Upcoming Tasks
              </div>
              <Badge variant="outline">{pendingTasks.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {pendingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {getTaskIcon(task.type)}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{task.description}</div>
                      <div className="text-xs text-gray-500">{task.claimantName} • {task.claimId}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </Badge>
                    <span className="text-xs text-gray-500">{formatDueDate(task.dueDate)}</span>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Tasks
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2 text-gray-600" />
            Recent Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{activity.description}</div>
                    <div className="text-xs text-gray-500">
                      {activity.claimantName} • {activity.claimId}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {activity.outcome && (
                    <Badge variant="outline" className="text-xs">
                      {activity.outcome}
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            <Archive className="h-4 w-4 mr-2" />
            View Activity History
          </Button>
        </CardContent>
      </Card>

      {/* Performance Summary */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
              This Week's Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.thisWeekActivities}</div>
                <div className="text-sm text-blue-700">Activities Logged</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.avgResponseTime}h</div>
                <div className="text-sm text-green-700">Avg Response Time</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <CheckCircle className="h-5 w-5 mr-2 text-blue-600" />
              Important Notes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm">
              <div className="p-3 bg-yellow-50 rounded-lg border-l-4 border-yellow-400">
                <p className="font-medium text-yellow-800">Reminder:</p>
                <p className="text-yellow-700">All activities must be logged within 24 hours</p>
              </div>
              <div className="p-3 bg-blue-50 rounded-lg border-l-4 border-blue-400">
                <p className="font-medium text-blue-800">Tip:</p>
                <p className="text-blue-700">Focus on high-priority claims first for better success rates</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}; 