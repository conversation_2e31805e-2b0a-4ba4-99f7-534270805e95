import { useAuth, usePermissions } from '@/contexts/AuthContext'
import { useTheme } from '@/contexts/ThemeContext'
import { Badge } from '@/components/ui/badge'
import { ROLE_DISPLAY_NAMES } from '@/types/auth'
import {
  Home,
  FileText,
  Upload,
  Users,
  DollarSign,
  Shield,
  BarChart3,
  TrendingUp,
  Search,
  Brain,
  Layout,
  Workflow,
  Settings,
  LogOut,
  Moon,
  Sun,
  Palette
} from 'lucide-react'

interface SidebarProps {
  open: boolean
  onToggle: (open: boolean) => void
  currentView: string
  onViewChange: (view: string) => void
}

interface NavigationItem {
  id: string
  label: string
  icon: React.ComponentType<{ className?: string }>
  permission?: string
  badge?: string
  badgeVariant?: 'default' | 'secondary' | 'destructive' | 'outline'
}

export function Sidebar({ open, onToggle, currentView, onViewChange }: SidebarProps) {
  const { user, logout } = useAuth()
  const { hasPermission } = usePermissions()
  const { theme, toggleTheme, actualTheme, colorScheme, setColorScheme } = useTheme()

  const navigationItems: NavigationItem[] = [
    { id: 'dashboard', label: 'Dashboard', icon: Home },
    { id: 'claims', label: 'Claims', icon: FileText, permission: 'claims:view_all' },
    { id: 'batch-import', label: 'Batch Import', icon: Upload, permission: 'batch:import' },
    { id: 'claimants', label: 'Claimants', icon: Users, permission: 'claimants:view' },
    { id: 'payments', label: 'Payments', icon: DollarSign, permission: 'claims:view_all' },
    { id: 'search', label: 'Global Search', icon: Search, permission: 'claims:view_all' },
    { id: 'agent-ai-search', label: 'AI Search', icon: Brain, permission: 'claims:view_all', badge: 'New', badgeVariant: 'default' },
    { id: 'analytics', label: 'Analytics', icon: BarChart3, permission: 'analytics:view_all' },
    { id: 'workflow', label: 'Workflow', icon: Workflow, permission: 'workflow:manage' },
    { id: 'admin', label: 'Admin', icon: Shield, permission: 'system:configure' },
    { id: 'compliance', label: 'Compliance', icon: BarChart3, permission: 'system:audit' },
    { id: 'finance', label: 'Finance', icon: TrendingUp, permission: 'analytics:view_all' },
    { id: 'users', label: 'Users', icon: Users, permission: 'users:view' },
    { id: 'settings', label: 'Settings', icon: Settings, permission: 'system:configure' }
  ]

  const filteredItems = navigationItems.filter(item => 
    !item.permission || hasPermission(item.permission)
  )

  const colorSchemes = [
    { id: 'blue', name: 'Blue', color: 'bg-blue-500' },
    { id: 'purple', name: 'Purple', color: 'bg-purple-500' },
    { id: 'green', name: 'Green', color: 'bg-green-500' },
    { id: 'orange', name: 'Orange', color: 'bg-orange-500' }
  ]

  return (
    <>
      {/* Mobile backdrop */}
      {open && (
        <div 
          className="fixed inset-0 bg-black/50 z-40 lg:hidden"
          onClick={() => onToggle(false)}
        />
      )}

      {/* Sidebar */}
      <div className={`
        fixed top-0 left-0 z-50 h-full bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 
        transition-all duration-300 flex flex-col
        ${open ? 'w-64' : 'w-16 lg:w-16'}
        ${open ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center space-x-3">
            <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
              <Layout className="h-6 w-6 text-blue-600" />
            </div>
            {open && (
              <div>
                <h1 className="text-lg font-bold text-gray-900 dark:text-gray-100">AssetHunterPro</h1>
                <p className="text-xs text-gray-500 dark:text-gray-400">Asset Recovery Platform</p>
              </div>
            )}
          </div>
        </div>

        {/* User Info */}
        {open && user && (
          <div className="p-4 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                {user.email?.charAt(0).toUpperCase()}
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 dark:text-gray-100 truncate">
                  {user.email?.split('@')[0]}
                </p>
                <div className="flex items-center space-x-2">
                  <Badge variant="outline" className="text-xs">
                    {ROLE_DISPLAY_NAMES[user.role as keyof typeof ROLE_DISPLAY_NAMES] || user.role}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 p-2 space-y-1 overflow-y-auto">
          {filteredItems.map((item) => {
            const Icon = item.icon
            const isActive = currentView === item.id
            
            return (
              <button
                key={item.id}
                onClick={() => onViewChange(item.id)}
                className={`
                  w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors
                  ${isActive 
                    ? 'bg-blue-100 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400' 
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
              >
                <Icon className="h-5 w-5 flex-shrink-0" />
                {open && (
                  <>
                    <span className="flex-1 text-sm font-medium">{item.label}</span>
                    {item.badge && (
                      <Badge variant={item.badgeVariant || 'default'} className="text-xs">
                        {item.badge}
                      </Badge>
                    )}
                  </>
                )}
              </button>
            )
          })}
        </nav>

        {/* Theme Controls */}
        {open && (
          <div className="p-4 border-t border-gray-200 dark:border-gray-700 space-y-3">
            {/* Theme Toggle */}
            <button
              onClick={toggleTheme}
              className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
            >
              {actualTheme === 'dark' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
              <span className="text-sm font-medium">
                {actualTheme === 'dark' ? 'Light Mode' : 'Dark Mode'}
              </span>
            </button>

            {/* Color Scheme */}
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Palette className="h-4 w-4 text-gray-500" />
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400">Color Scheme</span>
              </div>
              <div className="flex space-x-2">
                {colorSchemes.map((scheme) => (
                  <button
                    key={scheme.id}
                    onClick={() => setColorScheme(scheme.id as any)}
                    className={`
                      w-6 h-6 rounded-full ${scheme.color} border-2 transition-all
                      ${colorScheme === scheme.id 
                        ? 'border-gray-900 dark:border-gray-100 scale-110' 
                        : 'border-gray-300 dark:border-gray-600 hover:scale-105'
                      }
                    `}
                    title={scheme.name}
                  />
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Logout */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700">
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 transition-colors"
          >
            <LogOut className="h-5 w-5" />
            {open && <span className="text-sm font-medium">Logout</span>}
          </button>
        </div>
      </div>
    </>
  )
}
