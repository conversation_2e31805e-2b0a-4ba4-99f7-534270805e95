// Comprehensive Validation Tests for AssetHunterPro
// Tests all validation schemas and edge cases

import { describe, it, expect } from 'vitest'
import { validationService } from '@/services/validationService'
import { ValidationSchemas } from '@/utils/validation/schemas'

describe('ValidationService', () => {
  describe('User Validation', () => {
    it('should validate correct user creation data', () => {
      const validUser = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        role: 'junior_agent'
      }

      const result = validationService.validateUserCreate(validUser)
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
    })

    it('should reject invalid email format', () => {
      const invalidUser = {
        email: 'invalid-email',
        password: 'SecurePass123!',
        first_name: '<PERSON>',
        last_name: '<PERSON><PERSON>',
        role: 'junior_agent'
      }

      const result = validationService.validateUserCreate(invalidUser)
      expect(result.success).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors![0].field).toBe('email')
    })

    it('should reject weak passwords', () => {
      const weakPasswordUser = {
        email: '<EMAIL>',
        password: 'weak',
        first_name: 'John',
        last_name: 'Doe',
        role: 'junior_agent'
      }

      const result = validationService.validateUserCreate(weakPasswordUser)
      expect(result.success).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors!.some(e => e.field === 'password')).toBe(true)
    })

    it('should reject invalid user roles', () => {
      const invalidRoleUser = {
        email: '<EMAIL>',
        password: 'SecurePass123!',
        first_name: 'John',
        last_name: 'Doe',
        role: 'invalid_role'
      }

      const result = validationService.validateUserCreate(invalidRoleUser)
      expect(result.success).toBe(false)
      expect(result.errors).toBeDefined()
      expect(result.errors!.some(e => e.field === 'role')).toBe(true)
    })

    it('should validate user login data', () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'password123'
      }

      const result = validationService.validateUserLogin(loginData)
      expect(result.success).toBe(true)
    })
  })

  describe('Claim Validation', () => {
    it('should validate correct claim creation data', () => {
      const validClaim = {
        owner_name: 'John Smith',
        amount: 5000.00,
        state: 'CA',
        priority: 'medium'
      }

      const result = validationService.validateClaimCreate(validClaim)
      expect(result.success).toBe(true)
      expect(result.data).toBeDefined()
    })

    it('should reject invalid state codes', () => {
      const invalidClaim = {
        owner_name: 'John Smith',
        amount: 5000.00,
        state: 'California', // Should be 2-letter code
        priority: 'medium'
      }

      const result = validationService.validateClaimCreate(invalidClaim)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'state')).toBe(true)
    })

    it('should reject negative amounts', () => {
      const invalidClaim = {
        owner_name: 'John Smith',
        amount: -1000.00,
        state: 'CA',
        priority: 'medium'
      }

      const result = validationService.validateClaimCreate(invalidClaim)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'amount')).toBe(true)
    })

    it('should reject amounts exceeding maximum', () => {
      const invalidClaim = {
        owner_name: 'John Smith',
        amount: 15000000.00, // Exceeds $10M limit
        state: 'CA',
        priority: 'medium'
      }

      const result = validationService.validateClaimCreate(invalidClaim)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'amount')).toBe(true)
    })

    it('should validate claim search parameters', () => {
      const searchParams = {
        query: 'test search',
        status: 'new',
        limit: 20,
        offset: 0
      }

      const result = validationService.validateClaimSearch(searchParams)
      expect(result.success).toBe(true)
    })
  })

  describe('Claimant Validation', () => {
    it('should validate correct claimant data', () => {
      const validClaimant = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        first_name: 'Jane',
        last_name: 'Doe',
        email: '<EMAIL>',
        phone: '(*************',
        state: 'NY',
        zip_code: '12345'
      }

      const result = validationService.validateClaimantCreate(validClaimant)
      expect(result.success).toBe(true)
    })

    it('should reject invalid phone numbers', () => {
      const invalidClaimant = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        first_name: 'Jane',
        last_name: 'Doe',
        phone: '123' // Invalid format
      }

      const result = validationService.validateClaimantCreate(invalidClaimant)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'phone')).toBe(true)
    })

    it('should reject invalid ZIP codes', () => {
      const invalidClaimant = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        first_name: 'Jane',
        last_name: 'Doe',
        zip_code: '123' // Invalid format
      }

      const result = validationService.validateClaimantCreate(invalidClaimant)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'zip_code')).toBe(true)
    })
  })

  describe('Activity Validation', () => {
    it('should validate correct activity data', () => {
      const validActivity = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'call',
        description: 'Called claimant to discuss case',
        duration_minutes: 30
      }

      const result = validationService.validateActivityCreate(validActivity)
      expect(result.success).toBe(true)
    })

    it('should reject invalid activity types', () => {
      const invalidActivity = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'invalid_type',
        description: 'Test activity'
      }

      const result = validationService.validateActivityCreate(invalidActivity)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'type')).toBe(true)
    })

    it('should reject excessive duration', () => {
      const invalidActivity = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        type: 'call',
        description: 'Test activity',
        duration_minutes: 2000 // Exceeds 24 hours
      }

      const result = validationService.validateActivityCreate(invalidActivity)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'duration_minutes')).toBe(true)
    })
  })

  describe('Document Validation', () => {
    it('should validate correct document data', () => {
      const validDocument = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Agreement.pdf',
        type: 'agreement',
        file_size: 1024000, // 1MB
        file_type: 'application/pdf'
      }

      const result = validationService.validateDocumentCreate(validDocument)
      expect(result.success).toBe(true)
    })

    it('should reject files exceeding size limit', () => {
      const invalidDocument = {
        claim_id: '123e4567-e89b-12d3-a456-426614174000',
        name: 'Large_File.pdf',
        type: 'agreement',
        file_size: 60 * 1024 * 1024, // 60MB (exceeds 50MB limit)
        file_type: 'application/pdf'
      }

      const result = validationService.validateDocumentCreate(invalidDocument)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.field === 'file_size')).toBe(true)
    })
  })

  describe('Email Validation', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ]

      validEmails.forEach(email => {
        const result = validationService.validateEmail(email)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@domain.com',
        'user@',
        '<EMAIL>'
      ]

      invalidEmails.forEach(email => {
        const result = validationService.validateEmail(email)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('Phone Validation', () => {
    it('should validate correct phone numbers', () => {
      const validPhones = [
        '(*************',
        '************',
        '************',
        '5551234567',
        '****** 123 4567'
      ]

      validPhones.forEach(phone => {
        const result = validationService.validatePhone(phone)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid phone numbers', () => {
      const invalidPhones = [
        '123',
        '555-123',
        'abc-def-ghij',
        '************89'
      ]

      invalidPhones.forEach(phone => {
        const result = validationService.validatePhone(phone)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('Currency Validation', () => {
    it('should validate correct currency amounts', () => {
      const validAmounts = [0.01, 100.00, 9999999.99]

      validAmounts.forEach(amount => {
        const result = validationService.validateCurrency(amount)
        expect(result.success).toBe(true)
      })
    })

    it('should reject invalid currency amounts', () => {
      const invalidAmounts = [-100, 0, 15000000]

      invalidAmounts.forEach(amount => {
        const result = validationService.validateCurrency(amount)
        expect(result.success).toBe(false)
      })
    })
  })

  describe('File Upload Validation', () => {
    it('should validate correct file uploads', () => {
      const validFile = new File(['test content'], 'test.pdf', {
        type: 'application/pdf'
      })

      const result = validationService.validateFileUpload(validFile)
      expect(result.success).toBe(true)
    })

    it('should reject files that are too large', () => {
      // Create a mock file that exceeds size limit
      const largeFile = {
        name: 'large.pdf',
        size: 60 * 1024 * 1024, // 60MB
        type: 'application/pdf'
      } as File

      const result = validationService.validateFileUpload(largeFile)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.code === 'FILE_TOO_LARGE')).toBe(true)
    })

    it('should reject dangerous file types', () => {
      const dangerousFile = {
        name: 'malware.exe',
        size: 1024,
        type: 'application/octet-stream'
      } as File

      const result = validationService.validateFileUpload(dangerousFile)
      expect(result.success).toBe(false)
      expect(result.errors!.some(e => e.code === 'DANGEROUS_FILE_TYPE')).toBe(true)
    })
  })

  describe('Search Query Validation', () => {
    it('should validate safe search queries', () => {
      const validQueries = [
        'John Smith',
        'claim 12345',
        'property address'
      ]

      validQueries.forEach(query => {
        const result = validationService.validateSearchQuery(query)
        expect(result.success).toBe(true)
      })
    })

    it('should reject potentially dangerous queries', () => {
      const dangerousQueries = [
        'SELECT * FROM users',
        '<script>alert("xss")</script>',
        'javascript:alert(1)',
        "'; DROP TABLE users; --"
      ]

      dangerousQueries.forEach(query => {
        const result = validationService.validateSearchQuery(query)
        expect(result.success).toBe(false)
      })
    })

    it('should reject empty queries', () => {
      const result = validationService.validateSearchQuery('')
      expect(result.success).toBe(false)
      expect(result.errors![0].code).toBe('EMPTY_QUERY')
    })

    it('should reject queries that are too long', () => {
      const longQuery = 'a'.repeat(300)
      const result = validationService.validateSearchQuery(longQuery)
      expect(result.success).toBe(false)
      expect(result.errors![0].code).toBe('QUERY_TOO_LONG')
    })
  })

  describe('Pagination Validation', () => {
    it('should validate correct pagination parameters', () => {
      const validPagination = { page: 1, limit: 20 }
      const result = validationService.validatePagination(validPagination)
      expect(result.success).toBe(true)
    })

    it('should reject invalid pagination parameters', () => {
      const invalidPagination = { page: 0, limit: 200 }
      const result = validationService.validatePagination(invalidPagination)
      expect(result.success).toBe(false)
    })
  })

  describe('Sort Validation', () => {
    it('should validate correct sort parameters', () => {
      const allowedFields = ['name', 'created_at', 'amount']
      const validSort = { sort_by: 'name', sort_order: 'asc' }
      const result = validationService.validateSort(validSort, allowedFields)
      expect(result.success).toBe(true)
    })

    it('should reject invalid sort fields', () => {
      const allowedFields = ['name', 'created_at', 'amount']
      const invalidSort = { sort_by: 'invalid_field', sort_order: 'asc' }
      const result = validationService.validateSort(invalidSort, allowedFields)
      expect(result.success).toBe(false)
    })

    it('should reject invalid sort orders', () => {
      const allowedFields = ['name', 'created_at', 'amount']
      const invalidSort = { sort_by: 'name', sort_order: 'invalid' }
      const result = validationService.validateSort(invalidSort, allowedFields)
      expect(result.success).toBe(false)
    })
  })

  describe('Bulk Operations Validation', () => {
    it('should validate bulk claim updates', () => {
      const validBulkUpdate = {
        claim_ids: [
          '123e4567-e89b-12d3-a456-426614174000',
          '123e4567-e89b-12d3-a456-426614174001'
        ],
        updates: {
          status: 'in_progress',
          priority: 'high'
        }
      }

      const result = validationService.validateBulkClaimUpdate(validBulkUpdate)
      expect(result.success).toBe(true)
    })

    it('should reject bulk updates with too many claims', () => {
      const tooManyIds = Array(150).fill('123e4567-e89b-12d3-a456-426614174000')
      const invalidBulkUpdate = {
        claim_ids: tooManyIds,
        updates: { status: 'in_progress' }
      }

      const result = validationService.validateBulkClaimUpdate(invalidBulkUpdate)
      expect(result.success).toBe(false)
    })

    it('should validate bulk assignments', () => {
      const validBulkAssignment = {
        claim_ids: [
          '123e4567-e89b-12d3-a456-426614174000',
          '123e4567-e89b-12d3-a456-426614174001'
        ],
        assigned_agent_id: '123e4567-e89b-12d3-a456-426614174002'
      }

      const result = validationService.validateBulkAssignment(validBulkAssignment)
      expect(result.success).toBe(true)
    })
  })
})
