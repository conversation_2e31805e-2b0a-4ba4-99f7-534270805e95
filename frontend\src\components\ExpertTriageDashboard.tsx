import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './ui/card';
import { Button } from './ui/button';
import { Badge } from './ui/badge';
import { Brain, Search, Loader2, Clock, TrendingUp, Target, Phone, Mail, MapPin } from 'lucide-react';
import { budgetExpertAI, ExpertAnalysisResult } from '../services/budgetExpertAI';

interface ExpertTriageDashboardProps {
  claimData: any;
  onExpertAnalysisComplete?: (results: ExpertAnalysisResult) => boolean;
  canUseExpertAI?: () => { allowed: boolean; reason?: string };
}

export const ExpertTriageDashboard: React.FC<ExpertTriageDashboardProps> = ({ 
  claimData, 
  onExpertAnalysisComplete,
  canUseExpertAI 
}) => {
  const [analysis, setAnalysis] = useState<ExpertAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  
  const handleExpertAnalysis = async () => {
    // Check usage limits if provided
    if (canUseExpertAI) {
      const usageCheck = canUseExpertAI();
      if (!usageCheck.allowed) {
        alert(`❌ ${usageCheck.reason}\n\nUpgrade your plan for more Expert AI usage.`);
        return;
      }
    }
    
    setIsAnalyzing(true);
    
    try {
      // Run free expert analysis
      const result = await budgetExpertAI.runCompleteAnalysis(claimData);
      setAnalysis(result);
      
      // Call completion callback if provided
      if (onExpertAnalysisComplete) {
        onExpertAnalysisComplete(result);
      }
    } catch (error) {
      console.error('Expert analysis failed:', error);
    }
    
    setIsAnalyzing(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'border-l-green-500';
      case 'MODERATE': return 'border-l-yellow-500';
      case 'HARD': return 'border-l-red-500';
      default: return 'border-l-gray-500';
    }
  };

  const getDifficultyBadgeColor = (difficulty: string) => {
    switch (difficulty) {
      case 'EASY': return 'bg-green-500 text-white';
      case 'MODERATE': return 'bg-yellow-500 text-white';
      case 'HARD': return 'bg-red-500 text-white';
      default: return 'bg-gray-500 text-white';
    }
  };
  
  if (!analysis) {
    return (
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Brain className="w-5 h-5" />
            <span>Budget Expert AI Analysis</span>
            <Badge variant="outline" className="text-xs">$0 COST</Badge>
          </CardTitle>
          <CardDescription>
            Get 25-year veteran insight using free intelligence sources and smart algorithms
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div className="bg-green-50 p-3 rounded border-l-4 border-l-green-500">
                <div className="font-semibold text-green-800">Free Data Sources</div>
                <div className="text-green-600">Property Records, Court Records, Professional Licenses, Business Registrations</div>
              </div>
              <div className="bg-blue-50 p-3 rounded border-l-4 border-l-blue-500">
                <div className="font-semibold text-blue-800">Expert Algorithms</div>
                <div className="text-blue-600">Case Triage, Life Timeline Analysis, Contact Success Ranking</div>
              </div>
              <div className="bg-purple-50 p-3 rounded border-l-4 border-l-purple-500">
                <div className="font-semibold text-purple-800">Expected Results</div>
                <div className="text-purple-600">75-85% discovery success vs 40% manual</div>
              </div>
            </div>
            
            <Button onClick={handleExpertAnalysis} disabled={isAnalyzing} className="w-full">
              {isAnalyzing ? (
                <>
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  Running Expert Analysis...
                </>
              ) : (
                <>
                  <Search className="w-4 h-4 mr-2" />
                  Run Expert Analysis (Free)
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }
  
  return (
    <div className="space-y-4">
      {/* Expert Analysis Header */}
      <Card className="border-l-4 border-l-blue-500">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Brain className="w-5 h-5" />
              <span>Budget Expert AI Results</span>
              <Badge variant="outline" className="text-xs">FREE IMPLEMENTATION</Badge>
            </div>
            <Badge className="bg-blue-500 text-white">
              Intelligence Score: {analysis.intelligenceScore}%
            </Badge>
          </CardTitle>
          <CardDescription>
            Processed in {(analysis.processingTime / 1000).toFixed(1)}s using {analysis.dataSourcesUsed.length} free data sources
          </CardDescription>
        </CardHeader>
      </Card>

      {/* 30-Second Case Triage */}
      <Card className={`border-l-4 ${getDifficultyColor(analysis.triage.difficulty)}`}>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Target className="w-5 h-5" />
              <span>Expert Case Assessment</span>
            </div>
            <Badge className={getDifficultyBadgeColor(analysis.triage.difficulty)}>
              {analysis.triage.difficulty}
            </Badge>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {Math.round(analysis.triage.successProbability * 100)}%
              </div>
              <div className="text-sm text-gray-600">Success Probability</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">
                {analysis.triage.timeEstimate}
              </div>
              <div className="text-sm text-gray-600">Time Estimate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">
                {analysis.triage.priorityScore}
              </div>
              <div className="text-sm text-gray-600">Priority Score</div>
            </div>
          </div>
          
          <div className="space-y-3">
            <div className="p-3 bg-blue-50 rounded">
              <p className="text-sm font-medium text-blue-800">Expert Recommendation:</p>
              <p className="text-sm text-blue-700">{analysis.triage.recommendation}</p>
            </div>
            
            {analysis.triage.reasoning.length > 0 && (
              <div className="p-3 bg-gray-50 rounded">
                <p className="text-sm font-medium text-gray-800">Expert Reasoning:</p>
                <ul className="text-sm text-gray-700 list-disc list-inside">
                  {analysis.triage.reasoning.map((reason, index) => (
                    <li key={index}>{reason}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
      
      {/* Life Event Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <TrendingUp className="w-5 h-5" />
            <span>Life Event Intelligence</span>
            <Badge variant="outline" className="text-xs">FREE DATA SYNTHESIS</Badge>
          </CardTitle>
          <CardDescription>What happened to this person (free public records analysis)</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Timeline Events */}
            {analysis.timeline.events.length > 0 ? (
              <div className="space-y-3">
                {analysis.timeline.events.slice(0, 5).map((event, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <div className={`w-3 h-3 rounded-full mt-1 ${
                      event.impact === 'HIGH' ? 'bg-red-500' :
                      event.impact === 'MEDIUM' ? 'bg-yellow-500' :
                      'bg-green-500'
                    }`}></div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <span className="font-medium text-sm">{event.date}</span>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline" className="text-xs">{event.source}</Badge>
                          <Badge variant="outline" className="text-xs">
                            {Math.round(event.confidence * 100)}% confident
                          </Badge>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700">{event.event}</p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-4">
                No recent life events found in public records
              </div>
            )}
            
            {/* AI Analysis */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="p-3 bg-blue-50 rounded">
                <p className="text-sm font-medium text-blue-800">Pattern Analysis:</p>
                <p className="text-sm text-blue-700">{analysis.timeline.analysis}</p>
              </div>
              
              <div className="p-3 bg-green-50 rounded">
                <p className="text-sm font-medium text-green-800">Current Situation Prediction:</p>
                <p className="text-sm text-green-700">{analysis.timeline.predictions}</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Success-Ranked Contacts */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Phone className="w-5 h-5" />
            <span>Contact Strategy (Success-Ranked)</span>
            <Badge variant="outline" className="text-xs">SMART ALGORITHMS</Badge>
          </CardTitle>
          <CardDescription>AI-ranked contacts with expert approach recommendations</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {analysis.contacts.length > 0 ? (
              analysis.contacts.map((contact, index) => (
                <div key={index} className="p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                      {contact.type === 'mobile_phone' && <Phone className="w-4 h-4 text-green-600" />}
                      {contact.type === 'work_email' && <Mail className="w-4 h-4 text-blue-600" />}
                      {contact.type === 'home_address' && <MapPin className="w-4 h-4 text-purple-600" />}
                      <span className="font-medium">{contact.value}</span>
                    </div>
                    <Badge className="bg-green-500 text-white">
                      {Math.round(contact.successProbability * 100)}% Success
                    </Badge>
                  </div>
                  
                  <div className="space-y-2">
                    <div>
                      <span className="text-sm font-medium text-gray-700">Approach: </span>
                      <span className="text-sm text-gray-600">{contact.recommendedApproach}</span>
                    </div>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <Clock className="w-3 h-3 inline mr-1" />
                        <span className="text-xs text-gray-500">{contact.optimalTiming}</span>
                      </div>
                      <Badge variant="outline" className="text-xs">{contact.source}</Badge>
                    </div>
                    
                    <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
                      <strong>Expert Reasoning:</strong> {contact.reasoning}
                    </div>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center text-gray-500 py-4">
                No contacts discovered from available sources
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Data Sources Used */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm">Free Data Sources Utilized</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            {analysis.dataSourcesUsed.map((source, index) => (
              <Badge key={index} variant="outline" className="text-xs justify-center">
                {source.replace(/([A-Z])/g, ' $1').trim()}
              </Badge>
            ))}
          </div>
          <div className="mt-3 text-xs text-gray-500">
            Total operational cost: <strong>$0.00</strong> • Processing time: <strong>{(analysis.processingTime / 1000).toFixed(1)}s</strong>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ExpertTriageDashboard; 