// Validation Service for AssetHunterPro
// Provides centralized validation logic and error handling

import { z } from 'zod'
import { ValidationSchemas, sanitizeObject, sanitizeInput } from '@/utils/validation/schemas'

export interface ValidationResult<T = any> {
  success: boolean
  data?: T
  errors?: ValidationError[]
}

export interface ValidationError {
  field: string
  message: string
  code: string
}

export interface ValidationOptions {
  sanitize?: boolean
  strict?: boolean
  allowPartial?: boolean
}

export class ValidationService {
  private static instance: ValidationService

  static getInstance(): ValidationService {
    if (!ValidationService.instance) {
      ValidationService.instance = new ValidationService()
    }
    return ValidationService.instance
  }

  /**
   * Validate data against a schema
   */
  validate<T>(
    schema: z.ZodSchema<T>, 
    data: unknown, 
    options: ValidationOptions = {}
  ): ValidationResult<T> {
    const { sanitize = true, strict = true, allowPartial = false } = options

    try {
      // Sanitize input if requested
      let processedData = data
      if (sanitize && typeof data === 'object' && data !== null) {
        processedData = sanitizeObject(data as Record<string, any>)
      } else if (sanitize && typeof data === 'string') {
        processedData = sanitizeInput(data)
      }

      // Choose validation method
      const validationSchema = allowPartial ? schema.partial() : schema
      const validatedData = strict 
        ? validationSchema.parse(processedData)
        : validationSchema.safeParse(processedData)

      if (strict) {
        return { success: true, data: validatedData as T }
      } else {
        const result = validatedData as z.SafeParseReturnType<unknown, T>
        if (result.success) {
          return { success: true, data: result.data }
        } else {
          return {
            success: false,
            errors: this.formatZodErrors(result.error)
          }
        }
      }
    } catch (error) {
      if (error instanceof z.ZodError) {
        return {
          success: false,
          errors: this.formatZodErrors(error)
        }
      }
      
      return {
        success: false,
        errors: [{
          field: 'unknown',
          message: 'Validation failed',
          code: 'VALIDATION_ERROR'
        }]
      }
    }
  }

  /**
   * Validate user creation data
   */
  validateUserCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.UserCreate, data)
  }

  /**
   * Validate user update data
   */
  validateUserUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.UserUpdate, data, { allowPartial: true })
  }

  /**
   * Validate user login data
   */
  validateUserLogin(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.UserLogin, data)
  }

  /**
   * Validate claim creation data
   */
  validateClaimCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ClaimCreate, data)
  }

  /**
   * Validate claim update data
   */
  validateClaimUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ClaimUpdate, data, { allowPartial: true })
  }

  /**
   * Validate claim search parameters
   */
  validateClaimSearch(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ClaimSearch, data, { allowPartial: true })
  }

  /**
   * Validate claimant creation data
   */
  validateClaimantCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ClaimantCreate, data)
  }

  /**
   * Validate claimant update data
   */
  validateClaimantUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ClaimantUpdate, data, { allowPartial: true })
  }

  /**
   * Validate activity creation data
   */
  validateActivityCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ActivityCreate, data)
  }

  /**
   * Validate activity update data
   */
  validateActivityUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.ActivityUpdate, data, { allowPartial: true })
  }

  /**
   * Validate document creation data
   */
  validateDocumentCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.DocumentCreate, data)
  }

  /**
   * Validate document update data
   */
  validateDocumentUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.DocumentUpdate, data, { allowPartial: true })
  }

  /**
   * Validate commission creation data
   */
  validateCommissionCreate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.CommissionCreate, data)
  }

  /**
   * Validate bulk claim update data
   */
  validateBulkClaimUpdate(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.BulkClaimUpdate, data)
  }

  /**
   * Validate bulk assignment data
   */
  validateBulkAssignment(data: unknown): ValidationResult {
    return this.validate(ValidationSchemas.BulkAssignment, data)
  }

  /**
   * Validate email format
   */
  validateEmail(email: string): ValidationResult<string> {
    const emailSchema = z.string().email('Invalid email format')
    return this.validate(emailSchema, email)
  }

  /**
   * Validate phone number format
   */
  validatePhone(phone: string): ValidationResult<string> {
    const phoneRegex = /^(\+1|1)?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})$/
    const phoneSchema = z.string().regex(phoneRegex, 'Invalid phone number format')
    return this.validate(phoneSchema, phone)
  }

  /**
   * Validate currency amount
   */
  validateCurrency(amount: number): ValidationResult<number> {
    const currencySchema = z.number()
      .positive('Amount must be positive')
      .max(10000000, 'Amount cannot exceed $10,000,000')
      .multipleOf(0.01, 'Amount must be in cents')
    return this.validate(currencySchema, amount)
  }

  /**
   * Validate UUID format
   */
  validateUUID(uuid: string): ValidationResult<string> {
    const uuidSchema = z.string().uuid('Invalid UUID format')
    return this.validate(uuidSchema, uuid)
  }

  /**
   * Validate date string
   */
  validateDate(date: string): ValidationResult<string> {
    const dateSchema = z.string().datetime('Invalid date format')
    return this.validate(dateSchema, date)
  }

  /**
   * Validate file upload data
   */
  validateFileUpload(file: File, maxSize: number = 50 * 1024 * 1024): ValidationResult {
    const errors: ValidationError[] = []

    // Check file size
    if (file.size > maxSize) {
      errors.push({
        field: 'file',
        message: `File size cannot exceed ${Math.round(maxSize / 1024 / 1024)}MB`,
        code: 'FILE_TOO_LARGE'
      })
    }

    // Check file type
    const allowedTypes = [
      'application/pdf',
      'image/jpeg',
      'image/png',
      'image/gif',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ]

    if (!allowedTypes.includes(file.type)) {
      errors.push({
        field: 'file',
        message: 'File type not allowed',
        code: 'INVALID_FILE_TYPE'
      })
    }

    // Check filename
    if (file.name.length > 255) {
      errors.push({
        field: 'filename',
        message: 'Filename too long',
        code: 'FILENAME_TOO_LONG'
      })
    }

    // Check for potentially dangerous filenames
    const dangerousPatterns = [
      /\.exe$/i,
      /\.bat$/i,
      /\.cmd$/i,
      /\.scr$/i,
      /\.js$/i,
      /\.vbs$/i,
      /\.php$/i
    ]

    if (dangerousPatterns.some(pattern => pattern.test(file.name))) {
      errors.push({
        field: 'filename',
        message: 'File type not allowed for security reasons',
        code: 'DANGEROUS_FILE_TYPE'
      })
    }

    return {
      success: errors.length === 0,
      errors: errors.length > 0 ? errors : undefined,
      data: errors.length === 0 ? { 
        name: file.name, 
        size: file.size, 
        type: file.type 
      } : undefined
    }
  }

  /**
   * Validate form data with custom rules
   */
  validateForm(formData: Record<string, any>, rules: Record<string, z.ZodSchema>): ValidationResult {
    const errors: ValidationError[] = []
    const validatedData: Record<string, any> = {}

    for (const [field, schema] of Object.entries(rules)) {
      const value = formData[field]
      const result = this.validate(schema, value, { strict: false })

      if (result.success) {
        validatedData[field] = result.data
      } else if (result.errors) {
        errors.push(...result.errors.map(error => ({
          ...error,
          field: field
        })))
      }
    }

    return {
      success: errors.length === 0,
      data: errors.length === 0 ? validatedData : undefined,
      errors: errors.length > 0 ? errors : undefined
    }
  }

  /**
   * Format Zod errors into our error format
   */
  private formatZodErrors(zodError: z.ZodError): ValidationError[] {
    return zodError.errors.map(error => ({
      field: error.path.join('.') || 'unknown',
      message: error.message,
      code: error.code.toUpperCase()
    }))
  }

  /**
   * Check if value is safe for database insertion
   */
  isSafeForDatabase(value: string): boolean {
    // Check for SQL injection patterns
    const sqlInjectionPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
      /(\b(OR|AND)\s+\d+\s*=\s*\d+)/i,
      /(--|\/\*|\*\/)/,
      /(\b(SCRIPT|JAVASCRIPT|VBSCRIPT)\b)/i,
      /(<\s*script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>)/gi
    ]

    return !sqlInjectionPatterns.some(pattern => pattern.test(value))
  }

  /**
   * Sanitize and validate search query
   */
  validateSearchQuery(query: string): ValidationResult<string> {
    // Remove potentially dangerous characters
    const sanitized = query
      .replace(/[<>]/g, '')
      .replace(/javascript:/gi, '')
      .replace(/on\w+=/gi, '')
      .trim()

    if (sanitized.length === 0) {
      return {
        success: false,
        errors: [{
          field: 'query',
          message: 'Search query cannot be empty',
          code: 'EMPTY_QUERY'
        }]
      }
    }

    if (sanitized.length > 255) {
      return {
        success: false,
        errors: [{
          field: 'query',
          message: 'Search query too long',
          code: 'QUERY_TOO_LONG'
        }]
      }
    }

    if (!this.isSafeForDatabase(sanitized)) {
      return {
        success: false,
        errors: [{
          field: 'query',
          message: 'Search query contains invalid characters',
          code: 'UNSAFE_QUERY'
        }]
      }
    }

    return {
      success: true,
      data: sanitized
    }
  }

  /**
   * Validate pagination parameters
   */
  validatePagination(params: { page?: number; limit?: number }): ValidationResult {
    const paginationSchema = z.object({
      page: z.number().min(1).default(1),
      limit: z.number().min(1).max(100).default(20)
    })

    return this.validate(paginationSchema, params, { allowPartial: true })
  }

  /**
   * Validate sort parameters
   */
  validateSort(params: { sort_by?: string; sort_order?: string }, allowedFields: string[]): ValidationResult {
    const errors: ValidationError[] = []

    if (params.sort_by && !allowedFields.includes(params.sort_by)) {
      errors.push({
        field: 'sort_by',
        message: `Invalid sort field. Allowed fields: ${allowedFields.join(', ')}`,
        code: 'INVALID_SORT_FIELD'
      })
    }

    if (params.sort_order && !['asc', 'desc'].includes(params.sort_order.toLowerCase())) {
      errors.push({
        field: 'sort_order',
        message: 'Sort order must be "asc" or "desc"',
        code: 'INVALID_SORT_ORDER'
      })
    }

    return {
      success: errors.length === 0,
      data: errors.length === 0 ? {
        sort_by: params.sort_by || 'created_at',
        sort_order: (params.sort_order || 'desc').toLowerCase()
      } : undefined,
      errors: errors.length > 0 ? errors : undefined
    }
  }
}

export const validationService = ValidationService.getInstance()
