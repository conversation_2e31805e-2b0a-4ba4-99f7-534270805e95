{"name": "fastify", "version": "5.3.3", "description": "Fast and low overhead web framework, for Node.js", "main": "fastify.js", "type": "commonjs", "types": "fastify.d.ts", "scripts": {"bench": "branchcmp -r 2 -g -s \"npm run benchmark\"", "benchmark": "concurrently -k -s first \"node ./examples/benchmark/simple.js\" \"autocannon -c 100 -d 30 -p 10 localhost:3000/\"", "benchmark:parser": "concurrently -k -s first \"node ./examples/benchmark/parser.js\" \"autocannon -c 100 -d 30 -p 10 -i ./examples/benchmark/body.json -H \"content-type:application/jsoff\" -m POST localhost:3000/\"", "build:validation": "node build/build-error-serializer.js && node build/build-validation.js", "coverage": "c8 --reporter html borp --reporter=@jsumners/line-reporter --coverage --check-coverage --lines 100 ", "coverage:ci-check-coverage": "borp --reporter=@jsumners/line-reporter --coverage --check-coverage --lines 100", "lint": "npm run lint:eslint", "lint:fix": "eslint --fix", "lint:markdown": "markdownlint-cli2", "lint:eslint": "eslint", "prepublishOnly": "cross-env PREPUBLISH=true borp --reporter=@jsumners/line-reporter && npm run test:validator:integrity", "test": "npm run lint && npm run unit && npm run test:typescript", "test:ci": "npm run unit && npm run test:typescript", "test:report": "npm run lint && npm run unit:report && npm run test:typescript", "test:validator:integrity": "npm run build:validation && git diff --quiet --ignore-all-space --ignore-blank-lines --ignore-cr-at-eol lib/error-serializer.js && git diff --quiet --ignore-all-space --ignore-blank-lines --ignore-cr-at-eol lib/configValidator.js", "test:typescript": "tsc test/types/import.ts --target es2022 --moduleResolution node16 --module node16 --noEmit && tsd", "test:watch": "npm run unit -- --watch --coverage-report=none --reporter=terse", "unit": "borp --reporter=@jsumners/line-reporter --coverage --check-coverage", "unit:report": "c8 --reporter html borp --reporter=@jsumners/line-reporter", "citgm": "borp --reporter=@jsumners/line-reporter --coverage --check-coverage --concurrency=1"}, "repository": {"type": "git", "url": "git+https://github.com/fastify/fastify.git"}, "keywords": ["web", "framework", "json", "schema", "open", "api"], "author": "<PERSON> <<EMAIL>>", "contributors": [{"name": "<PERSON>", "url": "http://delved.org", "author": true}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://github.com/<PERSON>-<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "http://starptech.de", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/AyoubElk", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/rafaelgss"}, {"name": "<PERSON><PERSON><PERSON>", "url": "http://trivikr.github.io", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://loige.co"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "url": "https://maksim.dev"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "url": "https://james.sumners.info"}, {"name": "<PERSON>", "url": "https://github.com/SerayaEryn"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/kibertoad"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://github.com/zekth"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://luisorbaiceta.com"}, {"name": "<PERSON>", "email": "<EMAIL>", "url": "https://metcoder.dev"}, {"name": "G<PERSON><PERSON><PERSON>n <PERSON>", "email": "<EMAIL>", "url": "https://heyhey.to/G"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/fdawgs"}, {"name": "<PERSON><PERSON><PERSON>", "email": "<EMAIL>", "url": "https://github.com/climba03003"}], "license": "MIT", "bugs": {"url": "https://github.com/fastify/fastify/issues"}, "homepage": "https://fastify.dev/", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "devDependencies": {"@fastify/pre-commit": "^2.1.0", "@jsumners/line-reporter": "^1.0.1", "@sinclair/typebox": "^0.34.13", "@sinonjs/fake-timers": "^11.2.2", "@stylistic/eslint-plugin": "^4.1.0", "@stylistic/eslint-plugin-js": "^4.1.0", "@types/node": "^22.0.0", "ajv": "^8.12.0", "ajv-errors": "^3.0.0", "ajv-formats": "^3.0.1", "ajv-i18n": "^4.2.0", "ajv-merge-patch": "^5.0.1", "autocannon": "^8.0.0", "borp": "^0.20.0", "branch-comparer": "^1.1.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^9.0.0", "fast-json-body": "^1.1.0", "fastify-plugin": "^5.0.0", "fluent-json-schema": "^6.0.0", "h2url": "^0.2.0", "http-errors": "^2.0.0", "joi": "^17.12.3", "json-schema-to-ts": "^3.0.1", "JSONStream": "^1.3.5", "markdownlint-cli2": "^0.17.1", "neostandard": "^0.12.0", "node-forge": "^1.3.1", "proxyquire": "^2.1.3", "simple-get": "^4.0.1", "split2": "^4.2.0", "tap": "^21.0.0", "tsd": "^0.32.0", "typescript": "~5.8.2", "undici": "^6.13.0", "vary": "^1.1.2", "yup": "^1.4.0"}, "dependencies": {"@fastify/ajv-compiler": "^4.0.0", "@fastify/error": "^4.0.0", "@fastify/fast-json-stringify-compiler": "^5.0.0", "@fastify/proxy-addr": "^5.0.0", "abstract-logging": "^2.0.1", "avvio": "^9.0.0", "fast-json-stringify": "^6.0.0", "find-my-way": "^9.0.0", "light-my-request": "^6.0.0", "pino": "^9.0.0", "process-warning": "^5.0.0", "rfdc": "^1.3.1", "secure-json-parse": "^4.0.0", "semver": "^7.6.0", "toad-cache": "^3.7.0"}, "tsd": {"directory": "test/types"}}