// File list component for displaying uploaded files and their status

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { 
  FileText, MapPin, Eye, CheckCircle, XCircle, 
  Clock, AlertTriangle, Shield, RefreshCw
} from 'lucide-react'
import { BatchFile } from '@/types/batchImport'
import { STATUS_COLORS } from '@/utils/constants'
import { formatFileSize, formatNumber } from '@/utils/formatters'

interface FileListProps {
  files: BatchFile[]
  onStartMapping: (file: BatchFile) => void
  onViewReport: (file: BatchFile) => void
  onVerifyRecords: (file: BatchFile) => void
  isValidating: boolean
}

export function FileList({
  files,
  onStartMapping,
  onViewReport,
  onVerifyRecords,
  isValidating
}: FileListProps) {
  const getStatusIcon = (status: BatchFile['status']) => {
    const iconClass = "h-4 w-4"
    switch (status) {
      case 'uploading':
      case 'processing':
      case 'importing':
        return <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600" />
      case 'uploaded':
      case 'mapping':
      case 'verified':
      case 'ready_to_save':
      case 'completed':
        return <CheckCircle className={`${iconClass} text-green-600`} />
      case 'validating':
        return <Shield className={`${iconClass} text-purple-600`} />
      case 'error':
        return <XCircle className={`${iconClass} text-red-600`} />
      default:
        return <Clock className={`${iconClass} text-gray-600`} />
    }
  }

  const getStatusMessage = (file: BatchFile) => {
    switch (file.status) {
      case 'uploading':
        return 'Uploading file...'
      case 'processing':
        return 'Parsing CSV data...'
      case 'mapping':
        return 'Ready for field mapping'
      case 'validating':
        return 'Validating records...'
      case 'importing':
        return 'Saving to database...'
      case 'completed':
        return `Completed: ${formatNumber(file.validRecords || 0)} records saved`
      case 'error':
        return file.errorMessage || 'Processing failed'
      default:
        return 'Processing...'
    }
  }

  const canStartMapping = (file: BatchFile) => {
    return file.status === 'mapping' || file.status === 'verified'
  }

  const canVerifyRecords = (file: BatchFile) => {
    return file.status === 'mapping' && file.allData && file.allData.length > 0
  }

  const canViewReport = (file: BatchFile) => {
    return file.status === 'completed' || file.status === 'error' || file.status === 'verified'
  }

  if (files.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-4 w-4" />
            Uploaded Files
          </CardTitle>
          <CardDescription>
            No files uploaded yet. Upload CSV files to get started.
          </CardDescription>
        </CardHeader>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-4 w-4" />
          Uploaded Files ({files.length})
        </CardTitle>
        <CardDescription>
          Track the status of your uploaded files and manage processing.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {files.map((file) => (
            <div
              key={file.id}
              className="border rounded-lg p-4 space-y-3 hover:bg-gray-50 transition-colors"
            >
              {/* File Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {getStatusIcon(file.status)}
                  <div>
                    <h4 className="font-medium text-gray-900">{file.name}</h4>
                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <span>{formatFileSize(file.size)}</span>
                      {file.state && (
                        <>
                          <span>•</span>
                          <span className="flex items-center gap-1">
                            <MapPin className="h-3 w-3" />
                            {file.state}
                          </span>
                        </>
                      )}
                      {file.recordCount && (
                        <>
                          <span>•</span>
                          <span>{formatNumber(file.recordCount)} records</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-2">
                  <Badge 
                    className={STATUS_COLORS[file.status] || 'bg-gray-100 text-gray-800'}
                    variant="secondary"
                  >
                    {file.status.replace('_', ' ')}
                  </Badge>
                </div>
              </div>

              {/* Progress Bar */}
              {file.progress > 0 && file.progress < 100 && (
                <div className="space-y-1">
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">{getStatusMessage(file)}</span>
                    <span className="text-gray-500">{file.progress}%</span>
                  </div>
                  <Progress value={file.progress} className="h-2" />
                </div>
              )}

              {/* Status Message */}
              {(file.progress === 0 || file.progress === 100) && (
                <p className="text-sm text-gray-600">{getStatusMessage(file)}</p>
              )}

              {/* Record Summary */}
              {file.validRecords !== undefined && (
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Total:</span>
                    <span className="ml-1 font-medium">{formatNumber(file.recordCount || 0)}</span>
                  </div>
                  <div>
                    <span className="text-gray-500">Valid:</span>
                    <span className="ml-1 font-medium text-green-600">{formatNumber(file.validRecords)}</span>
                  </div>
                  {file.errorRecords !== undefined && (
                    <div>
                      <span className="text-gray-500">Errors:</span>
                      <span className="ml-1 font-medium text-red-600">{formatNumber(file.errorRecords)}</span>
                    </div>
                  )}
                  {file.duplicateRecords !== undefined && (
                    <div>
                      <span className="text-gray-500">Duplicates:</span>
                      <span className="ml-1 font-medium text-orange-600">{formatNumber(file.duplicateRecords)}</span>
                    </div>
                  )}
                </div>
              )}

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                {canVerifyRecords(file) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onVerifyRecords(file)}
                    disabled={isValidating}
                    className="flex items-center gap-1"
                  >
                    {isValidating ? (
                      <RefreshCw className="h-3 w-3 animate-spin" />
                    ) : (
                      <Shield className="h-3 w-3" />
                    )}
                    Verify Records
                  </Button>
                )}

                {canStartMapping(file) && (
                  <Button
                    size="sm"
                    onClick={() => onStartMapping(file)}
                    className="flex items-center gap-1"
                  >
                    <MapPin className="h-3 w-3" />
                    Start Mapping
                  </Button>
                )}

                {canViewReport(file) && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => onViewReport(file)}
                    className="flex items-center gap-1"
                  >
                    <Eye className="h-3 w-3" />
                    View Report
                  </Button>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
} 