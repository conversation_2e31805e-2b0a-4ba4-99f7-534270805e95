import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { useWidgetData } from '@/hooks/useWidgetData';
import { AgentMetrics } from '@/types/dashboard';
import { TrendingUp, TrendingDown, DollarSign, Clock, Target, Activity } from 'lucide-react';

interface ClaimsCounterProps {
  widgetId: string;
  className?: string;
}

export const ClaimsCounter: React.FC<ClaimsCounterProps> = ({ widgetId, className }) => {
  const { data, isLoading, error } = useWidgetData<AgentMetrics>(widgetId, 'claims-counter');

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Claims Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-2/3"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error || !data?.data) {
    return (
      <Card className={className}>
        <CardHeader className="pb-2">
          <CardTitle className="text-sm font-medium">Claims Overview</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-500">
            <Activity className="h-8 w-8 mx-auto mb-2 opacity-50" />
            <p className="text-sm">Failed to load claims data</p>
            <p className="text-xs text-gray-400">{error}</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const metrics = data.data;

  const formatValue = (value: number): string => {
    if (value >= 1000000) return `$${(value / 1000000).toFixed(1)}M`;
    if (value >= 1000) return `$${(value / 1000).toFixed(1)}K`;
    return `$${value.toLocaleString()}`;
  };

  const formatPercentage = (value: number): string => {
    return `${(value * 100).toFixed(1)}%`;
  };

  const formatGrowth = (growth: number): { value: string; isPositive: boolean; icon: React.ReactNode } => {
    const isPositive = growth >= 0;
    const percentage = (Math.abs(growth) * 100).toFixed(1);
    return {
      value: `${isPositive ? '+' : '-'}${percentage}%`,
      isPositive,
      icon: isPositive ? 
        <TrendingUp className="h-3 w-3" /> : 
        <TrendingDown className="h-3 w-3" />
    };
  };

  const weeklyGrowth = formatGrowth(metrics.weeklyGrowth);
  const monthlyGrowth = formatGrowth(metrics.monthlyGrowth);

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Target className="h-4 w-4" />
          Claims Overview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Total Claims */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600">Total Claims</span>
            <div className={`flex items-center gap-1 text-xs ${weeklyGrowth.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {weeklyGrowth.icon}
              {weeklyGrowth.value} 7d
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {metrics.totalClaims.toLocaleString()}
          </div>
          <div className="text-xs text-gray-500">
            {metrics.activeClaims} active • {metrics.completedClaims} completed
          </div>
        </div>

        {/* Total Value */}
        <div className="space-y-1">
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-600 flex items-center gap-1">
              <DollarSign className="h-3 w-3" />
              Total Value
            </span>
            <div className={`flex items-center gap-1 text-xs ${monthlyGrowth.isPositive ? 'text-green-600' : 'text-red-600'}`}>
              {monthlyGrowth.icon}
              {monthlyGrowth.value} 30d
            </div>
          </div>
          <div className="text-2xl font-bold text-gray-900">
            {formatValue(metrics.totalValue)}
          </div>
          <div className="text-xs text-gray-500">
            Avg: {formatValue(metrics.averageClaimValue)} per claim
          </div>
        </div>

        {/* Success Rate & Response Time */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-1">
            <span className="text-xs text-gray-600">Success Rate</span>
            <div className="text-lg font-semibold text-green-600">
              {formatPercentage(metrics.successRate)}
            </div>
          </div>
          <div className="space-y-1">
            <span className="text-xs text-gray-600 flex items-center gap-1">
              <Clock className="h-3 w-3" />
              Avg Response
            </span>
            <div className="text-lg font-semibold text-blue-600">
              {metrics.responseTime}d
            </div>
          </div>
        </div>

        {/* Progress Bar for Active Claims */}
        <div className="space-y-2">
          <div className="flex justify-between text-xs">
            <span className="text-gray-600">Active Progress</span>
            <span className="text-gray-800">{metrics.activeClaims}/{metrics.totalClaims}</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div 
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(metrics.activeClaims / metrics.totalClaims) * 100}%` }}
            />
          </div>
        </div>

        {/* Last Updated */}
        <div className="pt-2 border-t border-gray-100">
          <div className="text-xs text-gray-400">
            Last updated: {data.lastUpdated.toLocaleTimeString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}; 