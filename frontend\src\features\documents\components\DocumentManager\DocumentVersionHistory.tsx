import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  FileText, 
  Download, 
  Eye, 
  User,
  ArrowRight,
  GitBranch
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

interface Document {
  id: string;
  claim_id: string;
  file_name: string;
  original_name: string;
  category: string;
  subcategory?: string;
  file_size: number;
  file_type: string;
  file_url?: string;
  description?: string;
  tags: string[];
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'archived';
  version: number;
  parent_document_id?: string;
  uploaded_by: string;
  reviewed_by?: string;
  approved_by?: string;
  is_confidential: boolean;
  expiry_date?: string;
  permissions: 'internal' | 'shareable' | 'public';
  created_at: string;
  updated_at: string;
  uploader_name?: string;
  reviewer_name?: string;
  approver_name?: string;
}

interface DocumentVersionHistoryProps {
  document: Document;
  onClose: () => void;
}

interface DocumentVersion {
  id: string;
  version: number;
  file_name: string;
  original_name: string;
  file_size: number;
  file_url?: string;
  description?: string;
  status: string;
  uploaded_by: string;
  uploader_name?: string;
  created_at: string;
  change_summary?: string;
}

const STATUS_CONFIG = {
  draft: { color: 'gray', label: 'Draft' },
  pending_review: { color: 'yellow', label: 'Pending Review' },
  approved: { color: 'green', label: 'Approved' },
  rejected: { color: 'red', label: 'Rejected' },
  archived: { color: 'blue', label: 'Archived' }
};

export const DocumentVersionHistory: React.FC<DocumentVersionHistoryProps> = ({ 
  document: doc, 
  onClose 
}) => {
  const [versions, setVersions] = useState<DocumentVersion[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadVersionHistory();
  }, [doc.id]);

  const loadVersionHistory = async () => {
    try {
      setLoading(true);
      
      // Get all versions of this document
      const { data, error } = await supabase
        .from('claim_documents')
        .select(`
          id,
          version,
          file_name,
          original_name,
          file_size,
          file_url,
          description,
          status,
          uploaded_by,
          created_at
        `)
        .or(`id.eq.${doc.id},parent_document_id.eq.${doc.id}`)
        .order('version', { ascending: false });

      if (error) throw error;

      // Get uploader names separately
      const uploaderIds = [...new Set(data?.map(d => d.uploaded_by) || [])];
      const { data: uploaders } = await supabase
        .from('users')
        .select('id, name')
        .in('id', uploaderIds);

      const uploaderMap = new Map(uploaders?.map(u => [u.id, u.name]) || []);

      const versionsWithNames = data?.map(version => ({
        ...version,
        uploader_name: uploaderMap.get(version.uploaded_by) || 'Unknown',
        change_summary: getChangeSummary(version.version)
      })) || [];

      setVersions(versionsWithNames);
    } catch (error) {
      console.error('Error loading version history:', error);
    } finally {
      setLoading(false);
    }
  };

  const getChangeSummary = (version: number): string => {
    if (version === 1) return 'Initial upload';
    
    // In a real app, you'd store change summaries in the database
    const summaries = [
      'Updated document content',
      'Added additional information',
      'Corrected document details',
      'Updated per feedback',
      'Final version with signatures'
    ];
    
    return summaries[(version - 2) % summaries.length];
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDownload = (version: DocumentVersion) => {
    if (version.file_url) {
      const link = globalThis.document.createElement('a');
      link.href = version.file_url;
      link.download = version.original_name;
      link.target = '_blank';
      globalThis.document.body.appendChild(link);
      link.click();
      globalThis.document.body.removeChild(link);
    }
  };

  const handleView = (version: DocumentVersion) => {
    if (version.file_url) {
      window.open(version.file_url, '_blank');
    }
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <GitBranch className="h-5 w-5" />
            Version History
          </DialogTitle>
          <DialogDescription>
            Track all versions and changes for "{doc.original_name}"
          </DialogDescription>
        </DialogHeader>

        {loading ? (
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading version history...</p>
          </div>
        ) : versions.length === 0 ? (
          <div className="text-center py-8">
            <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No version history found</h3>
            <p className="text-gray-600">This document doesn't have any recorded versions.</p>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Current Document Info */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <div className="flex items-center gap-3">
                <FileText className="h-6 w-6 text-blue-600" />
                <div className="flex-1">
                  <h3 className="font-semibold text-blue-900">{doc.original_name}</h3>
                  <p className="text-sm text-blue-700">
                    Current Version: v{doc.version} • {formatFileSize(doc.file_size)} • {doc.category?.replace('_', ' ')}
                  </p>
                </div>
                <Badge variant="default">Current</Badge>
              </div>
            </div>

            {/* Version Timeline */}
            <div className="space-y-4">
              <h3 className="font-semibold text-lg">Version Timeline</h3>
              
              <div className="relative">
                {/* Timeline line */}
                <div className="absolute left-6 top-0 bottom-0 w-0.5 bg-gray-200"></div>
                
                <div className="space-y-6">
                  {versions.map((version, index) => (
                    <div key={version.id} className="relative flex gap-4">
                      {/* Timeline dot */}
                      <div className={`relative z-10 flex items-center justify-center w-12 h-12 rounded-full border-4 ${
                        version.version === doc.version 
                          ? 'bg-blue-600 border-blue-200' 
                          : 'bg-white border-gray-300'
                      }`}>
                        <span className={`text-sm font-medium ${
                          version.version === doc.version ? 'text-white' : 'text-gray-600'
                        }`}>
                          v{version.version}
                        </span>
                      </div>

                      {/* Version content */}
                      <div className="flex-1 min-w-0 pb-6">
                        <div className="bg-white border rounded-lg p-4 shadow-sm">
                          <div className="flex items-start justify-between">
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center gap-2 mb-2">
                                <h4 className="font-medium truncate">{version.original_name}</h4>
                                {version.version === doc.version && (
                                  <Badge variant="default" className="text-xs">Current</Badge>
                                )}
                                <Badge variant={STATUS_CONFIG[version.status as keyof typeof STATUS_CONFIG]?.color as any} className="text-xs">
                                  {STATUS_CONFIG[version.status as keyof typeof STATUS_CONFIG]?.label || version.status}
                                </Badge>
                              </div>
                              
                              <div className="text-sm text-gray-600 space-y-1">
                                <div className="flex items-center gap-4">
                                  <span className="flex items-center gap-1">
                                    <Clock className="h-3 w-3" />
                                    {formatDate(version.created_at)}
                                  </span>
                                  <span className="flex items-center gap-1">
                                    <User className="h-3 w-3" />
                                    {version.uploader_name || 'Unknown'}
                                  </span>
                                  <span>{formatFileSize(version.file_size)}</span>
                                </div>
                                
                                {version.change_summary && (
                                  <p className="text-gray-700 italic">{version.change_summary}</p>
                                )}
                                
                                {version.description && (
                                  <p className="text-gray-700 mt-2">{version.description}</p>
                                )}
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="flex gap-2 ml-4">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleView(version)}
                                disabled={!version.file_url}
                              >
                                <Eye className="h-3 w-3 mr-1" />
                                View
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => handleDownload(version)}
                                disabled={!version.file_url}
                              >
                                <Download className="h-3 w-3 mr-1" />
                                Download
                              </Button>
                            </div>
                          </div>

                          {/* Show changes from previous version */}
                          {index < versions.length - 1 && (
                            <div className="mt-3 pt-3 border-t">
                              <div className="flex items-center gap-2 text-xs text-gray-500">
                                <ArrowRight className="h-3 w-3" />
                                <span>
                                  Changes from v{versions[index + 1].version}: 
                                  {version.file_size !== versions[index + 1].file_size && (
                                    <span className="ml-1">
                                      Size changed ({formatFileSize(versions[index + 1].file_size)} → {formatFileSize(version.file_size)})
                                    </span>
                                  )}
                                  {version.original_name !== versions[index + 1].original_name && (
                                    <span className="ml-1">Name updated</span>
                                  )}
                                </span>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Summary Stats */}
            <div className="bg-gray-50 rounded-lg p-4">
              <h4 className="font-medium mb-2">Version Summary</h4>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Versions:</span>
                  <p className="font-medium">{versions.length}</p>
                </div>
                <div>
                  <span className="text-gray-600">Current Status:</span>
                  <p className="font-medium capitalize">{doc.status.replace('_', ' ')}</p>
                </div>
                <div>
                  <span className="text-gray-600">First Upload:</span>
                  <p className="font-medium">
                    {versions.length > 0 && formatDate(versions[versions.length - 1].created_at)}
                  </p>
                </div>
                <div>
                  <span className="text-gray-600">Last Modified:</span>
                  <p className="font-medium">{formatDate(doc.updated_at)}</p>
                </div>
              </div>
            </div>
          </div>
        )}

        <div className="flex justify-end mt-6">
          <Button onClick={onClose}>Close</Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 