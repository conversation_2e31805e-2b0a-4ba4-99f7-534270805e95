// Data management panel component for saved data operations

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Database, Download, Eye, Trash2, RefreshCw } from 'lucide-react'

interface DataManagementPanelProps {
  onViewData: () => void
  onExportData: () => void
  onClearData: () => void
  onLoadData: () => void
}

export function DataManagementPanel({
  onViewData,
  onExportData,
  onClearData,
  onLoadData
}: DataManagementPanelProps) {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-4 w-4" />
          Data Management
        </CardTitle>
        <CardDescription>
          Manage your saved data, export records, and view storage summary.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Button
            variant="outline"
            onClick={onViewData}
            className="flex items-center gap-2"
          >
            <Eye className="h-4 w-4" />
            View Summary
          </Button>
          
          <Button
            variant="outline"
            onClick={onExportData}
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Export Data
          </Button>
          
          <Button
            variant="outline"
            onClick={onLoadData}
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Reload Data
          </Button>
          
          <Button
            variant="destructive"
            onClick={onClearData}
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear All
          </Button>
        </div>
        
        <div className="mt-4 p-3 bg-blue-50 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>Note:</strong> Data is stored locally in your browser. 
            Export regularly to avoid data loss.
          </p>
        </div>
      </CardContent>
    </Card>
  )
} 