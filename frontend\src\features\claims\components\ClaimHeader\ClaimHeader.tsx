import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, Edit3, Save, X } from 'lucide-react';
import { Claim } from '../../types/claim.types';

interface ClaimHeaderProps {
  claim: Claim;
  isEditing: boolean;
  isUpdating: boolean;
  onBack: () => void;
  onStartEdit: () => void;
  onSave: () => void;
  onCancelEdit: () => void;
}

export const ClaimHeader: React.FC<ClaimHeaderProps> = ({
  claim,
  isEditing,
  isUpdating,
  onBack,
  onStartEdit,
  onSave,
  onCancelEdit
}) => {
  return (
    <div className="flex items-center justify-between">
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">
            Claim {claim.property_id || claim.id.slice(0, 8)}
          </h1>
          <p className="text-gray-600">{claim.owner_name}</p>
        </div>
      </div>
      
      <div className="flex gap-2">
        {isEditing ? (
          <>
            <Button variant="outline" onClick={onCancelEdit} disabled={isUpdating}>
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>
            <Button onClick={onSave} disabled={isUpdating}>
              <Save className="h-4 w-4 mr-2" />
              {isUpdating ? 'Saving...' : 'Save Changes'}
            </Button>
          </>
        ) : (
          <Button onClick={onStartEdit}>
            <Edit3 className="h-4 w-4 mr-2" />
            Edit Claim
          </Button>
        )}
      </div>
    </div>
  );
}; 