import React, { useState, useEffect } from 'react';
import { 
  Users, Database, Settings, BarChart3, Monitor, Shield, 
  Upload, UserCog, FileSpreadsheet, TrendingUp, AlertTriangle,
  Server, Activity, CheckCircle, Calendar, DollarSign
} from 'lucide-react';

// Import types and components
import { AdministratorState, SystemHealth, ExecutiveMetrics } from './types';
import { CSVUploadManager } from './components/CSVUploadManager';
import { UserManagement } from './components/UserManagement';
import { ExecutiveDashboard } from './components/ExecutiveDashboard';
import { SystemMonitoring } from './components/SystemMonitoring';
import { LeadPoolManager } from './components/LeadPoolManager';
import { ComplianceCenter } from './components/ComplianceCenter';
import { SystemConfiguration } from './components/SystemConfiguration';
import { ResourceManagement } from './components/ResourceManagement';

interface AdministratorWorkspaceProps {
  administratorId: string;
}

const AdministratorWorkspace: React.FC<AdministratorWorkspaceProps> = ({ 
  administratorId 
}) => {
  const [activeView, setActiveView] = useState<string>('dashboard');
  const [adminState, setAdminState] = useState<AdministratorState | null>(null);
  const [systemAlerts, setSystemAlerts] = useState<SystemAlert[]>([]);
  const [loading, setLoading] = useState(true);

  // Mock data for demonstration
  useEffect(() => {
    const mockAdminState: AdministratorState = {
      adminId: administratorId,
      organization: {
        id: 'org-001',
        name: 'Asset Recovery Solutions Inc.',
        subdomain: 'arsolutions',
        industry: 'Financial Services',
        size: 'enterprise',
        establishedDate: new Date('2015-03-15'),
        headquarters: {
          street: '123 Business Center Dr',
          city: 'Atlanta',
          state: 'GA',
          zipCode: '30309',
          country: 'USA'
        },
        contactInfo: {
          primaryEmail: '<EMAIL>',
          primaryPhone: '******-555-0100',
          website: 'https://arsolutions.com'
        },
        settings: {
          timezone: 'America/New_York',
          dateFormat: 'MM/DD/YYYY',
          currency: 'USD',
          language: 'en-US',
          fiscalYearStart: '01-01',
          businessHours: [],
          holidays: []
        },
        branding: {
          logo: '/assets/logo.png',
          primaryColor: '#1e40af',
          secondaryColor: '#64748b',
          accentColor: '#06b6d4',
          fontFamily: 'Inter'
        }
      },
      userAccounts: [],
      leadPools: [],
      systemConfiguration: {} as any,
      subscription: {} as any,
      systemHealth: {
        overallStatus: 'healthy',
        uptime: 99.98,
        responseTime: 142,
        activeUsers: 47,
        systemLoad: 68,
        databasePerformance: 95,
        apiResponseTime: 89,
        errorRate: 0.02,
        storageUsage: 73,
        bandwidthUsage: 45,
        lastHealthCheck: new Date(),
        alerts: []
      },
      executiveMetrics: {
        totalRevenue: 2847500,
        monthlyGrowth: 8.3,
        activeLeads: 15847,
        conversionRate: 12.4,
        teamProductivity: 87,
        customerSatisfaction: 4.6,
        systemUptime: 99.98,
        costPerLead: 23.50,
        averageDealSize: 18750,
        timeToClose: 14.2,
        topPerformingTeam: 'Southeast Region',
        criticalIssues: 2
      },
      complianceStatus: {
        overallScore: 94,
        activeViolations: 0,
        pendingReviews: 3,
        certificationStatus: 'current',
        auditReadiness: 98,
        lastAudit: new Date('2024-10-15'),
        nextAudit: new Date('2025-04-15'),
        frameworks: ['SOC2', 'GDPR', 'CCPA', 'TCPA']
      }
    };

    const mockAlerts: SystemAlert[] = [
      {
        id: 'alert-001',
        type: 'performance',
        severity: 'medium',
        title: 'Database Query Performance',
        message: 'Average query response time increased by 15% in the last hour',
        timestamp: new Date(Date.now() - 3600000),
        isResolved: false,
        affectedSystems: ['database'],
        recommendedActions: ['Optimize slow queries', 'Check index usage']
      },
      {
        id: 'alert-002',
        type: 'security',
        severity: 'high',
        title: 'Multiple Failed Login Attempts',
        message: '23 failed login attempts detected from suspicious IP ranges',
        timestamp: new Date(Date.now() - 1800000),
        isResolved: false,
        affectedSystems: ['authentication'],
        recommendedActions: ['Review security logs', 'Update IP restrictions']
      },
      {
        id: 'alert-003',
        type: 'capacity',
        severity: 'low',
        title: 'Storage Usage Warning',
        message: 'Storage usage at 73% of allocated capacity',
        timestamp: new Date(Date.now() - 7200000),
        isResolved: false,
        affectedSystems: ['storage'],
        recommendedActions: ['Archive old data', 'Consider storage upgrade']
      }
    ];

    setAdminState(mockAdminState);
    setSystemAlerts(mockAlerts);
    setLoading(false);
  }, [administratorId]);

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Executive Dashboard',
      icon: BarChart3,
      description: 'Executive-level analytics and KPIs'
    },
    {
      id: 'data-management',
      label: 'Data Management',
      icon: Database,
      description: 'Lead pools, CSV uploads, and data quality'
    },
    {
      id: 'user-management',
      label: 'User Management',
      icon: Users,
      description: 'Accounts, permissions, and team structure'
    },
    {
      id: 'system-config',
      label: 'System Configuration',
      icon: Settings,
      description: 'Platform settings and integrations'
    },
    {
      id: 'monitoring',
      label: 'System Monitoring',
      icon: Monitor,
      description: 'Performance, health, and maintenance'
    },
    {
      id: 'compliance',
      label: 'Compliance Center',
      icon: Shield,
      description: 'Security, compliance, and audit management'
    },
    {
      id: 'resources',
      label: 'Resource Management',
      icon: Server,
      description: 'Subscription, billing, and capacity planning'
    }
  ];

  const renderActiveView = () => {
    if (!adminState) return null;

    switch (activeView) {
      case 'dashboard':
        return <ExecutiveDashboard adminState={adminState} />;
      case 'data-management':
        return <DataManagementCenter adminState={adminState} />;
      case 'user-management':
        return <UserManagement adminState={adminState} />;
      case 'system-config':
        return <SystemConfiguration adminState={adminState} />;
      case 'monitoring':
        return <SystemMonitoring adminState={adminState} />;
      case 'compliance':
        return <ComplianceCenter adminState={adminState} />;
      case 'resources':
        return <ResourceManagement adminState={adminState} />;
      default:
        return <ExecutiveDashboard adminState={adminState} />;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Administrator Console...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-full mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-blue-600 rounded-lg flex items-center justify-center">
                <Settings className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Administrator Console</h1>
                <p className="text-sm text-gray-600">{adminState?.organization.name}</p>
              </div>
            </div>
            
            {/* Quick Stats */}
            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-2">
                <Activity className="w-5 h-5 text-green-500" />
                <span className="text-sm font-medium text-gray-700">
                  {adminState?.systemHealth.activeUsers} Active Users
                </span>
              </div>
              <div className="flex items-center space-x-2">
                <TrendingUp className="w-5 h-5 text-blue-500" />
                <span className="text-sm font-medium text-gray-700">
                  ${(adminState?.executiveMetrics.totalRevenue || 0).toLocaleString()}
                </span>
              </div>
              <div className="flex items-center space-x-2">
                {systemAlerts.filter(a => !a.isResolved).length > 0 ? (
                  <AlertTriangle className="w-5 h-5 text-yellow-500" />
                ) : (
                  <CheckCircle className="w-5 h-5 text-green-500" />
                )}
                <span className="text-sm font-medium text-gray-700">
                  {systemAlerts.filter(a => !a.isResolved).length} Alerts
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-full mx-auto flex">
        {/* Sidebar Navigation */}
        <nav className="w-80 bg-white shadow-sm border-r border-gray-200 min-h-screen">
          <div className="p-6">
            {/* System Status Overview */}
            <div className="bg-gray-50 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">System Status</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">System Health</span>
                  <div className="flex items-center space-x-1">
                    <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    <span className="text-sm font-medium text-green-600">Healthy</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Uptime</span>
                  <span className="text-sm font-medium text-gray-900">
                    {adminState?.systemHealth.uptime}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Response Time</span>
                  <span className="text-sm font-medium text-gray-900">
                    {adminState?.systemHealth.responseTime}ms
                  </span>
                </div>
              </div>
            </div>

            {/* Recent Alerts */}
            <div className="bg-red-50 rounded-lg p-4 mb-6">
              <h3 className="text-sm font-semibold text-gray-900 mb-3">Recent Alerts</h3>
              <div className="space-y-2">
                {systemAlerts.slice(0, 3).map(alert => (
                  <div key={alert.id} className="flex items-start space-x-2">
                    <AlertTriangle className={`w-4 h-4 mt-0.5 ${
                      alert.severity === 'high' ? 'text-red-500' :
                      alert.severity === 'medium' ? 'text-yellow-500' : 'text-blue-500'
                    }`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-gray-900 truncate">
                        {alert.title}
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(alert.timestamp).toLocaleTimeString()}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Navigation Menu */}
            <div className="space-y-2">
              {navigationItems.map(item => {
                const Icon = item.icon;
                const isActive = activeView === item.id;
                
                return (
                  <button
                    key={item.id}
                    onClick={() => setActiveView(item.id)}
                    className={`w-full flex items-center space-x-3 px-4 py-3 text-left rounded-lg transition-colors ${
                      isActive 
                        ? 'bg-blue-50 text-blue-700 border border-blue-200' 
                        : 'text-gray-700 hover:bg-gray-50'
                    }`}
                  >
                    <Icon className={`w-5 h-5 ${isActive ? 'text-blue-600' : 'text-gray-400'}`} />
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium truncate">{item.label}</p>
                      <p className="text-xs text-gray-500 truncate">{item.description}</p>
                    </div>
                  </button>
                );
              })}
            </div>
          </div>
        </nav>

        {/* Main Content */}
        <main className="flex-1 p-6">
          {renderActiveView()}
        </main>
      </div>
    </div>
  );
};

// Data Management Center Component (placeholder)
const DataManagementCenter: React.FC<{ adminState: AdministratorState }> = ({ adminState }) => {
  const [activeTab, setActiveTab] = useState('upload');

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-gray-900">Data Management Center</h2>
        <div className="flex space-x-3">
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>New Upload</span>
          </button>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {[
            { id: 'upload', label: 'CSV Upload', icon: Upload },
            { id: 'pools', label: 'Lead Pools', icon: Database },
            { id: 'quality', label: 'Data Quality', icon: CheckCircle },
            { id: 'archive', label: 'Archive', icon: FileSpreadsheet }
          ].map(tab => {
            const Icon = tab.icon;
            const isActive = activeTab === tab.id;
            
            return (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  isActive
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <Icon className="w-4 h-4" />
                <span>{tab.label}</span>
              </button>
            );
          })}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="bg-white rounded-lg shadow">
        {activeTab === 'upload' && <CSVUploadManager adminState={adminState} />}
        {activeTab === 'pools' && <LeadPoolManager adminState={adminState} />}
        {activeTab === 'quality' && <DataQualityManager adminState={adminState} />}
        {activeTab === 'archive' && <DataArchiveManager adminState={adminState} />}
      </div>
    </div>
  );
};

// Placeholder components for tabs
const DataQualityManager: React.FC<{ adminState: AdministratorState }> = () => (
  <div className="p-6">
    <h3 className="text-lg font-semibold mb-4">Data Quality Management</h3>
    <p className="text-gray-600">Data quality monitoring and improvement tools.</p>
  </div>
);

const DataArchiveManager: React.FC<{ adminState: AdministratorState }> = () => (
  <div className="p-6">
    <h3 className="text-lg font-semibold mb-4">Data Archive Management</h3>
    <p className="text-gray-600">Archive and retention management tools.</p>
  </div>
);

// Define SystemAlert interface
interface SystemAlert {
  id: string;
  type: 'performance' | 'security' | 'capacity' | 'error' | 'warning';
  severity: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  message: string;
  timestamp: Date;
  isResolved: boolean;
  affectedSystems: string[];
  recommendedActions: string[];
}

// Define additional type interfaces used
interface SystemHealth {
  overallStatus: 'healthy' | 'warning' | 'critical';
  uptime: number;
  responseTime: number;
  activeUsers: number;
  systemLoad: number;
  databasePerformance: number;
  apiResponseTime: number;
  errorRate: number;
  storageUsage: number;
  bandwidthUsage: number;
  lastHealthCheck: Date;
  alerts: SystemAlert[];
}

interface ExecutiveMetrics {
  totalRevenue: number;
  monthlyGrowth: number;
  activeLeads: number;
  conversionRate: number;
  teamProductivity: number;
  customerSatisfaction: number;
  systemUptime: number;
  costPerLead: number;
  averageDealSize: number;
  timeToClose: number;
  topPerformingTeam: string;
  criticalIssues: number;
}

interface ComplianceStatus {
  overallScore: number;
  activeViolations: number;
  pendingReviews: number;
  certificationStatus: string;
  auditReadiness: number;
  lastAudit: Date;
  nextAudit: Date;
  frameworks: string[];
}

export default AdministratorWorkspace; 