import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  CreditCard, 
  DollarSign, 
  Calendar, 
  Download, 
  Plus,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp,
  Receipt,
  Settings
} from 'lucide-react'
import { stripeService, type Subscription, type PaymentMethod, type Invoice } from '@/services/stripeService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

interface BillingDashboardProps {
  customerId?: string
}

export const BillingDashboard: React.FC<BillingDashboardProps> = ({ customerId }) => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Billing data
  const [subscription, setSubscription] = useState<Subscription | null>(null)
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([])
  const [invoices, setInvoices] = useState<Invoice[]>([])
  const [usageStats, setUsageStats] = useState({
    claimsProcessed: 0,
    claimsLimit: 1000,
    storageUsed: 0,
    storageLimit: 10
  })

  useEffect(() => {
    if (customerId || user) {
      loadBillingData()
    }
  }, [customerId, user])

  const loadBillingData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      const customerIdToUse = customerId || user?.id || ''
      
      // Load subscription
      if (user?.subscription_id) {
        const { data: subData, error: subError } = await stripeService.getSubscription(user.subscription_id)
        if (subError) {
          console.error('Error loading subscription:', subError)
        } else {
          setSubscription(subData)
        }
      }

      // Load payment methods
      const { data: pmData, error: pmError } = await stripeService.getPaymentMethods(customerIdToUse)
      if (pmError) {
        console.error('Error loading payment methods:', pmError)
      } else {
        setPaymentMethods(pmData)
      }

      // Load invoices
      const { data: invData, error: invError } = await stripeService.getInvoices(customerIdToUse)
      if (invError) {
        console.error('Error loading invoices:', invError)
      } else {
        setInvoices(invData)
      }

      // Load usage stats (mock data for now)
      setUsageStats({
        claimsProcessed: 247,
        claimsLimit: 1000,
        storageUsed: 3.2,
        storageLimit: 10
      })
    } catch (error) {
      setError('Failed to load billing data')
      console.error('Billing data loading error:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-100 text-green-700'
      case 'trialing': return 'bg-blue-100 text-blue-700'
      case 'past_due': return 'bg-yellow-100 text-yellow-700'
      case 'canceled': return 'bg-red-100 text-red-700'
      case 'incomplete': return 'bg-gray-100 text-gray-700'
      default: return 'bg-gray-100 text-gray-700'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return <CheckCircle className="h-4 w-4" />
      case 'trialing': return <Clock className="h-4 w-4" />
      case 'past_due': return <AlertTriangle className="h-4 w-4" />
      case 'canceled': return <AlertTriangle className="h-4 w-4" />
      default: return <Clock className="h-4 w-4" />
    }
  }

  const formatCurrency = (amount: number, currency: string = 'usd') => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency.toUpperCase()
    }).format(amount / 100)
  }

  const formatDate = (timestamp: number) => {
    return new Date(timestamp * 1000).toLocaleDateString()
  }

  const downloadInvoice = async (invoiceId: string) => {
    try {
      // In a real implementation, this would download the PDF
      toast({
        title: "Invoice Download",
        description: `Invoice ${invoiceId} download started.`,
      })
    } catch (error) {
      toast({
        title: "Download Failed",
        description: "Failed to download invoice.",
        variant: "destructive"
      })
    }
  }

  const cancelSubscription = async () => {
    if (!subscription) return

    setLoading(true)
    
    try {
      const { error } = await stripeService.cancelSubscription(subscription.id)
      
      if (error) {
        setError('Failed to cancel subscription')
        return
      }
      
      await loadBillingData()
      
      toast({
        title: "Subscription Canceled",
        description: "Your subscription will remain active until the end of the current billing period.",
      })
    } catch (error) {
      setError('Failed to cancel subscription')
      console.error('Subscription cancellation error:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Billing & Subscription</h2>
          <p className="text-gray-600">Manage your subscription and billing information</p>
        </div>
        <Button variant="outline">
          <Settings className="h-4 w-4 mr-2" />
          Billing Settings
        </Button>
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="subscription">Subscription</TabsTrigger>
          <TabsTrigger value="payment-methods">Payment Methods</TabsTrigger>
          <TabsTrigger value="invoices">Invoices</TabsTrigger>
          <TabsTrigger value="usage">Usage</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Current Plan</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {subscription?.plan.name || 'No Plan'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {subscription ? formatCurrency(subscription.plan.amount) + '/' + subscription.plan.interval : 'Not subscribed'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Next Payment</CardTitle>
                <Calendar className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {subscription ? formatDate(subscription.current_period_end) : 'N/A'}
                </div>
                <p className="text-xs text-muted-foreground">
                  {subscription ? formatCurrency(subscription.plan.amount) : 'No upcoming payment'}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Status</CardTitle>
                <TrendingUp className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="flex items-center space-x-2">
                  {subscription && getStatusIcon(subscription.status)}
                  <span className="text-2xl font-bold capitalize">
                    {subscription?.status || 'Inactive'}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {subscription?.cancel_at_period_end ? 'Cancels at period end' : 'Active subscription'}
                </p>
              </CardContent>
            </Card>
          </div>

          {subscription && (
            <Card>
              <CardHeader>
                <CardTitle>Subscription Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <CreditCard className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">{subscription.plan.name}</div>
                      <div className="text-sm text-gray-500">
                        {formatCurrency(subscription.plan.amount)} per {subscription.plan.interval}
                      </div>
                    </div>
                  </div>
                  <Badge className={getStatusColor(subscription.status)}>
                    {getStatusIcon(subscription.status)}
                    <span className="ml-1 capitalize">{subscription.status}</span>
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">Current Period:</span>
                    <div className="font-medium">
                      {formatDate(subscription.current_period_start)} - {formatDate(subscription.current_period_end)}
                    </div>
                  </div>
                  {subscription.trial_end && (
                    <div>
                      <span className="text-gray-500">Trial Ends:</span>
                      <div className="font-medium">{formatDate(subscription.trial_end)}</div>
                    </div>
                  )}
                </div>

                {subscription.plan.features && (
                  <div>
                    <h4 className="font-medium mb-2">Plan Features</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      {subscription.plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2">
                          <CheckCircle className="h-4 w-4 text-green-500" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </CardContent>
            </Card>
          )}
        </TabsContent>

        <TabsContent value="subscription" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Subscription Management</CardTitle>
              <CardDescription>
                Manage your subscription plan and billing cycle
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {subscription ? (
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <h3 className="font-medium">{subscription.plan.name}</h3>
                      <p className="text-sm text-gray-500">
                        {formatCurrency(subscription.plan.amount)} per {subscription.plan.interval}
                      </p>
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        Change Plan
                      </Button>
                      <Button 
                        variant="destructive" 
                        size="sm"
                        onClick={cancelSubscription}
                        disabled={loading || subscription.cancel_at_period_end}
                      >
                        {subscription.cancel_at_period_end ? 'Canceled' : 'Cancel'}
                      </Button>
                    </div>
                  </div>

                  {subscription.cancel_at_period_end && (
                    <Alert>
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        Your subscription will be canceled at the end of the current billing period on {formatDate(subscription.current_period_end)}.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Subscription</h3>
                  <p className="text-gray-500 mb-4">
                    Choose a plan to get started with AssetHunterPro
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Choose Plan
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="payment-methods" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>Payment Methods</span>
                <Button size="sm">
                  <Plus className="h-4 w-4 mr-2" />
                  Add Method
                </Button>
              </CardTitle>
              <CardDescription>
                Manage your payment methods for subscriptions and purchases
              </CardDescription>
            </CardHeader>
            <CardContent>
              {paymentMethods.length > 0 ? (
                <div className="space-y-3">
                  {paymentMethods.map((method) => (
                    <div key={method.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-gray-100 rounded-lg flex items-center justify-center">
                          <CreditCard className="h-5 w-5 text-gray-600" />
                        </div>
                        <div>
                          <div className="font-medium">
                            {method.card ? `**** **** **** ${method.card.last4}` : method.type}
                          </div>
                          <div className="text-sm text-gray-500">
                            {method.card ? `${method.card.brand.toUpperCase()} • Expires ${method.card.exp_month}/${method.card.exp_year}` : 'Bank Account'}
                          </div>
                        </div>
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          Edit
                        </Button>
                        <Button variant="destructive" size="sm">
                          Remove
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <CreditCard className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Payment Methods</h3>
                  <p className="text-gray-500 mb-4">
                    Add a payment method to manage your subscription
                  </p>
                  <Button>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Payment Method
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="invoices" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Billing History</CardTitle>
              <CardDescription>
                View and download your invoices and payment history
              </CardDescription>
            </CardHeader>
            <CardContent>
              {invoices.length > 0 ? (
                <div className="space-y-3">
                  {invoices.map((invoice) => (
                    <div key={invoice.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <Receipt className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">Invoice #{invoice.id}</div>
                          <div className="text-sm text-gray-500">
                            {invoice.due_date ? formatDate(invoice.due_date) : 'No due date'}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium">{formatCurrency(invoice.amount_due, invoice.currency)}</div>
                          <Badge className={invoice.status === 'paid' ? 'bg-green-100 text-green-700' : 'bg-yellow-100 text-yellow-700'}>
                            {invoice.status}
                          </Badge>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => downloadInvoice(invoice.id)}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Receipt className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Invoices</h3>
                  <p className="text-gray-500">
                    Your billing history will appear here once you have a subscription
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="usage" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card>
              <CardHeader>
                <CardTitle>Claims Processed</CardTitle>
                <CardDescription>
                  Current month usage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Used</span>
                    <span>{usageStats.claimsProcessed} / {usageStats.claimsLimit}</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${(usageStats.claimsProcessed / usageStats.claimsLimit) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {usageStats.claimsLimit - usageStats.claimsProcessed} claims remaining this month
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Storage Used</CardTitle>
                <CardDescription>
                  Document and file storage
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Used</span>
                    <span>{usageStats.storageUsed} GB / {usageStats.storageLimit} GB</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-600 h-2 rounded-full" 
                      style={{ width: `${(usageStats.storageUsed / usageStats.storageLimit) * 100}%` }}
                    ></div>
                  </div>
                  <p className="text-xs text-gray-500">
                    {usageStats.storageLimit - usageStats.storageUsed} GB available
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
