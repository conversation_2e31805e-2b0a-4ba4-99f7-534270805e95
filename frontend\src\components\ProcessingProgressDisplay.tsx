import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Activity, 
  CheckCircle, 
  AlertTriangle, 
  Clock, 
  TrendingUp,
  FileCheck,
  Zap
} from 'lucide-react';
import { ProcessingProgress } from '../services/largeFileProcessor';
import { StateDetectionResult } from '../services/universalFieldMapper';

interface ProcessingProgressDisplayProps {
  progress: ProcessingProgress;
  stateDetection?: StateDetectionResult | null;
}

export const ProcessingProgressDisplay: React.FC<ProcessingProgressDisplayProps> = ({
  progress,
  stateDetection
}) => {
  // Format time remaining
  const formatTimeRemaining = (seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Format processing rate
  const formatRate = (rate: number): string => {
    if (rate < 1000) return `${rate.toFixed(0)} rec/sec`;
    return `${(rate / 1000).toFixed(1)}k rec/sec`;
  };

  // Get stage icon and color
  const getStageDisplay = (stage: ProcessingProgress['stage']) => {
    switch (stage) {
      case 'detecting':
        return { icon: FileCheck, color: 'text-blue-600', bgColor: 'bg-blue-100' };
      case 'processing':
        return { icon: Activity, color: 'text-orange-600', bgColor: 'bg-orange-100' };
      case 'analyzing':
        return { icon: TrendingUp, color: 'text-purple-600', bgColor: 'bg-purple-100' };
      case 'saving':
        return { icon: Clock, color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'complete':
        return { icon: CheckCircle, color: 'text-green-600', bgColor: 'bg-green-100' };
      default:
        return { icon: Activity, color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  const stageDisplay = getStageDisplay(progress.stage);
  const StageIcon = stageDisplay.icon;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <StageIcon className={`w-5 h-5 ${stageDisplay.color}`} />
          Processing Progress
          <Badge variant="outline" className="ml-auto">
            {progress.stage.charAt(0).toUpperCase() + progress.stage.slice(1)}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Main Progress Bar */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="font-medium">Overall Progress</span>
            <span className="text-gray-600">{progress.percentage.toFixed(1)}%</span>
          </div>
          <Progress value={progress.percentage} className="h-3" />
        </div>

        {/* Processing Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-bold text-blue-600">
              {progress.recordsProcessed.toLocaleString()}
            </p>
            <p className="text-xs text-gray-600">Records Processed</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-bold text-green-600">
              {progress.totalRecords > 0 ? progress.totalRecords.toLocaleString() : 'Estimating...'}
            </p>
            <p className="text-xs text-gray-600">Total Records</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-bold text-purple-600">
              {progress.currentRate > 0 ? formatRate(progress.currentRate) : 'Calculating...'}
            </p>
            <p className="text-xs text-gray-600">Processing Speed</p>
          </div>
          
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <p className="text-lg font-bold text-orange-600">
              {progress.estimatedTimeRemaining > 0 
                ? formatTimeRemaining(progress.estimatedTimeRemaining) 
                : 'Calculating...'
              }
            </p>
            <p className="text-xs text-gray-600">ETA</p>
          </div>
        </div>

        {/* State Detection Results */}
        {stateDetection && (
          <div className="p-4 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center gap-2 mb-2">
              <Zap className="w-4 h-4 text-blue-600" />
              <span className="font-medium text-blue-900">Auto-Detection Results</span>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <p className="text-sm font-medium text-blue-900">
                  State Format: {stateDetection.state.toUpperCase()}
                </p>
                <p className="text-xs text-blue-700">
                  Confidence: {(stateDetection.confidence * 100).toFixed(1)}%
                </p>
              </div>
              <div>
                <p className="text-sm font-medium text-blue-900">
                  Fields Mapped: {Object.keys(stateDetection.mappings).length}
                </p>
                <p className="text-xs text-blue-700">
                  Required fields: {stateDetection.requiredFieldsPresent ? 'Present' : 'Missing'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Error Summary */}
        {progress.errors.length > 0 && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <div className="flex justify-between items-center">
                <span>
                  {progress.errors.filter(e => e.severity === 'error').length} errors, {' '}
                  {progress.errors.filter(e => e.severity === 'warning').length} warnings detected
                </span>
                <Badge variant="destructive" className="text-xs">
                  View Details
                </Badge>
              </div>
            </AlertDescription>
          </Alert>
        )}

        {/* Warnings Display */}
        {progress.warnings.length > 0 && (
          <div className="space-y-2">
            <p className="text-sm font-medium text-amber-800">Warnings:</p>
            <div className="space-y-1">
              {progress.warnings.slice(0, 3).map((warning, index) => (
                <p key={index} className="text-xs text-amber-700 bg-amber-50 p-2 rounded">
                  {warning}
                </p>
              ))}
              {progress.warnings.length > 3 && (
                <p className="text-xs text-amber-600">
                  +{progress.warnings.length - 3} more warnings...
                </p>
              )}
            </div>
          </div>
        )}

        {/* Duplicate Detection Results */}
        {progress.duplicatesFound > 0 && (
          <div className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-yellow-800">
                  Duplicates Detected
                </p>
                <p className="text-xs text-yellow-700">
                  {progress.duplicatesFound} potential duplicate{progress.duplicatesFound !== 1 ? 's' : ''} found
                </p>
              </div>
              <Badge variant="outline" className="text-yellow-800 border-yellow-300">
                Needs Review
              </Badge>
            </div>
          </div>
        )}

        {/* Processing Complete */}
        {progress.stage === 'complete' && (
          <div className="p-4 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center gap-2">
              <CheckCircle className="w-5 h-5 text-green-600" />
              <div>
                <p className="font-medium text-green-900">Processing Complete!</p>
                <p className="text-sm text-green-700">
                  Successfully processed {progress.recordsProcessed.toLocaleString()} records
                  {progress.currentRate > 0 && ` at ${formatRate(progress.currentRate)}`}
                </p>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 