import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  FileText, 
  Clock, 
  CheckCircle, 
  Phone, 
  Mail, 
  Calendar,
  Target,
  TrendingUp,
  AlertCircle,
  MessageSquare,
  Upload,
  User,
  Plus,
  Activity,
  DollarSign,
  Archive
} from 'lucide-react';

interface DashboardStats {
  myClaims: {
    total: number;
    active: number;
    completed: number;
    pending: number;
  };
  activities: {
    today: number;
    thisWeek: number;
    callsMade: number;
    emailsSent: number;
  };
  performance: {
    successRate: number;
    avgResponseTime: number;
    completionRate: number;
    monthlyGoal: number;
    monthlyProgress: number;
  };
  followUps: {
    today: number;
    overdue: number;
    thisWeek: number;
  };
}

interface RecentActivity {
  id: string;
  type: 'call' | 'email' | 'note' | 'document';
  claimId: string;
  claimantName: string;
  description: string;
  timestamp: string;
  outcome?: string;
}

interface UpcomingTask {
  id: string;
  type: 'follow_up' | 'document_review' | 'call_scheduled' | 'deadline';
  claimId: string;
  claimantName: string;
  description: string;
  dueDate: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
}

export const JuniorAgentDashboard: React.FC = () => {
  const [stats, setStats] = useState<DashboardStats>({
    myClaims: { total: 47, active: 23, completed: 18, pending: 6 },
    activities: { today: 12, thisWeek: 67, callsMade: 34, emailsSent: 28 },
    performance: { successRate: 68, avgResponseTime: 2.3, completionRate: 76, monthlyGoal: 25, monthlyProgress: 18 },
    followUps: { today: 8, overdue: 3, thisWeek: 15 }
  });

  const [recentActivities] = useState<RecentActivity[]>([
    {
      id: '1',
      type: 'call',
      claimId: 'CL-2024-001',
      claimantName: 'John Smith',
      description: 'Successful contact - interested in proceeding',
      timestamp: '2024-12-12T14:30:00Z',
      outcome: 'interested'
    },
    {
      id: '2',
      type: 'email',
      claimId: 'CL-2024-002',
      claimantName: 'Mary Johnson',
      description: 'Sent engagement letter for review',
      timestamp: '2024-12-12T13:15:00Z'
    },
    {
      id: '3',
      type: 'note',
      claimId: 'CL-2024-003',
      claimantName: 'Robert Davis',
      description: 'Updated address information after verification',
      timestamp: '2024-12-12T11:45:00Z'
    }
  ]);

  const [upcomingTasks] = useState<UpcomingTask[]>([
    {
      id: '1',
      type: 'follow_up',
      claimId: 'CL-2024-001',
      claimantName: 'John Smith',
      description: 'Follow up on document signing',
      dueDate: '2024-12-13T10:00:00Z',
      priority: 'high'
    },
    {
      id: '2',
      type: 'call_scheduled',
      claimId: 'CL-2024-004',
      claimantName: 'Lisa Wilson',
      description: 'Scheduled call to discuss claim details',
      dueDate: '2024-12-13T14:00:00Z',
      priority: 'medium'
    },
    {
      id: '3',
      type: 'deadline',
      claimId: 'CL-2024-005',
      claimantName: 'Mike Brown',
      description: 'Contract response deadline',
      dueDate: '2024-12-14T17:00:00Z',
      priority: 'urgent'
    }
  ]);

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'call': return <Phone className="h-4 w-4" />;
      case 'email': return <Mail className="h-4 w-4" />;
      case 'note': return <MessageSquare className="h-4 w-4" />;
      case 'document': return <Upload className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getTaskIcon = (type: string) => {
    switch (type) {
      case 'follow_up': return <Clock className="h-4 w-4" />;
      case 'call_scheduled': return <Phone className="h-4 w-4" />;
      case 'document_review': return <FileText className="h-4 w-4" />;
      case 'deadline': return <AlertCircle className="h-4 w-4" />;
      default: return <Activity className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date();
    const date = new Date(timestamp);
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    return `${Math.floor(diffInHours / 24)}d ago`;
  };

  const formatDueDate = (dueDate: string) => {
    const now = new Date();
    const date = new Date(dueDate);
    const diffInHours = Math.floor((date.getTime() - now.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 0) return 'Overdue';
    if (diffInHours < 24) return `In ${diffInHours}h`;
    return `In ${Math.floor(diffInHours / 24)}d`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">My Dashboard</h1>
          <p className="text-gray-600">Track your claims, activities, and daily tasks</p>
        </div>
        <div className="flex space-x-2">
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            New Activity
          </Button>
          <Button variant="outline">
            <FileText className="h-4 w-4 mr-2" />
            View All Claims
          </Button>
        </div>
      </div>

      {/* Key Metrics Row */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* My Claims */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">My Claims</CardTitle>
            <FileText className="h-5 w-5 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.myClaims.total}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{stats.myClaims.active} Active</span>
              </div>
              <div className="flex items-center">
                <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
                <span className="text-gray-600">{stats.myClaims.completed} Done</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Today's Activities */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Today's Activities</CardTitle>
            <Activity className="h-5 w-5 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.activities.today}</div>
            <div className="flex items-center space-x-4 mt-2 text-sm">
              <div className="flex items-center">
                <Phone className="h-3 w-3 text-gray-500 mr-1" />
                <span className="text-gray-600">{stats.activities.callsMade} Calls</span>
              </div>
              <div className="flex items-center">
                <Mail className="h-3 w-3 text-gray-500 mr-1" />
                <span className="text-gray-600">{stats.activities.emailsSent} Emails</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Success Rate */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Success Rate</CardTitle>
            <Target className="h-5 w-5 text-purple-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.performance.successRate}%</div>
            <div className="mt-2">
              <Progress value={stats.performance.successRate} className="h-2" />
            </div>
            <p className="text-sm text-gray-600 mt-1">Above team average (65%)</p>
          </CardContent>
        </Card>

        {/* Follow-ups */}
        <Card className="border-0 shadow-lg">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Follow-ups Due</CardTitle>
            <Clock className="h-5 w-5 text-orange-600" />
          </CardHeader>
          <CardContent>
            <div className="text-3xl font-bold text-gray-900">{stats.followUps.today}</div>
            <div className="mt-2">
              {stats.followUps.overdue > 0 && (
                <div className="flex items-center">
                  <AlertCircle className="h-3 w-3 text-red-500 mr-1" />
                  <span className="text-sm text-red-600">{stats.followUps.overdue} overdue</span>
                </div>
              )}
              <p className="text-sm text-gray-600">
                {stats.followUps.thisWeek} this week
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance and Tasks Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Monthly Performance */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
              Monthly Performance
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {/* Goal Progress */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <span className="text-sm font-medium">Claims Completed</span>
                <span className="text-sm text-gray-600">
                  {stats.performance.monthlyProgress} / {stats.performance.monthlyGoal}
                </span>
              </div>
              <Progress 
                value={(stats.performance.monthlyProgress / stats.performance.monthlyGoal) * 100} 
                className="h-3"
              />
              <p className="text-xs text-gray-500 mt-1">
                {stats.performance.monthlyGoal - stats.performance.monthlyProgress} more to reach goal
              </p>
            </div>

            {/* Key Metrics */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-3 bg-blue-50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{stats.performance.avgResponseTime}h</div>
                <div className="text-sm text-blue-700">Avg Response</div>
              </div>
              <div className="text-center p-3 bg-green-50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">{stats.performance.completionRate}%</div>
                <div className="text-sm text-green-700">Completion Rate</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Tasks */}
        <Card className="border-0 shadow-lg">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-purple-600" />
                Upcoming Tasks
              </div>
              <Badge variant="outline">{upcomingTasks.length}</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingTasks.map((task) => (
                <div key={task.id} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 bg-gray-100 rounded-lg">
                      {getTaskIcon(task.type)}
                    </div>
                    <div>
                      <div className="font-medium text-sm">{task.description}</div>
                      <div className="text-xs text-gray-500">{task.claimantName} • {task.claimId}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Badge className={`text-xs ${getPriorityColor(task.priority)}`}>
                      {task.priority}
                    </Badge>
                    <span className="text-xs text-gray-500">{formatDueDate(task.dueDate)}</span>
                  </div>
                </div>
              ))}
            </div>
            <Button variant="outline" className="w-full mt-4">
              View All Tasks
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activities */}
      <Card className="border-0 shadow-lg">
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="h-5 w-5 mr-2 text-gray-600" />
            Recent Activities
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {recentActivities.map((activity) => (
              <div key={activity.id} className="flex items-center justify-between p-3 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-gray-100 rounded-lg">
                    {getActivityIcon(activity.type)}
                  </div>
                  <div>
                    <div className="font-medium text-sm">{activity.description}</div>
                    <div className="text-xs text-gray-500">
                      {activity.claimantName} • {activity.claimId}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {activity.outcome && (
                    <Badge variant="outline" className="text-xs">
                      {activity.outcome}
                    </Badge>
                  )}
                  <span className="text-xs text-gray-500">{formatTimeAgo(activity.timestamp)}</span>
                </div>
              </div>
            ))}
          </div>
          <Button variant="outline" className="w-full mt-4">
            <Archive className="h-4 w-4 mr-2" />
            View Activity History
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}; 