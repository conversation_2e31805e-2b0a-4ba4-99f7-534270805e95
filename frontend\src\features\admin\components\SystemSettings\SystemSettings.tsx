import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Separator } from '@/components/ui/separator';
import { 
  Settings, 
  Shield, 
  Mail, 
  Database, 
  Bell, 
  Globe, 
  Lock, 
  Server, 
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';
import { usePermissions } from '@/contexts/AuthContext';

interface SystemSettingsProps {
  className?: string;
}

interface SettingsData {
  general: {
    siteName: string;
    siteUrl: string;
    timeZone: string;
    defaultLanguage: string;
    maintenanceMode: boolean;
  };
  security: {
    sessionTimeout: number;
    passwordMinLength: number;
    requireTwoFactor: boolean;
    allowSelfRegistration: boolean;
    maxLoginAttempts: number;
    accountLockoutDuration: number;
  };
  email: {
    smtpHost: string;
    smtpPort: number;
    smtpUser: string;
    smtpPassword: string;
    smtpEncryption: string;
    fromEmail: string;
    fromName: string;
    testEmailRecipient: string;
  };
  notifications: {
    emailNotifications: boolean;
    claimAssignmentNotification: boolean;
    paymentNotification: boolean;
    systemAlerts: boolean;
    weeklyReports: boolean;
  };
  database: {
    autoBackup: boolean;
    backupFrequency: string;
    retentionPeriod: number;
    compressionEnabled: boolean;
  };
  audit: {
    logLevel: string;
    retainLogs: number;
    logUserActions: boolean;
    logSystemEvents: boolean;
  };
}

const defaultSettings: SettingsData = {
  general: {
    siteName: 'AssetHunterPro',
    siteUrl: 'https://assethunterpro.com',
    timeZone: 'UTC',
    defaultLanguage: 'en',
    maintenanceMode: false,
  },
  security: {
    sessionTimeout: 480, // 8 hours in minutes
    passwordMinLength: 8,
    requireTwoFactor: false,
    allowSelfRegistration: false,
    maxLoginAttempts: 5,
    accountLockoutDuration: 30, // minutes
  },
  email: {
    smtpHost: 'smtp.company.com',
    smtpPort: 587,
    smtpUser: '<EMAIL>',
    smtpPassword: '',
    smtpEncryption: 'tls',
    fromEmail: '<EMAIL>',
    fromName: 'AssetHunterPro',
    testEmailRecipient: '',
  },
  notifications: {
    emailNotifications: true,
    claimAssignmentNotification: true,
    paymentNotification: true,
    systemAlerts: true,
    weeklyReports: false,
  },
  database: {
    autoBackup: true,
    backupFrequency: 'daily',
    retentionPeriod: 30,
    compressionEnabled: true,
  },
  audit: {
    logLevel: 'info',
    retainLogs: 90,
    logUserActions: true,
    logSystemEvents: true,
  },
};

export const SystemSettings: React.FC<SystemSettingsProps> = ({ className }) => {
  const [settings, setSettings] = useState<SettingsData>(defaultSettings);
  const [activeTab, setActiveTab] = useState('general');
  const [isSaving, setIsSaving] = useState(false);
  const [lastSaved, setLastSaved] = useState<Date | null>(null);
  const [showPasswords, setShowPasswords] = useState(false);
  const [testEmailSent, setTestEmailSent] = useState(false);
  
  const { hasPermission } = usePermissions();

  const handleSave = async () => {
    setIsSaving(true);
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    setLastSaved(new Date());
    setIsSaving(false);
  };

  const handleTestEmail = async () => {
    if (!settings.email.testEmailRecipient) {
      alert('Please enter a test email recipient first.');
      return;
    }
    setTestEmailSent(true);
    setTimeout(() => setTestEmailSent(false), 3000);
  };

  const handleReset = () => {
    if (confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.')) {
      setSettings(defaultSettings);
    }
  };

  const updateSettings = (section: keyof SettingsData, field: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }));
  };

  if (!hasPermission('system:configure')) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600">You don't have permission to access system settings.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-purple-100 rounded-lg">
            <Settings className="h-6 w-6 text-purple-600" />
          </div>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">System Settings</h2>
            <p className="text-gray-600">Configure application settings and preferences</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          {lastSaved && (
            <span className="text-sm text-green-600 flex items-center">
              <CheckCircle className="h-4 w-4 mr-1" />
              Saved {lastSaved.toLocaleTimeString()}
            </span>
          )}
          <Button variant="outline" onClick={handleReset}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Reset
          </Button>
          <Button onClick={handleSave} disabled={isSaving}>
            {isSaving ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                Save Changes
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Settings Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-6">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="email">Email</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="database">Database</TabsTrigger>
          <TabsTrigger value="audit">Audit</TabsTrigger>
        </TabsList>

        {/* General Settings */}
        <TabsContent value="general" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Globe className="h-5 w-5 mr-2" />
                General Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="siteName">Site Name</Label>
                  <Input
                    id="siteName"
                    value={settings.general.siteName}
                    onChange={(e) => updateSettings('general', 'siteName', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="siteUrl">Site URL</Label>
                  <Input
                    id="siteUrl"
                    value={settings.general.siteUrl}
                    onChange={(e) => updateSettings('general', 'siteUrl', e.target.value)}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="timeZone">Time Zone</Label>
                  <Select value={settings.general.timeZone} onValueChange={(value) => updateSettings('general', 'timeZone', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="UTC">UTC</SelectItem>
                      <SelectItem value="America/New_York">Eastern Time</SelectItem>
                      <SelectItem value="America/Chicago">Central Time</SelectItem>
                      <SelectItem value="America/Denver">Mountain Time</SelectItem>
                      <SelectItem value="America/Los_Angeles">Pacific Time</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="defaultLanguage">Default Language</Label>
                  <Select value={settings.general.defaultLanguage} onValueChange={(value) => updateSettings('general', 'defaultLanguage', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="es">Spanish</SelectItem>
                      <SelectItem value="fr">French</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="maintenanceMode"
                  checked={settings.general.maintenanceMode}
                  onCheckedChange={(checked) => updateSettings('general', 'maintenanceMode', checked)}
                />
                <Label htmlFor="maintenanceMode">Maintenance Mode</Label>
                {settings.general.maintenanceMode && (
                  <Badge variant="destructive">
                    <AlertTriangle className="h-3 w-3 mr-1" />
                    Active
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Security Settings */}
        <TabsContent value="security" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Shield className="h-5 w-5 mr-2" />
                Security Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
                  <Input
                    id="sessionTimeout"
                    type="number"
                    value={settings.security.sessionTimeout}
                    onChange={(e) => updateSettings('security', 'sessionTimeout', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
                  <Input
                    id="passwordMinLength"
                    type="number"
                    value={settings.security.passwordMinLength}
                    onChange={(e) => updateSettings('security', 'passwordMinLength', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="maxLoginAttempts">Max Login Attempts</Label>
                  <Input
                    id="maxLoginAttempts"
                    type="number"
                    value={settings.security.maxLoginAttempts}
                    onChange={(e) => updateSettings('security', 'maxLoginAttempts', parseInt(e.target.value))}
                  />
                </div>
                <div>
                  <Label htmlFor="accountLockoutDuration">Lockout Duration (minutes)</Label>
                  <Input
                    id="accountLockoutDuration"
                    type="number"
                    value={settings.security.accountLockoutDuration}
                    onChange={(e) => updateSettings('security', 'accountLockoutDuration', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="requireTwoFactor"
                    checked={settings.security.requireTwoFactor}
                    onCheckedChange={(checked) => updateSettings('security', 'requireTwoFactor', checked)}
                  />
                  <Label htmlFor="requireTwoFactor">Require Two-Factor Authentication</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="allowSelfRegistration"
                    checked={settings.security.allowSelfRegistration}
                    onCheckedChange={(checked) => updateSettings('security', 'allowSelfRegistration', checked)}
                  />
                  <Label htmlFor="allowSelfRegistration">Allow Self Registration</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Email Settings */}
        <TabsContent value="email" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Mail className="h-5 w-5 mr-2" />
                Email Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpHost">SMTP Host</Label>
                  <Input
                    id="smtpHost"
                    value={settings.email.smtpHost}
                    onChange={(e) => updateSettings('email', 'smtpHost', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPort">SMTP Port</Label>
                  <Input
                    id="smtpPort"
                    type="number"
                    value={settings.email.smtpPort}
                    onChange={(e) => updateSettings('email', 'smtpPort', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="smtpUser">SMTP Username</Label>
                  <Input
                    id="smtpUser"
                    value={settings.email.smtpUser}
                    onChange={(e) => updateSettings('email', 'smtpUser', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="smtpPassword">SMTP Password</Label>
                  <div className="relative">
                    <Input
                      id="smtpPassword"
                      type={showPasswords ? "text" : "password"}
                      value={settings.email.smtpPassword}
                      onChange={(e) => updateSettings('email', 'smtpPassword', e.target.value)}
                    />
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                      onClick={() => setShowPasswords(!showPasswords)}
                    >
                      {showPasswords ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="smtpEncryption">Encryption</Label>
                  <Select value={settings.email.smtpEncryption} onValueChange={(value) => updateSettings('email', 'smtpEncryption', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="none">None</SelectItem>
                      <SelectItem value="tls">TLS</SelectItem>
                      <SelectItem value="ssl">SSL</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="fromEmail">From Email</Label>
                  <Input
                    id="fromEmail"
                    type="email"
                    value={settings.email.fromEmail}
                    onChange={(e) => updateSettings('email', 'fromEmail', e.target.value)}
                  />
                </div>
                <div>
                  <Label htmlFor="fromName">From Name</Label>
                  <Input
                    id="fromName"
                    value={settings.email.fromName}
                    onChange={(e) => updateSettings('email', 'fromName', e.target.value)}
                  />
                </div>
              </div>
              <Separator />
              <div className="flex items-center space-x-2">
                <div className="flex-1">
                  <Label htmlFor="testEmailRecipient">Test Email Recipient</Label>
                  <Input
                    id="testEmailRecipient"
                    type="email"
                    placeholder="<EMAIL>"
                    value={settings.email.testEmailRecipient}
                    onChange={(e) => updateSettings('email', 'testEmailRecipient', e.target.value)}
                  />
                </div>
                <Button onClick={handleTestEmail} className="mt-6">
                  {testEmailSent ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Sent!
                    </>
                  ) : (
                    'Send Test Email'
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Notifications Settings */}
        <TabsContent value="notifications" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Notification Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="emailNotifications"
                    checked={settings.notifications.emailNotifications}
                    onCheckedChange={(checked) => updateSettings('notifications', 'emailNotifications', checked)}
                  />
                  <Label htmlFor="emailNotifications">Enable Email Notifications</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="claimAssignmentNotification"
                    checked={settings.notifications.claimAssignmentNotification}
                    onCheckedChange={(checked) => updateSettings('notifications', 'claimAssignmentNotification', checked)}
                  />
                  <Label htmlFor="claimAssignmentNotification">Claim Assignment Notifications</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="paymentNotification"
                    checked={settings.notifications.paymentNotification}
                    onCheckedChange={(checked) => updateSettings('notifications', 'paymentNotification', checked)}
                  />
                  <Label htmlFor="paymentNotification">Payment Notifications</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="systemAlerts"
                    checked={settings.notifications.systemAlerts}
                    onCheckedChange={(checked) => updateSettings('notifications', 'systemAlerts', checked)}
                  />
                  <Label htmlFor="systemAlerts">System Alerts</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="weeklyReports"
                    checked={settings.notifications.weeklyReports}
                    onCheckedChange={(checked) => updateSettings('notifications', 'weeklyReports', checked)}
                  />
                  <Label htmlFor="weeklyReports">Weekly Reports</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Database Settings */}
        <TabsContent value="database" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Database className="h-5 w-5 mr-2" />
                Database Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="backupFrequency">Backup Frequency</Label>
                  <Select value={settings.database.backupFrequency} onValueChange={(value) => updateSettings('database', 'backupFrequency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="hourly">Hourly</SelectItem>
                      <SelectItem value="daily">Daily</SelectItem>
                      <SelectItem value="weekly">Weekly</SelectItem>
                      <SelectItem value="monthly">Monthly</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="retentionPeriod">Retention Period (days)</Label>
                  <Input
                    id="retentionPeriod"
                    type="number"
                    value={settings.database.retentionPeriod}
                    onChange={(e) => updateSettings('database', 'retentionPeriod', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="autoBackup"
                    checked={settings.database.autoBackup}
                    onCheckedChange={(checked) => updateSettings('database', 'autoBackup', checked)}
                  />
                  <Label htmlFor="autoBackup">Enable Automatic Backups</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="compressionEnabled"
                    checked={settings.database.compressionEnabled}
                    onCheckedChange={(checked) => updateSettings('database', 'compressionEnabled', checked)}
                  />
                  <Label htmlFor="compressionEnabled">Enable Backup Compression</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Audit Settings */}
        <TabsContent value="audit" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Server className="h-5 w-5 mr-2" />
                Audit & Logging Settings
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="logLevel">Log Level</Label>
                  <Select value={settings.audit.logLevel} onValueChange={(value) => updateSettings('audit', 'logLevel', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="error">Error</SelectItem>
                      <SelectItem value="warn">Warning</SelectItem>
                      <SelectItem value="info">Info</SelectItem>
                      <SelectItem value="debug">Debug</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="retainLogs">Retain Logs (days)</Label>
                  <Input
                    id="retainLogs"
                    type="number"
                    value={settings.audit.retainLogs}
                    onChange={(e) => updateSettings('audit', 'retainLogs', parseInt(e.target.value))}
                  />
                </div>
              </div>
              <Separator />
              <div className="space-y-3">
                <div className="flex items-center space-x-2">
                  <Switch
                    id="logUserActions"
                    checked={settings.audit.logUserActions}
                    onCheckedChange={(checked) => updateSettings('audit', 'logUserActions', checked)}
                  />
                  <Label htmlFor="logUserActions">Log User Actions</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Switch
                    id="logSystemEvents"
                    checked={settings.audit.logSystemEvents}
                    onCheckedChange={(checked) => updateSettings('audit', 'logSystemEvents', checked)}
                  />
                  <Label htmlFor="logSystemEvents">Log System Events</Label>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}; 