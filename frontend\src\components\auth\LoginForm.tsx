import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/AuthContext'
import { SignupForm } from './SignupForm'
import { MFAVerification } from './MFAVerification'
import { ROLE_DISPLAY_NAMES } from '@/types/auth'
import { LogIn, Eye, EyeOff, Shield, Sparkles, Lock, Mail, UserPlus } from 'lucide-react'

export function LoginForm() {
  const [showSignup, setShowSignup] = useState(false)
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [error, setError] = useState('')
  const { login, isLoading, mfaRequired, pendingMFAUser, completeMFALogin, cancelMFALogin } = useAuth()

  // Show signup form if requested
  if (showSignup) {
    return <SignupForm onBackToLogin={() => setShowSignup(false)} />
  }

  // Show MFA verification if required
  if (mfaRequired && pendingMFAUser) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4">
        <MFAVerification
          userId={pendingMFAUser.id}
          onSuccess={completeMFALogin}
          onCancel={cancelMFALogin}
        />
      </div>
    )
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setError('')

    try {
      const result = await login(email, password)

      if (result.mfaRequired) {
        // MFA verification will be handled by the MFAVerification component
        return
      }

      // Login successful without MFA
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Login failed')
    }
  }

  const demoUsers = [
    { email: '<EMAIL>', role: 'junior_agent', name: 'Jane Smith', color: 'bg-blue-100 text-blue-700', mfaRequired: false },
    { email: '<EMAIL>', role: 'senior_agent', name: 'John Doe', color: 'bg-purple-100 text-purple-700', mfaRequired: false },
    { email: '<EMAIL>', role: 'admin', name: 'System Administrator', color: 'bg-red-100 text-red-700', mfaRequired: true },
    { email: '<EMAIL>', role: 'contractor', name: 'Mike Wilson', color: 'bg-green-100 text-green-700', mfaRequired: false },
    { email: '<EMAIL>', role: 'compliance', name: 'Sarah Johnson', color: 'bg-orange-100 text-orange-700', mfaRequired: true },
    { email: '<EMAIL>', role: 'finance', name: 'David Brown', color: 'bg-emerald-100 text-emerald-700', mfaRequired: true },
  ]

  const handleDemoLogin = (demoEmail: string) => {
    setEmail(demoEmail)
    setPassword('password')
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-100 p-4 relative overflow-hidden">
      {/* Background decorations */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-600/20 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-indigo-400/20 to-pink-600/20 rounded-full blur-3xl"></div>
      </div>

      <div className="w-full max-w-md space-y-8 relative z-10">
        {/* Header */}
        <div className="text-center">
          <div className="flex items-center justify-center mb-6">
            <div className="p-4 bg-gradient-to-r from-blue-600 to-purple-600 rounded-2xl shadow-lg">
              <Shield className="h-12 w-12 text-white" />
            </div>
          </div>
          <div className="space-y-2">
            <h1 className="text-2xl font-bold tracking-tight text-gray-900">
              AssetHunterPro
            </h1>
            <p className="text-sm text-gray-600 mt-2">
              Professional Asset Recovery Platform
            </p>
          </div>
        </div>

        {/* Login Form */}
        <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-md">
          <CardHeader className="text-center pb-4">
            <CardTitle className="text-2xl font-bold text-gray-900">Welcome Back</CardTitle>
            <CardDescription className="text-gray-600">
              Enter your credentials to access the system
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-2">
                <label htmlFor="email" className="block text-sm font-semibold text-gray-700">
                  Email Address
                </label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    placeholder="Enter your email"
                    className="pl-10 h-12"
                    required
                  />
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="block text-sm font-semibold text-gray-700">
                  Password
                </label>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                  <Input
                    id="password"
                    type={showPassword ? 'text' : 'password'}
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    placeholder="Enter your password"
                    className="pl-10 pr-12 h-12"
                    required
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-1 top-1/2 transform -translate-y-1/2 h-10 w-10 hover:bg-gray-100"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-400" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-400" />
                    )}
                  </Button>
                </div>
              </div>

              {error && (
                <div className="text-sm text-red-600 bg-red-50 border border-red-200 p-4 rounded-lg">
                  {error}
                </div>
              )}

              <div className="space-y-3">
                <Button type="submit" className="w-full h-12 text-base font-semibold" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-3" />
                      Signing in...
                    </>
                  ) : (
                    <>
                      <LogIn className="h-5 w-5 mr-3" />
                      Sign In
                    </>
                  )}
                </Button>
                
                <div className="relative">
                  <div className="absolute inset-0 flex items-center">
                    <span className="w-full border-t border-gray-300" />
                  </div>
                  <div className="relative flex justify-center text-xs uppercase">
                    <span className="bg-white px-2 text-gray-500">Or</span>
                  </div>
                </div>
                
                <Button 
                  type="button" 
                  variant="outline" 
                  className="w-full h-12 text-base font-semibold border-2 border-blue-200 hover:border-blue-300 hover:bg-blue-50"
                  onClick={() => setShowSignup(true)}
                >
                  <UserPlus className="h-5 w-5 mr-3" />
                  Create New Account
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        {/* Demo Users */}
        <Card className="border-0 shadow-2xl bg-white/80 backdrop-blur-md">
          <CardHeader className="pb-4">
            <CardTitle className="text-xl font-bold text-gray-900 flex items-center">
              <Sparkles className="h-5 w-5 mr-2 text-blue-500" />
              Demo Users
            </CardTitle>
            <CardDescription className="text-gray-600">
              Click any user below to auto-fill credentials (password: "password")
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3">
              {demoUsers.map((user) => (
                <Button
                  key={user.email}
                  variant="outline"
                  className="w-full justify-between h-auto p-4 hover:shadow-md transition-all duration-200 border-gray-200 hover:border-blue-300"
                  onClick={() => handleDemoLogin(user.email)}
                >
                  <div className="text-left">
                    <div className="font-semibold text-gray-900">{user.name}</div>
                    <div className="text-sm text-gray-500">{user.email}</div>
                    {user.mfaRequired && (
                      <div className="text-xs text-blue-600 font-medium mt-1 flex items-center">
                        <Shield className="h-3 w-3 mr-1" />
                        MFA Required
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col items-end space-y-1">
                    <Badge variant="outline" className={`${user.color} border-0 font-medium`}>
                      {ROLE_DISPLAY_NAMES[user.role as keyof typeof ROLE_DISPLAY_NAMES]}
                    </Badge>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p className="font-medium">© 2024 AssetHunterPro</p>
          <div className="flex items-center justify-center space-x-4 text-xs">
            <span className="flex items-center">
              <div className="w-2 h-2 bg-green-500 rounded-full mr-1"></div>
              Secure
            </span>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-blue-500 rounded-full mr-1"></div>
              Compliant
            </span>
            <span className="flex items-center">
              <div className="w-2 h-2 bg-purple-500 rounded-full mr-1"></div>
              Efficient
            </span>
          </div>
        </div>
      </div>
    </div>
  )
} 