import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  Shield, 
  Smartphone, 
  Key, 
  CheckCircle, 
  AlertTriangle, 
  Settings,
  Clock,
  Download,
  RefreshCw,
  Plus,
  Trash2
} from 'lucide-react'
import { MFASetup } from '@/components/auth/MFASetup'
import { mfaService, type MFAFactor, type MFASettings as MFASettingsType } from '@/services/mfaService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

export const MFASettings: React.FC = () => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [factors, setFactors] = useState<MFAFactor[]>([])
  const [mfaSettings, setMfaSettings] = useState<MFASettingsType | null>(null)
  const [showSetup, setShowSetup] = useState(false)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (user) {
      mfaService.setUserId(user.id)
      loadMFAData()
    }
  }, [user])

  const loadMFAData = async () => {
    setLoading(true)
    setError(null)
    
    try {
      // Load MFA factors
      const { data: factorsData, error: factorsError } = await mfaService.getMFAFactors()
      if (factorsError) {
        setError('Failed to load MFA factors')
        return
      }
      setFactors(factorsData)

      // Load MFA settings
      const settings = await mfaService.getMFASettings()
      setMfaSettings(settings)
    } catch (error) {
      setError('Failed to load MFA data')
      console.error('Error loading MFA data:', error)
    } finally {
      setLoading(false)
    }
  }

  const removeFactor = async (factorId: string) => {
    setLoading(true)
    
    try {
      const { error } = await mfaService.unenrollMFA(factorId)
      
      if (error) {
        setError(error.message || 'Failed to remove MFA factor')
        return
      }
      
      await loadMFAData()
      
      toast({
        title: "MFA Factor Removed",
        description: "The authentication factor has been removed from your account.",
      })
    } catch (error) {
      setError('Failed to remove MFA factor')
      console.error('Error removing MFA factor:', error)
    } finally {
      setLoading(false)
    }
  }

  const generateNewBackupCodes = async () => {
    setLoading(true)
    
    try {
      const newCodes = await mfaService.generateBackupCodes()
      
      if (mfaSettings) {
        const updatedSettings = {
          ...mfaSettings,
          backup_codes: newCodes
        }
        setMfaSettings(updatedSettings)
      }
      
      toast({
        title: "New Backup Codes Generated",
        description: "Your old backup codes are no longer valid. Save the new ones securely.",
      })
    } catch (error) {
      setError('Failed to generate new backup codes')
      console.error('Error generating backup codes:', error)
    } finally {
      setLoading(false)
    }
  }

  const downloadBackupCodes = () => {
    if (!mfaSettings?.backup_codes) return
    
    const codesText = mfaSettings.backup_codes
      .filter(code => !code.used)
      .map(code => code.code)
      .join('\n')
    
    const blob = new Blob([codesText], { type: 'text/plain' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'assethunterpro-backup-codes.txt'
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    
    toast({
      title: "Backup codes downloaded",
      description: "Save these codes in a secure location.",
    })
  }

  const isMFARequired = user && ['admin', 'compliance', 'finance'].includes(user.role)
  const hasMFAEnabled = factors.length > 0 && factors.some(f => f.status === 'verified')
  const unusedBackupCodes = mfaSettings?.backup_codes?.filter(code => !code.used) || []

  if (showSetup) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold">Multi-Factor Authentication Setup</h2>
            <p className="text-gray-600">Add an extra layer of security to your account</p>
          </div>
          <Button variant="outline" onClick={() => setShowSetup(false)}>
            Back to Settings
          </Button>
        </div>
        
        <MFASetup
          onComplete={() => {
            setShowSetup(false)
            loadMFAData()
          }}
          onCancel={() => setShowSetup(false)}
          required={isMFARequired}
        />
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Multi-Factor Authentication</h2>
          <p className="text-gray-600">Manage your account security settings</p>
        </div>
        {isMFARequired && (
          <Badge variant="destructive" className="flex items-center space-x-1">
            <Shield className="h-3 w-3" />
            <span>Required</span>
          </Badge>
        )}
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="overview" className="space-y-6">
        <TabsList>
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="factors">Authentication Factors</TabsTrigger>
          <TabsTrigger value="backup">Backup Codes</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Shield className="h-5 w-5" />
                <span>Security Status</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${hasMFAEnabled ? 'bg-green-500' : 'bg-red-500'}`} />
                  <div>
                    <div className="font-medium">Multi-Factor Authentication</div>
                    <div className="text-sm text-gray-500">
                      {hasMFAEnabled ? 'Enabled and active' : 'Not configured'}
                    </div>
                  </div>
                </div>
                <Badge variant={hasMFAEnabled ? 'default' : 'destructive'}>
                  {hasMFAEnabled ? 'Enabled' : 'Disabled'}
                </Badge>
              </div>

              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  <Key className="h-5 w-5 text-gray-500" />
                  <div>
                    <div className="font-medium">Backup Codes</div>
                    <div className="text-sm text-gray-500">
                      {unusedBackupCodes.length} unused codes available
                    </div>
                  </div>
                </div>
                <Badge variant={unusedBackupCodes.length > 0 ? 'default' : 'secondary'}>
                  {unusedBackupCodes.length} Available
                </Badge>
              </div>

              {mfaSettings?.last_verified && (
                <div className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Clock className="h-5 w-5 text-gray-500" />
                    <div>
                      <div className="font-medium">Last Verified</div>
                      <div className="text-sm text-gray-500">
                        {new Date(mfaSettings.last_verified).toLocaleString()}
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {!hasMFAEnabled && (
                <Button 
                  onClick={() => setShowSetup(true)}
                  className="w-full"
                  disabled={loading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Set Up Multi-Factor Authentication
                </Button>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="factors" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center space-x-2">
                  <Smartphone className="h-5 w-5" />
                  <span>Authentication Factors</span>
                </span>
                <Button 
                  variant="outline" 
                  size="sm"
                  onClick={() => setShowSetup(true)}
                  disabled={loading}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Factor
                </Button>
              </CardTitle>
              <CardDescription>
                Manage your authentication methods
              </CardDescription>
            </CardHeader>
            <CardContent>
              {factors.length === 0 ? (
                <div className="text-center py-8">
                  <Smartphone className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Authentication Factors</h3>
                  <p className="text-gray-500 mb-4">
                    Set up multi-factor authentication to secure your account
                  </p>
                  <Button onClick={() => setShowSetup(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Your First Factor
                  </Button>
                </div>
              ) : (
                <div className="space-y-3">
                  {factors.map((factor) => (
                    <div key={factor.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <Smartphone className="h-5 w-5 text-gray-500" />
                        <div>
                          <div className="font-medium">{factor.friendly_name}</div>
                          <div className="text-sm text-gray-500">
                            Added {new Date(factor.created_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant={factor.status === 'verified' ? 'default' : 'secondary'}>
                          {factor.status}
                        </Badge>
                        {!isMFARequired && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => removeFactor(factor.id)}
                            disabled={loading}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="backup" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Key className="h-5 w-5" />
                <span>Backup Codes</span>
              </CardTitle>
              <CardDescription>
                Use these codes to access your account if you lose your authentication device
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {unusedBackupCodes.length > 0 ? (
                <>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <div className="grid grid-cols-2 gap-2 text-sm font-mono">
                      {unusedBackupCodes.map((code, index) => (
                        <div key={index} className="text-center py-1">
                          {code.code}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="flex space-x-2">
                    <Button
                      variant="outline"
                      onClick={downloadBackupCodes}
                      className="flex-1"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download Codes
                    </Button>
                    <Button
                      variant="outline"
                      onClick={generateNewBackupCodes}
                      disabled={loading}
                      className="flex-1"
                    >
                      {loading ? (
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                      ) : (
                        <RefreshCw className="mr-2 h-4 w-4" />
                      )}
                      Generate New
                    </Button>
                  </div>
                  
                  <Alert>
                    <AlertTriangle className="h-4 w-4" />
                    <AlertDescription>
                      Each backup code can only be used once. Generate new codes after using any of these.
                    </AlertDescription>
                  </Alert>
                </>
              ) : (
                <div className="text-center py-8">
                  <Key className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Backup Codes</h3>
                  <p className="text-gray-500 mb-4">
                    Generate backup codes to ensure you can always access your account
                  </p>
                  <Button onClick={generateNewBackupCodes} disabled={loading}>
                    {loading ? (
                      <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                    ) : (
                      <Plus className="mr-2 h-4 w-4" />
                    )}
                    Generate Backup Codes
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
