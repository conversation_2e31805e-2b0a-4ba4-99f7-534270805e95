// AssetHunterPro Role-Based Dashboard
// Dynamic dashboard that adapts based on user role and permissions

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  Target, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  BarChart3,
  UserCheck,
  Settings,
  Eye,
  FileText,
  DollarSign,
  MapPin,
  Building,
  Star,
  Zap,
  Shield,
  Crown,
  Activity,
  Phone,
  Mail,
  Upload,
  Award,
  RotateCcw
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { FEATURE_FLAGS } from '@/config/features';
import { supabase } from '@/lib/supabase';
import type { UserRole, RolePermissions } from '@/types/lead-assignment';
import { ROLE_PERMISSIONS } from '@/types/lead-assignment';
import { LeadTrackingService } from '@/services/leadTrackingService';
// Temporarily remove the LeadAssignmentDashboard import to avoid issues
// import { LeadAssignmentDashboard } from '@/components/lead-assignment/LeadAssignmentDashboard';
import { dashboardStateService, DashboardState, DashboardTab } from '@/services/dashboardStateService';
import { leadManagementService } from '@/services/leadManagementService';

interface DashboardMetrics {
  myLeads: {
    total: number;
    inProgress: number;
    contacted: number;
    completed: number;
  };
  teamMetrics?: {
    totalAgents: number;
    totalLeads: number;
    avgSuccessRate: number;
    totalValue: number;
  };
  systemMetrics?: {
    totalUsers: number;
    totalClaims: number;
    totalValue: number;
    systemHealth: number;
  };
}

// Persistent lead assignment storage keys
const STORAGE_KEYS = {
  CHECKED_OUT_LEADS: 'assethunter_checked_out_leads',
  AVAILABLE_LEADS: 'assethunter_available_leads'
};

// Helper functions for persistent storage
const saveToStorage = (key: string, data: any) => {
  try {
    localStorage.setItem(key, JSON.stringify(data));
  } catch (error) {
    console.error('Error saving to localStorage:', error);
  }
};

const loadFromStorage = (key: string, defaultValue: any = null) => {
  try {
    const stored = localStorage.getItem(key);
    return stored ? JSON.parse(stored) : defaultValue;
  } catch (error) {
    console.error('Error loading from localStorage:', error);
    return defaultValue;
  }
};

export const RoleBasedDashboard: React.FC = () => {
  const { user, login, logout } = useAuth();
  const [dashboardState, setDashboardState] = useState<DashboardState | null>(null);
  const [loading, setLoading] = useState(true);
  const [assignmentInProgress, setAssignmentInProgress] = useState(false);
  const [assignmentResults, setAssignmentResults] = useState<string | null>(null);
  const [selectedAgents, setSelectedAgents] = useState<string[]>([]);
  
  // Load persisted assignments from localStorage
  const [checkedOutLeads, setCheckedOutLeads] = useState<Record<string, { agentName: string; checkedOutAt: Date }>>(() => {
    const stored = loadFromStorage(STORAGE_KEYS.CHECKED_OUT_LEADS, {});
    // Convert date strings back to Date objects
    const converted: Record<string, { agentName: string; checkedOutAt: Date }> = {};
    Object.entries(stored).forEach(([leadId, assignment]: [string, any]) => {
      converted[leadId] = {
        agentName: assignment.agentName,
        checkedOutAt: new Date(assignment.checkedOutAt)
      };
    });
    return converted;
  });
  
  const [testingRole, setTestingRole] = useState<UserRole | null>(null);

  // Persist assignments whenever they change
  useEffect(() => {
    const assignmentsToStore: Record<string, { agentName: string; checkedOutAt: string }> = {};
    Object.entries(checkedOutLeads).forEach(([leadId, assignment]) => {
      assignmentsToStore[leadId] = {
        agentName: assignment.agentName,
        checkedOutAt: assignment.checkedOutAt.toISOString()
      };
    });
    saveToStorage(STORAGE_KEYS.CHECKED_OUT_LEADS, assignmentsToStore);
  }, [checkedOutLeads]);

  // Get user role from auth context or testing role override
  const currentUserRole: UserRole = testingRole || (user ? user.role as UserRole : 'admin');
  const currentUserId = user ? user.id : 'admin-user-1';

  // Get permissions for current role
  const permissions = useMemo(() => ROLE_PERMISSIONS[currentUserRole], [currentUserRole]);

  // Demo users for easy switching
  const demoUsers = [
    { email: '<EMAIL>', name: 'System Administrator', role: 'admin' as UserRole, description: 'Full system access' },
    { email: '<EMAIL>', name: 'John Doe', role: 'senior_agent' as UserRole, description: 'Senior field agent' },
    { email: '<EMAIL>', name: 'Jane Smith', role: 'junior_agent' as UserRole, description: 'Junior field agent' },
    { email: '<EMAIL>', name: 'Mike Wilson', role: 'contractor' as UserRole, description: 'Independent contractor' },
    { email: '<EMAIL>', name: 'Sarah Johnson', role: 'compliance' as UserRole, description: 'Compliance officer' },
    { email: '<EMAIL>', name: 'David Brown', role: 'finance' as UserRole, description: 'Finance manager' }
  ];

  // Handle switching to a different user account
  const handleSwitchUser = async (email: string) => {
    try {
      await login(email, 'password');
      // Reset testing role when switching users
      setTestingRole(null);
      setAssignmentResults(`✅ Switched to user: ${email}`);
      setTimeout(() => setAssignmentResults(null), 3000);
    } catch (error) {
      setAssignmentResults(`❌ Failed to switch user: ${error}`);
      setTimeout(() => setAssignmentResults(null), 5000);
    }
  };

  // Available roles for testing (role override while keeping same user)
  const availableRoles: { role: UserRole; name: string; description: string }[] = [
    { role: 'admin', name: 'Admin', description: 'Full system access' },
    { role: 'diamond', name: 'Diamond Agent', description: 'Premium tier agent' },
    { role: 'ruby', name: 'Ruby Agent', description: 'Advanced agent' },
    { role: 'topaz', name: 'Topaz Agent', description: 'Team lead agent' },
    { role: 'gold', name: 'Gold Agent', description: 'National agent' },
    { role: 'silver', name: 'Silver Agent', description: 'Multi-state agent' },
    { role: 'bronze', name: 'Bronze Agent', description: 'Starter agent' },
    { role: 'senior_agent', name: 'Senior Agent', description: 'Experienced field agent' },
    { role: 'junior_agent', name: 'Junior Agent', description: 'Entry-level agent' }
  ];

  // Get agent name for role (for testing)
  const getAgentNameForRole = (role: UserRole): string => {
    // If we have a real user, use their name
    if (user && !testingRole) {
      return user.name;
    }
    
    // Map roles to demo user names
    const roleToAgentMap: Record<UserRole, string> = {
      admin: 'System Administrator',
      diamond: 'Emma Thompson',
      ruby: 'Sarah Johnson', 
      topaz: 'Lisa Chen',
      gold: 'Mike Rodriguez',
      silver: 'David Wilson',
      bronze: 'John Smith',
      senior_agent: 'John Doe',
      junior_agent: 'Jane Smith',
      contractor: 'Mike Wilson',
      compliance: 'Sarah Johnson',
      finance: 'David Brown'
    };
    return roleToAgentMap[role] || 'Unknown Agent';
  };

  // Get current user's display name
  const getCurrentUserName = (): string => {
    if (testingRole) {
      return getAgentNameForRole(testingRole);
    }
    return user?.name || getAgentNameForRole(currentUserRole);
  };

  // Get my assigned leads for the current user
  const getMyAssignedLeads = () => {
    const currentAgentName = getCurrentUserName();
    
    if (currentUserRole === 'admin') {
      // Admin sees all assigned leads
      return Object.entries(checkedOutLeads).map(([leadId, assignment]) => ({
        id: leadId,
        assignedAgent: assignment.agentName,
        checkedOutAt: assignment.checkedOutAt,
        status: 'in_progress',
        priority: 'High',
        value: Math.floor(Math.random() * 75000) + 25000, // $25K - $100K
        state: ['CA', 'TX', 'FL', 'NY', 'WA', 'OR', 'NV', 'AZ'][Math.floor(Math.random() * 8)],
        type: ['Insurance Claim', 'Property Recovery', 'Asset Recovery', 'Unclaimed Funds'][Math.floor(Math.random() * 4)],
        claimant: `Claimant ${leadId.split('-')[1]}`,
        description: `Asset recovery case for ${['$50K+ insurance claim', 'unclaimed property', 'estate settlement', 'business asset'][Math.floor(Math.random() * 4)]}`
      }));
    } else {
      // Individual agents see only their assigned leads
      const myAssignments = Object.entries(checkedOutLeads)
        .filter(([, assignment]) => assignment.agentName === currentAgentName);
      
      return myAssignments.map(([leadId, assignment]) => ({
        id: leadId,
        assignedAgent: assignment.agentName,
        checkedOutAt: assignment.checkedOutAt,
        status: Math.random() > 0.7 ? 'contacted' : 'in_progress',
        priority: Math.random() > 0.5 ? 'High' : 'Medium',
        value: Math.floor(Math.random() * 75000) + 25000, // $25K - $100K
        state: ['CA', 'TX', 'FL', 'NY', 'WA', 'OR', 'NV', 'AZ'][Math.floor(Math.random() * 8)],
        type: ['Insurance Claim', 'Property Recovery', 'Asset Recovery', 'Unclaimed Funds'][Math.floor(Math.random() * 4)],
        claimant: `Claimant ${leadId.split('-')[1]}`,
        description: `Asset recovery case for ${['$50K+ insurance claim', 'unclaimed property', 'estate settlement', 'business asset'][Math.floor(Math.random() * 4)]}`
      }));
    }
  };

  // Subscribe to dashboard state changes
  useEffect(() => {
    const unsubscribe = dashboardStateService.subscribe((state: DashboardState) => {
      setDashboardState(state);
      setLoading(false);
    });

    // Load initial data and restore persisted uploads count
    // For production testing: Admin should have access to a large pool representing all user uploads
    const defaultLeadsForTesting = currentUserRole === 'admin' ? 47500 : 1500;
    const persistedUploadsCount = loadFromStorage(STORAGE_KEYS.AVAILABLE_LEADS, defaultLeadsForTesting);
    dashboardStateService.loadInitialData(currentUserId, currentUserRole);
    dashboardStateService.setTotalUploadedLeads(persistedUploadsCount);

    return unsubscribe;
  }, [currentUserId, currentUserRole]);

  // Persist uploaded leads count whenever it changes
  useEffect(() => {
    if (dashboardState?.totalUploadedLeads !== undefined) {
      saveToStorage(STORAGE_KEYS.AVAILABLE_LEADS, dashboardState.totalUploadedLeads);
    }
  }, [dashboardState?.totalUploadedLeads]);

  // Handle tab change
  const handleTabChange = (value: string) => {
    dashboardStateService.setActiveTab(value as DashboardTab);
  };

  // Handle assign leads button click
  const handleAssignLeadsClick = () => {
    dashboardStateService.navigateToAssignment();
  };

  // Handle view leads button click
  const handleViewLeadsClick = () => {
    dashboardStateService.navigateToLeads();
  };

  // Handle agent selection
  const handleAgentSelection = (agentName: string, isSelected: boolean) => {
    setSelectedAgents(prev => 
      isSelected 
        ? [...prev, agentName]
        : prev.filter(name => name !== agentName)
    );
  };

  // Handle bulk assignment process
  const handleStartAssignmentProcess = async () => {
    if (selectedAgents.length === 0) {
      setAssignmentResults('❌ Please select at least one agent to assign leads to.');
      setTimeout(() => setAssignmentResults(null), 5000);
      return;
    }

    setAssignmentInProgress(true);
    setAssignmentResults(null);

    try {
      // Get form values
      const assignmentMethodSelect = document.querySelector('select[data-assignment-method]') as HTMLSelectElement;
      const numberOfLeadsInput = document.querySelector('input[data-number-of-leads]') as HTMLInputElement;
      const priorityLevelSelect = document.querySelector('select[data-priority-level]') as HTMLSelectElement;

      const assignmentMethod = assignmentMethodSelect?.value || 'Workload Based Assignment';
      const numberOfLeads = parseInt(numberOfLeadsInput?.value || '25');
      const priorityLevel = priorityLevelSelect?.value || 'High Priority ($50K+)';

      // Simulate assignment process
      await new Promise(resolve => setTimeout(resolve, 2000)); // 2 second delay

      // Distribute leads among selected agents
      const totalLeads = Math.min(numberOfLeads, dashboardState?.totalUploadedLeads || 0);
      const leadsPerAgent = Math.floor(totalLeads / selectedAgents.length);
      const remainderLeads = totalLeads % selectedAgents.length;

      // Create assignment distribution
      const assignments = selectedAgents.map((agentName, index) => {
        const baseLeads = leadsPerAgent;
        const extraLead = index < remainderLeads ? 1 : 0;
        const totalForAgent = baseLeads + extraLead;
        return { agentName, leadCount: totalForAgent };
      });

      // Generate unique lead IDs and assign them
      const newCheckedOutLeads = { ...checkedOutLeads };
      let leadIdCounter = Object.keys(checkedOutLeads).length + 1;

      assignments.forEach(assignment => {
        for (let i = 0; i < assignment.leadCount; i++) {
          const leadId = `lead-${leadIdCounter++}`;
          newCheckedOutLeads[leadId] = {
            agentName: assignment.agentName,
            checkedOutAt: new Date()
          };
        }
      });

      setCheckedOutLeads(newCheckedOutLeads);

      // Update the assignment statistics
      const newTotalUploaded = (dashboardState?.totalUploadedLeads || 0) - totalLeads;
      dashboardStateService.setTotalUploadedLeads(newTotalUploaded);
      
      // Create detailed success message
      const assignmentDetails = assignments
        .filter(a => a.leadCount > 0)
        .map(a => `${a.agentName}: ${a.leadCount} leads`)
        .join(', ');
      
      setAssignmentResults(
        `✅ Successfully assigned ${totalLeads} leads using ${assignmentMethod}. 
        📊 Distribution: ${assignmentDetails}. 
        🎯 Priority: ${priorityLevel}. 
        📈 Remaining available: ${newTotalUploaded.toLocaleString()} leads.
        💡 Switch to "My Leads" tab or use role switcher to see individual agent views!`
      );

      // Clear selected agents after successful assignment
      setSelectedAgents([]);

      // Auto-clear success message after 15 seconds
      setTimeout(() => {
        setAssignmentResults(null);
      }, 15000);

    } catch (error) {
      console.error('Assignment failed:', error);
      setAssignmentResults('❌ Assignment failed. Please try again.');
    } finally {
      setAssignmentInProgress(false);
    }
  };

  // Handle checking leads back in
  const handleCheckInLeads = (agentName: string) => {
    const agentLeads = Object.entries(checkedOutLeads).filter(
      ([, assignment]) => assignment.agentName === agentName
    );

    if (agentLeads.length === 0) return;

    // Remove agent's leads from checked out list
    const newCheckedOutLeads = { ...checkedOutLeads };
    agentLeads.forEach(([leadId]) => {
      delete newCheckedOutLeads[leadId];
    });

    setCheckedOutLeads(newCheckedOutLeads);

    // Return leads to available pool
    const returnedLeadsCount = agentLeads.length;
    const newTotalUploaded = (dashboardState?.totalUploadedLeads || 0) + returnedLeadsCount;
    dashboardStateService.setTotalUploadedLeads(newTotalUploaded);

    setAssignmentResults(
      `✅ Checked in ${returnedLeadsCount} leads from ${agentName}. Leads returned to available pool.`
    );

    setTimeout(() => setAssignmentResults(null), 8000);
  };

  // Clear all assignments (for testing)
  const handleClearAllAssignments = () => {
    const totalAssigned = Object.keys(checkedOutLeads).length;
    if (totalAssigned === 0) {
      setAssignmentResults('ℹ️ No leads are currently assigned.');
      setTimeout(() => setAssignmentResults(null), 3000);
      return;
    }

    // Return all leads to available pool
    const newTotalUploaded = (dashboardState?.totalUploadedLeads || 0) + totalAssigned;
    dashboardStateService.setTotalUploadedLeads(newTotalUploaded);
    
    // Clear all assignments
    setCheckedOutLeads({});
    
    setAssignmentResults(
      `✅ Cleared all assignments! ${totalAssigned} leads returned to available pool. Total available: ${newTotalUploaded.toLocaleString()}`
    );

    setTimeout(() => setAssignmentResults(null), 8000);
  };

  // Get agent's assigned lead count
  const getAgentLeadCount = (agentName: string) => {
    return Object.values(checkedOutLeads).filter(
      assignment => assignment.agentName === agentName
    ).length;
  };

  if (loading || !dashboardState) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Loading dashboard...</p>
        </div>
      </div>
    );
  }

  const { metrics, activeSessions, totalUploadedLeads, activeTab } = dashboardState;

  const getRoleBasedLeadCount = (role: UserRole): number => {
    const baseCounts: Record<UserRole, number> = {
      bronze: 5,
      silver: 8,
      gold: 12,
      topaz: 18,
      ruby: 25,
      diamond: 35,
      admin: 50,
      junior_agent: 5,
      senior_agent: 25,
      contractor: 5,
      compliance: 0,
      finance: 0
    };
    return baseCounts[role] || 5;
  };

  const getRoleIcon = (role: UserRole) => {
    const icons: Record<UserRole, any> = {
      bronze: Star,
      silver: Award,
      gold: Crown,
      topaz: Shield,
      ruby: Zap,
      diamond: Crown,
      admin: Settings,
      junior_agent: Star,
      senior_agent: Award,
      contractor: Building,
      compliance: Shield,
      finance: DollarSign
    };
    return icons[role] || Star;
  };

  const getRoleColor = (role: UserRole) => {
    const colors: Record<UserRole, string> = {
      bronze: 'text-orange-600',
      silver: 'text-gray-600',
      gold: 'text-yellow-600',
      topaz: 'text-blue-600',
      ruby: 'text-red-600',
      diamond: 'text-purple-600',
      admin: 'text-indigo-600',
      junior_agent: 'text-green-600',
      senior_agent: 'text-blue-600',
      contractor: 'text-gray-600',
      compliance: 'text-purple-600',
      finance: 'text-green-600'
    };
    return colors[role] || 'text-gray-600';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const RoleIcon = getRoleIcon(currentUserRole);

  return (
    <div className="space-y-8">
      {/* Testing Role Switcher - For Development Only */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="p-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-3">
              <RotateCcw className="h-5 w-5 text-yellow-600" />
              <div>
                <h3 className="font-medium text-yellow-800">Production Testing Mode</h3>
                <p className="text-sm text-yellow-700">Test all subscription tiers and lead assignment capabilities</p>
              </div>
            </div>
            
            {/* Current User Info */}
            <div className="bg-white p-3 rounded-lg border">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600">Currently logged in as:</p>
                  <p className="font-medium text-gray-900">{user?.name || 'Unknown'} ({user?.email || 'No email'})</p>
                  <p className="text-sm text-blue-600">
                    Role: {testingRole ? `${testingRole} (Override)` : (user?.role || 'No role')}
                  </p>
                  <p className="text-xs text-green-600">
                    Lead Capacity: {getRoleBasedLeadCount(currentUserRole)} leads | 
                    Available to Assign: {totalUploadedLeads.toLocaleString()} leads
                  </p>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={logout}
                  className="border-red-300 text-red-700 hover:bg-red-50"
                >
                  Logout
                </Button>
              </div>
            </div>

            {/* Subscription Tier Testing */}
            <div className="space-y-3">
              <h4 className="font-medium text-yellow-800">🎯 Subscription Tier Testing</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {[
                  { 
                    tier: 'bronze', 
                    name: 'Bronze Starter', 
                    leads: 5, 
                    price: '$99/mo',
                    features: ['Basic lead access', 'Email support', 'State-level coverage'],
                    canAssign: false,
                    canViewTeam: false
                  },
                  { 
                    tier: 'silver', 
                    name: 'Silver Multi-State', 
                    leads: 8, 
                    price: '$199/mo',
                    features: ['Multi-state access', 'Priority support', 'Advanced filters'],
                    canAssign: false,
                    canViewTeam: false
                  },
                  { 
                    tier: 'gold', 
                    name: 'Gold National', 
                    leads: 12, 
                    price: '$399/mo',
                    features: ['National coverage', 'API access', 'Custom reports'],
                    canAssign: false,
                    canViewTeam: false
                  },
                  { 
                    tier: 'topaz', 
                    name: 'Topaz Team Lead', 
                    leads: 18, 
                    price: '$699/mo',
                    features: ['Team management', 'Lead assignment', 'Performance analytics'],
                    canAssign: true,
                    canViewTeam: true
                  },
                  { 
                    tier: 'ruby', 
                    name: 'Ruby Advanced', 
                    leads: 25, 
                    price: '$999/mo',
                    features: ['Advanced assignment', 'Bulk operations', 'White-label'],
                    canAssign: true,
                    canViewTeam: true
                  },
                  { 
                    tier: 'diamond', 
                    name: 'Diamond Premium', 
                    leads: 35, 
                    price: '$1499/mo',
                    features: ['Unlimited features', 'Priority support', 'Custom integrations'],
                    canAssign: true,
                    canViewTeam: true
                  },
                  { 
                    tier: 'admin', 
                    name: 'System Admin', 
                    leads: 50, 
                    price: 'Full Access',
                    features: ['Full system control', 'User management', 'System analytics'],
                    canAssign: true,
                    canViewTeam: true
                  }
                ].map((plan) => (
                  <div 
                    key={plan.tier} 
                    className={`p-3 border rounded-lg cursor-pointer transition-all ${
                      currentUserRole === plan.tier 
                        ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-200' 
                        : 'border-gray-200 bg-white hover:border-blue-300'
                    }`}
                    onClick={() => setTestingRole(plan.tier as UserRole)}
                  >
                    <div className="text-center">
                      <h5 className="font-medium text-sm">{plan.name}</h5>
                      <p className="text-xs text-gray-600">{plan.price}</p>
                      <p className="text-lg font-bold text-blue-600">{plan.leads} Leads</p>
                      
                      <div className="mt-2 space-y-1">
                        {plan.canAssign && (
                          <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                            ✓ Can Assign
                          </Badge>
                        )}
                        {plan.canViewTeam && (
                          <Badge variant="secondary" className="text-xs bg-purple-100 text-purple-800">
                            ✓ Team View
                          </Badge>
                        )}
                      </div>
                      
                      <div className="mt-2 text-xs text-gray-500">
                        {plan.features.slice(0, 2).map((feature, i) => (
                          <p key={i}>• {feature}</p>
                        ))}
                      </div>
                      
                      {currentUserRole === plan.tier && (
                        <Badge variant="default" className="mt-2 text-xs bg-blue-600">
                          ACTIVE
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
              
              <div className="text-xs text-yellow-700 bg-yellow-100 p-2 rounded">
                💡 <strong>Testing Instructions:</strong> Click any tier above to test its capabilities. 
                Higher tiers (Topaz+) can assign leads and view team performance. 
                Admin has access to {totalUploadedLeads.toLocaleString()} leads representing real production volume.
              </div>
            </div>

            {/* User Switcher */}
            <div className="space-y-2 border-t pt-3">
              <label className="text-sm font-medium text-yellow-800">Quick User Switch (Alternative Testing):</label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                {demoUsers.map((demoUser) => (
                  <Button
                    key={demoUser.email}
                    size="sm"
                    variant={user?.email === demoUser.email ? "default" : "outline"}
                    onClick={() => handleSwitchUser(demoUser.email)}
                    className={`text-xs ${user?.email === demoUser.email ? 'bg-blue-600' : 'border-yellow-300 text-yellow-700 hover:bg-yellow-100'}`}
                    disabled={user?.email === demoUser.email}
                  >
                    {demoUser.name}
                    <br />
                    <span className="text-xs opacity-75">({demoUser.role})</span>
                  </Button>
                ))}
              </div>
            </div>

            {/* Reset Testing Data */}
            <div className="border-t pt-3">
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    // Reset to default testing values
                    const newDefault = currentUserRole === 'admin' ? 47500 : 1500;
                    dashboardStateService.setTotalUploadedLeads(newDefault);
                    setCheckedOutLeads({});
                    setAssignmentResults('✅ Reset testing data to defaults for current role.');
                    setTimeout(() => setAssignmentResults(null), 3000);
                  }}
                  className="border-orange-300 text-orange-700 hover:bg-orange-50"
                >
                  <RotateCcw className="h-4 w-4 mr-1" />
                  Reset Test Data
                </Button>
                
                {testingRole && (
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => setTestingRole(null)}
                    className="border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    Reset to Auth Role
                  </Button>
                )}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Header Section */}
      <div className="bg-gradient-to-r from-blue-600 to-purple-600 text-white p-6 rounded-lg">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className={`p-3 bg-white bg-opacity-20 rounded-lg`}>
              <RoleIcon className="h-8 w-8" />
            </div>
            <div>
              <h1 className="text-2xl font-bold capitalize">
                {getCurrentUserName()} - {currentUserRole.toUpperCase()} Dashboard
                {testingRole && <span className="text-yellow-200 ml-2">(Testing Mode)</span>}
              </h1>
              <p className="text-blue-100">
                {currentUserRole === 'admin' 
                  ? `System Administrator • Full Access • ${totalUploadedLeads.toLocaleString()} leads available`
                  : `${currentUserRole.charAt(0).toUpperCase() + currentUserRole.slice(1)} Tier • Individual Agent`
                }
                {testingRole && <span className="text-yellow-200"> • Testing as {getAgentNameForRole(testingRole)}</span>}
              </p>
            </div>
          </div>
          <div className="text-right">
            <div className="space-y-1">
              <p className="text-blue-100">Personal Lead Capacity</p>
              <p className="text-2xl font-bold">
                {getRoleBasedLeadCount(currentUserRole)} leads
              </p>
              <p className="text-sm text-blue-200">
                {currentUserRole === 'admin' 
                  ? `Can assign ${totalUploadedLeads.toLocaleString()} to agents`
                  : `Personal work capacity limit`
                }
              </p>
              
              {/* Tier Capabilities */}
              <div className="flex flex-wrap gap-1 mt-2">
                {permissions?.canAssignLeads && (
                  <Badge variant="secondary" className="bg-green-500 text-white text-xs">
                    Lead Assignment
                  </Badge>
                )}
                {permissions?.canViewTeamPerformance && (
                  <Badge variant="secondary" className="bg-purple-500 text-white text-xs">
                    Team Management
                  </Badge>
                )}
                {permissions?.canManageUsers && (
                  <Badge variant="secondary" className="bg-red-500 text-white text-xs">
                    User Management
                  </Badge>
                )}
                {!permissions?.canAssignLeads && (
                  <Badge variant="secondary" className="bg-gray-500 text-white text-xs">
                    Individual Agent
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </div>
        
        {/* Production Testing Notice */}
        {currentUserRole === 'admin' && (
          <div className="mt-4 p-3 bg-white bg-opacity-10 rounded-lg">
            <p className="text-sm text-yellow-200">
              🧪 <strong>Production Testing Mode:</strong> You have access to {totalUploadedLeads.toLocaleString()} leads representing 
              real production volume from all user uploads. Test assignment capabilities across all subscription tiers.
            </p>
          </div>
        )}
      </div>

      {/* Navigation Tabs */}
      <Tabs value={activeTab} onValueChange={handleTabChange} className="space-y-6">
        <TabsList className="grid w-full grid-cols-auto">
          <TabsTrigger value="overview">My Overview</TabsTrigger>
          <TabsTrigger value="leads">My Leads</TabsTrigger>
          {permissions?.canAssignLeads && (
            <TabsTrigger value="assignment" className="flex items-center gap-2">
              <UserCheck className="h-4 w-4" />
              Lead Assignment
              {totalUploadedLeads > 0 && (
                <Badge variant="secondary" className="ml-1">
                  {totalUploadedLeads.toLocaleString()}
                </Badge>
              )}
            </TabsTrigger>
          )}
          {permissions?.canViewTeamPerformance && (
            <TabsTrigger value="team">Team Performance</TabsTrigger>
          )}
          {permissions?.canManageUsers && (
            <TabsTrigger value="admin">System Admin</TabsTrigger>
          )}
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="space-y-6">
          {/* Tier Capabilities Notice */}
          <Card className={`border-l-4 ${
            permissions?.canAssignLeads 
              ? 'border-l-green-500 bg-green-50' 
              : 'border-l-blue-500 bg-blue-50'
          }`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {permissions?.canAssignLeads ? (
                    <UserCheck className="h-8 w-8 text-green-600" />
                  ) : (
                    <Target className="h-8 w-8 text-blue-600" />
                  )}
                  <div>
                    <h3 className="font-semibold text-gray-900">
                      {permissions?.canAssignLeads 
                        ? `🎯 Management Tier Active (${currentUserRole.toUpperCase()})`
                        : `👤 Individual Agent Tier (${currentUserRole.toUpperCase()})`
                      }
                    </h3>
                    <p className="text-sm text-gray-600">
                      {permissions?.canAssignLeads 
                        ? `You can assign leads from the pool of ${totalUploadedLeads.toLocaleString()} available leads and manage team performance`
                        : `You can work on up to ${getRoleBasedLeadCount(currentUserRole)} leads personally. Contact admin for lead assignments.`
                      }
                    </p>
                    
                    {/* Show tier pricing for testing */}
                    <div className="mt-2 flex items-center gap-3 text-xs text-gray-500">
                      <span>💰 Tier Pricing: {
                        currentUserRole === 'bronze' ? '$99/mo' :
                        currentUserRole === 'silver' ? '$199/mo' :
                        currentUserRole === 'gold' ? '$399/mo' :
                        currentUserRole === 'topaz' ? '$699/mo' :
                        currentUserRole === 'ruby' ? '$999/mo' :
                        currentUserRole === 'diamond' ? '$1499/mo' :
                        'Admin Access'
                      }</span>
                      <span>📊 Lead Capacity: {getRoleBasedLeadCount(currentUserRole)} leads</span>
                      <span>🔧 Capabilities: {
                        permissions?.canAssignLeads ? 'Management' : 'Individual'
                      }</span>
                    </div>
                  </div>
                </div>
                
                {permissions?.canAssignLeads && totalUploadedLeads > 0 && (
                  <Button 
                    onClick={handleAssignLeadsClick}
                    className="bg-green-600 hover:bg-green-700"
                  >
                    <UserCheck className="h-4 w-4 mr-2" />
                    Assign Leads Now
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>

          {/* My Leads Metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Target className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">My Total Leads</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.myLeads.total}</p>
                    <p className="text-xs text-gray-500">Capacity: {getRoleBasedLeadCount(currentUserRole)}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">In Progress</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.myLeads.inProgress}</p>
                    <p className="text-xs text-gray-500">Active work</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Contacted</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.myLeads.contacted}</p>
                    <p className="text-xs text-gray-500">Reached out</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Star className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-gray-900">{metrics.myLeads.completed}</p>
                    <p className="text-xs text-gray-500">Success rate: {metrics.myLeads.total > 0 ? Math.round((metrics.myLeads.completed / metrics.myLeads.total) * 100) : 0}%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Role-Specific Features */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* My Active Sessions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-600" />
                  Active Work Sessions
                </CardTitle>
              </CardHeader>
              <CardContent>
                {activeSessions.length > 0 ? (
                  <div className="space-y-3">
                    {activeSessions.map((session) => (
                      <div key={session.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <p className="font-medium">Lead {session.leadId}</p>
                          <p className="text-sm text-gray-600">
                            Started {Math.floor((new Date().getTime() - session.checkedOutAt.getTime()) / (1000 * 60))} min ago
                          </p>
                        </div>
                        <Badge className="bg-green-100 text-green-800">Active</Badge>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600">No active work sessions</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Performance Overview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2 text-green-600" />
                  My Performance
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Completion Rate</span>
                    <span className="text-sm text-gray-600">
                      {metrics.myLeads.total > 0 ? Math.round((metrics.myLeads.completed / metrics.myLeads.total) * 100) : 0}%
                    </span>
                  </div>
                  <Progress value={metrics.myLeads.total > 0 ? (metrics.myLeads.completed / metrics.myLeads.total) * 100 : 0} />
                </div>

                <div>
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium">Contact Rate</span>
                    <span className="text-sm text-gray-600">
                      {metrics.myLeads.total > 0 ? Math.round(((metrics.myLeads.contacted + metrics.myLeads.completed) / metrics.myLeads.total) * 100) : 0}%
                    </span>
                  </div>
                  <Progress value={metrics.myLeads.total > 0 ? ((metrics.myLeads.contacted + metrics.myLeads.completed) / metrics.myLeads.total) * 100 : 0} />
                </div>

                {permissions.canViewAdvancedAnalytics && (
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Avg Processing Time</span>
                      <span className="text-sm text-gray-600">2.3 days</span>
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* My Leads Tab */}
        <TabsContent value="leads" className="space-y-6">
          {/* Debug Information Card */}
          <Card className="bg-gray-50 border-gray-200">
            <CardContent className="p-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                <div>
                  <span className="font-medium text-gray-700">Current Context:</span>
                  <p className="text-gray-600">User: {getCurrentUserName()}</p>
                  <p className="text-gray-600">Role: {currentUserRole} {testingRole && '(Override)'}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Assignment Stats:</span>
                  <p className="text-gray-600">Total Assigned: {Object.keys(checkedOutLeads).length}</p>
                  <p className="text-gray-600">My Leads: {getMyAssignedLeads().length}</p>
                </div>
                <div>
                  <span className="font-medium text-gray-700">Lead Pool:</span>
                  <p className="text-gray-600">Available: {totalUploadedLeads.toLocaleString()}</p>
                  <p className="text-gray-600">Checked Out: {Object.keys(checkedOutLeads).length}</p>
                </div>
              </div>
              
              {/* Show which agents have assignments */}
              {Object.keys(checkedOutLeads).length > 0 && (
                <div className="mt-3 pt-3 border-t">
                  <span className="font-medium text-gray-700 text-sm">Agents with Assignments:</span>
                  <div className="mt-1 flex flex-wrap gap-2">
                    {Object.entries(
                      Object.values(checkedOutLeads).reduce((acc, assignment) => {
                        acc[assignment.agentName] = (acc[assignment.agentName] || 0) + 1;
                        return acc;
                      }, {} as Record<string, number>)
                    ).map(([agentName, count]) => (
                      <Badge key={agentName} variant="outline" className="text-xs">
                        {agentName}: {count} leads
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
          
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span>My Assigned Leads</span>
                <Badge variant="secondary">{getMyAssignedLeads().length} Active</Badge>
              </CardTitle>
              <p className="text-gray-600">
                {currentUserRole === 'admin' 
                  ? 'All leads currently assigned to agents (Admin View)' 
                  : `Leads currently assigned to ${getCurrentUserName()}`
                }
              </p>
            </CardHeader>
            <CardContent>
              {getMyAssignedLeads().length > 0 ? (
                <div className="space-y-4">
                  {getMyAssignedLeads().map((lead) => (
                    <div key={lead.id} className="p-4 border rounded-lg hover:shadow-md transition-shadow">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-3">
                          <div className={`w-3 h-3 rounded-full ${lead.status === 'contacted' ? 'bg-green-500' : 'bg-blue-500'}`}></div>
                          <h4 className="font-medium">Lead {lead.id}</h4>
                          <Badge variant="outline" className="text-xs">
                            {lead.type}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="default" className={`${lead.priority === 'High' ? 'bg-red-600' : 'bg-yellow-600'}`}>
                            {lead.priority} Priority
                          </Badge>
                          <Badge variant="secondary">
                            ${lead.value.toLocaleString()}
                          </Badge>
                        </div>
                      </div>
                      
                      <div className="mb-3">
                        <p className="text-sm text-gray-700 font-medium">{lead.claimant}</p>
                        <p className="text-sm text-gray-600">{lead.description}</p>
                      </div>
                      
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                        <div>
                          <span className="text-gray-600">Assigned To:</span>
                          <p className="font-medium">{lead.assignedAgent}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">State:</span>
                          <p className="font-medium">{lead.state}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Status:</span>
                          <p className="font-medium capitalize text-blue-600">{lead.status.replace('_', ' ')}</p>
                        </div>
                        <div>
                          <span className="text-gray-600">Assigned:</span>
                          <p className="font-medium">{lead.checkedOutAt.toLocaleDateString()} {lead.checkedOutAt.toLocaleTimeString()}</p>
                        </div>
                      </div>
                      
                      <div className="flex gap-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3 mr-1" />
                          View Details
                        </Button>
                        <Button size="sm" variant="outline">
                          <Phone className="h-3 w-3 mr-1" />
                          Contact
                        </Button>
                        <Button size="sm" variant="outline">
                          <FileText className="h-3 w-3 mr-1" />
                          Update Status
                        </Button>
                        {(currentUserRole === 'admin' || lead.assignedAgent === getCurrentUserName()) && (
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => handleCheckInLeads(lead.assignedAgent)}
                            className="ml-auto border-red-300 text-red-700 hover:bg-red-50"
                          >
                            <RotateCcw className="h-3 w-3 mr-1" />
                            Check In
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                  
                  <div className="text-center py-4 border-t">
                    <p className="text-sm text-gray-600 mb-3">
                      Total: {getMyAssignedLeads().length} leads assigned
                      {currentUserRole !== 'admin' && ` to ${getAgentNameForRole(currentUserRole)}`}
                    </p>
                    <div className="flex justify-center gap-2">
                      <Button variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        Export Lead List
                      </Button>
                      <Button variant="outline">
                        <BarChart3 className="h-4 w-4 mr-2" />
                        View Analytics
                      </Button>
                      {currentUserRole === 'admin' && getMyAssignedLeads().length > 0 && (
                        <Button 
                          variant="outline"
                          onClick={() => {
                            // Check in all leads for testing
                            Object.values(checkedOutLeads).forEach(assignment => {
                              handleCheckInLeads(assignment.agentName);
                            });
                          }}
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Check In All Leads
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Assigned Leads</h3>
                  <p className="text-gray-600 mb-4">
                    {currentUserRole === 'admin' 
                      ? 'No leads are currently assigned to any agents. Use the Lead Assignment tab to assign leads.'
                      : `No leads are currently assigned to ${getCurrentUserName()}. Contact your admin or check back later.`
                    }
                  </p>
                  <div className="space-y-2 text-sm text-gray-500">
                    <p>💡 <strong>Testing tip:</strong> Use the role switcher above to test different user perspectives</p>
                    <p>📊 <strong>Current status:</strong> {Object.keys(checkedOutLeads).length.toLocaleString()} total leads assigned across all agents</p>
                    <p>📈 <strong>Available leads:</strong> {totalUploadedLeads.toLocaleString()} leads ready for assignment</p>
                  </div>
                  {currentUserRole === 'admin' && totalUploadedLeads > 0 && (
                    <Button onClick={handleAssignLeadsClick} className="mt-4">
                      <UserCheck className="h-4 w-4 mr-2" />
                      Go to Assignment Tab
                    </Button>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Lead Assignment Tab */}
        {permissions?.canAssignLeads && (
          <TabsContent value="assignment" className="space-y-6">
            <Card className="border-l-4 border-l-blue-500 bg-blue-50">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <UserCheck className="h-8 w-8 text-blue-600" />
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">
                        ✅ Lead Assignment Interface Working!
                      </h3>
                      <p className="text-sm text-gray-600">
                        Button navigation successful! You have {totalUploadedLeads.toLocaleString()} uploaded leads ready for assignment.
                      </p>
                    </div>
                  </div>
                  <Badge variant="secondary" className="text-lg px-3 py-1">
                    {totalUploadedLeads.toLocaleString()} Available
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Assignment Controls */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2 text-green-600" />
                    Quick Assignment
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <label className="text-sm font-medium text-gray-700">Assignment Method</label>
                    <select 
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                      data-assignment-method
                      disabled={assignmentInProgress}
                    >
                      <option>Round Robin Distribution</option>
                      <option selected>Workload Based Assignment</option>
                      <option>Expertise Based Matching</option>
                      <option>Geographic Priority</option>
                    </select>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Number of Leads to Assign</label>
                    <input 
                      type="number" 
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500" 
                      placeholder="Enter number of leads"
                      defaultValue="25"
                      min="1"
                      max="1000"
                      data-number-of-leads
                      disabled={assignmentInProgress}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium text-gray-700">Priority Level</label>
                    <select 
                      className="w-full mt-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500"
                      data-priority-level
                      disabled={assignmentInProgress}
                    >
                      <option selected>High Priority ($50K+)</option>
                      <option>Medium Priority ($10K-$50K)</option>
                      <option>Low Priority (&lt;$10K)</option>
                      <option>All Priorities</option>
                    </select>
                  </div>
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={handleStartAssignmentProcess}
                    disabled={assignmentInProgress || (dashboardState?.totalUploadedLeads || 0) === 0 || selectedAgents.length === 0}
                  >
                    {assignmentInProgress ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Processing Assignment...
                      </>
                    ) : (
                      <>
                        <UserCheck className="h-4 w-4 mr-2" />
                        Assign to {selectedAgents.length} Agent{selectedAgents.length !== 1 ? 's' : ''} 
                        ({selectedAgents.length === 0 ? 'Select Agents' : selectedAgents.join(', ')})
                      </>
                    )}
                  </Button>
                  
                  {selectedAgents.length > 0 && (
                    <div className="mt-2 p-2 bg-blue-50 rounded-md">
                      <p className="text-sm text-blue-800">
                        <strong>Selected Agents:</strong> {selectedAgents.join(', ')}
                      </p>
                    </div>
                  )}
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BarChart3 className="h-5 w-5 mr-2 text-purple-600" />
                    Assignment Statistics
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="bg-blue-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-blue-600">{totalUploadedLeads.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">Available to Assign</div>
                    </div>
                    <div className="bg-green-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-green-600">{Object.keys(checkedOutLeads).length.toLocaleString()}</div>
                      <div className="text-sm text-gray-600">Currently Assigned</div>
                    </div>
                    <div className="bg-purple-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-purple-600">{selectedAgents.length}</div>
                      <div className="text-sm text-gray-600">Selected Agents</div>
                    </div>
                    <div className="bg-yellow-50 p-3 rounded-lg">
                      <div className="text-2xl font-bold text-yellow-600">{assignmentInProgress ? 'Processing...' : Object.keys(checkedOutLeads).length > 0 ? Object.keys(checkedOutLeads).length : '0'}</div>
                      <div className="text-sm text-gray-600">Checked Out</div>
                    </div>
                  </div>
                  
                  <div className="pt-4 border-t">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium">Assignment Progress</span>
                      <span className="text-sm text-gray-600">
                        {Object.keys(checkedOutLeads).length.toLocaleString()} leads assigned to agents
                      </span>
                    </div>
                    <Progress 
                      value={totalUploadedLeads > 0 ? (Object.keys(checkedOutLeads).length / (totalUploadedLeads + Object.keys(checkedOutLeads).length)) * 100 : 0} 
                      className="h-3" 
                    />
                    <div className="text-xs text-gray-500 mt-1">
                      Available: {totalUploadedLeads.toLocaleString()} | Assigned: {Object.keys(checkedOutLeads).length.toLocaleString()}
                    </div>
                    
                    {Object.keys(checkedOutLeads).length > 0 && (
                      <div className="mt-3">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={handleClearAllAssignments}
                          className="w-full border-orange-300 text-orange-700 hover:bg-orange-50"
                          disabled={assignmentInProgress}
                        >
                          <RotateCcw className="h-4 w-4 mr-2" />
                          Clear All Assignments (Testing)
                        </Button>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Assignment Results */}
            {assignmentResults && (
              <Card className="border-l-4 border-l-green-500 bg-green-50">
                <CardContent className="p-4">
                  <div className="flex items-center">
                    <CheckCircle className="h-6 w-6 text-green-600 mr-3" />
                    <p className="text-green-800 font-medium">{assignmentResults}</p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Current Agent Assignments */}
            {Object.keys(checkedOutLeads).length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <UserCheck className="h-5 w-5 mr-2 text-green-600" />
                    Current Agent Assignments
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(
                      Object.values(checkedOutLeads).reduce((acc, assignment) => {
                        if (!acc[assignment.agentName]) {
                          acc[assignment.agentName] = {
                            count: 0,
                            latestCheckout: assignment.checkedOutAt
                          };
                        }
                        acc[assignment.agentName].count++;
                        if (assignment.checkedOutAt > acc[assignment.agentName].latestCheckout) {
                          acc[assignment.agentName].latestCheckout = assignment.checkedOutAt;
                        }
                        return acc;
                      }, {} as Record<string, { count: number; latestCheckout: Date }>)
                    ).map(([agentName, data]) => (
                      <div key={agentName} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          <div>
                            <p className="font-medium">{agentName}</p>
                            <p className="text-sm text-gray-600">
                              {data.count} lead{data.count !== 1 ? 's' : ''} assigned • 
                              Last assignment: {data.latestCheckout.toLocaleTimeString()}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge variant="secondary">{data.count} Leads</Badge>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCheckInLeads(agentName)}
                            disabled={assignmentInProgress}
                          >
                            Check In All
                          </Button>
                        </div>
                      </div>
                    ))}
                    
                    <div className="text-center py-2 border-t">
                      <p className="text-sm text-gray-600">
                        Total: {Object.keys(checkedOutLeads).length} leads assigned to {Object.keys(Object.values(checkedOutLeads).reduce((acc, assignment) => ({ ...acc, [assignment.agentName]: true }), {})).length} agent{Object.keys(Object.values(checkedOutLeads).reduce((acc, assignment) => ({ ...acc, [assignment.agentName]: true }), {})).length !== 1 ? 's' : ''}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Available Agents */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-indigo-600" />
                  Available Agents
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[
                    { 
                      name: 'John Smith', // bronze role
                      role: 'Bronze Agent', 
                      leads: '5/25', 
                      success: '78%',
                      states: ['CA', 'NV'],
                      status: 'Available',
                      color: 'green'
                    },
                    { 
                      name: 'David Wilson', // silver role
                      role: 'Silver Agent', 
                      leads: '8/25', 
                      success: '82%',
                      states: ['FL', 'GA'],
                      status: 'Available',
                      color: 'green'
                    },
                    { 
                      name: 'Mike Rodriguez', // gold role
                      role: 'Gold Agent', 
                      leads: '12/25', 
                      success: '85%',
                      states: ['CA', 'OR'],
                      status: 'Available',
                      color: 'green'
                    },
                    { 
                      name: 'Lisa Chen', // topaz role
                      role: 'Topaz Team Lead', 
                      leads: '18/40', 
                      success: '89%',
                      states: ['NY', 'NJ'],
                      status: 'Available',
                      color: 'green'
                    },
                    { 
                      name: 'Sarah Johnson', // ruby role
                      role: 'Ruby Agent', 
                      leads: '25/40', 
                      success: '92%',
                      states: ['TX', 'FL'],
                      status: 'Available',
                      color: 'green'
                    },
                    { 
                      name: 'Emma Thompson', // diamond role
                      role: 'Diamond Agent', 
                      leads: '35/40', 
                      success: '94%',
                      states: ['WA', 'OR'],
                      status: 'Available',
                      color: 'green'
                    }
                  ].map((agent, index) => {
                    const assignedLeadCount = getAgentLeadCount(agent.name);
                    const isSelected = selectedAgents.includes(agent.name);
                    const hasAssignedLeads = assignedLeadCount > 0;
                    
                    return (
                      <div key={index} className={`p-4 border rounded-lg hover:shadow-md transition-shadow ${isSelected ? 'ring-2 ring-blue-500 bg-blue-50' : ''}`}>
                        <div className="flex items-center justify-between mb-3">
                          <div className="flex items-center gap-3">
                            <input
                              type="checkbox"
                              checked={isSelected}
                              onChange={(e) => handleAgentSelection(agent.name, e.target.checked)}
                              disabled={assignmentInProgress}
                              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                            />
                            <h4 className="font-medium text-gray-900">{agent.name}</h4>
                          </div>
                          <div className="flex items-center gap-2">
                            <Badge 
                              variant="outline" 
                              className="text-xs text-green-700 border-green-300"
                            >
                              {agent.status}
                            </Badge>
                            {hasAssignedLeads && (
                              <Badge variant="default" className="text-xs bg-blue-600">
                                {assignedLeadCount} Assigned
                              </Badge>
                            )}
                          </div>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Role:</span>
                            <span className="font-medium">{agent.role}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Capacity:</span>
                            <span className="font-medium">{agent.leads}</span>
                          </div>
                          {hasAssignedLeads && (
                            <div className="flex justify-between">
                              <span className="text-gray-600">Checked Out:</span>
                              <span className="font-medium text-blue-600">{assignedLeadCount} leads</span>
                            </div>
                          )}
                          <div className="flex justify-between">
                            <span className="text-gray-600">Success Rate:</span>
                            <span className="font-medium text-green-600">{agent.success}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">States:</span>
                            <span className="font-medium">{agent.states.join(', ')}</span>
                          </div>
                        </div>
                        
                        <div className="flex gap-2 mt-3">
                          <Button 
                            size="sm" 
                            variant={isSelected ? "default" : "outline"}
                            className="flex-1"
                            disabled={assignmentInProgress}
                            onClick={() => handleAgentSelection(agent.name, !isSelected)}
                          >
                            {isSelected ? (
                              <>
                                <CheckCircle className="h-3 w-3 mr-1" />
                                Selected
                              </>
                            ) : (
                              'Select Agent'
                            )}
                          </Button>
                          
                          {hasAssignedLeads && (
                            <Button 
                              size="sm" 
                              variant="outline"
                              className="flex-1"
                              onClick={() => handleCheckInLeads(agent.name)}
                              disabled={assignmentInProgress}
                            >
                              Check In
                            </Button>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            {/* Recent Assignment Activity */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Activity className="h-5 w-5 mr-2 text-orange-600" />
                  Recent Assignment Activity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">25 leads assigned to Sarah Johnson</p>
                        <p className="text-sm text-gray-600">High-value CA properties • 2 hours ago</p>
                      </div>
                    </div>
                    <Badge variant="secondary">Completed</Badge>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <div>
                        <p className="font-medium">15 leads assigned to Mike Rodriguez</p>
                        <p className="text-sm text-gray-600">Insurance claims OR/WA • 4 hours ago</p>
                      </div>
                    </div>
                    <Badge variant="secondary">Completed</Badge>
                  </div>
                  
                  <div className="text-center py-4">
                    <Button variant="outline">
                      <Eye className="h-4 w-4 mr-2" />
                      View All Assignment History
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        )}

        {/* Team Performance Tab */}
        {permissions?.canViewTeamPerformance && (
          <TabsContent value="team" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Users className="h-6 w-6 text-blue-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Agents</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.teamMetrics?.totalAgents || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Target className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Team Leads</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.teamMetrics?.totalLeads || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <TrendingUp className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Success Rate</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.teamMetrics?.avgSuccessRate || 0}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-yellow-100 rounded-lg">
                      <DollarSign className="h-6 w-6 text-yellow-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Value</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {formatCurrency(metrics.teamMetrics?.totalValue || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}

        {/* System Admin Tab */}
        {permissions?.canManageUsers && (
          <TabsContent value="admin" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-indigo-100 rounded-lg">
                      <Users className="h-6 w-6 text-indigo-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Users</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.systemMetrics?.totalUsers || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <FileText className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">Total Claims</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.systemMetrics?.totalClaims || 0}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-purple-100 rounded-lg">
                      <DollarSign className="h-6 w-6 text-purple-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">System Value</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {formatCurrency(metrics.systemMetrics?.totalValue || 0)}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className="p-2 bg-green-100 rounded-lg">
                      <Activity className="h-6 w-6 text-green-600" />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">System Health</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {metrics.systemMetrics?.systemHealth || 0}%
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}; 