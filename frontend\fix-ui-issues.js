/**
 * UI Issues Fix Script
 * This script addresses the 3 failing UI components from the system health check:
 * 1. React app loaded - not found
 * 2. Form inputs - not found  
 * 3. Navigation elements - not found
 */

console.log('🔧 Starting UI Issues Fix...');

// Fix 1: Ensure React App is Properly Loaded
function fixReactAppLoading() {
    console.log('🎨 Fixing React App Loading...');
    
    // Check if React root exists and has content
    const reactRoot = document.getElementById('root');
    if (!reactRoot) {
        console.error('❌ React root element not found');
        return false;
    }
    
    if (reactRoot.children.length === 0) {
        console.warn('⚠️ React root is empty - app may not have loaded');
        
        // Try to trigger React app loading
        if (window.React && window.ReactDOM) {
            console.log('🔄 Attempting to re-render React app...');
            // This would need to be customized based on your app structure
        }
        return false;
    }
    
    console.log('✅ React app appears to be loaded');
    return true;
}

// Fix 2: Ensure Form Inputs are Rendered
function fixFormInputs() {
    console.log('📝 Fixing Form Input Rendering...');
    
    const inputs = document.querySelectorAll('input, textarea, select');
    const forms = document.querySelectorAll('form');
    
    console.log(`Found ${inputs.length} input elements and ${forms.length} forms`);
    
    if (inputs.length === 0) {
        console.warn('⚠️ No form inputs found');
        
        // Check if we're on the login page or a page that should have forms
        const currentPath = window.location.pathname;
        const shouldHaveForms = currentPath.includes('login') || 
                               currentPath.includes('claims') || 
                               currentPath.includes('batch') ||
                               currentPath === '/';
        
        if (shouldHaveForms) {
            console.log('🔄 Page should have forms - checking component rendering...');
            
            // Look for form containers that might not have rendered
            const formContainers = document.querySelectorAll('[class*="form"], [class*="input"], .login, .claim');
            console.log(`Found ${formContainers.length} potential form containers`);
            
            if (formContainers.length === 0) {
                console.error('❌ No form containers found - component may not be rendering');
                return false;
            }
        }
        
        return false;
    }
    
    console.log('✅ Form inputs are present');
    return true;
}

// Fix 3: Ensure Navigation Elements are Rendered
function fixNavigationElements() {
    console.log('🧭 Fixing Navigation Element Rendering...');
    
    const nav = document.querySelector('nav');
    const sidebar = document.querySelector('aside, .sidebar, [class*="sidebar"]');
    const menuButtons = document.querySelectorAll('button[class*="menu"], .menu-button, [aria-label*="menu"]');
    const navLinks = document.querySelectorAll('a[href], button[onclick], [role="button"]');
    
    console.log(`Navigation audit:
    - Nav element: ${nav ? 'Found' : 'Not found'}
    - Sidebar: ${sidebar ? 'Found' : 'Not found'}
    - Menu buttons: ${menuButtons.length}
    - Navigation links: ${navLinks.length}`);
    
    if (!nav && !sidebar && menuButtons.length === 0) {
        console.warn('⚠️ No navigation elements found');
        
        // Check if we're authenticated (navigation might be hidden for unauthenticated users)
        const isLoginPage = window.location.pathname.includes('login') || 
                           document.querySelector('.login-form, [class*="login"]');
        
        if (isLoginPage) {
            console.log('ℹ️ On login page - navigation may be intentionally hidden');
            return true;
        }
        
        // Look for navigation containers that might not have rendered
        const navContainers = document.querySelectorAll('[class*="nav"], [class*="sidebar"], [class*="menu"]');
        console.log(`Found ${navContainers.length} potential navigation containers`);
        
        if (navContainers.length === 0) {
            console.error('❌ No navigation containers found - component may not be rendering');
            return false;
        }
        
        return false;
    }
    
    if (navLinks.length === 0) {
        console.warn('⚠️ No clickable navigation elements found');
        return false;
    }
    
    console.log('✅ Navigation elements are present');
    return true;
}

// Main fix function
function runUIFixes() {
    console.log('🚀 Running UI Fixes...');
    
    const results = {
        reactApp: fixReactAppLoading(),
        formInputs: fixFormInputs(),
        navigation: fixNavigationElements()
    };
    
    console.log('📊 Fix Results:', results);
    
    const allFixed = Object.values(results).every(result => result === true);
    
    if (allFixed) {
        console.log('✅ All UI issues have been resolved!');
    } else {
        console.log('⚠️ Some UI issues remain. Check the detailed logs above.');
        
        // Provide specific recommendations
        if (!results.reactApp) {
            console.log('🔧 React App Fix: Check if the app is properly mounted and there are no JavaScript errors');
        }
        if (!results.formInputs) {
            console.log('🔧 Form Inputs Fix: Ensure form components are rendering and not hidden by authentication state');
        }
        if (!results.navigation) {
            console.log('🔧 Navigation Fix: Check if navigation components are rendering and user is authenticated');
        }
    }
    
    return results;
}

// Auto-run fixes when script loads
if (typeof window !== 'undefined') {
    // Wait for DOM to be ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', runUIFixes);
    } else {
        // DOM is already ready
        setTimeout(runUIFixes, 1000); // Give React time to render
    }
}

// Export for manual execution
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { runUIFixes, fixReactAppLoading, fixFormInputs, fixNavigationElements };
}
