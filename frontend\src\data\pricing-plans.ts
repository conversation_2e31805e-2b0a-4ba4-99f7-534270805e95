import { PricingPlan, PricingFeature, CommissionTier, VolumeDiscount, AddOnService, UpgradePath, StateLicense } from '@/types/pricing';

// US States data for licensing
export const US_STATES: StateLicense[] = [
  { state_code: 'AL', state_name: 'Alabama', included: false },
  { state_code: 'AK', state_name: 'Alaska', included: false },
  { state_code: 'AZ', state_name: 'Arizona', included: false },
  { state_code: 'AR', state_name: 'Arkansas', included: false },
  { state_code: 'CA', state_name: 'California', included: false },
  { state_code: 'CO', state_name: 'Colorado', included: false },
  { state_code: 'CT', state_name: 'Connecticut', included: false },
  { state_code: 'DE', state_name: 'Delaware', included: false },
  { state_code: 'FL', state_name: 'Florida', included: false },
  { state_code: 'GA', state_name: 'Georgia', included: false },
  { state_code: 'HI', state_name: 'Hawaii', included: false },
  { state_code: 'ID', state_name: 'Idaho', included: false },
  { state_code: 'IL', state_name: 'Illinois', included: false },
  { state_code: 'IN', state_name: 'Indiana', included: false },
  { state_code: 'IA', state_name: 'Iowa', included: false },
  { state_code: 'KS', state_name: 'Kansas', included: false },
  { state_code: 'KY', state_name: 'Kentucky', included: false },
  { state_code: 'LA', state_name: 'Louisiana', included: false },
  { state_code: 'ME', state_name: 'Maine', included: false },
  { state_code: 'MD', state_name: 'Maryland', included: false },
  { state_code: 'MA', state_name: 'Massachusetts', included: false },
  { state_code: 'MI', state_name: 'Michigan', included: false },
  { state_code: 'MN', state_name: 'Minnesota', included: false },
  { state_code: 'MS', state_name: 'Mississippi', included: false },
  { state_code: 'MO', state_name: 'Missouri', included: false },
  { state_code: 'MT', state_name: 'Montana', included: false },
  { state_code: 'NE', state_name: 'Nebraska', included: false },
  { state_code: 'NV', state_name: 'Nevada', included: false },
  { state_code: 'NH', state_name: 'New Hampshire', included: false },
  { state_code: 'NJ', state_name: 'New Jersey', included: false },
  { state_code: 'NM', state_name: 'New Mexico', included: false },
  { state_code: 'NY', state_name: 'New York', included: false },
  { state_code: 'NC', state_name: 'North Carolina', included: false },
  { state_code: 'ND', state_name: 'North Dakota', included: false },
  { state_code: 'OH', state_name: 'Ohio', included: false },
  { state_code: 'OK', state_name: 'Oklahoma', included: false },
  { state_code: 'OR', state_name: 'Oregon', included: false },
  { state_code: 'PA', state_name: 'Pennsylvania', included: false },
  { state_code: 'RI', state_name: 'Rhode Island', included: false },
  { state_code: 'SC', state_name: 'South Carolina', included: false },
  { state_code: 'SD', state_name: 'South Dakota', included: false },
  { state_code: 'TN', state_name: 'Tennessee', included: false },
  { state_code: 'TX', state_name: 'Texas', included: false },
  { state_code: 'UT', state_name: 'Utah', included: false },
  { state_code: 'VT', state_name: 'Vermont', included: false },
  { state_code: 'VA', state_name: 'Virginia', included: false },
  { state_code: 'WA', state_name: 'Washington', included: false },
  { state_code: 'WV', state_name: 'West Virginia', included: false },
  { state_code: 'WI', state_name: 'Wisconsin', included: false },
  { state_code: 'WY', state_name: 'Wyoming', included: false },
  { state_code: 'DC', state_name: 'District of Columbia', included: false },
];

// Enhanced feature definitions for asset recovery
export const PRICING_FEATURES: PricingFeature[] = [
  // Core Features
  {
    id: 'basic_claim_management',
    name: 'Basic Claim Management',
    description: 'Create, track, and manage asset recovery claims',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'contact_tracking',
    name: 'Contact Tracking',
    description: 'Track claimant contact information and communication history',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'document_management',
    name: 'Document Management',
    description: 'Upload, organize, and manage claim documents',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'email_templates',
    name: 'Email Templates',
    description: 'Pre-built email templates for common communications',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'activity_logging',
    name: 'Activity Logging',
    description: 'Complete timeline of all claim activities and interactions',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'mobile_app',
    name: 'Mobile App Access',
    description: 'iOS and Android apps for field work',
    category: 'core',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },

  // Analytics Features
  {
    id: 'basic_reporting',
    name: 'Basic Reporting',
    description: 'Standard reports on claim status and recovery amounts',
    category: 'analytics',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'advanced_analytics',
    name: 'Advanced Analytics',
    description: 'Detailed performance metrics and recovery analytics',
    category: 'analytics',
    tier_availability: ['silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'forecasting',
    name: 'Forecasting',
    description: 'Predictive analytics for recovery estimates',
    category: 'analytics',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'team_analytics',
    name: 'Team Analytics',
    description: 'Team performance tracking and management reports',
    category: 'analytics',
    tier_availability: ['topaz', 'ruby', 'diamond']
  },

  // Automation Features
  {
    id: 'basic_csv_import',
    name: 'Basic CSV Import',
    description: 'Import claims from CSV files with basic mapping',
    category: 'automation',
    tier_availability: ['silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'advanced_csv_import',
    name: 'Advanced CSV Import',
    description: 'Unlimited CSV import with custom mapping templates',
    category: 'automation',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'email_automation',
    name: 'Email Automation',
    description: 'Automated email sequences and follow-ups',
    category: 'automation',
    tier_availability: ['silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'workflow_automation',
    name: 'Workflow Automation',
    description: 'Custom workflow sequences and automation rules',
    category: 'automation',
    tier_availability: ['ruby', 'diamond']
  },

  // AI Features
  {
    id: 'ai_lead_scoring',
    name: 'AI Lead Scoring',
    description: 'AI-powered claim prioritization and scoring',
    category: 'ai',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'predictive_analytics',
    name: 'Predictive Analytics',
    description: 'AI-powered insights and recovery predictions',
    category: 'ai',
    tier_availability: ['diamond']
  },

  // Compliance Features
  {
    id: 'state_compliance',
    name: 'State Compliance',
    description: 'Built-in compliance for selected states',
    category: 'compliance',
    tier_availability: ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'all_state_compliance',
    name: 'All State Compliance',
    description: 'Compliance monitoring and alerts for all 50 states',
    category: 'compliance',
    tier_availability: ['gold', 'diamond']
  },
  {
    id: 'audit_reporting',
    name: 'Audit Reporting',
    description: 'Advanced compliance and audit reporting',
    category: 'compliance',
    tier_availability: ['topaz', 'ruby', 'diamond']
  },

  // Integration Features
  {
    id: 'basic_integrations',
    name: 'Basic Integrations',
    description: 'DocuSign and basic phone system integrations',
    category: 'integration',
    tier_availability: ['silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'skip_tracing',
    name: 'Skip Tracing Integration',
    description: 'Built-in skip tracing with monthly credits',
    category: 'integration',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'advanced_integrations',
    name: 'Advanced Integrations',
    description: 'QuickBooks, Salesforce, and other business integrations',
    category: 'integration',
    tier_availability: ['ruby', 'diamond']
  },
  {
    id: 'api_access',
    name: 'API Access',
    description: 'Full API access for custom integrations',
    category: 'integration',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'custom_integrations',
    name: 'Custom Integrations',
    description: 'Bespoke integration development and support',
    category: 'integration',
    tier_availability: ['diamond']
  },

  // Team Features
  {
    id: 'team_management',
    name: 'Team Management',
    description: 'User management and claim assignment',
    category: 'team',
    tier_availability: ['topaz', 'ruby', 'diamond']
  },
  {
    id: 'supervisor_dashboard',
    name: 'Supervisor Dashboard',
    description: 'Management dashboard with team oversight',
    category: 'team',
    tier_availability: ['topaz', 'ruby', 'diamond']
  },
  {
    id: 'role_permissions',
    name: 'Role & Permissions',
    description: 'Custom role and permission management',
    category: 'team',
    tier_availability: ['ruby', 'diamond']
  },
  {
    id: 'enterprise_admin',
    name: 'Enterprise Admin',
    description: 'Advanced admin controls and system monitoring',
    category: 'team',
    tier_availability: ['diamond']
  },

  // Support Features
  {
    id: 'sms_messaging',
    name: 'SMS Messaging',
    description: 'SMS communication capabilities',
    category: 'core',
    tier_availability: ['silver', 'gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'white_label',
    name: 'White Label Branding',
    description: 'Custom branding and white-label documents',
    category: 'core',
    tier_availability: ['gold', 'ruby', 'diamond']
  },
  {
    id: 'custom_domains',
    name: 'Custom Domains',
    description: 'Custom domain and complete white-labeling',
    category: 'core',
    tier_availability: ['diamond']
  }
];

// Main pricing plans based on revised structure
export const PRICING_PLANS: PricingPlan[] = [
  {
    id: 'bronze',
    name: 'Bronze',
    tagline: 'Perfect for new agents or single-state specialists',
    description: 'Essential tools to start your asset recovery business in one state',
    category: 'individual',
    pricing: {
      monthly: 79,
      annual: 869, // 2 months free
      annual_discount: 17,
      annual_free_months: 2
    },
    limits: [
      { type: 'user_licenses', value: 1, unit: 'users' },
      { type: 'state_licenses', value: 1, unit: 'states' },
      { type: 'active_claims', value: 500, unit: 'claims' },
      { type: 'storage_gb', value: 5, unit: 'GB' },
      { type: 'api_calls_per_month', value: 0, unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'state_compliance'
    ],
    trial_days: 7,
    state_licensing: {
      included_states: 1,
      additional_state_cost: 15,
      upgrade_packs: [
        { name: 'Bronze to Silver (4 additional states)', additional_states: 4, monthly_cost: 30 }
      ]
    },
    support_level: 'email',
    response_time: '48-hour response',
    target_customer: 'Solo agent specializing in one state (TX, CA, FL, etc.)',
    monthly_value_range: 'Process 40-60 claims/month in primary state',
    roi_description: '$3,000-6,000 additional monthly revenue vs. $79 cost'
  },

  {
    id: 'silver',
    name: 'Silver',
    tagline: 'Perfect for regional agents or multi-state specialists',
    description: 'Advanced features for agents working across multiple states',
    category: 'individual',
    pricing: {
      monthly: 119,
      annual: 1309, // 2 months free
      annual_discount: 17,
      annual_free_months: 2
    },
    limits: [
      { type: 'user_licenses', value: 1, unit: 'users' },
      { type: 'state_licenses', value: 5, unit: 'states' },
      { type: 'active_claims', value: 1500, unit: 'claims' },
      { type: 'storage_gb', value: 15, unit: 'GB' },
      { type: 'csv_import_records', value: 10000, unit: 'records' },
      { type: 'api_calls_per_month', value: 0, unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'advanced_analytics',
      'basic_csv_import',
      'email_automation',
      'basic_integrations',
      'sms_messaging',
      'state_compliance'
    ],
    popular: true,
    trial_days: 14,
    state_licensing: {
      included_states: 5,
      additional_state_cost: 15,
      upgrade_packs: [
        { name: 'Silver to Gold (all remaining states)', additional_states: 45, monthly_cost: 89 }
      ]
    },
    support_level: 'priority',
    response_time: '24-hour response',
    target_customer: 'Agent working multiple states or growing their business',
    monthly_value_range: 'Process 80-120 claims/month across 5 states',
    roi_description: '$6,000-12,000 additional monthly revenue vs. $119 cost'
  },

  {
    id: 'gold',
    name: 'Gold',
    tagline: 'Perfect for power users and high-volume independent agents',
    description: 'Complete solution for national asset recovery operations',
    category: 'individual',
    pricing: {
      monthly: 229,
      annual: 2519, // 2 months free
      annual_discount: 17,
      annual_free_months: 2
    },
    limits: [
      { type: 'user_licenses', value: 1, unit: 'users' },
      { type: 'state_licenses', value: 'unlimited', unit: 'states' },
      { type: 'active_claims', value: 'unlimited', unit: 'claims' },
      { type: 'storage_gb', value: 50, unit: 'GB' },
      { type: 'skip_tracing_credits', value: 100, unit: 'credits' },
      { type: 'csv_import_records', value: 'unlimited', unit: 'records' },
      { type: 'api_calls_per_month', value: 50000, unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'advanced_analytics',
      'forecasting',
      'advanced_csv_import',
      'email_automation',
      'ai_lead_scoring',
      'basic_integrations',
      'skip_tracing',
      'api_access',
      'sms_messaging',
      'white_label',
      'all_state_compliance'
    ],
    trial_days: 14,
    state_licensing: {
      included_states: 'all',
      additional_state_cost: 0
    },
    support_level: 'phone',
    response_time: 'Same-day response',
    target_customer: 'Established agent working nationally or high-volume specialist',
    monthly_value_range: 'Process 150+ claims/month across all states',
    roi_description: '$15,000-30,000 additional monthly revenue vs. $229 cost'
  },

  {
    id: 'topaz',
    name: 'Topaz',
    tagline: 'Perfect for small recovery firms',
    description: 'Team management and collaboration tools for growing firms',
    category: 'team',
    pricing: {
      monthly: 299,
      annual: 2691, // 3 months free
      annual_discount: 25,
      annual_free_months: 3
    },
    limits: [
      { type: 'user_licenses', value: 5, unit: 'users' },
      { type: 'state_licenses', value: 5, unit: 'states' },
      { type: 'active_claims', value: 5000, unit: 'claims' },
      { type: 'storage_gb', value: 100, unit: 'GB' },
      { type: 'csv_import_records', value: 50000, unit: 'records' },
      { type: 'api_calls_per_month', value: 25000, unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'advanced_analytics',
      'basic_csv_import',
      'email_automation',
      'basic_integrations',
      'sms_messaging',
      'team_management',
      'supervisor_dashboard',
      'audit_reporting',
      'state_compliance'
    ],
    trial_days: 14,
    state_licensing: {
      included_states: 5,
      additional_state_cost: 15,
      upgrade_packs: [
        { name: 'Additional 10 states', additional_states: 10, monthly_cost: 99 }
      ]
    },
    support_level: 'phone',
    response_time: 'Same-day response',
    target_customer: 'Small firms with 3-5 agents in regional markets',
    monthly_value_range: 'Team processes 400-600 claims/month',
    roi_description: '$30,000-60,000 additional monthly revenue vs. $299 cost'
  },

  {
    id: 'ruby',
    name: 'Ruby',
    tagline: 'Perfect for medium recovery firms',
    description: 'Advanced team features and enterprise integrations',
    category: 'team',
    pricing: {
      monthly: 499,
      annual: 4491, // 3 months free
      annual_discount: 25,
      annual_free_months: 3
    },
    limits: [
      { type: 'user_licenses', value: 10, unit: 'users' },
      { type: 'state_licenses', value: 15, unit: 'states' },
      { type: 'active_claims', value: 15000, unit: 'claims' },
      { type: 'storage_gb', value: 250, unit: 'GB' },
      { type: 'skip_tracing_credits', value: 500, unit: 'credits' },
      { type: 'csv_import_records', value: 'unlimited', unit: 'records' },
      { type: 'api_calls_per_month', value: 100000, unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'advanced_analytics',
      'forecasting',
      'team_analytics',
      'advanced_csv_import',
      'email_automation',
      'workflow_automation',
      'ai_lead_scoring',
      'basic_integrations',
      'advanced_integrations',
      'skip_tracing',
      'api_access',
      'sms_messaging',
      'white_label',
      'team_management',
      'supervisor_dashboard',
      'role_permissions',
      'audit_reporting',
      'state_compliance'
    ],
    recommended: true,
    trial_days: 14,
    state_licensing: {
      included_states: 15,
      additional_state_cost: 10,
      upgrade_packs: [
        { name: 'All remaining states', additional_states: 35, monthly_cost: 200 }
      ]
    },
    support_level: 'dedicated',
    response_time: 'Priority phone support with SLA',
    target_customer: 'Established firms with 6-10 agents in multiple states',
    monthly_value_range: 'Team processes 800-1,200 claims/month',
    roi_description: '$60,000-120,000 additional monthly revenue vs. $499 cost'
  },

  {
    id: 'diamond',
    name: 'Diamond',
    tagline: 'Perfect for large enterprise recovery firms',
    description: 'Complete enterprise solution with unlimited everything',
    category: 'enterprise',
    pricing: {
      monthly: 899,
      annual: 7192, // 4 months free
      annual_discount: 33,
      annual_free_months: 4
    },
    limits: [
      { type: 'user_licenses', value: 100, unit: 'users' },
      { type: 'state_licenses', value: 'unlimited', unit: 'states' },
      { type: 'active_claims', value: 'unlimited', unit: 'claims' },
      { type: 'storage_gb', value: 'unlimited', unit: 'GB' },
      { type: 'skip_tracing_credits', value: 'unlimited', unit: 'credits' },
      { type: 'csv_import_records', value: 'unlimited', unit: 'records' },
      { type: 'api_calls_per_month', value: 'unlimited', unit: 'calls' }
    ],
    features: [
      'basic_claim_management',
      'contact_tracking',
      'document_management',
      'email_templates',
      'activity_logging',
      'mobile_app',
      'basic_reporting',
      'advanced_analytics',
      'forecasting',
      'team_analytics',
      'advanced_csv_import',
      'email_automation',
      'workflow_automation',
      'ai_lead_scoring',
      'predictive_analytics',
      'basic_integrations',
      'advanced_integrations',
      'custom_integrations',
      'skip_tracing',
      'api_access',
      'sms_messaging',
      'white_label',
      'custom_domains',
      'team_management',
      'supervisor_dashboard',
      'role_permissions',
      'enterprise_admin',
      'audit_reporting',
      'all_state_compliance'
    ],
    trial_days: 30,
    state_licensing: {
      included_states: 'all',
      additional_state_cost: 0
    },
    support_level: 'enterprise',
    response_time: '24/7 dedicated support with phone SLA',
    target_customer: 'Large firms, multi-location operations, enterprise clients',
    monthly_value_range: 'Team processes 5,000+ claims/month',
    roi_description: '$500,000+ additional monthly revenue vs. $899 cost'
  }
];

// Add-on services
export const ADD_ON_SERVICES: AddOnService[] = [
  {
    id: 'additional_state_license',
    name: 'Additional State License',
    description: 'Add one additional state to your plan',
    monthly_price: 15,
    unit: 'per state',
    available_for: ['bronze', 'silver', 'topaz', 'ruby']
  },
  {
    id: 'skip_tracing_credits',
    name: 'Advanced Skip Tracing Credits',
    description: 'Additional skip tracing searches beyond plan limits',
    monthly_price: 50,
    unit: 'per 100 searches',
    available_for: ['bronze', 'silver', 'topaz', 'ruby']
  },
  {
    id: 'premium_templates',
    name: 'Premium Document Templates',
    description: 'State-specific legal forms and premium templates',
    monthly_price: 25,
    unit: 'monthly access',
    available_for: ['bronze', 'silver', 'gold', 'topaz', 'ruby']
  },
  {
    id: 'enhanced_storage',
    name: 'Enhanced Storage',
    description: 'Additional storage beyond plan limits',
    monthly_price: 5,
    unit: 'per GB',
    available_for: ['bronze', 'silver', 'gold', 'topaz', 'ruby']
  },
  {
    id: 'priority_support',
    name: 'Priority Support Upgrade',
    description: 'Upgrade to priority support with faster response times',
    monthly_price: 50,
    unit: 'monthly',
    available_for: ['bronze', 'silver']
  },
  {
    id: 'custom_integration',
    name: 'Custom Integration Development',
    description: 'Professional services for custom integrations',
    monthly_price: 150,
    unit: 'per hour',
    available_for: ['gold', 'topaz', 'ruby', 'diamond']
  },
  {
    id: 'training_session',
    name: 'Additional Training Sessions',
    description: 'Comprehensive platform training for your team',
    monthly_price: 500,
    unit: 'per 2-hour session',
    available_for: ['topaz', 'ruby', 'diamond']
  }
];

// Natural upgrade paths
export const UPGRADE_PATHS: UpgradePath[] = [
  {
    from: 'bronze',
    to: 'silver',
    trigger: 'When agent wants to expand to additional states',
    benefit: 'Multi-state capability and advanced features',
    timing: 'Typically after 3-6 months'
  },
  {
    from: 'silver',
    to: 'gold',
    trigger: 'When agent is ready for national expansion',
    benefit: 'All states and AI-powered features',
    timing: 'Typically after 6-12 months'
  },
  {
    from: 'gold',
    to: 'topaz',
    trigger: 'When agent hires first team members',
    benefit: 'Team management and collaboration tools',
    timing: 'When hiring 2+ additional staff'
  },
  {
    from: 'topaz',
    to: 'ruby',
    trigger: 'As team grows beyond 5 agents',
    benefit: 'Advanced team features and integrations',
    timing: 'When reaching 6-10 team members'
  },
  {
    from: 'ruby',
    to: 'diamond',
    trigger: 'When reaching enterprise scale (50+ agents)',
    benefit: 'Unlimited everything and enterprise features',
    timing: 'When scaling to large enterprise operations'
  }
];

// Commission tiers for recovery amounts
export const COMMISSION_TIERS: CommissionTier[] = [
  {
    min_amount: 0,
    max_amount: 5000,
    rate: 30,
    description: 'Small claims - higher effort required'
  },
  {
    min_amount: 5001,
    max_amount: 25000,
    rate: 25,
    description: 'Standard claims'
  },
  {
    min_amount: 25001,
    max_amount: 100000,
    rate: 20,
    description: 'Large claims'
  },
  {
    min_amount: 100001,
    max_amount: 500000,
    rate: 18,
    description: 'Very large claims'
  },
  {
    min_amount: 500001,
    rate: 15,
    description: 'Enterprise-level recoveries'
  }
];

// Volume discounts for high-volume clients
export const VOLUME_DISCOUNTS: VolumeDiscount[] = [
  {
    min_monthly_volume: 1000000,
    discount_percentage: 5,
    description: 'Volume discount for $1M+ monthly recovery'
  },
  {
    min_monthly_volume: 5000000,
    discount_percentage: 10,
    description: 'Volume discount for $5M+ monthly recovery'
  },
  {
    min_monthly_volume: 10000000,
    discount_percentage: 15,
    description: 'Volume discount for $10M+ monthly recovery'
  }
];

// Overage pricing for exceeding plan limits
export const OVERAGE_PRICING = {
  claims_per_month: 0.50, // $0.50 per additional claim
  team_members: 25, // $25 per additional user per month
  storage_gb: 2, // $2 per additional GB per month
  api_calls_per_month: 0.001, // $0.001 per additional API call
  exports: 5 // $5 per additional export
};
