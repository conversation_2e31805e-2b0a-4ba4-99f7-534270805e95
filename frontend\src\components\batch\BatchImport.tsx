// Simplified BatchImport component using refactored services

import React, { useState, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Upload, MapPin, FileText, Database, TestTube2 } from 'lucide-react'

// Hooks and services
import { useFileUpload, createBatchFile } from '@/hooks/useFileUpload'
import { useDataPersistence } from '@/hooks/useDataPersistence'

// Components
import { CSVFieldMapper } from './CSVFieldMapper'
import { BatchImportTest } from '@/tests/BatchImportTest'

// Types and constants
import { BatchFile, FieldMapping } from '@/types/batchImport'
import { US_STATES } from '@/utils/constants'
import { formatFileSize, formatNumber } from '@/utils/formatters'

export default function BatchImport() {
  const fileInputRef = useRef<HTMLInputElement>(null)
  
  // Core state
  const [selectedState, setSelectedState] = useState<string>('')
  const [files, setFiles] = useState<BatchFile[]>([])
  const [currentFile, setCurrentFile] = useState<BatchFile | null>(null)
  const [showFieldMapper, setShowFieldMapper] = useState(false)
  const [showTestSuite, setShowTestSuite] = useState(false)

  // Custom hooks
  const { uploadFile, parseCSV, isUploading } = useFileUpload()
  const { saveData, exportData, clearAllData, viewSavedData } = useDataPersistence(files, setFiles)

  // Handlers
  const handleFileSelection = async (selectedFiles: FileList) => {
    if (!selectedState) {
      alert('Please select a state first')
      return
    }

    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i]
      const batchFile = createBatchFile(file)
      batchFile.state = selectedState

      try {
        setFiles(prev => [...prev, batchFile])
        await uploadFile(file, batchFile)
        await parseCSV(file, batchFile)
        setFiles(prev => prev.map(f => f.id === batchFile.id ? batchFile : f))
      } catch (error) {
        console.error('File processing failed:', error)
        batchFile.status = 'error'
        batchFile.errorMessage = error instanceof Error ? error.message : 'Processing failed'
        setFiles(prev => prev.map(f => f.id === batchFile.id ? batchFile : f))
      }
    }
  }

  const handleStartMapping = (file: BatchFile) => {
    setCurrentFile(file)
    setShowFieldMapper(true)
  }

  const handleMappingComplete = async (mappings: FieldMapping[]) => {
    if (!currentFile) return

    try {
      const updatedFile = await saveData(currentFile, mappings)
      setFiles(prev => prev.map(f => f.id === updatedFile.id ? updatedFile : f))
      setShowFieldMapper(false)
      setCurrentFile(null)
      console.log(`✅ Import completed for ${updatedFile.name}`)
    } catch (error) {
      console.error('Mapping completion error:', error)
    }
  }

  const handleBackToUpload = () => {
    setShowFieldMapper(false)
    setCurrentFile(null)
  }

  const generateSampleCSV = () => {
    const sampleData = `name,email,phone,address,amount,description
John Doe,<EMAIL>,555-0123,"123 Main St, Anytown, ST 12345",1500.00,Auto loan recovery
Jane Smith,<EMAIL>,555-0456,"456 Oak Ave, Somewhere, ST 67890",2300.50,Credit card debt
Bob Johnson,<EMAIL>,555-0789,"789 Pine Rd, Nowhere, ST 13579",875.25,Personal loan`

    const blob = new Blob([sampleData], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.style.display = 'none'
    a.href = url
    a.download = 'sample-asset-recovery-data.csv'
    document.body.appendChild(a)
    a.click()
    window.URL.revokeObjectURL(url)
    document.body.removeChild(a)
  }

  // Show test suite
  if (showTestSuite) {
    return (
      <div className="space-y-4">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={() => setShowTestSuite(false)}>
            ← Back to Batch Import
          </Button>
          <h2 className="text-xl font-semibold">Comprehensive Test Suite</h2>
        </div>
        <BatchImportTest />
      </div>
    )
  }

  // Show field mapper
  if (showFieldMapper && currentFile) {
    return (
      <CSVFieldMapper
        detectedHeaders={currentFile.detectedHeaders || []}
        stateCode={currentFile.state || selectedState}
        sampleData={currentFile.sampleData || []}
        onMappingComplete={handleMappingComplete}
        onBack={handleBackToUpload}
      />
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Upload className="h-5 w-5" />
              Batch Import System - Refactored ✨
            </div>
            <Button 
              variant="outline" 
              size="sm" 
              onClick={() => setShowTestSuite(true)}
              className="flex items-center gap-2"
            >
              <TestTube2 className="h-4 w-4" />
              Run Tests
            </Button>
          </CardTitle>
          <CardDescription>
            Upload and process CSV files for asset recovery records. Now with modular architecture supporting unlimited records!
          </CardDescription>
        </CardHeader>
      </Card>

      {/* State Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-4 w-4" />
            State Selection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <Select value={selectedState} onValueChange={setSelectedState}>
              <SelectTrigger className="w-64">
                <SelectValue placeholder="Select state..." />
              </SelectTrigger>
              <SelectContent>
                {US_STATES.map(state => (
                  <SelectItem key={state} value={state}>
                    {state}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {selectedState && (
              <Badge variant="outline">Selected: {selectedState}</Badge>
            )}
          </div>
        </CardContent>
      </Card>

      {/* File Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Upload className="h-4 w-4" />
            File Upload
          </CardTitle>
          <CardDescription>
            Supports CSV files up to 400MB with millions of records using chunked processing.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="border-2 border-dashed rounded-lg p-8 text-center">
              {!selectedState ? (
                <p className="text-gray-600">Please select a state first</p>
              ) : isUploading ? (
                <p className="text-blue-600">Uploading files...</p>
              ) : (
                <>
                  <Upload className="h-12 w-12 text-blue-500 mx-auto mb-4" />
                  <div className="space-y-2">
                    <Button onClick={() => fileInputRef.current?.click()}>
                      Browse Files
                    </Button>
                    <Button variant="outline" onClick={generateSampleCSV}>
                      Download Sample CSV
                    </Button>
                  </div>
                </>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Files List */}
      {files.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Uploaded Files ({files.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {files.map((file) => (
                <div key={file.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">{file.name}</h4>
                      <p className="text-sm text-gray-500">
                        {formatFileSize(file.size)} • {file.state} • {formatNumber(file.recordCount || 0)} records
                      </p>
                      {file.progress > 0 && file.progress < 100 && (
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div 
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300" 
                            style={{ width: `${file.progress}%` }}
                          />
                        </div>
                      )}
                    </div>
                    <div className="flex gap-2">
                      <Badge>{file.status.replace('_', ' ')}</Badge>
                      {file.status === 'mapping' && (
                        <Button size="sm" onClick={() => handleStartMapping(file)}>
                          Start Mapping
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Data Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-4 w-4" />
            Data Management
          </CardTitle>
          <CardDescription>
            Manage your saved data with the new persistent storage system.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            <Button variant="outline" onClick={viewSavedData}>View Summary</Button>
            <Button variant="outline" onClick={exportData}>Export Data</Button>
            <Button variant="destructive" onClick={clearAllData}>Clear All</Button>
          </div>
        </CardContent>
      </Card>

      {/* Hidden file input */}
      <input
        type="file"
        ref={fileInputRef}
        className="hidden"
        accept=".csv"
        multiple
        onChange={(e) => e.target.files && handleFileSelection(e.target.files)}
      />
    </div>
  )
} 