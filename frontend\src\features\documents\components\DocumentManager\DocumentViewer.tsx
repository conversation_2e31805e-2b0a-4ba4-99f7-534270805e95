import React from 'react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Download, 
  ExternalLink, 
  Eye, 
  FileText, 
  Image,
  Share2,
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react';

interface Document {
  id: string;
  claim_id: string;
  file_name: string;
  original_name: string;
  category: string;
  subcategory?: string;
  file_size: number;
  file_type: string;
  file_url?: string;
  description?: string;
  tags: string[];
  status: 'draft' | 'pending_review' | 'approved' | 'rejected' | 'archived';
  version: number;
  parent_document_id?: string;
  uploaded_by: string;
  reviewed_by?: string;
  approved_by?: string;
  is_confidential: boolean;
  expiry_date?: string;
  permissions: 'internal' | 'shareable' | 'public';
  created_at: string;
  updated_at: string;
  uploader_name?: string;
  reviewer_name?: string;
  approver_name?: string;
}

interface DocumentViewerProps {
  document: Document;
  onClose: () => void;
}

const STATUS_CONFIG = {
  draft: { color: 'gray', label: 'Draft' },
  pending_review: { color: 'yellow', label: 'Pending Review' },
  approved: { color: 'green', label: 'Approved' },
  rejected: { color: 'red', label: 'Rejected' },
  archived: { color: 'blue', label: 'Archived' }
};

export const DocumentViewer: React.FC<DocumentViewerProps> = ({ document: doc, onClose }) => {
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-6 w-6 text-blue-500" />;
    if (fileType === 'application/pdf') return <FileText className="h-6 w-6 text-red-500" />;
    return <FileText className="h-6 w-6 text-gray-500" />;
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const handleDownload = () => {
    if (doc.file_url) {
      const link = globalThis.document.createElement('a');
      link.href = doc.file_url;
      link.download = doc.original_name;
      link.target = '_blank';
      globalThis.document.body.appendChild(link);
      link.click();
      globalThis.document.body.removeChild(link);
    }
  };

  const handleOpenInNewTab = () => {
    if (doc.file_url) {
      window.open(doc.file_url, '_blank');
    }
  };

  const isImageFile = doc.file_type.startsWith('image/');
  const isPdfFile = doc.file_type === 'application/pdf';

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-3">
            {getFileIcon(doc.file_type)}
            <div className="flex-1 min-w-0">
              <h2 className="text-lg font-semibold truncate">{doc.original_name}</h2>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant={STATUS_CONFIG[doc.status].color as any}>
                  {STATUS_CONFIG[doc.status].label}
                </Badge>
                {doc.is_confidential && (
                  <Badge variant="destructive">Confidential</Badge>
                )}
                {doc.version > 1 && (
                  <Badge variant="outline">v{doc.version}</Badge>
                )}
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Document Preview */}
          <div className="lg:col-span-2">
            <div className="border rounded-lg p-4 bg-gray-50 min-h-[400px]">
              {isImageFile && doc.file_url ? (
                <div className="text-center">
                  <img 
                    src={doc.file_url} 
                    alt={doc.original_name}
                    className="max-w-full max-h-96 mx-auto rounded-lg shadow-md"
                    onError={(e) => {
                      e.currentTarget.style.display = 'none';
                      e.currentTarget.nextElementSibling?.removeAttribute('style');
                    }}
                  />
                  <div className="hidden text-gray-500 mt-8">
                    <FileText className="h-16 w-16 mx-auto mb-4" />
                    <p>Image preview not available</p>
                  </div>
                </div>
              ) : isPdfFile && doc.file_url ? (
                <div className="text-center">
                  <iframe
                    src={`${doc.file_url}#toolbar=0`}
                    className="w-full h-96 border rounded"
                    title={doc.original_name}
                  />
                  <p className="text-sm text-gray-500 mt-2">
                    PDF preview may not work in all browsers. Use "Open in New Tab" for full viewing.
                  </p>
                </div>
              ) : (
                <div className="text-center text-gray-500 mt-16">
                  <FileText className="h-16 w-16 mx-auto mb-4" />
                  <p className="text-lg font-medium">Preview not available</p>
                  <p className="text-sm">This file type doesn't support preview</p>
                  <div className="mt-4 space-x-2">
                    <Button onClick={handleDownload} variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-2" />
                      Download
                    </Button>
                    <Button onClick={handleOpenInNewTab} variant="outline" size="sm">
                      <ExternalLink className="h-4 w-4 mr-2" />
                      Open in New Tab
                    </Button>
                  </div>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex gap-2 mt-4">
              <Button onClick={handleDownload} variant="outline">
                <Download className="h-4 w-4 mr-2" />
                Download
              </Button>
              <Button onClick={handleOpenInNewTab} variant="outline">
                <ExternalLink className="h-4 w-4 mr-2" />
                Open in New Tab
              </Button>
              <Button variant="outline">
                <Share2 className="h-4 w-4 mr-2" />
                Share
              </Button>
            </div>
          </div>

          {/* Document Metadata */}
          <div className="space-y-6">
            {/* Basic Info */}
            <div>
              <h3 className="font-semibold mb-3">Document Information</h3>
              <div className="space-y-3 text-sm">
                <div>
                  <label className="font-medium text-gray-600">Category:</label>
                  <p className="capitalize">{doc.category?.replace('_', ' ')}</p>
                </div>
                {doc.subcategory && (
                  <div>
                    <label className="font-medium text-gray-600">Subcategory:</label>
                    <p className="capitalize">{doc.subcategory.replace('_', ' ')}</p>
                  </div>
                )}
                <div>
                  <label className="font-medium text-gray-600">File Size:</label>
                  <p>{formatFileSize(doc.file_size)}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-600">File Type:</label>
                  <p>{doc.file_type}</p>
                </div>
                <div>
                  <label className="font-medium text-gray-600">Permissions:</label>
                  <p className="capitalize">{doc.permissions}</p>
                </div>
                {doc.expiry_date && (
                  <div>
                    <label className="font-medium text-gray-600">Expiry Date:</label>
                    <p>{formatDate(doc.expiry_date)}</p>
                  </div>
                )}
              </div>
            </div>

            {/* Description */}
            {doc.description && (
              <div>
                <h3 className="font-semibold mb-3">Description</h3>
                <p className="text-sm text-gray-700">{doc.description}</p>
              </div>
            )}

            {/* Tags */}
            {doc.tags && doc.tags.length > 0 && (
              <div>
                <h3 className="font-semibold mb-3">Tags</h3>
                <div className="flex flex-wrap gap-1">
                  {doc.tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* History */}
            <div>
              <h3 className="font-semibold mb-3">History</h3>
              <div className="space-y-3 text-sm">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-gray-400" />
                  <div>
                    <p className="font-medium">Uploaded</p>
                    <p className="text-gray-600">{formatDate(doc.created_at)}</p>
                    {doc.uploader_name && (
                      <p className="text-gray-500">by {doc.uploader_name}</p>
                    )}
                  </div>
                </div>

                {doc.updated_at !== doc.created_at && (
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-gray-400" />
                    <div>
                      <p className="font-medium">Last Updated</p>
                      <p className="text-gray-600">{formatDate(doc.updated_at)}</p>
                    </div>
                  </div>
                )}

                {doc.reviewed_by && (
                  <div className="flex items-center gap-2">
                    {doc.status === 'approved' ? (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    ) : doc.status === 'rejected' ? (
                      <AlertCircle className="h-4 w-4 text-red-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-blue-500" />
                    )}
                    <div>
                      <p className="font-medium">
                        {doc.status === 'approved' ? 'Approved' : 
                         doc.status === 'rejected' ? 'Rejected' : 'Reviewed'}
                      </p>
                      {doc.reviewer_name && (
                        <p className="text-gray-500">by {doc.reviewer_name}</p>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Version Info */}
            {doc.version > 1 && (
              <div>
                <h3 className="font-semibold mb-3">Version Information</h3>
                <div className="text-sm">
                  <p>Current Version: <span className="font-medium">v{doc.version}</span></p>
                  {doc.parent_document_id && (
                    <p className="text-gray-600 mt-1">
                      This is an updated version of a previous document
                    </p>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}; 