// Administrator Feature Types
import { User } from '@/types/database';

// ===================================
// ADMINISTRATOR CORE TYPES
// ===================================

export interface AdministratorState {
  adminId: string;
  organization: Organization;
  userAccounts: UserAccount[];
  leadPools: LeadPool[];
  systemConfiguration: SystemConfiguration;
  subscription: SubscriptionDetails;
  systemHealth: SystemHealth;
  executiveMetrics: ExecutiveMetrics;
  complianceStatus: ComplianceStatus;
}

export interface Organization {
  id: string;
  name: string;
  subdomain: string;
  industry: string;
  size: 'small' | 'medium' | 'large' | 'enterprise';
  establishedDate: Date;
  headquarters: Address;
  contactInfo: ContactInfo;
  settings: OrganizationSettings;
  branding: BrandingConfiguration;
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface ContactInfo {
  primaryEmail: string;
  primaryPhone: string;
  website?: string;
  linkedIn?: string;
}

export interface OrganizationSettings {
  timezone: string;
  dateFormat: string;
  currency: string;
  language: string;
  fiscalYearStart: string;
  businessHours: BusinessHours[];
  holidays: Holiday[];
}

export interface BusinessHours {
  dayOfWeek: number; // 0-6 (Sunday-Saturday)
  startTime: string; // HH:MM
  endTime: string; // HH:MM
  isWorkingDay: boolean;
}

export interface Holiday {
  name: string;
  date: Date;
  isRecurring: boolean;
  description?: string;
}

export interface BrandingConfiguration {
  logo: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  fontFamily: string;
  customCSS?: string;
}

// ===================================
// LEAD DATA MANAGEMENT TYPES
// ===================================

export interface CSVUploadSession {
  sessionId: string;
  fileName: string;
  fileSize: number;
  uploadProgress: number;
  processingStatus: 'uploading' | 'mapping' | 'processing' | 'completed' | 'error';
  recordCount: number;
  processedRecords: number;
  errorCount: number;
  warnings: string[];
  errors: UploadError[];
  startTime: Date;
  completionTime?: Date;
  estimatedCompletion?: Date;
  processingRate: number; // records per minute
  dataMapping: ColumnMapping[];
  qualityScore: number;
  preview: CSVPreview;
}

export interface UploadError {
  rowNumber: number;
  column: string;
  errorType: 'validation' | 'format' | 'missing' | 'duplicate' | 'invalid';
  message: string;
  value: string;
  suggestedFix?: string;
}

export interface ColumnMapping {
  sourceColumn: string;
  targetField: string;
  dataType: 'string' | 'number' | 'date' | 'boolean' | 'email' | 'phone' | 'address';
  isRequired: boolean;
  transformationRules: TransformationRule[];
  validationRules: ValidationRule[];
  mappingConfidence: number;
  sampleValues: string[];
}

export interface TransformationRule {
  type: 'format' | 'case' | 'trim' | 'replace' | 'calculate' | 'merge';
  parameters: Record<string, any>;
  description: string;
}

export interface ValidationRule {
  type: 'required' | 'format' | 'range' | 'length' | 'pattern' | 'custom';
  parameters: Record<string, any>;
  errorMessage: string;
}

export interface CSVPreview {
  headers: string[];
  sampleRows: string[][];
  columnTypes: Record<string, string>;
  columnStats: Record<string, ColumnStatistics>;
}

export interface ColumnStatistics {
  totalValues: number;
  uniqueValues: number;
  nullValues: number;
  averageLength: number;
  minValue?: string;
  maxValue?: string;
  commonValues: ValueFrequency[];
}

export interface ValueFrequency {
  value: string;
  frequency: number;
  percentage: number;
}

export interface LeadPool {
  id: string;
  name: string;
  description: string;
  source: string;
  uploadDate: Date;
  totalRecords: number;
  activeRecords: number;
  assignedRecords: number;
  completedRecords: number;
  status: 'active' | 'processing' | 'completed' | 'archived';
  priority: 'urgent' | 'high' | 'medium' | 'low';
  tags: string[];
  metadata: PoolMetadata;
  qualityMetrics: DataQualityMetrics;
  analytics: PoolAnalytics;
  accessControl: PoolAccessControl;
}

export interface PoolMetadata {
  sourceType: 'csv_upload' | 'api_import' | 'manual_entry' | 'data_vendor';
  sourceDetails: Record<string, any>;
  processingHistory: ProcessingEvent[];
  dataRetention: RetentionPolicy;
  complianceFlags: string[];
}

export interface ProcessingEvent {
  eventType: 'created' | 'processed' | 'modified' | 'archived';
  timestamp: Date;
  userId: string;
  details: string;
  affectedRecords: number;
}

export interface RetentionPolicy {
  retentionPeriod: number; // days
  archiveAfter: number; // days
  deleteAfter: number; // days
  complianceRequirements: string[];
}

export interface DataQualityMetrics {
  overallScore: number; // 0-100
  completenessScore: number;
  accuracyScore: number;
  consistencyScore: number;
  validityScore: number;
  uniquenessScore: number;
  issueBreakdown: QualityIssueBreakdown;
  improvementSuggestions: string[];
}

export interface QualityIssueBreakdown {
  missingValues: number;
  invalidFormats: number;
  duplicates: number;
  inconsistencies: number;
  outliers: number;
}

export interface PoolAnalytics {
  conversionRate: number;
  averageValue: number;
  totalValue: number;
  processingTime: number; // days
  successRate: number;
  topPerformingAgents: string[];
  geographicDistribution: GeographicData[];
  valueDistribution: ValueDistribution[];
}

export interface GeographicData {
  state: string;
  count: number;
  value: number;
  conversionRate: number;
}

export interface ValueDistribution {
  range: string;
  count: number;
  percentage: number;
}

export interface PoolAccessControl {
  owners: string[];
  managers: string[];
  viewers: string[];
  restrictions: AccessRestriction[];
}

export interface AccessRestriction {
  type: 'geographic' | 'value_based' | 'time_based' | 'role_based';
  criteria: Record<string, any>;
  description: string;
}

// ===================================
// USER ADMINISTRATION TYPES
// ===================================

export interface UserAccount extends User {
  profile: UserProfile;
  permissions: PermissionSet;
  subscription: UserSubscription;
  activity: UserActivity;
  security: SecuritySettings;
  preferences: UserPreferences;
  performance: UserPerformance;
}

export interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  avatar?: string;
  department: string;
  jobTitle: string;
  location: string;
  timezone: string;
  language: string;
  startDate: Date;
  manager?: string;
  directReports: string[];
  skills: string[];
  certifications: string[];
  emergencyContact?: EmergencyContact;
}

export interface EmergencyContact {
  name: string;
  relationship: string;
  phone: string;
  email?: string;
}

export interface PermissionSet {
  role: string;
  permissions: Permission[];
  restrictions: Restriction[];
  inheritedFrom?: string;
  customRules: CustomPermissionRule[];
  effectiveDate: Date;
  expirationDate?: Date;
}

export interface Permission {
  resource: string;
  actions: string[];
  scope: 'global' | 'organization' | 'team' | 'personal';
  conditions?: PermissionCondition[];
}

export interface PermissionCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface Restriction {
  type: 'data_access' | 'feature_access' | 'time_based' | 'location_based';
  description: string;
  criteria: Record<string, any>;
  isActive: boolean;
}

export interface CustomPermissionRule {
  name: string;
  description: string;
  conditions: PermissionCondition[];
  actions: string[];
  priority: number;
}

export interface UserSubscription {
  plan: string;
  features: string[];
  limits: ResourceLimits;
  usage: ResourceUsage;
  billing: BillingInfo;
}

export interface ResourceLimits {
  searchCredits: number;
  storageGB: number;
  apiCalls: number;
  activeLeads: number;
  teamMembers: number;
  customFields: number;
}

export interface ResourceUsage {
  searchCreditsUsed: number;
  storageUsedGB: number;
  apiCallsUsed: number;
  activeLeadsCount: number;
  teamMembersCount: number;
  lastResetDate: Date;
}

export interface BillingInfo {
  billingCycle: 'monthly' | 'quarterly' | 'annual';
  nextBillingDate: Date;
  paymentMethod: string;
  billingAddress: Address;
  invoiceHistory: Invoice[];
}

export interface Invoice {
  id: string;
  date: Date;
  amount: number;
  status: 'paid' | 'pending' | 'overdue' | 'failed';
  items: InvoiceItem[];
  paymentDate?: Date;
}

export interface InvoiceItem {
  description: string;
  quantity: number;
  rate: number;
  amount: number;
}

export interface UserActivity {
  lastLogin: Date;
  loginCount: number;
  sessionDuration: number;
  featuresUsed: FeatureUsage[];
  recentActions: UserAction[];
  performanceMetrics: ActivityMetrics;
}

export interface FeatureUsage {
  feature: string;
  usageCount: number;
  lastUsed: Date;
  averageSessionTime: number;
}

export interface UserAction {
  actionType: string;
  timestamp: Date;
  resource: string;
  details: Record<string, any>;
  ipAddress: string;
  userAgent: string;
}

export interface ActivityMetrics {
  productivityScore: number;
  engagementLevel: 'low' | 'medium' | 'high';
  featureAdoptionRate: number;
  helpDeskTickets: number;
}

export interface SecuritySettings {
  mfaEnabled: boolean;
  mfaMethod: 'app' | 'sms' | 'email';
  passwordLastChanged: Date;
  securityQuestions: SecurityQuestion[];
  trustedDevices: TrustedDevice[];
  securityAlerts: SecurityAlert[];
  ipRestrictions: string[];
}

export interface SecurityQuestion {
  question: string;
  answerHash: string;
  setDate: Date;
}

export interface TrustedDevice {
  deviceId: string;
  deviceName: string;
  deviceType: string;
  lastUsed: Date;
  isActive: boolean;
}

export interface SecurityAlert {
  alertType: 'suspicious_login' | 'password_change' | 'permission_change' | 'data_access';
  timestamp: Date;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  isResolved: boolean;
}

export interface UserPreferences {
  notifications: NotificationPreferences;
  dashboard: DashboardPreferences;
  communication: CommunicationPreferences;
  privacy: PrivacyPreferences;
  accessibility: AccessibilityPreferences;
}

export interface NotificationPreferences {
  email: boolean;
  sms: boolean;
  push: boolean;
  inApp: boolean;
  frequency: 'real_time' | 'hourly' | 'daily' | 'weekly';
  types: string[];
  quietHours: QuietHours;
}

export interface QuietHours {
  enabled: boolean;
  startTime: string;
  endTime: string;
  timezone: string;
}

export interface DashboardPreferences {
  layout: string;
  widgets: DashboardWidget[];
  theme: 'light' | 'dark' | 'auto';
  refreshRate: number;
  defaultView: string;
}

export interface DashboardWidget {
  id: string;
  type: string;
  position: Position;
  size: Size;
  configuration: Record<string, any>;
  isVisible: boolean;
}

export interface Position {
  x: number;
  y: number;
}

export interface Size {
  width: number;
  height: number;
}

export interface CommunicationPreferences {
  preferredChannels: string[];
  responseTimeExpectation: number;
  language: string;
  timezone: string;
  workingHours: BusinessHours[];
}

export interface PrivacyPreferences {
  profileVisibility: 'public' | 'organization' | 'team' | 'private';
  activityTracking: boolean;
  locationSharing: boolean;
  dataCollection: boolean;
  marketingCommunications: boolean;
}

export interface AccessibilityPreferences {
  screenReader: boolean;
  highContrast: boolean;
  largeText: boolean;
  keyboardNavigation: boolean;
  motionReduction: boolean;
}

export interface UserPerformance {
  overallRating: number;
  goals: Goal[];
  achievements: Achievement[];
  feedback: FeedbackItem[];
  developmentPlan: DevelopmentPlan;
  performanceHistory: PerformanceReview[];
}

export interface Goal {
  id: string;
  title: string;
  description: string;
  category: string;
  target: number;
  current: number;
  unit: string;
  deadline: Date;
  status: 'not_started' | 'in_progress' | 'completed' | 'overdue';
  priority: 'low' | 'medium' | 'high';
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  category: string;
  achievedDate: Date;
  badge?: string;
  points: number;
}

export interface FeedbackItem {
  id: string;
  providerId: string;
  providerName: string;
  type: 'praise' | 'constructive' | 'coaching' | 'recognition';
  content: string;
  date: Date;
  isPublic: boolean;
  rating?: number;
}

export interface DevelopmentPlan {
  id: string;
  objectives: DevelopmentObjective[];
  timeline: string;
  mentor?: string;
  budget: number;
  status: 'draft' | 'approved' | 'in_progress' | 'completed';
  progress: number;
}

export interface DevelopmentObjective {
  objective: string;
  skills: string[];
  activities: DevelopmentActivity[];
  measurements: string[];
  deadline: Date;
  status: string;
}

export interface DevelopmentActivity {
  activity: string;
  type: 'training' | 'mentoring' | 'project' | 'certification' | 'conference';
  provider?: string;
  cost: number;
  duration: string;
  scheduledDate?: Date;
  completionDate?: Date;
  outcome?: string;
}

export interface PerformanceReview {
  id: string;
  reviewPeriod: string;
  reviewDate: Date;
  reviewer: string;
  overallRating: number;
  competencyRatings: CompetencyRating[];
  strengths: string[];
  improvementAreas: string[];
  goals: string[];
  comments: string;
  employeeComments?: string;
}

export interface CompetencyRating {
  competency: string;
  rating: number;
  comments: string;
  developmentNeeds: string[];
}

// ===================================
// TEAM STRUCTURE TYPES
// ===================================

export interface TeamStructure {
  teams: Team[];
  hierarchies: Hierarchy[];
  reportingLines: ReportingLine[];
  organizationalChart: OrganizationalNode[];
}

export interface Team {
  id: string;
  name: string;
  description: string;
  type: 'department' | 'project' | 'functional' | 'cross_functional';
  leaderId: string;
  memberIds: string[];
  parentTeamId?: string;
  childTeamIds: string[];
  location?: string;
  budget?: number;
  goals: TeamGoal[];
  performance: TeamPerformance;
  resources: TeamResource[];
}

export interface TeamGoal {
  id: string;
  title: string;
  description: string;
  metric: string;
  target: number;
  current: number;
  deadline: Date;
  status: string;
  assignedTo: string[];
}

export interface TeamPerformance {
  overallScore: number;
  metrics: PerformanceMetric[];
  trends: PerformanceTrend[];
  rankings: TeamRanking;
  benchmarks: Benchmark[];
}

export interface PerformanceMetric {
  metric: string;
  value: number;
  target: number;
  unit: string;
  trend: 'up' | 'down' | 'stable';
  period: string;
}

export interface PerformanceTrend {
  metric: string;
  data: DataPoint[];
  trendLine: number[];
  seasonality?: SeasonalPattern;
}

export interface DataPoint {
  date: Date;
  value: number;
}

export interface SeasonalPattern {
  pattern: string;
  strength: number;
  peaks: string[];
  troughs: string[];
}

export interface TeamRanking {
  overall: number;
  category: Record<string, number>;
  percentile: number;
  comparison: string;
}

export interface Benchmark {
  metric: string;
  internal: number;
  industry: number;
  bestInClass: number;
  target: number;
}

export interface TeamResource {
  type: 'human' | 'financial' | 'technical' | 'physical';
  name: string;
  allocation: number;
  utilization: number;
  cost?: number;
  availability: string;
}

export interface Hierarchy {
  level: number;
  name: string;
  description: string;
  roles: string[];
  responsibilities: string[];
  reportingTo?: number;
  spansOfControl: number[];
}

export interface ReportingLine {
  managerId: string;
  directReportId: string;
  relationshipType: 'direct' | 'dotted' | 'matrix' | 'functional';
  effectiveDate: Date;
  endDate?: Date;
  responsibilities: string[];
}

export interface OrganizationalNode {
  id: string;
  name: string;
  title: string;
  type: 'person' | 'position' | 'team' | 'department';
  parentId?: string;
  children: string[];
  level: number;
  span: number;
}

// ===================================
// SUBSCRIPTION MANAGEMENT TYPES
// ===================================

export interface SubscriptionDetails {
  plan: SubscriptionPlan;
  billing: BillingDetails;
  usage: OrganizationUsage;
  limits: OrganizationLimits;
  addOns: AddOn[];
  history: SubscriptionEvent[];
}

export interface SubscriptionPlan {
  id: string;
  name: string;
  tier: 'basic' | 'professional' | 'enterprise' | 'custom';
  price: number;
  billingCycle: 'monthly' | 'quarterly' | 'annual';
  features: PlanFeature[];
  limits: PlanLimits;
  supportLevel: 'basic' | 'standard' | 'premium' | 'dedicated';
}

export interface PlanFeature {
  feature: string;
  included: boolean;
  limit?: number;
  description: string;
}

export interface PlanLimits {
  users: number;
  searchCredits: number;
  storageGB: number;
  apiCalls: number;
  customFields: number;
  integrations: number;
}

export interface BillingDetails {
  currentPeriodStart: Date;
  currentPeriodEnd: Date;
  nextBillingDate: Date;
  paymentMethod: PaymentMethod;
  billingAddress: Address;
  taxRate: number;
  discounts: Discount[];
  invoices: Invoice[];
}

export interface PaymentMethod {
  type: 'credit_card' | 'bank_transfer' | 'check' | 'wire';
  details: Record<string, string>;
  isDefault: boolean;
  expirationDate?: Date;
}

export interface Discount {
  code: string;
  type: 'percentage' | 'fixed_amount';
  value: number;
  description: string;
  validFrom: Date;
  validTo: Date;
  isActive: boolean;
}

export interface OrganizationUsage {
  currentPeriod: UsagePeriod;
  historical: UsagePeriod[];
  projections: UsageProjection[];
  alerts: UsageAlert[];
}

export interface UsagePeriod {
  periodStart: Date;
  periodEnd: Date;
  users: UsageMetric;
  searchCredits: UsageMetric;
  storage: UsageMetric;
  apiCalls: UsageMetric;
  features: FeatureUsageMetric[];
}

export interface UsageMetric {
  used: number;
  limit: number;
  percentage: number;
  trend: 'increasing' | 'decreasing' | 'stable';
}

export interface FeatureUsageMetric {
  feature: string;
  usageCount: number;
  userCount: number;
  averageUsage: number;
  peakUsage: number;
}

export interface UsageProjection {
  metric: string;
  projectedUsage: number;
  projectedDate: Date;
  confidence: number;
  factors: string[];
}

export interface UsageAlert {
  metric: string;
  threshold: number;
  currentUsage: number;
  alertLevel: 'warning' | 'critical';
  triggered: Date;
  isActive: boolean;
}

export interface OrganizationLimits {
  hard: ResourceLimits;
  soft: ResourceLimits;
  overagePolicy: OveragePolicy;
  notifications: LimitNotification[];
}

export interface OveragePolicy {
  allowOverage: boolean;
  overageRate: number;
  overageLimit?: number;
  autoUpgrade: boolean;
  notificationThresholds: number[];
}

export interface LimitNotification {
  metric: string;
  threshold: number;
  recipients: string[];
  method: 'email' | 'sms' | 'webhook';
  isActive: boolean;
}

export interface AddOn {
  id: string;
  name: string;
  type: 'feature' | 'capacity' | 'service';
  price: number;
  billingCycle: string;
  quantity: number;
  isActive: boolean;
  addedDate: Date;
}

export interface SubscriptionEvent {
  eventType: 'upgrade' | 'downgrade' | 'renewal' | 'cancellation' | 'add_on' | 'payment';
  timestamp: Date;
  description: string;
  amount?: number;
  previousPlan?: string;
  newPlan?: string;
  userId: string;
}

// Export main types for external use
export * from './systemConfiguration';
export * from './analytics';
export * from './monitoring'; 