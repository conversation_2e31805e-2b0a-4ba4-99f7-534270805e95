import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { 
  Wand2, 
  AlertCircle, 
  CheckCircle, 
  Info,
  ArrowLeft,
  Play,
  Settings
} from 'lucide-react'

interface FieldMapping {
  standardField: string
  csvHeader: string | null
  isRequired: boolean
  confidence: number
  reason: string
}

interface CSVFieldMapperProps {
  detectedHeaders: string[]
  stateCode: string
  sampleData: any[]
  fieldAnalysis?: Record<string, {
    suggestedType: string
    confidence: number
    patterns: string[]
    samples: string[]
  }>
  onMappingComplete: (mappings: FieldMapping[]) => void
  onBack: () => void
}

// Standard fields for asset recovery - Enhanced with more comprehensive mapping
const STANDARD_FIELDS = [
  { field_name: 'property_id', is_required: false, description: 'Unique property identifier' },
  { field_name: 'owner_name', is_required: true, description: 'Owner full name' },
  { field_name: 'owner_first_name', is_required: false, description: 'Owner first name' },
  { field_name: 'owner_last_name', is_required: false, description: 'Owner last name' },
  { field_name: 'owner_business_name', is_required: false, description: 'Business/company name' },
  { field_name: 'amount', is_required: true, description: 'Unclaimed amount or cash reported' },
  { field_name: 'property_type', is_required: false, description: 'Type of unclaimed property' },
  { field_name: 'report_date', is_required: false, description: 'Date reported to state' },
  { field_name: 'owner_address', is_required: false, description: 'Owner street address' },
  { field_name: 'owner_city', is_required: false, description: 'Owner city' },
  { field_name: 'owner_state', is_required: false, description: 'Owner state' },
  { field_name: 'owner_zip', is_required: false, description: 'Owner ZIP code' },
  { field_name: 'holder_name', is_required: false, description: 'Holder/company name' },
  { field_name: 'holder_address', is_required: false, description: 'Holder address' },
  { field_name: 'holder_city', is_required: false, description: 'Holder city' },
  { field_name: 'holder_state', is_required: false, description: 'Holder state' },
  { field_name: 'holder_zip', is_required: false, description: 'Holder ZIP code' },
  { field_name: 'shares_reported', is_required: false, description: 'Number of shares reported' },
  { field_name: 'securities_name', is_required: false, description: 'Name of securities' },
  { field_name: 'cusip', is_required: false, description: 'CUSIP identifier' },
  { field_name: 'email', is_required: false, description: 'Contact email address' },
  { field_name: 'phone', is_required: false, description: 'Contact phone number' },
]

export function CSVFieldMapper({ 
  detectedHeaders, 
  stateCode, 
  sampleData, 
  fieldAnalysis, 
  onMappingComplete, 
  onBack 
}: CSVFieldMapperProps) {
  console.log('=== CSVFieldMapper COMPONENT STARTED ===')
  console.log('Props received:', {
    detectedHeaders,
    stateCode,
    sampleData,
    fieldAnalysis: !!fieldAnalysis,
    onMappingComplete: !!onMappingComplete,
    onBack: !!onBack
  })
  console.log('Sample data details:', {
    sampleDataExists: !!sampleData,
    sampleDataLength: sampleData?.length,
    sampleDataFirstRow: sampleData?.[0],
    detectedHeadersLength: detectedHeaders?.length
  })
  
  const [mappings, setMappings] = useState<FieldMapping[]>([])
  const [unmappedHeaders, setUnmappedHeaders] = useState<string[]>(detectedHeaders)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    console.log('CSVFieldMapper useEffect triggered')
    initializeMappings()
  }, [detectedHeaders])

  const initializeMappings = () => {
    try {
      setIsLoading(true)
      
      // Initialize mappings with smart detection
      const initialMappings: FieldMapping[] = STANDARD_FIELDS.map(field => {
        const bestMatch = findBestHeaderMatch(field.field_name, detectedHeaders)
        return {
          standardField: field.field_name,
          csvHeader: bestMatch,
          isRequired: field.is_required,
          confidence: 0,
          reason: ''
        }
      })

      setMappings(initialMappings)
      updateUnmappedHeaders(initialMappings)
    } catch (error) {
      console.error('Error initializing mappings:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const findBestHeaderMatch = (standardField: string, headers: string[]): string | null => {
    // Enhanced matching logic using field analysis
    if (fieldAnalysis) {
      // First, look for headers that were analyzed to match this standard field
      const analysisMatch = Object.entries(fieldAnalysis).find(([header, analysis]) => 
        analysis.suggestedType === standardField && analysis.confidence > 60
      )
      
      if (analysisMatch) {
        console.log(`Field analysis match: ${standardField} -> ${analysisMatch[0]} (confidence: ${analysisMatch[1].confidence}%)`)
        return analysisMatch[0]
      }
    }
    
    // Enhanced field mappings with comprehensive patterns for unclaimed property data
    const fieldPatterns: Record<string, string[]> = {
      'property_id': ['property_id', 'prop_id', 'id', 'claim_id', 'record_id', 'ref_id', 'reference', 'number'],
      'owner_name': ['owner_name', 'name', 'claimant', 'beneficiary', 'account_holder', 'customer', 'full_name'],
      'owner_first_name': ['first_name', 'fname', 'first', 'given_name', 'owner_first', 'firstname'],
      'owner_last_name': ['last_name', 'lname', 'last', 'surname', 'family_name', 'owner_last', 'lastname'],
      'owner_business_name': ['business_name', 'company', 'corporation', 'entity', 'organization', 'business', 'corp'],
      'amount': ['amount', 'cash_reported', 'balance', 'value', 'dollar_amount', 'current_cash_balance', 'cash', 'total'],
      'property_type': ['property_type', 'type', 'category', 'asset_type', 'account_type', 'kind'],
      'holder_name': ['holder_name', 'company', 'institution', 'bank', 'financial_institution', 'holder', 'firm'],
      'owner_address': ['owner_street', 'address', 'street', 'addr', 'street_1', 'street_address', 'owner_address', 'owner_street_1'],
      'owner_city': ['owner_city', 'city', 'town', 'municipality'],
      'owner_state': ['owner_state', 'state', 'st', 'province', 'region'],
      'owner_zip': ['owner_zip', 'zip', 'postal_code', 'zipcode', 'postal', 'zip_code'],
      'holder_address': ['holder_street', 'holder_address', 'company_address', 'holder_street_1'],
      'holder_city': ['holder_city', 'company_city'],
      'holder_state': ['holder_state', 'company_state'],
      'holder_zip': ['holder_zip', 'company_zip', 'company_postal'],
      'report_date': ['report_date', 'date', 'reported_date', 'created_date', 'date_reported'],
      'shares_reported': ['shares_reported', 'shares', 'units', 'quantity', 'share_count'],
      'securities_name': ['name_of_securities', 'securities', 'stock_name', 'security_name', 'securities_reported'],
      'cusip': ['cusip', 'security_id', 'stock_id', 'identifier'],
      'email': ['email', 'mail', 'email_address', 'e_mail'],
      'phone': ['phone', 'telephone', 'tel', 'mobile', 'phone_number']
    }
    
    const patterns = fieldPatterns[standardField] || []
    
    // Enhanced scoring system for better accuracy
    let bestMatch = ''
    let bestScore = 0
    
    headers.forEach(header => {
      const headerLower = header.toLowerCase().replace(/[_\s\-]/g, '')
      
      patterns.forEach(pattern => {
        const patternLower = pattern.toLowerCase().replace(/[_\s\-]/g, '')
        
        // Exact match gets highest score
        if (headerLower === patternLower) {
          if (100 > bestScore) {
            bestScore = 100
            bestMatch = header
          }
        }
        // Perfect substring match
        else if (headerLower.includes(patternLower) && patternLower.length > 3) {
          const score = Math.round((patternLower.length / headerLower.length) * 95)
          if (score > bestScore) {
            bestScore = score
            bestMatch = header
          }
        }
        // Reverse substring match (pattern contains header)
        else if (patternLower.includes(headerLower) && headerLower.length > 3) {
          const score = Math.round((headerLower.length / patternLower.length) * 90)
          if (score > bestScore) {
            bestScore = score
            bestMatch = header
          }
        }
        // Fuzzy matching for similar words
        else {
          const similarity = calculateStringSimilarity(headerLower, patternLower)
          const score = Math.round(similarity * 80)
          if (score > 50 && score > bestScore) {
            bestScore = score
            bestMatch = header
          }
        }
      })
      
      // Also check if the standard field name is contained in the header
      const standardLower = standardField.toLowerCase().replace(/[_\s\-]/g, '')
      if (headerLower.includes(standardLower) && standardLower.length > 3) {
        const score = Math.round((standardLower.length / headerLower.length) * 85)
        if (score > bestScore) {
          bestScore = score
          bestMatch = header
        }
      }
    })
    
    // Only return matches with reasonable confidence
    return bestScore > 40 ? bestMatch : null
  }

  // Helper function to calculate string similarity
  const calculateStringSimilarity = (str1: string, str2: string): number => {
    const longer = str1.length > str2.length ? str1 : str2
    const shorter = str1.length > str2.length ? str2 : str1
    
    if (longer.length === 0) return 1.0
    
    const editDistance = levenshteinDistance(longer, shorter)
    return (longer.length - editDistance) / longer.length
  }

  // Levenshtein distance calculation
  const levenshteinDistance = (str1: string, str2: string): number => {
    const matrix = []
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i]
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1]
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          )
        }
      }
    }
    
    return matrix[str2.length][str1.length]
  }

  const updateMapping = (standardField: string, csvHeader: string | null) => {
    const newMappings = mappings.map(mapping => 
      mapping.standardField === standardField 
        ? { ...mapping, csvHeader }
        : mapping
    )
    setMappings(newMappings)
    updateUnmappedHeaders(newMappings)
  }

  const updateUnmappedHeaders = (currentMappings: FieldMapping[]) => {
    const mappedHeaders = currentMappings
      .map(m => m.csvHeader)
      .filter(h => h !== null) as string[]
    
    setUnmappedHeaders(detectedHeaders.filter(h => !mappedHeaders.includes(h)))
  }

  const autoDetectMappings = () => {
    console.log('Starting auto-detection with field analysis:', fieldAnalysis)
    
    const autoMappings = STANDARD_FIELDS.map(field => {
      let bestMatch = null
      let confidence = 0
      let reason = ''
      
      // First try to use field analysis if available
      if (fieldAnalysis) {
        const analysisMatch = Object.entries(fieldAnalysis).find(([header, analysis]) => 
          analysis.suggestedType === field.field_name && analysis.confidence > 50
        )
        
        if (analysisMatch) {
          bestMatch = analysisMatch[0]
          confidence = analysisMatch[1].confidence
          reason = `Field analysis (${confidence}% confidence)`
          console.log(`Auto-mapped ${field.field_name} -> ${bestMatch} via analysis (${confidence}%)`)
        }
      }
      
      // If no analysis match, use pattern matching
      if (!bestMatch) {
        bestMatch = findBestHeaderMatch(field.field_name, detectedHeaders)
        if (bestMatch) {
          // Estimate confidence based on match quality
          const headerLower = bestMatch.toLowerCase().replace(/[_\s\-]/g, '')
          const fieldLower = field.field_name.toLowerCase().replace(/[_\s\-]/g, '')
          
          if (headerLower === fieldLower) {
            confidence = 95
            reason = 'Exact name match'
          } else if (headerLower.includes(fieldLower) || fieldLower.includes(headerLower)) {
            confidence = 80
            reason = 'Partial name match'
          } else {
            confidence = 60
            reason = 'Pattern match'
          }
          
          console.log(`Auto-mapped ${field.field_name} -> ${bestMatch} via pattern matching (${confidence}%)`)
        }
      }
      
      return {
        standardField: field.field_name,
        csvHeader: bestMatch,
        isRequired: field.is_required,
        confidence,
        reason
      }
    })

    // Log mapping summary
    const mappedCount = autoMappings.filter(m => m.csvHeader).length
    const highConfidenceCount = autoMappings.filter(m => m.confidence > 80).length
    console.log(`Auto-detection complete: ${mappedCount}/${STANDARD_FIELDS.length} fields mapped, ${highConfidenceCount} high confidence`)
    
    setMappings(autoMappings)
    updateUnmappedHeaders(autoMappings)
  }

  const validateMappings = (): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []
    const requiredFields = STANDARD_FIELDS.filter(f => f.is_required)
    
    requiredFields.forEach(field => {
      const mapping = mappings.find(m => m.standardField === field.field_name)
      if (!mapping?.csvHeader) {
        errors.push(`Required field "${field.field_name}" must be mapped`)
      }
    })

    return { isValid: errors.length === 0, errors }
  }

  const handleContinue = () => {
    const validation = validateMappings()
    if (!validation.isValid) {
      alert(`Please fix the following errors:\n${validation.errors.join('\n')}`)
      return
    }

    onMappingComplete(mappings)
  }

  const getSampleValue = (csvHeader: string): string => {
    if (!sampleData || sampleData.length === 0) return 'No sample data'
    
    const firstRow = sampleData[0]
    const value = firstRow[csvHeader]
    return value ? String(value).substring(0, 50) : 'Empty'
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p className="text-gray-600">Loading field mapping interface...</p>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Map CSV Fields</h2>
          <p className="text-gray-600">
            Map your CSV columns to standard fields for {stateCode}
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={onBack}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <Button onClick={autoDetectMappings}>
            <Wand2 className="h-4 w-4 mr-2" />
            Auto-Detect
          </Button>
          <Button onClick={handleContinue}>
            <Play className="h-4 w-4 mr-2" />
            Continue
          </Button>
        </div>
      </div>

      {/* Mapping Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Info className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm font-medium">CSV Headers</p>
                <p className="text-2xl font-bold">{detectedHeaders.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm font-medium">Mapped</p>
                <p className="text-2xl font-bold">
                  {mappings.filter(m => m.csvHeader).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertCircle className="h-5 w-5 text-orange-500" />
              <div>
                <p className="text-sm font-medium">Required</p>
                <p className="text-2xl font-bold">
                  {mappings.filter(m => m.isRequired && !m.csvHeader).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm font-medium">Unmapped</p>
                <p className="text-2xl font-bold">{unmappedHeaders.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Field Mapping Table */}
      <Card>
        <CardHeader>
          <CardTitle>Field Mappings</CardTitle>
          <CardDescription>
            Map each standard field to a column in your CSV file
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Standard Field</TableHead>
                <TableHead>CSV Column</TableHead>
                <TableHead>Sample Value</TableHead>
                <TableHead>Confidence</TableHead>
                <TableHead>Required</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {mappings.map((mapping) => {
                const fieldInfo = STANDARD_FIELDS.find(f => f.field_name === mapping.standardField)
                const analysis = mapping.csvHeader && fieldAnalysis ? fieldAnalysis[mapping.csvHeader] : null
                const confidence = mapping.confidence || analysis?.confidence || 0
                
                // Confidence color coding
                const getConfidenceColor = (conf: number) => {
                  if (conf >= 80) return 'text-green-600 bg-green-50'
                  if (conf >= 60) return 'text-yellow-600 bg-yellow-50'
                  if (conf >= 40) return 'text-orange-600 bg-orange-50'
                  return 'text-red-600 bg-red-50'
                }
                
                const getConfidenceIcon = (conf: number) => {
                  if (conf >= 80) return '●'
                  if (conf >= 60) return '●'
                  if (conf >= 40) return '●'
                  return '●'
                }
                
                return (
                  <TableRow key={mapping.standardField} className={mapping.isRequired && !mapping.csvHeader ? 'bg-red-50' : ''}>
                    <TableCell>
                      <div>
                        <p className="font-medium">{mapping.standardField.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}</p>
                        <p className="text-sm text-gray-500">{fieldInfo?.description}</p>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Select
                        value={mapping.csvHeader || 'none'}
                        onValueChange={(value) => updateMapping(mapping.standardField, value === 'none' ? null : value)}
                      >
                        <SelectTrigger className="w-full">
                          <SelectValue placeholder="Select column..." />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="none">
                            <span className="text-gray-500">-- No mapping --</span>
                          </SelectItem>
                          {detectedHeaders.map((header) => {
                            const headerAnalysis = fieldAnalysis?.[header]
                            const headerConfidence = headerAnalysis?.confidence || 0
                            const isRecommended = headerAnalysis?.suggestedType === mapping.standardField && headerConfidence > 60
                            
                            return (
                              <SelectItem key={header} value={header}>
                                <div className="flex items-center justify-between w-full">
                                  <span className={isRecommended ? 'font-medium text-blue-600' : ''}>{header}</span>
                                  {isRecommended && (
                                    <Badge variant="secondary" className="ml-2 text-xs">
                                      {headerConfidence}%
                                    </Badge>
                                  )}
                                </div>
                              </SelectItem>
                            )
                          })}
                        </SelectContent>
                      </Select>
                      {mapping.reason && (
                        <p className="text-xs text-gray-500 mt-1">{mapping.reason}</p>
                      )}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs">
                        {mapping.csvHeader ? (
                          <div>
                            <p className="text-sm truncate">{getSampleValue(mapping.csvHeader)}</p>
                            {analysis && analysis.patterns.length > 0 && (
                              <p className="text-xs text-gray-500 mt-1">
                                {analysis.patterns[0]}
                              </p>
                            )}
                          </div>
                        ) : (
                          <span className="text-gray-400 text-sm">No mapping</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      {confidence > 0 ? (
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getConfidenceColor(confidence)}`}>
                            <span className="mr-1">{getConfidenceIcon(confidence)}</span>
                            {confidence}%
                          </span>
                        </div>
                      ) : (
                        <span className="text-gray-400 text-sm">--</span>
                      )}
                    </TableCell>
                    <TableCell>
                      {mapping.isRequired ? (
                        <Badge variant={mapping.csvHeader ? "default" : "destructive"}>
                          Required
                        </Badge>
                      ) : (
                        <Badge variant="secondary">Optional</Badge>
                      )}
                    </TableCell>
                  </TableRow>
                )
              })}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Auto-Mapping Summary */}
      {mappings.some(m => m.confidence > 0) && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wand2 className="h-5 w-5 mr-2" />
              Auto-Mapping Results
            </CardTitle>
            <CardDescription>
              Summary of automatic field detection and mapping confidence
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
              <div className="text-center p-4 bg-green-50 rounded-lg">
                <p className="text-2xl font-bold text-green-600">
                  {mappings.filter(m => m.confidence >= 80).length}
                </p>
                <p className="text-sm text-green-700">High Confidence (≥80%)</p>
              </div>
              <div className="text-center p-4 bg-yellow-50 rounded-lg">
                <p className="text-2xl font-bold text-yellow-600">
                  {mappings.filter(m => m.confidence >= 60 && m.confidence < 80).length}
                </p>
                <p className="text-sm text-yellow-700">Medium Confidence (60-79%)</p>
              </div>
              <div className="text-center p-4 bg-red-50 rounded-lg">
                <p className="text-2xl font-bold text-red-600">
                  {mappings.filter(m => m.confidence > 0 && m.confidence < 60).length}
                </p>
                <p className="text-sm text-red-700">Low Confidence (&lt;60%)</p>
              </div>
            </div>
            
            {/* High confidence mappings */}
            {mappings.filter(m => m.confidence >= 80).length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-green-700 mb-2">✓ High Confidence Mappings:</h4>
                <div className="flex flex-wrap gap-2">
                  {mappings.filter(m => m.confidence >= 80).map(m => (
                    <Badge key={m.standardField} variant="secondary" className="bg-green-100 text-green-800">
                      {m.standardField} → {m.csvHeader} ({m.confidence}%)
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* Medium confidence mappings */}
            {mappings.filter(m => m.confidence >= 60 && m.confidence < 80).length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-yellow-700 mb-2">⚠ Medium Confidence Mappings (Review Recommended):</h4>
                <div className="flex flex-wrap gap-2">
                  {mappings.filter(m => m.confidence >= 60 && m.confidence < 80).map(m => (
                    <Badge key={m.standardField} variant="secondary" className="bg-yellow-100 text-yellow-800">
                      {m.standardField} → {m.csvHeader} ({m.confidence}%)
                    </Badge>
                  ))}
                </div>
              </div>
            )}
            
            {/* Low confidence mappings */}
            {mappings.filter(m => m.confidence > 0 && m.confidence < 60).length > 0 && (
              <div className="mb-4">
                <h4 className="font-medium text-red-700 mb-2">⚠ Low Confidence Mappings (Manual Review Required):</h4>
                <div className="flex flex-wrap gap-2">
                  {mappings.filter(m => m.confidence > 0 && m.confidence < 60).map(m => (
                    <Badge key={m.standardField} variant="secondary" className="bg-red-100 text-red-800">
                      {m.standardField} → {m.csvHeader} ({m.confidence}%)
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Unmapped Headers */}
      {unmappedHeaders.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertCircle className="h-5 w-5 mr-2 text-orange-500" />
              Unmapped CSV Headers
            </CardTitle>
            <CardDescription>
              These columns from your CSV file are not mapped to any standard field
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2">
              {unmappedHeaders.map(header => {
                const analysis = fieldAnalysis?.[header]
                return (
                  <div key={header} className="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                    <span className="font-medium">{header}</span>
                    {analysis && (
                      <div className="text-xs text-gray-600">
                        <span>({analysis.suggestedType}, {analysis.confidence}%)</span>
                      </div>
                    )}
                  </div>
                )
              })}
            </div>
            <p className="text-sm text-gray-600 mt-3">
              💡 Tip: These columns will be ignored during import. If any contain important data, 
              you can map them to standard fields using the dropdowns above.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Data Preview */}
      <Card>
        <CardHeader>
          <CardTitle>Data Preview</CardTitle>
          <CardDescription>
            Preview of the first {Math.min(sampleData.length, 5)} rows from your CSV file
          </CardDescription>
        </CardHeader>
        <CardContent>
          {sampleData && sampleData.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    {detectedHeaders.map(header => (
                      <TableHead key={header} className="min-w-[120px]">
                        {header}
                      </TableHead>
                    ))}
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {sampleData.slice(0, 5).map((row, index) => (
                    <TableRow key={index}>
                      {detectedHeaders.map(header => (
                        <TableCell key={header} className="max-w-[200px] truncate">
                          {row[header] || '-'}
                        </TableCell>
                      ))}
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              No sample data available
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
} 