// AssetHunterPro Lead Assignment Dashboard
// Comprehensive lead assignment interface for admins and senior agents

import React, { useState, useEffect, useMemo } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { 
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Dialog, 
  DialogContent, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger,
  DialogFooter 
} from '@/components/ui/dialog';
import { Separator } from '@/components/ui/separator';
import { 
  Users, 
  UserPlus, 
  Target, 
  TrendingUp, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Plus,
  Filter,
  Search,
  BarChart3,
  Zap,
  Brain,
  Settings,
  Eye,
  Edit,
  PhoneCall,
  Mail,
  FileText,
  Calendar,
  DollarSign,
  MapPin,
  Building,
  Star,
  Loader,
  RefreshCw
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { FEATURE_FLAGS } from '@/config/features';
import type {
  Agent,
  AssignmentCriteria,
  AssignmentRecommendation,
  WorkloadAnalysis,
  LeadStatus,
  UserRole
} from '@/types/lead-assignment';
import { ROLE_PERMISSIONS } from '@/types/lead-assignment';
import { LeadAssignmentService } from '@/services/leadAssignmentService';
import { LeadTrackingService } from '@/services/leadTrackingService';

interface LeadAssignmentDashboardProps {
  uploadSessionId?: string;
  leads?: any[];
  onAssignmentComplete?: (assignments: any[]) => void;
}

export const LeadAssignmentDashboard: React.FC<LeadAssignmentDashboardProps> = ({
  uploadSessionId,
  leads = [],
  onAssignmentComplete
}) => {
  const { user } = useAuth();
  const [selectedLeads, setSelectedLeads] = useState<string[]>([]);
  const [availableAgents, setAvailableAgents] = useState<Agent[]>([]);
  const [assignmentRecommendations, setAssignmentRecommendations] = useState<AssignmentRecommendation[]>([]);
  const [workloadAnalysis, setWorkloadAnalysis] = useState<WorkloadAnalysis[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [assignmentCriteria, setAssignmentCriteria] = useState<AssignmentCriteria>({});
  const [distributionMethod, setDistributionMethod] = useState<'round_robin' | 'workload_based' | 'expertise_based'>('workload_based');
  const [showAssignmentPreview, setShowAssignmentPreview] = useState(false);

  const assignmentService = useMemo(() => new LeadAssignmentService(), []);
  const trackingService = useMemo(() => new LeadTrackingService(), []);

  // Check if user has permission to assign leads
  const canAssignLeads = user && ROLE_PERMISSIONS[user.role as UserRole]?.canAssignLeads;
  const canBulkAssign = user && ROLE_PERMISSIONS[user.role as UserRole]?.canBulkAssign;

  useEffect(() => {
    if (canAssignLeads) {
      loadInitialData();
    }
  }, [canAssignLeads]);

  const loadInitialData = async () => {
    setIsLoading(true);
    try {
      // Load available agents
      const agents = await loadAvailableAgents();
      setAvailableAgents(agents);

      // Load workload analysis
      const workload = await assignmentService.analyzeWorkloads();
      setWorkloadAnalysis(workload);

      // Generate recommendations for selected leads
      if (selectedLeads.length > 0) {
        const recommendations = await assignmentService.calculateOptimalAssignment(
          selectedLeads,
          agents,
          assignmentCriteria
        );
        setAssignmentRecommendations(recommendations);
      }
    } catch (error) {
      console.error('Error loading assignment data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const loadAvailableAgents = async (): Promise<Agent[]> => {
    // Mock data for now - in real implementation, this would fetch from API
    return [
      {
        id: 'agent-1',
        name: 'John Smith',
        role: 'junior_agent',
        email: '<EMAIL>',
        activeLeads: 15,
        maxLeads: 25,
        stateExperience: ['CA', 'NV', 'AZ'],
        propertyTypeExperience: ['stocks', 'bonds'],
        successRate: 0.78,
        experienceLevel: 'junior',
        workloadScore: 60,
        isActive: true
      },
      {
        id: 'agent-2',
        name: 'Sarah Johnson',
        role: 'senior_agent',
        email: '<EMAIL>',
        activeLeads: 30,
        maxLeads: 40,
        stateExperience: ['TX', 'FL', 'NY'],
        propertyTypeExperience: ['real_estate', 'business_assets'],
        successRate: 0.92,
        experienceLevel: 'senior',
        workloadScore: 75,
        isActive: true
      },
      {
        id: 'agent-3',
        name: 'Mike Rodriguez',
        role: 'junior_agent',
        email: '<EMAIL>',
        activeLeads: 8,
        maxLeads: 20,
        stateExperience: ['CA', 'OR', 'WA'],
        propertyTypeExperience: ['insurance', 'retirement'],
        successRate: 0.85,
        experienceLevel: 'junior',
        workloadScore: 40,
        isActive: true
      }
    ];
  };

  const handleLeadSelection = (leadId: string, selected: boolean) => {
    setSelectedLeads(prev => 
      selected 
        ? [...prev, leadId]
        : prev.filter(id => id !== leadId)
    );
  };

  const handleSelectAll = (selected: boolean) => {
    setSelectedLeads(selected ? leads.map(lead => lead.id) : []);
  };

  const handleBulkAssignment = async () => {
    if (!canBulkAssign || selectedLeads.length === 0) return;

    setIsLoading(true);
    try {
      const assignments = await assignmentService.executeBulkAssignment(
        selectedLeads,
        assignmentCriteria,
        distributionMethod,
        user!.id
      );

      console.log('✅ Bulk assignment completed:', assignments);
      onAssignmentComplete?.(assignments);
      setSelectedLeads([]);
      await loadInitialData(); // Refresh data
    } catch (error) {
      console.error('❌ Error executing bulk assignment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleIndividualAssignment = async (leadId: string, agentId: string) => {
    setIsLoading(true);
    try {
      const assignment = await assignmentService.assignLeadToAgent(
        leadId,
        agentId,
        user!.id,
        'Manual assignment from dashboard'
      );

      if (assignment) {
        console.log('✅ Individual assignment completed:', assignment);
        await loadInitialData(); // Refresh data
      }
    } catch (error) {
      console.error('❌ Error executing individual assignment:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const generateAIRecommendations = async () => {
    if (selectedLeads.length === 0) return;

    setIsLoading(true);
    try {
      const recommendations = await assignmentService.calculateOptimalAssignment(
        selectedLeads,
        availableAgents,
        { ...assignmentCriteria, agentExperienceLevel: undefined }
      );
      setAssignmentRecommendations(recommendations);
      setShowAssignmentPreview(true);
    } catch (error) {
      console.error('Error generating AI recommendations:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    const colors = {
      new: 'bg-blue-100 text-blue-800',
      assigned: 'bg-yellow-100 text-yellow-800',
      in_progress: 'bg-purple-100 text-purple-800',
      contacted: 'bg-green-100 text-green-800',
      completed: 'bg-gray-100 text-gray-800'
    };
    return colors[status as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const getPriorityColor = (priority: string) => {
    const colors = {
      low: 'bg-green-100 text-green-800',
      medium: 'bg-yellow-100 text-yellow-800',
      high: 'bg-orange-100 text-orange-800',
      urgent: 'bg-red-100 text-red-800'
    };
    return colors[priority as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  if (!canAssignLeads) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-64">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Access Denied</h3>
            <p className="text-gray-600">You don't have permission to assign leads.</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">Lead Assignment Dashboard</h2>
          <p className="text-gray-600">Assign leads to agents using smart algorithms and manual controls</p>
        </div>
        <div className="flex space-x-2">
          <Button 
            onClick={generateAIRecommendations}
            disabled={selectedLeads.length === 0 || isLoading}
            className="bg-gradient-to-r from-purple-600 to-blue-600 text-white"
          >
            <Brain className="h-4 w-4 mr-2" />
            AI Recommendations
          </Button>
          <Button 
            onClick={() => setShowAssignmentPreview(true)}
            disabled={selectedLeads.length === 0}
            variant="outline"
          >
            <Eye className="h-4 w-4 mr-2" />
            Preview Assignment
          </Button>
        </div>
      </div>

      <Tabs defaultValue="leads" className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="leads">Lead Assignment</TabsTrigger>
          <TabsTrigger value="agents">Agent Workload</TabsTrigger>
          <TabsTrigger value="analytics">Assignment Analytics</TabsTrigger>
          <TabsTrigger value="settings">Assignment Rules</TabsTrigger>
        </TabsList>

        {/* Lead Assignment Tab */}
        <TabsContent value="leads" className="space-y-6">
          {/* Assignment Controls */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Settings className="h-5 w-5 mr-2 text-blue-600" />
                Assignment Controls
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Distribution Method */}
                <div>
                  <Label className="text-sm font-medium">Distribution Method</Label>
                  <Select value={distributionMethod} onValueChange={(value: any) => setDistributionMethod(value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="round_robin">Round Robin</SelectItem>
                      <SelectItem value="workload_based">Workload Based</SelectItem>
                      <SelectItem value="expertise_based">Expertise Based</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Experience Level Filter */}
                <div>
                  <Label className="text-sm font-medium">Agent Experience</Label>
                  <Select 
                    value={assignmentCriteria.agentExperienceLevel || ''} 
                    onValueChange={(value) => setAssignmentCriteria(prev => ({
                      ...prev, 
                      agentExperienceLevel: value as 'junior' | 'senior' | undefined
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any experience level" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any Experience Level</SelectItem>
                      <SelectItem value="junior">Junior Agents</SelectItem>
                      <SelectItem value="senior">Senior Agents</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Difficulty Level */}
                <div>
                  <Label className="text-sm font-medium">Lead Difficulty</Label>
                  <Select 
                    value={assignmentCriteria.discoveryDifficulty || ''} 
                    onValueChange={(value) => setAssignmentCriteria(prev => ({
                      ...prev, 
                      discoveryDifficulty: value as 'easy' | 'medium' | 'hard' | undefined
                    }))}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Any difficulty" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="">Any Difficulty</SelectItem>
                      <SelectItem value="easy">Easy</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="hard">Hard</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Bulk Assignment Actions */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="flex items-center space-x-2">
                  <Checkbox 
                    checked={selectedLeads.length === leads.length && leads.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                  <span className="text-sm text-gray-600">
                    {selectedLeads.length} of {leads.length} leads selected
                  </span>
                </div>
                <Button 
                  onClick={handleBulkAssignment}
                  disabled={selectedLeads.length === 0 || isLoading || !canBulkAssign}
                  className="bg-gradient-to-r from-green-600 to-blue-600 text-white"
                >
                  {isLoading ? (
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                  ) : (
                    <UserPlus className="h-4 w-4 mr-2" />
                  )}
                  Assign Selected Leads
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Leads Table */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-blue-600" />
                  Available Leads ({leads.length})
                </span>
                <Button size="sm" variant="outline" onClick={loadInitialData}>
                  <RefreshCw className="h-4 w-4 mr-2" />
                  Refresh
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="w-12">
                        <Checkbox 
                          checked={selectedLeads.length === leads.length && leads.length > 0}
                          onCheckedChange={handleSelectAll}
                        />
                      </TableHead>
                      <TableHead>Lead Details</TableHead>
                      <TableHead>Amount</TableHead>
                      <TableHead>State</TableHead>
                      <TableHead>Status</TableHead>
                      <TableHead>Priority</TableHead>
                      <TableHead>Current Agent</TableHead>
                      <TableHead>Actions</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {leads.map((lead) => (
                      <TableRow key={lead.id}>
                        <TableCell>
                          <Checkbox 
                            checked={selectedLeads.includes(lead.id)}
                            onCheckedChange={(checked) => handleLeadSelection(lead.id, !!checked)}
                          />
                        </TableCell>
                        <TableCell>
                          <div>
                            <div className="font-medium">{lead.owner_name}</div>
                            <div className="text-sm text-gray-500">{lead.property_type}</div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <span className="font-medium text-green-600">
                            {formatCurrency(lead.amount)}
                          </span>
                        </TableCell>
                        <TableCell>
                          <Badge variant="outline">{lead.state}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getStatusColor(lead.status)}>{lead.status}</Badge>
                        </TableCell>
                        <TableCell>
                          <Badge className={getPriorityColor(lead.priority)}>{lead.priority}</Badge>
                        </TableCell>
                        <TableCell>
                          {lead.assigned_agent_id ? (
                            <span className="text-sm text-gray-600">Agent {lead.assigned_agent_id}</span>
                          ) : (
                            <span className="text-sm text-gray-400">Unassigned</span>
                          )}
                        </TableCell>
                        <TableCell>
                          <div className="flex space-x-1">
                            <Select onValueChange={(agentId) => handleIndividualAssignment(lead.id, agentId)}>
                              <SelectTrigger className="w-32">
                                <SelectValue placeholder="Assign to..." />
                              </SelectTrigger>
                              <SelectContent>
                                {availableAgents.map((agent) => (
                                  <SelectItem key={agent.id} value={agent.id}>
                                    {agent.name}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        {/* Agent Workload Tab */}
        <TabsContent value="agents" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {availableAgents.map((agent) => {
              const workload = workloadAnalysis.find(w => w.agentId === agent.id);
              return (
                <Card key={agent.id} className="border-0 shadow-lg">
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-lg">{agent.name}</CardTitle>
                        <p className="text-sm text-gray-600">{agent.experienceLevel} agent</p>
                      </div>
                      <Badge className={agent.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}>
                        {agent.isActive ? 'Active' : 'Inactive'}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {/* Workload Progress */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Workload</span>
                        <span className="text-sm text-gray-600">
                          {agent.activeLeads}/{agent.maxLeads}
                        </span>
                      </div>
                      <Progress value={(agent.activeLeads / agent.maxLeads) * 100} />
                    </div>

                    {/* Success Rate */}
                    <div>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">Success Rate</span>
                        <span className="text-sm text-gray-600">
                          {Math.round(agent.successRate * 100)}%
                        </span>
                      </div>
                      <Progress value={agent.successRate * 100} className="bg-blue-100" />
                    </div>

                    {/* Expertise */}
                    <div>
                      <Label className="text-sm font-medium mb-2 block">State Experience</Label>
                      <div className="flex flex-wrap gap-1">
                        {agent.stateExperience.map((state) => (
                          <Badge key={state} variant="outline" className="text-xs">
                            {state}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    <div>
                      <Label className="text-sm font-medium mb-2 block">Property Types</Label>
                      <div className="flex flex-wrap gap-1">
                        {agent.propertyTypeExperience.map((type) => (
                          <Badge key={type} variant="outline" className="text-xs">
                            {type}
                          </Badge>
                        ))}
                      </div>
                    </div>

                    {/* Recommended Assignments */}
                    {workload && (
                      <div className="pt-2 border-t">
                        <div className="flex items-center justify-between">
                          <span className="text-sm font-medium">Recommended New Assignments</span>
                          <Badge className="bg-blue-100 text-blue-800">
                            +{workload.recommendedNewAssignments}
                          </Badge>
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Total Agents</p>
                    <p className="text-2xl font-bold text-gray-900">{availableAgents.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <Target className="h-6 w-6 text-green-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Available Leads</p>
                    <p className="text-2xl font-bold text-gray-900">{leads.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-yellow-100 rounded-lg">
                    <Clock className="h-6 w-6 text-yellow-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Avg Assignment Time</p>
                    <p className="text-2xl font-bold text-gray-900">2.3 min</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="border-0 shadow-lg">
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <TrendingUp className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">Success Rate</p>
                    <p className="text-2xl font-bold text-gray-900">84%</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Settings Tab */}
        <TabsContent value="settings" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Assignment Rules Configuration</CardTitle>
              <p className="text-gray-600">Configure automatic assignment rules and preferences</p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center py-8">
                <Settings className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Assignment Rules</h3>
                <p className="text-gray-600">Advanced assignment rule configuration coming soon</p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Assignment Preview Dialog */}
      <Dialog open={showAssignmentPreview} onOpenChange={setShowAssignmentPreview}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Assignment Preview</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {assignmentRecommendations.map((rec) => (
              <div key={rec.leadId} className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">Lead {rec.leadId}</p>
                  <p className="text-sm text-gray-600">Score: {Math.round(rec.score)}/100</p>
                </div>
                <div>
                  <p className="font-medium">{rec.recommendedAgent.name}</p>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {rec.reasoning.slice(0, 2).map((reason, idx) => (
                      <Badge key={idx} variant="outline" className="text-xs">
                        {reason}
                      </Badge>
                    ))}
                  </div>
                </div>
                <Badge className={rec.confidence > 0.8 ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                  {Math.round(rec.confidence * 100)}% confidence
                </Badge>
              </div>
            ))}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setShowAssignmentPreview(false)}>
              Cancel
            </Button>
            <Button onClick={handleBulkAssignment} disabled={isLoading}>
              Execute Assignments
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}; 