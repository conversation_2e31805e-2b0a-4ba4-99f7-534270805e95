import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { 
  CheckSquare, 
  Users, 
  RefreshCw, 
  MessageSquare, 
  Calendar,
  AlertTriangle,
  CheckCircle,
  X,
  Zap
} from 'lucide-react';

interface BatchOperationsProps {
  selectedClaimIds: string[];
  onClearSelection: () => void;
  onBatchUpdate: (operation: string, value: string) => Promise<void>;
  totalSelected: number;
}

interface BatchOperation {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  type: 'status' | 'priority' | 'assignment' | 'action';
  options?: Array<{ value: string; label: string; color?: string }>;
  requiresValue?: boolean;
}

const BATCH_OPERATIONS: BatchOperation[] = [
  {
    id: 'update_status',
    label: 'Update Status',
    description: 'Change status for all selected claims',
    icon: <RefreshCw className="h-4 w-4" />,
    type: 'status',
    requiresValue: true,
    options: [
      { value: 'assigned', label: 'Assigned', color: 'bg-yellow-100 text-yellow-800' },
      { value: 'contacted', label: 'Contacted', color: 'bg-purple-100 text-purple-800' },
      { value: 'documents_requested', label: 'Documents Requested', color: 'bg-indigo-100 text-indigo-800' },
      { value: 'in_progress', label: 'In Progress', color: 'bg-orange-100 text-orange-800' },
      { value: 'under_review', label: 'Under Review', color: 'bg-cyan-100 text-cyan-800' },
      { value: 'approved', label: 'Approved', color: 'bg-green-100 text-green-800' },
      { value: 'on_hold', label: 'On Hold', color: 'bg-gray-100 text-gray-800' }
    ]
  },
  {
    id: 'update_priority',
    label: 'Update Priority',
    description: 'Change priority level for all selected claims',
    icon: <AlertTriangle className="h-4 w-4" />,
    type: 'priority',
    requiresValue: true,
    options: [
      { value: 'low', label: 'Low Priority', color: 'bg-green-100 text-green-800' },
      { value: 'medium', label: 'Medium Priority', color: 'bg-yellow-100 text-yellow-800' },
      { value: 'high', label: 'High Priority', color: 'bg-orange-100 text-orange-800' },
      { value: 'urgent', label: 'Urgent', color: 'bg-red-100 text-red-800' }
    ]
  },
  {
    id: 'assign_agent',
    label: 'Assign Agent',
    description: 'Assign selected claims to an agent',
    icon: <Users className="h-4 w-4" />,
    type: 'assignment',
    requiresValue: true,
    options: [
      { value: 'agent1', label: 'John Smith' },
      { value: 'agent2', label: 'Sarah Johnson' },
      { value: 'agent3', label: 'Mike Wilson' },
      { value: 'unassigned', label: 'Unassigned' }
    ]
  },
  {
    id: 'add_note',
    label: 'Add Bulk Note',
    description: 'Add the same note to all selected claims',
    icon: <MessageSquare className="h-4 w-4" />,
    type: 'action',
    requiresValue: true
  },
  {
    id: 'schedule_followup',
    label: 'Schedule Follow-up',
    description: 'Set follow-up date for all selected claims',
    icon: <Calendar className="h-4 w-4" />,
    type: 'action',
    requiresValue: true
  }
];

const QUICK_BATCH_ACTIONS = [
  {
    id: 'mark_contacted',
    label: 'Mark as Contacted',
    description: 'Quick status update + add contact note',
    icon: '📞',
    operation: 'quick_contacted'
  },
  {
    id: 'request_documents',
    label: 'Request Documents',
    description: 'Update status + send document request',
    icon: '📄',
    operation: 'quick_documents'
  },
  {
    id: 'escalate_urgent',
    label: 'Escalate to Urgent',
    description: 'Set high priority + add escalation note',
    icon: '🚨',
    operation: 'quick_escalate'
  },
  {
    id: 'schedule_tomorrow',
    label: 'Follow-up Tomorrow',
    description: 'Set tomorrow as follow-up date',
    icon: '📅',
    operation: 'quick_tomorrow'
  }
];

export const BatchOperations: React.FC<BatchOperationsProps> = ({
  selectedClaimIds,
  onClearSelection,
  onBatchUpdate,
  totalSelected
}) => {
  const [selectedOperation, setSelectedOperation] = useState<string>('');
  const [operationValue, setOperationValue] = useState<string>('');
  const [customNote, setCustomNote] = useState<string>('');
  const [loading, setLoading] = useState<string | null>(null);

  const handleQuickAction = async (operation: string) => {
    if (selectedClaimIds.length === 0) return;
    
    setLoading(operation);
    try {
      await onBatchUpdate(operation, '');
    } catch (error) {
      console.error('Batch operation failed:', error);
    } finally {
      setLoading(null);
    }
  };

  const handleBatchOperation = async () => {
    if (!selectedOperation || selectedClaimIds.length === 0) return;
    
    const operation = BATCH_OPERATIONS.find(op => op.id === selectedOperation);
    if (!operation) return;

    let value = operationValue;
    if (selectedOperation === 'add_note') {
      value = customNote;
    }

    if (operation.requiresValue && !value) {
      alert('Please provide a value for this operation');
      return;
    }

    setLoading(selectedOperation);
    try {
      await onBatchUpdate(selectedOperation, value);
      setSelectedOperation('');
      setOperationValue('');
      setCustomNote('');
    } catch (error) {
      console.error('Batch operation failed:', error);
    } finally {
      setLoading(null);
    }
  };

  if (selectedClaimIds.length === 0) {
    return null;
  }

  const selectedOperationData = BATCH_OPERATIONS.find(op => op.id === selectedOperation);

  return (
    <Card className="border-blue-200 bg-blue-50">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <CheckSquare className="h-5 w-5 text-blue-600" />
            Batch Operations
            <Badge variant="secondary">{totalSelected} selected</Badge>
          </CardTitle>
          <Button variant="ghost" size="sm" onClick={onClearSelection}>
            <X className="h-4 w-4" />
          </Button>
        </div>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Quick Actions */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm">Quick Actions</h4>
          <div className="grid grid-cols-2 gap-2">
            {QUICK_BATCH_ACTIONS.map((action) => (
              <Button
                key={action.id}
                variant="outline"
                size="sm"
                className="h-auto py-2 px-3 text-left justify-start"
                onClick={() => handleQuickAction(action.operation)}
                disabled={loading === action.operation}
              >
                <div className="flex items-center gap-2 w-full">
                  <span className="text-lg">{action.icon}</span>
                  <div className="min-w-0">
                    <div className="font-medium text-xs">{action.label}</div>
                    <div className="text-xs text-gray-500 truncate">{action.description}</div>
                  </div>
                </div>
              </Button>
            ))}
          </div>
        </div>

        {/* Advanced Operations */}
        <div className="space-y-3">
          <h4 className="font-medium text-sm">Advanced Operations</h4>
          
          {/* Operation Selection */}
          <Select value={selectedOperation} onValueChange={setSelectedOperation}>
            <SelectTrigger>
              <SelectValue placeholder="Choose operation..." />
            </SelectTrigger>
            <SelectContent>
              {BATCH_OPERATIONS.map((operation) => (
                <SelectItem key={operation.id} value={operation.id}>
                  <div className="flex items-center gap-2">
                    {operation.icon}
                    <span>{operation.label}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {/* Operation Value Input */}
          {selectedOperationData?.requiresValue && (
            <div className="space-y-2">
              {selectedOperationData.options ? (
                <Select value={operationValue} onValueChange={setOperationValue}>
                  <SelectTrigger>
                    <SelectValue placeholder={`Select ${selectedOperationData.label.toLowerCase()}...`} />
                  </SelectTrigger>
                  <SelectContent>
                    {selectedOperationData.options.map((option) => (
                      <SelectItem key={option.value} value={option.value}>
                        <div className="flex items-center gap-2">
                          <span>{option.label}</span>
                          {option.color && (
                            <Badge className={option.color}>
                              {option.label}
                            </Badge>
                          )}
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : selectedOperation === 'add_note' ? (
                <textarea
                  className="w-full p-2 border rounded text-sm"
                  placeholder="Enter note to add to all selected claims..."
                  value={customNote}
                  onChange={(e) => setCustomNote(e.target.value)}
                  rows={3}
                />
              ) : selectedOperation === 'schedule_followup' ? (
                <input
                  type="date"
                  className="w-full p-2 border rounded text-sm"
                  value={operationValue}
                  onChange={(e) => setOperationValue(e.target.value)}
                  min={new Date().toISOString().split('T')[0]}
                />
              ) : null}
            </div>
          )}

          {/* Execute Button */}
          <div className="flex gap-2">
            <Button 
              onClick={handleBatchOperation}
              disabled={!selectedOperation || loading === selectedOperation}
              className="flex-1"
            >
              {loading === selectedOperation ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                <>
                  <Zap className="h-4 w-4 mr-2" />
                  Apply to {totalSelected} Claims
                </>
              )}
            </Button>
          </div>

          {selectedOperationData && (
            <p className="text-xs text-gray-600">
              {selectedOperationData.description}
            </p>
          )}
        </div>
      </CardContent>
    </Card>
  );
}; 