import React, { useState } from 'react';
import { 
  Users, 
  Phone, 
  Mail, 
  MapPin, 
  Building, 
  UserX, 
  Star, 
  TrendingUp, 
  Clock,
  ExternalLink,
  CheckCircle,
  AlertTriangle,
  Eye,
  ChevronDown,
  ChevronRight
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from './ui/collapsible';
import { 
  DiscoveryResults, 
  ContactQualityScore, 
  HeirDiscoveryResult,
  SocialMediaPlatform,
  BusinessOfficer
} from '../services/aiDiscoveryEngine';

interface DiscoveryResultsPanelProps {
  results: DiscoveryResults;
  claimantName: string;
  onContactSelected?: (contactInfo: any) => void;
  onUpdateRecord?: (updates: any) => void;
}

export const DiscoveryResultsPanel: React.FC<DiscoveryResultsPanelProps> = ({
  results,
  claimantName,
  onContactSelected,
  onUpdateRecord
}) => {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(new Set(['quality']));

  const toggleSection = (section: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(section)) {
      newExpanded.delete(section);
    } else {
      newExpanded.add(section);
    }
    setExpandedSections(newExpanded);
  };

  // Quality score color and styling
  const getQualityColor = (score: number) => {
    if (score >= 80) return { color: 'text-green-600', bg: 'bg-green-100', border: 'border-green-200' };
    if (score >= 60) return { color: 'text-blue-600', bg: 'bg-blue-100', border: 'border-blue-200' };
    if (score >= 40) return { color: 'text-yellow-600', bg: 'bg-yellow-100', border: 'border-yellow-200' };
    return { color: 'text-red-600', bg: 'bg-red-100', border: 'border-red-200' };
  };

  const getRecommendationIcon = (recommendation: ContactQualityScore['recommendation']) => {
    switch (recommendation) {
      case 'high_priority': return <Star className="w-4 h-4 text-green-600" />;
      case 'medium_priority': return <TrendingUp className="w-4 h-4 text-blue-600" />;
      case 'low_priority': return <Clock className="w-4 h-4 text-yellow-600" />;
      case 'skip': return <UserX className="w-4 h-4 text-red-600" />;
    }
  };

  const qualityStyle = getQualityColor(results.contactQuality.overall);

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Eye className="w-5 h-5" />
            Discovery Results for: {claimantName}
            <Badge variant="outline" className="ml-auto">
              {(results.processingTime / 1000).toFixed(1)}s processing
            </Badge>
          </CardTitle>
        </CardHeader>
      </Card>

      {/* Contact Quality Score */}
      <Collapsible open={expandedSections.has('quality')} onOpenChange={() => toggleSection('quality')}>
        <Card className={`border-2 ${qualityStyle.border}`}>
          <CollapsibleTrigger asChild>
            <CardHeader className={`cursor-pointer ${qualityStyle.bg}`}>
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  {getRecommendationIcon(results.contactQuality.recommendation)}
                  <span className={qualityStyle.color}>
                    Contact Quality Score: {results.contactQuality.overall}/100
                  </span>
                </div>
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className={qualityStyle.color}>
                    {results.contactQuality.recommendation.replace('_', ' ').toUpperCase()}
                  </Badge>
                  {expandedSections.has('quality') ? 
                    <ChevronDown className="w-4 h-4" /> : 
                    <ChevronRight className="w-4 h-4" />
                  }
                </div>
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent className="pt-6">
              <div className="space-y-4">
                {/* Overall Progress */}
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>Overall Quality</span>
                    <span className={qualityStyle.color}>{results.contactQuality.overall}/100</span>
                  </div>
                  <Progress value={results.contactQuality.overall} className="h-3" />
                </div>

                {/* Component Breakdown */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(results.contactQuality.components).map(([component, score]) => (
                    <div key={component} className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span className="capitalize">{component.replace(/([A-Z])/g, ' $1')}</span>
                        <span>{score.toFixed(0)}/100</span>
                      </div>
                      <Progress value={score} className="h-2" />
                    </div>
                  ))}
                </div>

                {/* Expected Response Rate */}
                <div className="p-3 bg-gray-50 rounded-lg">
                  <p className="text-sm text-gray-700">
                    <strong>Expected Response Rate:</strong> {results.contactQuality.expectedResponseRate.toFixed(1)}%
                  </p>
                  <p className="text-xs text-gray-600 mt-1">
                    Based on data quality, contact availability, and historical patterns
                  </p>
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      {/* Contact Information */}
      <Collapsible open={expandedSections.has('contact')} onOpenChange={() => toggleSection('contact')}>
        <Card>
          <CollapsibleTrigger asChild>
            <CardHeader className="cursor-pointer hover:bg-gray-50">
              <CardTitle className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="w-5 h-5" />
                  Contact Information
                </div>
                {expandedSections.has('contact') ? 
                  <ChevronDown className="w-4 h-4" /> : 
                  <ChevronRight className="w-4 h-4" />
                }
              </CardTitle>
            </CardHeader>
          </CollapsibleTrigger>
          <CollapsibleContent>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {/* Phone */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Phone className="w-4 h-4 text-green-600" />
                    <span className="font-medium">Phone</span>
                  </div>
                  {results.phoneValidation.isValid ? (
                    <div className="p-3 bg-green-50 rounded-lg">
                      <p className="font-medium text-green-900">{results.phoneValidation.phoneNumber}</p>
                      <p className="text-sm text-green-700">{results.phoneValidation.type} • {results.phoneValidation.carrier}</p>
                      <p className="text-xs text-green-600">{results.phoneValidation.location}</p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline">
                          {(results.phoneValidation.confidence * 100).toFixed(0)}% confident
                        </Badge>
                        <Button 
                          size="sm" 
                          onClick={() => onContactSelected?.({ type: 'phone', value: results.phoneValidation.phoneNumber })}
                        >
                          Use Phone
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-500">
                      <p className="text-sm">No valid phone number found</p>
                    </div>
                  )}
                </div>

                {/* Email */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Mail className="w-4 h-4 text-blue-600" />
                    <span className="font-medium">Email</span>
                  </div>
                  {results.emailValidation.isValid ? (
                    <div className="p-3 bg-blue-50 rounded-lg">
                      <p className="font-medium text-blue-900">{results.emailValidation.emailAddress}</p>
                      <p className="text-sm text-blue-700">
                        {results.emailValidation.domainType} • {results.emailValidation.deliverable ? 'Deliverable' : 'Not deliverable'}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline">
                          {(results.emailValidation.confidence * 100).toFixed(0)}% confident
                        </Badge>
                        <Button 
                          size="sm" 
                          onClick={() => onContactSelected?.({ type: 'email', value: results.emailValidation.emailAddress })}
                        >
                          Use Email
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-500">
                      <p className="text-sm">No valid email address found</p>
                    </div>
                  )}
                </div>

                {/* Address */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <MapPin className="w-4 h-4 text-purple-600" />
                    <span className="font-medium">Address</span>
                  </div>
                  {results.addressValidation.isValid ? (
                    <div className="p-3 bg-purple-50 rounded-lg">
                      <p className="font-medium text-purple-900">{results.addressValidation.standardizedAddress}</p>
                      <p className="text-sm text-purple-700">
                        {results.addressValidation.addressType} • {results.addressValidation.deliverable ? 'Deliverable' : 'Not deliverable'}
                      </p>
                      <div className="flex items-center gap-2 mt-2">
                        <Badge variant="outline">
                          {(results.addressValidation.confidence * 100).toFixed(0)}% confident
                        </Badge>
                        <Button 
                          size="sm" 
                          onClick={() => onContactSelected?.({ type: 'address', value: results.addressValidation.standardizedAddress })}
                        >
                          Use Address
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <div className="p-3 bg-gray-50 rounded-lg text-gray-500">
                      <p className="text-sm">No valid address found</p>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </CollapsibleContent>
        </Card>
      </Collapsible>

      {/* Business Entity Information */}
      {results.businessEntityCheck.isBusinessEntity && (
        <Collapsible open={expandedSections.has('business')} onOpenChange={() => toggleSection('business')}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Building className="w-5 h-5" />
                    Business Entity Information
                    <Badge variant="outline">
                      {results.businessEntityCheck.businessType.toUpperCase()}
                    </Badge>
                  </div>
                  {expandedSections.has('business') ? 
                    <ChevronDown className="w-4 h-4" /> : 
                    <ChevronRight className="w-4 h-4" />
                  }
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Business Type</p>
                      <p className="font-medium">{results.businessEntityCheck.businessType.toUpperCase()}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Registration State</p>
                      <p className="font-medium">{results.businessEntityCheck.registrationState || 'Unknown'}</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Status</p>
                      <Badge variant={results.businessEntityCheck.businessStatus === 'active' ? 'default' : 'secondary'}>
                        {results.businessEntityCheck.businessStatus.toUpperCase()}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Confidence</p>
                      <p className="font-medium">{(results.businessEntityCheck.confidence * 100).toFixed(0)}%</p>
                    </div>
                  </div>

                  {results.businessEntityCheck.registeredAddress && (
                    <div className="p-3 bg-gray-50 rounded-lg">
                      <p className="text-sm text-gray-600">Registered Address</p>
                      <p className="font-medium">{results.businessEntityCheck.registeredAddress}</p>
                    </div>
                  )}

                  {results.businessEntityCheck.officers.length > 0 && (
                    <div>
                      <p className="font-medium mb-2">Officers & Directors</p>
                      <div className="space-y-2">
                        {results.businessEntityCheck.officers.map((officer: BusinessOfficer, index: number) => (
                          <div key={index} className="p-3 bg-blue-50 rounded-lg">
                            <div className="flex justify-between items-start">
                              <div>
                                <p className="font-medium text-blue-900">{officer.name}</p>
                                <p className="text-sm text-blue-700">{officer.title}</p>
                                {officer.address && (
                                  <p className="text-xs text-blue-600">{officer.address}</p>
                                )}
                              </div>
                              <Badge variant="outline">
                                {officer.role}
                              </Badge>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      )}

      {/* Death Record Check */}
      {results.deathRecordCheck.isDeceased && (
        <Card className="border-red-200 bg-red-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-900">
              <UserX className="w-5 h-5" />
              Death Record Found
              <Badge variant="destructive">Deceased</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div>
                <p className="text-sm text-red-600">Death Date</p>
                <p className="font-medium text-red-900">
                  {results.deathRecordCheck.deathDate?.toLocaleDateString() || 'Unknown'}
                </p>
              </div>
              <div>
                <p className="text-sm text-red-600">Death State</p>
                <p className="font-medium text-red-900">{results.deathRecordCheck.deathState || 'Unknown'}</p>
              </div>
              <div>
                <p className="text-sm text-red-600">Source</p>
                <p className="font-medium text-red-900">{results.deathRecordCheck.source.replace('_', ' ').toUpperCase()}</p>
              </div>
              <div>
                <p className="text-sm text-red-600">Confidence</p>
                <p className="font-medium text-red-900">{(results.deathRecordCheck.confidence * 100).toFixed(0)}%</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Heir Discovery */}
      {results.heirDiscovery.length > 0 && (
        <Collapsible open={expandedSections.has('heirs')} onOpenChange={() => toggleSection('heirs')}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Discovered Heirs & Related Parties
                    <Badge variant="outline">{results.heirDiscovery.length} found</Badge>
                  </div>
                  {expandedSections.has('heirs') ? 
                    <ChevronDown className="w-4 h-4" /> : 
                    <ChevronRight className="w-4 h-4" />
                  }
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent>
                <div className="space-y-3">
                  {results.heirDiscovery.map((heir: HeirDiscoveryResult, index: number) => (
                    <div key={index} className="p-4 bg-green-50 rounded-lg border border-green-200">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h4 className="font-medium text-green-900">{heir.name}</h4>
                          <Badge variant="outline" className="mt-1">
                            {heir.relationship.replace('_', ' ').toUpperCase()}
                          </Badge>
                        </div>
                        <div className="text-right">
                          <p className="text-sm text-green-700">{(heir.confidence * 100).toFixed(0)}% confident</p>
                          <p className="text-xs text-green-600">{heir.source.replace('_', ' ')}</p>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                        {heir.contact.phone && (
                          <div className="flex items-center gap-2">
                            <Phone className="w-3 h-3 text-green-600" />
                            <span className="text-sm">{heir.contact.phone}</span>
                          </div>
                        )}
                        {heir.contact.email && (
                          <div className="flex items-center gap-2">
                            <Mail className="w-3 h-3 text-green-600" />
                            <span className="text-sm">{heir.contact.email}</span>
                          </div>
                        )}
                        {heir.contact.address && (
                          <div className="flex items-center gap-2">
                            <MapPin className="w-3 h-3 text-green-600" />
                            <span className="text-sm">{heir.contact.address}</span>
                          </div>
                        )}
                      </div>
                      
                      <div className="flex gap-2 mt-3">
                        <Button 
                          size="sm" 
                          onClick={() => onContactSelected?.({ type: 'heir', heir })}
                        >
                          Contact Heir
                        </Button>
                        <Button 
                          size="sm" 
                          variant="outline"
                          onClick={() => onUpdateRecord?.({ preferredContact: heir })}
                        >
                          Set as Primary
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      )}

      {/* Social Media Presence */}
      {results.socialMediaFingerprint.platforms.length > 0 && (
        <Collapsible open={expandedSections.has('social')} onOpenChange={() => toggleSection('social')}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <TrendingUp className="w-5 h-5" />
                    Social Media Presence
                    <Badge variant="outline">
                      {results.socialMediaFingerprint.overallPresence}
                    </Badge>
                  </div>
                  {expandedSections.has('social') ? 
                    <ChevronDown className="w-4 h-4" /> : 
                    <ChevronRight className="w-4 h-4" />
                  }
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div>
                      <p className="text-sm text-gray-600">Overall Presence</p>
                      <Badge variant={results.socialMediaFingerprint.overallPresence === 'active' ? 'default' : 'secondary'}>
                        {results.socialMediaFingerprint.overallPresence.toUpperCase()}
                      </Badge>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Last Activity</p>
                      <p className="font-medium">
                        {results.socialMediaFingerprint.lastActivity?.toLocaleDateString() || 'Unknown'}
                      </p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Engagement Score</p>
                      <p className="font-medium">{results.socialMediaFingerprint.engagementScore}/100</p>
                    </div>
                    <div>
                      <p className="text-sm text-gray-600">Confidence</p>
                      <p className="font-medium">{(results.socialMediaFingerprint.confidence * 100).toFixed(0)}%</p>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {results.socialMediaFingerprint.platforms.map((platform: SocialMediaPlatform, index: number) => (
                      <div key={index} className="p-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div className="flex justify-between items-center">
                          <div className="flex items-center gap-3">
                            <Badge variant="outline">{platform.platform.toUpperCase()}</Badge>
                            <div>
                              <p className="font-medium text-blue-900">
                                {platform.verified && <CheckCircle className="w-3 h-3 inline mr-1 text-blue-600" />}
                                Profile Found
                              </p>
                              <p className="text-sm text-blue-700">
                                {platform.followerCount && `${platform.followerCount.toLocaleString()} followers • `}
                                {platform.activityLevel} activity
                              </p>
                            </div>
                          </div>
                          <Button 
                            size="sm" 
                            variant="outline"
                            onClick={() => window.open(platform.profileUrl, '_blank')}
                          >
                            <ExternalLink className="w-3 h-3 mr-1" />
                            View
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      )}

      {/* Name Variants */}
      {results.nameVariants.length > 1 && (
        <Collapsible open={expandedSections.has('names')} onOpenChange={() => toggleSection('names')}>
          <Card>
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-gray-50">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Users className="w-5 h-5" />
                    Name Variations
                    <Badge variant="outline">{results.nameVariants.length} variants</Badge>
                  </div>
                  {expandedSections.has('names') ? 
                    <ChevronDown className="w-4 h-4" /> : 
                    <ChevronRight className="w-4 h-4" />
                  }
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            <CollapsibleContent>
              <CardContent>
                <div className="flex flex-wrap gap-2">
                  {results.nameVariants.map((variant, index) => (
                    <Badge key={index} variant="outline" className="text-sm">
                      {variant}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      )}
    </div>
  );
}; 