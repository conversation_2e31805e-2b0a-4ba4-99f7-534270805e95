import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { 
  Save, 
  ArrowLeft, 
  User, 
  MapPin, 
  DollarSign, 
  FileText, 
  Building,
  AlertCircle,
  CheckCircle,
  X,
  Calendar,
  Plus,
  Trash2
} from 'lucide-react';
import { supabase } from '@/lib/supabase';

// Types
interface ClaimFormData {
  // Owner Information
  owner_name: string;
  owner_first_name: string;
  owner_last_name: string;
  owner_business_name: string;
  
  // Property Information
  property_id: string;
  amount: string;
  property_type: string;
  securities_name: string;
  cusip: string;
  shares_reported: string;
  
  // Address Information
  owner_address: string;
  owner_city: string;
  owner_state: string;
  owner_zip: string;
  
  // Holder Information
  holder_name: string;
  holder_address: string;
  holder_city: string;
  holder_state: string;
  holder_zip: string;
  
  // Priority and Assignment
  priority: string;
  state: string;
  
  // Dates
  report_date: string;
  
  // Contact Information
  primary_phone: string;
  primary_email: string;
  
  // Notes
  description: string;
}

interface Props {
  onBack: () => void;
  onSave?: (claimId: string) => void;
}

const US_STATES = [
  'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA',
  'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD',
  'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ',
  'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC',
  'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY'
];

const PROPERTY_TYPES = [
  'Bank Account',
  'Checking Account',
  'Savings Account',
  'Safe Deposit Box',
  'Insurance Policy',
  'Stocks/Securities',
  'Uncashed Check',
  'Wages/Payroll',
  'Other'
];

const PRIORITY_LEVELS = [
  { value: 'low', label: 'Low' },
  { value: 'medium', label: 'Medium' },
  { value: 'high', label: 'High' },
  { value: 'urgent', label: 'Urgent' }
];

export default function ClaimForm({ onBack, onSave }: Props) {
  const [formData, setFormData] = useState<ClaimFormData>({
    owner_name: '',
    owner_first_name: '',
    owner_last_name: '',
    owner_business_name: '',
    property_id: '',
    amount: '',
    property_type: '',
    securities_name: '',
    cusip: '',
    shares_reported: '',
    owner_address: '',
    owner_city: '',
    owner_state: '',
    owner_zip: '',
    holder_name: '',
    holder_address: '',
    holder_city: '',
    holder_state: '',
    holder_zip: '',
    priority: 'medium',
    state: '',
    report_date: '',
    primary_phone: '',
    primary_email: '',
    description: ''
  });

  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [successMessage, setSuccessMessage] = useState('');

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    // Required fields
    if (!formData.owner_name.trim()) {
      newErrors.owner_name = 'Owner name is required';
    }
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      newErrors.amount = 'Valid amount is required';
    }
    if (!formData.state) {
      newErrors.state = 'State is required';
    }

    // Email validation
    if (formData.primary_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.primary_email)) {
      newErrors.primary_email = 'Invalid email format';
    }

    // Phone validation (basic)
    if (formData.primary_phone && !/^[\d\s\-\(\)\+\.]+$/.test(formData.primary_phone)) {
      newErrors.primary_phone = 'Invalid phone format';
    }

    // ZIP code validation
    if (formData.owner_zip && !/^\d{5}(-\d{4})?$/.test(formData.owner_zip)) {
      newErrors.owner_zip = 'Invalid ZIP code format';
    }
    if (formData.holder_zip && !/^\d{5}(-\d{4})?$/.test(formData.holder_zip)) {
      newErrors.holder_zip = 'Invalid ZIP code format';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    setErrors({});
    setSuccessMessage('');

    try {
      // Prepare claim data for database
      const claimData = {
        owner_name: formData.owner_name.trim(),
        owner_first_name: formData.owner_first_name.trim() || null,
        owner_last_name: formData.owner_last_name.trim() || null,
        owner_business_name: formData.owner_business_name.trim() || null,
        property_id: formData.property_id.trim() || null,
        amount: parseFloat(formData.amount),
        property_type: formData.property_type || null,
        securities_name: formData.securities_name.trim() || null,
        cusip: formData.cusip.trim() || null,
        shares_reported: formData.shares_reported ? parseInt(formData.shares_reported) : null,
        owner_address: formData.owner_address.trim() || null,
        owner_city: formData.owner_city.trim() || null,
        owner_state: formData.owner_state || null,
        owner_zip: formData.owner_zip.trim() || null,
        holder_name: formData.holder_name.trim() || null,
        holder_address: formData.holder_address.trim() || null,
        holder_city: formData.holder_city.trim() || null,
        holder_state: formData.holder_state || null,
        holder_zip: formData.holder_zip.trim() || null,
        priority: formData.priority,
        state: formData.state,
        report_date: formData.report_date || null,
        status: 'new',
        commission_rate: 0.25,
        complexity_score: 1,
        compliance_status: 'pending'
      };

      // Insert claim into database
      const { data: claimResult, error: claimError } = await supabase
        .from('claims')
        .insert([claimData])
        .select()
        .single();

      if (claimError) throw claimError;

      // Add contact information if provided
      const contacts = [];
      if (formData.primary_phone.trim()) {
        contacts.push({
          claim_id: claimResult.id,
          contact_type: 'phone',
          contact_value: formData.primary_phone.trim(),
          label: 'Primary',
          is_primary: true
        });
      }
      if (formData.primary_email.trim()) {
        contacts.push({
          claim_id: claimResult.id,
          contact_type: 'email',
          contact_value: formData.primary_email.trim(),
          label: 'Primary',
          is_primary: true
        });
      }

      if (contacts.length > 0) {
        const { error: contactError } = await supabase
          .from('claim_contacts')
          .insert(contacts);

        if (contactError) {
          console.warn('Failed to add contacts:', contactError);
        }
      }

      // Add initial activity
      if (formData.description.trim()) {
        const { error: activityError } = await supabase
          .from('claim_activities')
          .insert([{
            claim_id: claimResult.id,
            agent_id: (await supabase.auth.getUser()).data.user?.id,
            activity_type: 'note',
            title: 'Initial claim creation',
            description: formData.description.trim()
          }]);

        if (activityError) {
          console.warn('Failed to add initial activity:', activityError);
        }
      }

      setSuccessMessage('Claim created successfully!');
      
      // Callback to parent component
      if (onSave) {
        onSave(claimResult.id);
      } else {
        // Wait a moment to show success message, then go back
        setTimeout(() => {
          onBack();
        }, 2000);
      }

    } catch (error) {
      console.error('Error creating claim:', error);
      setErrors({ submit: error instanceof Error ? error.message : 'Failed to create claim' });
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof ClaimFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const generatePropertyId = () => {
    const prefix = formData.state || 'XX';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.random().toString(36).substr(2, 4).toUpperCase();
    return `${prefix}-${timestamp}-${random}`;
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Create New Claim</h1>
          <p className="text-gray-600">Add a new asset recovery claim to the system</p>
        </div>
      </div>

      {/* Success Message */}
      {successMessage && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-green-800">
              <CheckCircle className="h-5 w-5" />
              <span className="font-medium">{successMessage}</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main Form */}
      <form onSubmit={handleSubmit}>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Left Column - Owner Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Owner Information
                </CardTitle>
                <CardDescription>Details about the property owner</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="owner_name">Full Name *</Label>
                  <Input
                    id="owner_name"
                    value={formData.owner_name}
                    onChange={(e) => handleInputChange('owner_name', e.target.value)}
                    placeholder="John Smith or ABC Company LLC"
                    className={errors.owner_name ? 'border-red-300' : ''}
                  />
                  {errors.owner_name && <p className="text-sm text-red-600 mt-1">{errors.owner_name}</p>}
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="owner_first_name">First Name</Label>
                    <Input
                      id="owner_first_name"
                      value={formData.owner_first_name}
                      onChange={(e) => handleInputChange('owner_first_name', e.target.value)}
                      placeholder="John"
                    />
                  </div>
                  <div>
                    <Label htmlFor="owner_last_name">Last Name</Label>
                    <Input
                      id="owner_last_name"
                      value={formData.owner_last_name}
                      onChange={(e) => handleInputChange('owner_last_name', e.target.value)}
                      placeholder="Smith"
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="owner_business_name">Business Name</Label>
                  <Input
                    id="owner_business_name"
                    value={formData.owner_business_name}
                    onChange={(e) => handleInputChange('owner_business_name', e.target.value)}
                    placeholder="If applicable"
                  />
                </div>

                <Separator />

                <div>
                  <Label htmlFor="primary_phone">Primary Phone</Label>
                  <Input
                    id="primary_phone"
                    value={formData.primary_phone}
                    onChange={(e) => handleInputChange('primary_phone', e.target.value)}
                    placeholder="(*************"
                    className={errors.primary_phone ? 'border-red-300' : ''}
                  />
                  {errors.primary_phone && <p className="text-sm text-red-600 mt-1">{errors.primary_phone}</p>}
                </div>

                <div>
                  <Label htmlFor="primary_email">Primary Email</Label>
                  <Input
                    id="primary_email"
                    type="email"
                    value={formData.primary_email}
                    onChange={(e) => handleInputChange('primary_email', e.target.value)}
                    placeholder="<EMAIL>"
                    className={errors.primary_email ? 'border-red-300' : ''}
                  />
                  {errors.primary_email && <p className="text-sm text-red-600 mt-1">{errors.primary_email}</p>}
                </div>
              </CardContent>
            </Card>

            {/* Owner Address */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5" />
                  Owner Address
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="owner_address">Street Address</Label>
                  <Input
                    id="owner_address"
                    value={formData.owner_address}
                    onChange={(e) => handleInputChange('owner_address', e.target.value)}
                    placeholder="123 Main Street"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="owner_city">City</Label>
                    <Input
                      id="owner_city"
                      value={formData.owner_city}
                      onChange={(e) => handleInputChange('owner_city', e.target.value)}
                      placeholder="Austin"
                    />
                  </div>
                  <div>
                    <Label htmlFor="owner_state">State</Label>
                    <Select value={formData.owner_state} onValueChange={(value) => handleInputChange('owner_state', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        {US_STATES.map(state => (
                          <SelectItem key={state} value={state}>{state}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="owner_zip">ZIP Code</Label>
                  <Input
                    id="owner_zip"
                    value={formData.owner_zip}
                    onChange={(e) => handleInputChange('owner_zip', e.target.value)}
                    placeholder="78701"
                    className={errors.owner_zip ? 'border-red-300' : ''}
                  />
                  {errors.owner_zip && <p className="text-sm text-red-600 mt-1">{errors.owner_zip}</p>}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Middle Column - Property Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Property Information
                </CardTitle>
                <CardDescription>Details about the unclaimed property</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="property_id">Property ID</Label>
                    <div className="flex gap-2">
                      <Input
                        id="property_id"
                        value={formData.property_id}
                        onChange={(e) => handleInputChange('property_id', e.target.value)}
                        placeholder="Auto-generated"
                      />
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => handleInputChange('property_id', generatePropertyId())}
                      >
                        Generate
                      </Button>
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="state">State *</Label>
                    <Select value={formData.state} onValueChange={(value) => handleInputChange('state', value)}>
                      <SelectTrigger className={errors.state ? 'border-red-300' : ''}>
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        {US_STATES.map(state => (
                          <SelectItem key={state} value={state}>{state}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    {errors.state && <p className="text-sm text-red-600 mt-1">{errors.state}</p>}
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="amount">Amount *</Label>
                    <Input
                      id="amount"
                      type="number"
                      step="0.01"
                      min="0"
                      value={formData.amount}
                      onChange={(e) => handleInputChange('amount', e.target.value)}
                      placeholder="1000.00"
                      className={errors.amount ? 'border-red-300' : ''}
                    />
                    {errors.amount && <p className="text-sm text-red-600 mt-1">{errors.amount}</p>}
                  </div>
                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Select value={formData.priority} onValueChange={(value) => handleInputChange('priority', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {PRIORITY_LEVELS.map(priority => (
                          <SelectItem key={priority.value} value={priority.value}>
                            {priority.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="property_type">Property Type</Label>
                  <Select value={formData.property_type} onValueChange={(value) => handleInputChange('property_type', value)}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {PROPERTY_TYPES.map(type => (
                        <SelectItem key={type} value={type}>{type}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label htmlFor="report_date">Report Date</Label>
                  <Input
                    id="report_date"
                    type="date"
                    value={formData.report_date}
                    onChange={(e) => handleInputChange('report_date', e.target.value)}
                  />
                </div>

                <Separator />

                <div>
                  <Label htmlFor="securities_name">Securities Name</Label>
                  <Input
                    id="securities_name"
                    value={formData.securities_name}
                    onChange={(e) => handleInputChange('securities_name', e.target.value)}
                    placeholder="Apple Inc. Common Stock"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="cusip">CUSIP</Label>
                    <Input
                      id="cusip"
                      value={formData.cusip}
                      onChange={(e) => handleInputChange('cusip', e.target.value)}
                      placeholder="037833100"
                    />
                  </div>
                  <div>
                    <Label htmlFor="shares_reported">Shares</Label>
                    <Input
                      id="shares_reported"
                      type="number"
                      min="0"
                      value={formData.shares_reported}
                      onChange={(e) => handleInputChange('shares_reported', e.target.value)}
                      placeholder="100"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Holder Information */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  Holder Information
                </CardTitle>
                <CardDescription>Current holder of the property</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="holder_name">Holder Name</Label>
                  <Input
                    id="holder_name"
                    value={formData.holder_name}
                    onChange={(e) => handleInputChange('holder_name', e.target.value)}
                    placeholder="State Treasury Department"
                  />
                </div>

                <div>
                  <Label htmlFor="holder_address">Address</Label>
                  <Input
                    id="holder_address"
                    value={formData.holder_address}
                    onChange={(e) => handleInputChange('holder_address', e.target.value)}
                    placeholder="123 Government Way"
                  />
                </div>

                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label htmlFor="holder_city">City</Label>
                    <Input
                      id="holder_city"
                      value={formData.holder_city}
                      onChange={(e) => handleInputChange('holder_city', e.target.value)}
                      placeholder="Capital City"
                    />
                  </div>
                  <div>
                    <Label htmlFor="holder_state">State</Label>
                    <Select value={formData.holder_state} onValueChange={(value) => handleInputChange('holder_state', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder="Select state" />
                      </SelectTrigger>
                      <SelectContent>
                        {US_STATES.map(state => (
                          <SelectItem key={state} value={state}>{state}</SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div>
                  <Label htmlFor="holder_zip">ZIP Code</Label>
                  <Input
                    id="holder_zip"
                    value={formData.holder_zip}
                    onChange={(e) => handleInputChange('holder_zip', e.target.value)}
                    placeholder="12345"
                    className={errors.holder_zip ? 'border-red-300' : ''}
                  />
                  {errors.holder_zip && <p className="text-sm text-red-600 mt-1">{errors.holder_zip}</p>}
                </div>
              </CardContent>
            </Card>

            {/* Notes Section */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Notes & Description
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div>
                  <Label htmlFor="description">Initial Notes</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder="Any additional information about this claim..."
                    rows={4}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Submit Buttons */}
            <Card>
              <CardContent className="pt-6">
                {errors.submit && (
                  <div className="flex items-center gap-2 text-red-600 mb-4">
                    <AlertCircle className="h-4 w-4" />
                    <span className="text-sm">{errors.submit}</span>
                  </div>
                )}
                
                <div className="flex gap-3">
                  <Button
                    type="submit"
                    disabled={loading}
                    className="flex-1"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                        Creating...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4 mr-2" />
                        Create Claim
                      </>
                    )}
                  </Button>
                  <Button
                    type="button"
                    variant="outline"
                    onClick={onBack}
                    disabled={loading}
                  >
                    Cancel
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </form>
    </div>
  );
} 