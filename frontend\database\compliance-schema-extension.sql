-- ===================================================================
-- AssetHunterPro Compliance Schema Extension
-- Adds comprehensive compliance tables to existing database schema
-- ===================================================================

-- ===================================
-- STATE REGULATORY COMPLIANCE
-- ===================================

-- State-specific compliance rules and regulations
CREATE TABLE state_compliance_rules (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    state_code VARCHAR(2) NOT NULL,
    fee_cap_percentage DECIMAL(5,2) NOT NULL,
    license_required BOOLEAN DEFAULT true,
    bond_amount DECIMAL(12,2) DEFAULT 0,
    bond_expiry DATE,
    filing_requirements JSONB DEFAULT '[]',
    documentation_standards JSONB DEFAULT '{}',
    effective_date DATE NOT NULL,
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    UNIQUE(state_code, effective_date)
);

-- License tracking for each state
CREATE TABLE state_licenses (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    state_code VARCHAR(2) NOT NULL,
    license_number VARCHAR(100) NOT NULL,
    license_type VARCHAR(50) NOT NULL CHECK (license_type IN ('recovery_agent', 'debt_collector', 'legal_services')),
    issue_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'suspended', 'revoked')),
    renewal_alerts_sent INTEGER DEFAULT 0,
    next_renewal_alert DATE,
    issuing_authority VARCHAR(255),
    license_document_url TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(state_code, license_number)
);

-- Bond tracking for each state
CREATE TABLE state_bonds (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    state_code VARCHAR(2) NOT NULL,
    bond_number VARCHAR(100) NOT NULL,
    bond_amount DECIMAL(12,2) NOT NULL,
    bond_company VARCHAR(255) NOT NULL,
    effective_date DATE NOT NULL,
    expiry_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'expired', 'cancelled')),
    bond_document_url TEXT,
    premium_amount DECIMAL(10,2),
    renewal_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(state_code, bond_number)
);

-- ===================================
-- PRIVACY & DATA PROTECTION
-- ===================================

-- PII deletion and access requests (GDPR/CCPA compliance)
CREATE TABLE pii_requests (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    request_type VARCHAR(20) NOT NULL CHECK (request_type IN ('deletion', 'export', 'correction', 'access')),
    claimant_id UUID REFERENCES claims(claimant_id),
    requested_by VARCHAR(255) NOT NULL,
    request_date TIMESTAMPTZ DEFAULT NOW(),
    fulfillment_date TIMESTAMPTZ,
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'denied', 'verified')),
    reason TEXT,
    verification_method VARCHAR(20) CHECK (verification_method IN ('email', 'phone', 'document', 'in_person')),
    verification_data JSONB DEFAULT '{}',
    assigned_to UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data retention policies
CREATE TABLE data_retention_policies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    data_type VARCHAR(50) NOT NULL,
    retention_period INTERVAL NOT NULL,
    deletion_method VARCHAR(20) CHECK (deletion_method IN ('soft_delete', 'hard_delete', 'anonymize')),
    legal_basis VARCHAR(100),
    exceptions TEXT[],
    is_active BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES users(id),
    last_review_date DATE,
    next_review_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(data_type, is_active) DEFERRABLE INITIALLY DEFERRED
);

-- Consent tracking for data processing
CREATE TABLE consent_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claimant_id UUID REFERENCES claims(claimant_id),
    consent_type VARCHAR(20) NOT NULL CHECK (consent_type IN ('data_processing', 'communication', 'marketing', 'analytics')),
    consent_granted BOOLEAN NOT NULL,
    consent_date TIMESTAMPTZ NOT NULL,
    consent_method VARCHAR(20) CHECK (consent_method IN ('written', 'electronic', 'verbal', 'implied')),
    withdrawal_date TIMESTAMPTZ,
    source_document VARCHAR(255),
    ip_address INET,
    user_agent TEXT,
    legal_basis VARCHAR(100),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- COMMUNICATION COMPLIANCE
-- ===================================

-- TCPA and CAN-SPAM consent tracking
CREATE TABLE communication_consent (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone_number VARCHAR(20),
    email_address VARCHAR(255),
    consent_type VARCHAR(20) NOT NULL CHECK (consent_type IN ('calls', 'texts', 'emails', 'all')),
    consent_granted BOOLEAN DEFAULT false,
    consent_date TIMESTAMPTZ,
    consent_method VARCHAR(20) CHECK (consent_method IN ('written', 'verbal', 'electronic')),
    opt_out_date TIMESTAMPTZ,
    source_document VARCHAR(255),
    tcpa_compliant BOOLEAN DEFAULT false,
    can_spam_compliant BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    CHECK (phone_number IS NOT NULL OR email_address IS NOT NULL)
);

-- Do not contact registry
CREATE TABLE do_not_contact (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    contact_value VARCHAR(255) NOT NULL,
    contact_type VARCHAR(20) NOT NULL CHECK (contact_type IN ('phone', 'email', 'address')),
    date_added TIMESTAMPTZ DEFAULT NOW(),
    reason VARCHAR(100) CHECK (reason IN ('user_request', 'bounce', 'complaint', 'legal', 'internal')),
    added_by UUID NOT NULL REFERENCES users(id),
    expiry_date TIMESTAMPTZ,
    notes TEXT,
    is_active BOOLEAN DEFAULT true,
    UNIQUE(contact_value, contact_type)
);

-- Communication violations tracking
CREATE TABLE communication_violations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    communication_id UUID REFERENCES communication_logs(id),
    violation_type VARCHAR(20) NOT NULL CHECK (violation_type IN ('tcpa', 'can_spam', 'do_not_call', 'consent')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    detected_date TIMESTAMPTZ DEFAULT NOW(),
    resolved_date TIMESTAMPTZ,
    resolution_notes TEXT,
    penalty_amount DECIMAL(10,2),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- FINANCIAL SERVICES COMPLIANCE
-- ===================================

-- Enhanced transaction monitoring for AML/BSA
CREATE TABLE transaction_monitoring (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claim_id UUID NOT NULL REFERENCES claims(id),
    transaction_amount DECIMAL(12,2) NOT NULL,
    transaction_type VARCHAR(50) NOT NULL CHECK (transaction_type IN ('payment_received', 'payment_sent', 'commission', 'fee')),
    risk_score INTEGER DEFAULT 0 CHECK (risk_score >= 0 AND risk_score <= 100),
    flags TEXT[],
    review_required BOOLEAN DEFAULT false,
    reviewed_by UUID REFERENCES users(id),
    review_date TIMESTAMPTZ,
    review_notes TEXT,
    parties TEXT[],
    source_of_funds VARCHAR(255),
    destination VARCHAR(255),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Suspicious Activity Reports (SAR)
CREATE TABLE suspicious_activity_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    transaction_ids UUID[],
    report_type VARCHAR(50) NOT NULL CHECK (report_type IN ('cash', 'suspicious_activity', 'currency_transaction')),
    description TEXT NOT NULL,
    filed_date TIMESTAMPTZ,
    filed_by UUID NOT NULL REFERENCES users(id),
    external_reference VARCHAR(100),
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'filed', 'acknowledged')),
    regulatory_response TEXT,
    follow_up_required BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- AML checks and screenings
CREATE TABLE aml_checks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    claimant_id UUID NOT NULL REFERENCES claims(claimant_id),
    check_type VARCHAR(30) NOT NULL CHECK (check_type IN ('identity_verification', 'sanctions_screening', 'pep_check')),
    provider VARCHAR(100) NOT NULL,
    check_date TIMESTAMPTZ DEFAULT NOW(),
    result VARCHAR(20) NOT NULL CHECK (result IN ('clear', 'match', 'partial_match', 'error')),
    risk_level VARCHAR(20) NOT NULL CHECK (risk_level IN ('low', 'medium', 'high', 'critical')),
    details JSONB DEFAULT '{}',
    expiry_date TIMESTAMPTZ,
    manual_review_required BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- ELECTRONIC SIGNATURE COMPLIANCE
-- ===================================

-- Electronic signatures with full audit trail
CREATE TABLE electronic_signatures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES claim_documents(id),
    signer_email VARCHAR(255) NOT NULL,
    signer_name VARCHAR(255) NOT NULL,
    signature_provider VARCHAR(20) CHECK (signature_provider IN ('docusign', 'hellosign', 'adobe_sign', 'internal')),
    external_signature_id VARCHAR(255),
    signature_timestamp TIMESTAMPTZ,
    signer_ip_address INET,
    authentication_method VARCHAR(30) CHECK (authentication_method IN ('email', 'sms', 'knowledge_based', 'id_verification')),
    certificate_data JSONB DEFAULT '{}',
    document_hash VARCHAR(64),
    is_valid BOOLEAN DEFAULT true,
    invalidation_reason TEXT,
    legal_validity_confirmed BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Signature audit trail
CREATE TABLE signature_audit_trails (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    signature_id UUID NOT NULL REFERENCES electronic_signatures(id),
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('sent', 'viewed', 'signed', 'completed', 'voided')),
    event_timestamp TIMESTAMPTZ DEFAULT NOW(),
    event_details JSONB DEFAULT '{}',
    user_agent TEXT,
    ip_address INET
);

-- ===================================
-- SECURITY COMPLIANCE
-- ===================================

-- Enhanced security event logging
CREATE TABLE security_events (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    source_ip INET,
    user_id UUID REFERENCES users(id),
    automated_response TEXT[],
    manual_review_required BOOLEAN DEFAULT false,
    reviewed_by UUID REFERENCES users(id),
    review_date TIMESTAMPTZ,
    false_positive BOOLEAN DEFAULT false,
    incident_created BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Access reviews for SOC 2 compliance
CREATE TABLE access_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    reviewer_id UUID NOT NULL REFERENCES users(id),
    review_date TIMESTAMPTZ DEFAULT NOW(),
    access_appropriate BOOLEAN,
    changes_made TEXT[],
    next_review_date TIMESTAMPTZ,
    review_type VARCHAR(20) CHECK (review_type IN ('quarterly', 'annual', 'triggered', 'termination')),
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Data breach incident tracking
CREATE TABLE data_breach_incidents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    incident_type VARCHAR(30) NOT NULL CHECK (incident_type IN ('unauthorized_access', 'data_loss', 'system_compromise', 'human_error')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    discovery_date TIMESTAMPTZ DEFAULT NOW(),
    estimated_occurrence_date TIMESTAMPTZ,
    affected_records_count INTEGER DEFAULT 0,
    data_types_affected TEXT[],
    containment_date TIMESTAMPTZ,
    notification_required BOOLEAN DEFAULT false,
    notifications_sent TEXT[],
    regulatory_filing_required BOOLEAN DEFAULT false,
    investigation_status VARCHAR(20) DEFAULT 'open' CHECK (investigation_status IN ('open', 'in_progress', 'closed')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- COMPLIANCE REPORTING
-- ===================================

-- Compliance reports and regulatory filings
CREATE TABLE compliance_reports (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    report_type VARCHAR(50) NOT NULL,
    regulation VARCHAR(20) NOT NULL CHECK (regulation IN ('GDPR', 'CCPA', 'SOX', 'HIPAA', 'AML', 'BSA', 'TCPA', 'STATE')),
    reporting_period VARCHAR(20),
    generated_date TIMESTAMPTZ DEFAULT NOW(),
    submitted_date TIMESTAMPTZ,
    submission_reference VARCHAR(100),
    report_data JSONB DEFAULT '{}',
    status VARCHAR(20) DEFAULT 'draft' CHECK (status IN ('draft', 'pending_review', 'approved', 'submitted', 'acknowledged')),
    generated_by UUID NOT NULL REFERENCES users(id),
    reviewed_by UUID REFERENCES users(id),
    file_path TEXT,
    file_size BIGINT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Regulatory filings tracking
CREATE TABLE regulatory_filings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    filing_type VARCHAR(50) NOT NULL,
    regulatory_body VARCHAR(100) NOT NULL,
    filing_deadline DATE NOT NULL,
    filed_date TIMESTAMPTZ,
    confirmation_number VARCHAR(100),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'filed', 'acknowledged', 'rejected')),
    rejection_reason TEXT,
    associated_reports UUID[],
    fine_amount DECIMAL(10,2),
    follow_up_required BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- AUDIT AND MONITORING
-- ===================================

-- Compliance audits
CREATE TABLE compliance_audits (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audit_type VARCHAR(20) NOT NULL CHECK (audit_type IN ('internal', 'external', 'regulatory', 'client')),
    auditor VARCHAR(255) NOT NULL,
    audit_scope TEXT[],
    start_date DATE NOT NULL,
    end_date DATE,
    status VARCHAR(20) DEFAULT 'planning' CHECK (status IN ('planning', 'in_progress', 'review', 'completed')),
    overall_rating VARCHAR(30) CHECK (overall_rating IN ('satisfactory', 'needs_improvement', 'unsatisfactory')),
    remediation_plan TEXT,
    next_audit_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Audit findings
CREATE TABLE audit_findings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    audit_id UUID NOT NULL REFERENCES compliance_audits(id),
    finding_type VARCHAR(20) NOT NULL CHECK (finding_type IN ('deficiency', 'observation', 'best_practice')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
    description TEXT NOT NULL,
    regulation_reference VARCHAR(100),
    remediation_required BOOLEAN DEFAULT false,
    remediation_deadline DATE,
    remediation_status VARCHAR(20) DEFAULT 'pending' CHECK (remediation_status IN ('pending', 'in_progress', 'completed', 'verified')),
    assigned_to UUID REFERENCES users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Compliance metrics tracking
CREATE TABLE compliance_metrics (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    metric_name VARCHAR(100) NOT NULL,
    metric_category VARCHAR(30) NOT NULL CHECK (metric_category IN ('privacy', 'security', 'financial', 'communication', 'state_regulatory')),
    value DECIMAL(12,4) NOT NULL,
    target_value DECIMAL(12,4),
    measurement_period VARCHAR(20),
    measurement_date DATE NOT NULL,
    trend VARCHAR(20) CHECK (trend IN ('improving', 'stable', 'declining')),
    requires_attention BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- ===================================
-- RISK MANAGEMENT
-- ===================================

-- Compliance risks
CREATE TABLE compliance_risks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    risk_type VARCHAR(50) NOT NULL,
    risk_category VARCHAR(30) NOT NULL CHECK (risk_category IN ('privacy', 'security', 'financial', 'operational', 'regulatory')),
    description TEXT NOT NULL,
    probability INTEGER CHECK (probability >= 1 AND probability <= 100),
    impact INTEGER CHECK (impact >= 1 AND impact <= 100),
    risk_score INTEGER GENERATED ALWAYS AS (probability * impact / 100) STORED,
    risk_level VARCHAR(20) GENERATED ALWAYS AS (
        CASE 
            WHEN (probability * impact / 100) >= 80 THEN 'critical'
            WHEN (probability * impact / 100) >= 60 THEN 'high'
            WHEN (probability * impact / 100) >= 30 THEN 'medium'
            ELSE 'low'
        END
    ) STORED,
    mitigation_strategies TEXT[],
    owner UUID NOT NULL REFERENCES users(id),
    review_date DATE NOT NULL,
    status VARCHAR(20) DEFAULT 'open' CHECK (status IN ('open', 'mitigating', 'mitigated', 'accepted')),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Risk assessments
CREATE TABLE risk_assessments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    assessment_type VARCHAR(30) NOT NULL CHECK (assessment_type IN ('privacy_impact', 'security_risk', 'operational_risk')),
    scope TEXT NOT NULL,
    conducted_by UUID NOT NULL REFERENCES users(id),
    assessment_date DATE DEFAULT CURRENT_DATE,
    overall_risk_level VARCHAR(20) CHECK (overall_risk_level IN ('low', 'medium', 'high', 'critical')),
    recommendations TEXT[],
    next_assessment_date DATE,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Link risks to assessments
CREATE TABLE risk_assessment_risks (
    assessment_id UUID NOT NULL REFERENCES risk_assessments(id),
    risk_id UUID NOT NULL REFERENCES compliance_risks(id),
    PRIMARY KEY (assessment_id, risk_id)
);

-- ===================================
-- TRAINING AND CERTIFICATION
-- ===================================

-- Compliance training programs
CREATE TABLE compliance_training (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    training_name VARCHAR(255) NOT NULL,
    training_type VARCHAR(20) NOT NULL CHECK (training_type IN ('online', 'classroom', 'certification', 'workshop')),
    required_for_roles TEXT[],
    frequency VARCHAR(20) CHECK (frequency IN ('annual', 'biannual', 'quarterly', 'one_time')),
    content_topics TEXT[],
    passing_score INTEGER,
    certification_valid_period INTERVAL,
    provider VARCHAR(255),
    last_updated TIMESTAMPTZ DEFAULT NOW(),
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMPTZ DEFAULT NOW()
);

-- User training records
CREATE TABLE user_training_records (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id),
    training_id UUID NOT NULL REFERENCES compliance_training(id),
    completion_date TIMESTAMPTZ DEFAULT NOW(),
    score INTEGER,
    passed BOOLEAN NOT NULL,
    certificate_number VARCHAR(100),
    expiry_date TIMESTAMPTZ,
    renewal_required BOOLEAN DEFAULT false,
    notes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, training_id, completion_date)
);

-- ===================================
-- COMPLIANCE ALERTS AND NOTIFICATIONS
-- ===================================

-- Compliance alerts system
CREATE TABLE compliance_alerts (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    alert_type VARCHAR(30) NOT NULL CHECK (alert_type IN ('license_expiry', 'bond_expiry', 'audit_due', 'violation_detected', 'training_due')),
    severity VARCHAR(20) NOT NULL CHECK (severity IN ('info', 'warning', 'error', 'critical')),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    entity_id UUID,
    entity_type VARCHAR(50),
    due_date TIMESTAMPTZ,
    assigned_to UUID REFERENCES users(id),
    acknowledged BOOLEAN DEFAULT false,
    acknowledged_by UUID REFERENCES users(id),
    acknowledged_date TIMESTAMPTZ,
    created_date TIMESTAMPTZ DEFAULT NOW(),
    is_resolved BOOLEAN DEFAULT false,
    resolution_notes TEXT
);

-- ===================================
-- INDEXES FOR PERFORMANCE
-- ===================================

-- State compliance indexes
CREATE INDEX idx_state_compliance_rules_state_code ON state_compliance_rules(state_code);
CREATE INDEX idx_state_licenses_expiry ON state_licenses(expiry_date) WHERE status = 'active';
CREATE INDEX idx_state_bonds_expiry ON state_bonds(expiry_date) WHERE status = 'active';

-- Privacy compliance indexes
CREATE INDEX idx_pii_requests_status ON pii_requests(status);
CREATE INDEX idx_pii_requests_due ON pii_requests(request_date) WHERE status IN ('pending', 'processing');
CREATE INDEX idx_consent_records_claimant ON consent_records(claimant_id);

-- Communication compliance indexes
CREATE INDEX idx_communication_consent_phone ON communication_consent(phone_number);
CREATE INDEX idx_communication_consent_email ON communication_consent(email_address);
CREATE INDEX idx_do_not_contact_value ON do_not_contact(contact_value, contact_type);

-- Financial compliance indexes
CREATE INDEX idx_transaction_monitoring_amount ON transaction_monitoring(transaction_amount);
CREATE INDEX idx_transaction_monitoring_risk ON transaction_monitoring(risk_score) WHERE risk_score > 50;
CREATE INDEX idx_transaction_monitoring_review ON transaction_monitoring(review_required) WHERE review_required = true;

-- Security compliance indexes
CREATE INDEX idx_security_events_severity ON security_events(severity, created_at);
CREATE INDEX idx_security_events_review ON security_events(manual_review_required) WHERE manual_review_required = true;
CREATE INDEX idx_access_reviews_due ON access_reviews(next_review_date);

-- Alert indexes
CREATE INDEX idx_compliance_alerts_due ON compliance_alerts(due_date) WHERE is_resolved = false;
CREATE INDEX idx_compliance_alerts_assigned ON compliance_alerts(assigned_to) WHERE is_resolved = false;

-- ===================================
-- TRIGGERS FOR AUTOMATION
-- ===================================

-- Update timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply to all compliance tables
CREATE TRIGGER update_state_compliance_rules_updated_at BEFORE UPDATE ON state_compliance_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_state_licenses_updated_at BEFORE UPDATE ON state_licenses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_state_bonds_updated_at BEFORE UPDATE ON state_bonds FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_pii_requests_updated_at BEFORE UPDATE ON pii_requests FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_retention_policies_updated_at BEFORE UPDATE ON data_retention_policies FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_consent_records_updated_at BEFORE UPDATE ON consent_records FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_communication_consent_updated_at BEFORE UPDATE ON communication_consent FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_communication_violations_updated_at BEFORE UPDATE ON communication_violations FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_transaction_monitoring_updated_at BEFORE UPDATE ON transaction_monitoring FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_suspicious_activity_reports_updated_at BEFORE UPDATE ON suspicious_activity_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_aml_checks_updated_at BEFORE UPDATE ON aml_checks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_electronic_signatures_updated_at BEFORE UPDATE ON electronic_signatures FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_security_events_updated_at BEFORE UPDATE ON security_events FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_data_breach_incidents_updated_at BEFORE UPDATE ON data_breach_incidents FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_reports_updated_at BEFORE UPDATE ON compliance_reports FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_regulatory_filings_updated_at BEFORE UPDATE ON regulatory_filings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_audits_updated_at BEFORE UPDATE ON compliance_audits FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_audit_findings_updated_at BEFORE UPDATE ON audit_findings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_compliance_risks_updated_at BEFORE UPDATE ON compliance_risks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_risk_assessments_updated_at BEFORE UPDATE ON risk_assessments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- ===================================
-- SAMPLE DATA FOR TESTING
-- ===================================

-- Insert sample state compliance rules
INSERT INTO state_compliance_rules (state_code, fee_cap_percentage, license_required, bond_amount, filing_requirements) VALUES
('CA', 15.00, true, 50000, '["Form XYZ", "Annual Report"]'),
('TX', 20.00, true, 25000, '["Registration Form", "Bond Certificate"]'),
('NY', 10.00, true, 100000, '["License Application", "Background Check", "Bond Certificate"]'),
('FL', 18.00, true, 30000, '["Application Form", "Fee Schedule"]');

-- Insert sample data retention policies
INSERT INTO data_retention_policies (data_type, retention_period, deletion_method, legal_basis, created_by) VALUES
('claim_data', '7 years', 'soft_delete', 'Business records retention', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1)),
('communication_logs', '3 years', 'hard_delete', 'Compliance requirement', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1)),
('audit_logs', '7 years', 'anonymize', 'Legal requirement', (SELECT id FROM users WHERE email = '<EMAIL>' LIMIT 1));

-- Insert sample compliance training
INSERT INTO compliance_training (training_name, training_type, required_for_roles, frequency, content_topics, passing_score, provider) VALUES
('GDPR Privacy Training', 'online', '["admin", "compliance", "senior_agent"]', 'annual', '["Data Protection", "Privacy Rights", "Breach Response"]', 80, 'ComplianceAcademy'),
('AML/BSA Training', 'certification', '["finance", "admin"]', 'annual', '["Money Laundering Detection", "Suspicious Activity Reporting", "Customer Due Diligence"]', 85, 'FinCEN Institute'),
('TCPA Compliance', 'online', '["junior_agent", "senior_agent"]', 'annual', '["Consent Requirements", "Call Restrictions", "Opt-out Procedures"]', 75, 'TelecomCompliance Inc');

COMMENT ON SCHEMA public IS 'AssetHunterPro database with comprehensive compliance extensions'; 