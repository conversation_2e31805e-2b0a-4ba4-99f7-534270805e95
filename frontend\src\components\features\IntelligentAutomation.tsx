import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Bot, 
  Zap, 
  Workflow,
  Settings,
  Timer,
  TrendingUp,
  ArrowRight,
  CheckCircle,
  AlertTriangle,
  Lightbulb,
  Activity,
  Users,
  FileText,
  Mail,
  Phone,
  Calendar,
  Shield,
  Target,
  BarChart3,
  Clock,
  DollarSign,
  ArrowUp,
  ArrowDown,
  Play,
  Pause,
  RotateCcw,
  Cpu,
  Database,
  Network,
  Eye
} from 'lucide-react';
import { 
  IntelligentAutomation, 
  WorkflowAI,
  AIMLPlatform 
} from '@/types/ai-ml-platform';

interface IntelligentAutomationProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const IntelligentAutomationComponent: React.FC<IntelligentAutomationProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('overview');
  const [selectedWorkflow, setSelectedWorkflow] = useState<string | null>(null);
  
  const [automationMetrics, setAutomationMetrics] = useState({
    active_workflows: 47,
    automation_rate: 84.2,
    time_saved_hours: 2847,
    cost_reduction: 890000,
    accuracy_improvement: 96.8,
    processing_speed_increase: 340
  });

  const automatedWorkflows = [
    {
      id: 'WF-001',
      name: 'Document Processing & Classification',
      type: 'Document Automation',
      status: 'Active',
      efficiency: 95.7,
      volume_processed: 12847,
      time_saved_hours: 847,
      automation_level: 'Full',
      confidence: 98.2,
      description: 'Automatically processes incoming documents, extracts key information, and classifies by type and priority',
      triggers: ['Document Upload', 'Email Attachment', 'API Submission'],
      actions: ['OCR Processing', 'Data Extraction', 'Classification', 'Routing'],
      success_rate: 96.4
    },
    {
      id: 'WF-002',
      name: 'Client Communication Sequence',
      type: 'Communication Automation',
      status: 'Active',
      efficiency: 89.3,
      volume_processed: 8934,
      time_saved_hours: 445,
      automation_level: 'Hybrid',
      confidence: 91.7,
      description: 'Automated multi-channel communication sequence with intelligent timing and personalization',
      triggers: ['Case Assignment', 'Payment Due', 'Status Update'],
      actions: ['Email Generation', 'SMS Sending', 'Call Scheduling', 'Follow-up'],
      success_rate: 87.9
    },
    {
      id: 'WF-003',
      name: 'Risk Assessment & Scoring',
      type: 'Decision Automation',
      status: 'Active',
      efficiency: 92.8,
      volume_processed: 6745,
      time_saved_hours: 523,
      automation_level: 'Full',
      confidence: 94.6,
      description: 'AI-powered risk assessment with automated scoring and priority assignment',
      triggers: ['New Case', 'Data Update', 'Payment Event'],
      actions: ['Risk Calculation', 'Score Assignment', 'Priority Setting', 'Alert Generation'],
      success_rate: 93.1
    },
    {
      id: 'WF-004',
      name: 'Legal Document Generation',
      type: 'Content Automation',
      status: 'Active',
      efficiency: 87.1,
      volume_processed: 4562,
      time_saved_hours: 678,
      automation_level: 'Hybrid',
      confidence: 89.4,
      description: 'Automated generation of legal documents with compliance checking and personalization',
      triggers: ['Legal Action Required', 'Template Request', 'Compliance Check'],
      actions: ['Template Selection', 'Data Population', 'Compliance Check', 'Document Generation'],
      success_rate: 91.7
    }
  ];

  const processOptimizations = [
    {
      process_name: 'Initial Case Assessment',
      current_time: 240,
      optimized_time: 85,
      improvement: 64.6,
      automation_opportunities: [
        'Automated data extraction from submissions',
        'AI-powered risk scoring',
        'Intelligent priority assignment',
        'Automated compliance checking'
      ],
      roi_annual: 145000,
      implementation_effort: 'Medium'
    },
    {
      process_name: 'Client Onboarding',
      current_time: 180,
      optimized_time: 45,
      improvement: 75.0,
      automation_opportunities: [
        'Digital form processing',
        'Automated verification workflows',
        'AI document review',
        'Intelligent routing'
      ],
      roi_annual: 89000,
      implementation_effort: 'Low'
    },
    {
      process_name: 'Recovery Planning',
      current_time: 360,
      optimized_time: 120,
      improvement: 66.7,
      automation_opportunities: [
        'AI strategy recommendations',
        'Automated timeline generation',
        'Resource optimization',
        'Risk assessment automation'
      ],
      roi_annual: 234000,
      implementation_effort: 'High'
    }
  ];

  const intelligentDecisions = [
    {
      decision_type: 'Case Prioritization',
      decisions_made: 15678,
      accuracy: 94.7,
      confidence: 'High',
      impact: 'High',
      description: 'AI automatically prioritizes cases based on value, urgency, and success probability',
      criteria: ['Asset Value', 'Recovery Probability', 'Timeline Urgency', 'Resource Availability'],
      outcomes: ['Faster high-value recovery', '32% improvement in resource allocation', 'Reduced processing delays']
    },
    {
      decision_type: 'Communication Strategy',
      decisions_made: 12456,
      accuracy: 89.3,
      confidence: 'Medium',
      impact: 'Medium',
      description: 'Selects optimal communication channels and timing for each client interaction',
      criteria: ['Client Preferences', 'Historical Response', 'Urgency Level', 'Communication History'],
      outcomes: ['28% higher response rates', 'Reduced communication costs', 'Improved client satisfaction']
    },
    {
      decision_type: 'Resource Allocation',
      decisions_made: 8934,
      accuracy: 91.8,
      confidence: 'High',
      impact: 'High',
      description: 'Optimally assigns team members and resources to cases based on expertise and workload',
      criteria: ['Team Expertise', 'Current Workload', 'Case Complexity', 'Success History'],
      outcomes: ['25% faster case resolution', 'Improved team efficiency', 'Better workload distribution']
    }
  ];

  const cognitiveCapabilities = [
    {
      capability: 'Natural Language Processing',
      applications: ['Document Understanding', 'Email Classification', 'Sentiment Analysis'],
      accuracy: 96.2,
      processing_volume: 45678,
      use_cases: [
        'Automated contract analysis',
        'Client communication sentiment tracking',
        'Legal document classification',
        'Compliance requirement extraction'
      ]
    },
    {
      capability: 'Computer Vision',
      applications: ['Document Scanning', 'Image Analysis', 'Signature Verification'],
      accuracy: 94.8,
      processing_volume: 23456,
      use_cases: [
        'Automated document digitization',
        'Asset photo analysis',
        'Identity verification',
        'Damage assessment from images'
      ]
    },
    {
      capability: 'Pattern Recognition',
      applications: ['Fraud Detection', 'Behavioral Analysis', 'Anomaly Detection'],
      accuracy: 92.4,
      processing_volume: 67890,
      use_cases: [
        'Payment pattern analysis',
        'Unusual behavior detection',
        'Risk indicator identification',
        'Process optimization insights'
      ]
    }
  ];

  const renderOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Workflows</p>
                <p className="text-2xl font-bold text-blue-600">{automationMetrics.active_workflows}</p>
              </div>
              <Workflow className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              12 fully automated
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-green-600">{automationMetrics.automation_rate}%</p>
              </div>
              <Bot className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +8.7% vs last quarter
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Time Saved</p>
                <p className="text-2xl font-bold text-purple-600">{automationMetrics.time_saved_hours.toLocaleString()}</p>
              </div>
              <Timer className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Hours this month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Cost Reduction</p>
                <p className="text-2xl font-bold text-green-600">
                  ${(automationMetrics.cost_reduction / 1000000).toFixed(1)}M
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Annual savings
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Accuracy Rate</p>
                <p className="text-2xl font-bold text-orange-600">{automationMetrics.accuracy_improvement}%</p>
              </div>
              <Target className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 flex items-center text-xs text-green-600">
              <ArrowUp className="h-3 w-3 mr-1" />
              +4.2% improvement
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Speed Increase</p>
                <p className="text-2xl font-bold text-indigo-600">+{automationMetrics.processing_speed_increase}%</p>
              </div>
              <Zap className="h-8 w-8 text-indigo-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">
              Processing speed boost
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Active Automation Workflows
            </CardTitle>
            <CardDescription>
              Real-time status of intelligent automation processes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {automatedWorkflows.slice(0, 3).map((workflow, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-semibold">{workflow.name}</h4>
                    <div className="text-sm text-gray-600">{workflow.type}</div>
                  </div>
                  <Badge variant="default">{workflow.status}</Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Efficiency</div>
                    <div className="font-semibold text-green-600">{workflow.efficiency}%</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Volume Processed</div>
                    <div className="font-medium">{workflow.volume_processed.toLocaleString()}</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Automation Level: {workflow.automation_level}</span>
                    <span>{workflow.confidence}% confidence</span>
                  </div>
                  <Progress value={workflow.efficiency} className="h-2" />
                </div>

                <div className="flex justify-between items-center">
                  <div className="text-sm text-gray-600">
                    Saved: {workflow.time_saved_hours} hours
                  </div>
                  <Button size="sm" variant="outline">
                    <Eye className="h-4 w-4 mr-1" />
                    View Details
                  </Button>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Intelligent Decisions
            </CardTitle>
            <CardDescription>
              AI-powered decision making across business processes
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {intelligentDecisions.map((decision, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">{decision.decision_type}</h4>
                  <Badge variant={decision.impact === 'High' ? 'default' : 'secondary'}>
                    {decision.impact} Impact
                  </Badge>
                </div>
                
                <p className="text-sm text-gray-600">{decision.description}</p>
                
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <div className="text-gray-600">Decisions Made</div>
                    <div className="font-medium">{decision.decisions_made.toLocaleString()}</div>
                  </div>
                  <div>
                    <div className="text-gray-600">Accuracy</div>
                    <div className="font-semibold text-green-600">{decision.accuracy}%</div>
                  </div>
                </div>

                <div className="space-y-1">
                  <div className="text-sm font-medium text-gray-700">Key Outcomes:</div>
                  {decision.outcomes.slice(0, 2).map((outcome, outcomeIndex) => (
                    <div key={outcomeIndex} className="text-xs text-gray-600 flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      {outcome}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderWorkflows = () => (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Automated Workflows</h3>
        <Button variant="outline">
          <Play className="h-4 w-4 mr-2" />
          Create New Workflow
        </Button>
      </div>

      <div className="grid grid-cols-1 gap-4">
        {automatedWorkflows.map((workflow, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-semibold text-lg">{workflow.name}</h4>
                      <div className="text-sm text-gray-600">{workflow.id} • {workflow.type}</div>
                    </div>
                    <Badge variant={workflow.status === 'Active' ? 'default' : 'secondary'}>
                      {workflow.status}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600">{workflow.description}</p>
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{workflow.automation_level}</Badge>
                    <span className="text-sm text-gray-600">{workflow.confidence}% confidence</span>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="text-sm font-medium">Performance Metrics</div>
                    <div className="grid grid-cols-2 gap-4 text-sm">
                      <div>
                        <div className="text-gray-600">Efficiency</div>
                        <div className="font-semibold text-green-600">{workflow.efficiency}%</div>
                      </div>
                      <div>
                        <div className="text-gray-600">Success Rate</div>
                        <div className="font-semibold text-blue-600">{workflow.success_rate}%</div>
                      </div>
                    </div>
                    <Progress value={workflow.efficiency} className="h-2" />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <div className="text-gray-600">Volume Processed</div>
                      <div className="font-medium">{workflow.volume_processed.toLocaleString()}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Time Saved</div>
                      <div className="font-medium">{workflow.time_saved_hours} hours</div>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <div className="text-sm font-medium mb-2">Triggers</div>
                    <div className="space-y-1">
                      {workflow.triggers.map((trigger, triggerIndex) => (
                        <div key={triggerIndex} className="text-xs bg-blue-50 text-blue-700 px-2 py-1 rounded">
                          {trigger}
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium mb-2">Actions</div>
                    <div className="space-y-1">
                      {workflow.actions.map((action, actionIndex) => (
                        <div key={actionIndex} className="text-xs bg-green-50 text-green-700 px-2 py-1 rounded">
                          {action}
                        </div>
                      ))}
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Settings className="h-4 w-4 mr-1" />
                      Configure
                    </Button>
                    <Button size="sm" variant="outline">
                      <BarChart3 className="h-4 w-4 mr-1" />
                      Analytics
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderOptimization = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Process Optimization Opportunities
          </CardTitle>
          <CardDescription>
            AI-identified opportunities for process improvement and automation
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {processOptimizations.map((optimization, index) => (
            <div key={index} className="border rounded-lg p-4 space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <h4 className="font-semibold text-lg">{optimization.process_name}</h4>
                  <div className="text-sm text-gray-600">
                    Implementation Effort: <span className="font-medium">{optimization.implementation_effort}</span>
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-2xl font-bold text-green-600">{optimization.improvement}%</div>
                  <div className="text-sm text-gray-600">Improvement</div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-3 bg-red-50 rounded-lg">
                  <div className="text-sm text-gray-600">Current Time</div>
                  <div className="text-xl font-bold text-red-600">{optimization.current_time} min</div>
                </div>
                <div className="flex items-center justify-center">
                  <ArrowRight className="h-6 w-6 text-gray-400" />
                </div>
                <div className="text-center p-3 bg-green-50 rounded-lg">
                  <div className="text-sm text-gray-600">Optimized Time</div>
                  <div className="text-xl font-bold text-green-600">{optimization.optimized_time} min</div>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium mb-2">Automation Opportunities:</div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {optimization.automation_opportunities.map((opportunity, oppIndex) => (
                    <div key={oppIndex} className="text-sm bg-blue-50 text-blue-700 p-2 rounded flex items-center gap-2">
                      <Lightbulb className="h-4 w-4" />
                      {opportunity}
                    </div>
                  ))}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">
                  Annual ROI: <span className="font-semibold text-green-600">${optimization.roi_annual.toLocaleString()}</span>
                </div>
                <Button size="sm" variant="default">
                  Implement Optimization
                </Button>
              </div>
            </div>
          ))}
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Cognitive Capabilities</CardTitle>
            <CardDescription>AI cognitive functions powering automation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {cognitiveCapabilities.map((capability, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-semibold">{capability.capability}</h4>
                  <div className="text-right">
                    <div className="font-semibold text-green-600">{capability.accuracy}%</div>
                    <div className="text-xs text-gray-500">Accuracy</div>
                  </div>
                </div>
                
                <div className="flex flex-wrap gap-1">
                  {capability.applications.map((app, appIndex) => (
                    <Badge key={appIndex} variant="outline" className="text-xs">
                      {app}
                    </Badge>
                  ))}
                </div>

                <div className="text-sm text-gray-600">
                  Processing Volume: <span className="font-medium">{capability.processing_volume.toLocaleString()}</span>
                </div>

                <div className="space-y-1">
                  <div className="text-sm font-medium">Use Cases:</div>
                  {capability.use_cases.slice(0, 2).map((useCase, useCaseIndex) => (
                    <div key={useCaseIndex} className="text-xs text-gray-600 flex items-center gap-1">
                      <CheckCircle className="h-3 w-3 text-green-600" />
                      {useCase}
                    </div>
                  ))}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Automation Impact Metrics</CardTitle>
            <CardDescription>Business impact of intelligent automation</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: 'Process Efficiency', value: 84, improvement: '+34%' },
              { metric: 'Error Reduction', value: 96, improvement: '+67%' },
              { metric: 'Response Time', value: 78, improvement: '+45%' },
              { metric: 'Cost Effectiveness', value: 91, improvement: '+52%' },
              { metric: 'Client Satisfaction', value: 89, improvement: '+28%' },
              { metric: 'Resource Utilization', value: 86, improvement: '+41%' }
            ].map((impact, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{impact.metric}</span>
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium">{impact.value}%</span>
                    <span className="text-xs text-green-600">{impact.improvement}</span>
                  </div>
                </div>
                <Progress value={impact.value} className="h-2" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Intelligent Automation</h2>
          <p className="text-gray-600">
            AI-powered workflow automation and intelligent decision making
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
            {userPlan === 'diamond' ? 'Full Automation Suite' : 'Basic Automation'}
          </Badge>
          {userPlan === 'platinum' && (
            <Button onClick={onUpgrade} variant="outline">
              Upgrade for Advanced AI
            </Button>
          )}
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Automation Overview</TabsTrigger>
          <TabsTrigger value="workflows">Active Workflows</TabsTrigger>
          <TabsTrigger value="optimization">Process Optimization</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {renderOverview()}
        </TabsContent>

        <TabsContent value="workflows" className="space-y-6">
          {renderWorkflows()}
        </TabsContent>

        <TabsContent value="optimization" className="space-y-6">
          {renderOptimization()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-indigo-200 bg-indigo-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Bot className="h-12 w-12 text-indigo-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unlock Advanced Automation</h3>
              <p className="text-gray-600 mb-4">
                Upgrade to Diamond for cognitive automation, advanced AI decision engines, and enterprise workflow capabilities
              </p>
              <Button onClick={onUpgrade} className="bg-indigo-600 hover:bg-indigo-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 