import React, { useState, useCallback } from 'react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { 
  Upload, 
  File, 
  X, 
  CheckCircle, 
  AlertCircle,
  FileText,
  Image,
  FileAudio,
  FileVideo,
  Archive,
  Plus,
  Tag
} from 'lucide-react';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';

interface DocumentUploadModalProps {
  claimId: string;
  onClose: () => void;
  onUploadComplete: () => void;
}

interface FileWithMetadata extends File {
  id: string;
  preview?: string;
  category?: string;
  subcategory?: string;
  description?: string;
  tags?: string[];
  isConfidential?: boolean;
  expiryDate?: string;
  permissions?: 'internal' | 'shareable' | 'public';
}

const DOCUMENT_CATEGORIES = [
  {
    value: 'id_documents',
    label: 'ID Documents',
    subcategories: ['drivers_license', 'passport', 'ssn_card', 'birth_certificate', 'other_id']
  },
  {
    value: 'contracts',
    label: 'Contracts & Agreements',
    subcategories: ['service_agreement', 'nda', 'power_of_attorney', 'authorization', 'settlement']
  },
  {
    value: 'correspondence',
    label: 'Correspondence',
    subcategories: ['email', 'letter', 'notice', 'demand_letter', 'response']
  },
  {
    value: 'state_forms',
    label: 'State Forms',
    subcategories: ['unclaimed_property', 'tax_forms', 'regulatory', 'compliance', 'filing']
  },
  {
    value: 'signatures',
    label: 'Signatures & Authorization',
    subcategories: ['wet_signature', 'digital_signature', 'authorization_form', 'consent']
  },
  {
    value: 'financial',
    label: 'Financial Documents',
    subcategories: ['bank_statements', 'tax_returns', 'w2', '1099', 'invoice', 'receipt']
  },
  {
    value: 'legal',
    label: 'Legal Documents',
    subcategories: ['court_filing', 'judgment', 'lien', 'lawsuit', 'settlement_agreement']
  },
  {
    value: 'other',
    label: 'Other Documents',
    subcategories: ['photo', 'screenshot', 'report', 'analysis', 'misc']
  }
];

const MAX_FILE_SIZE = 10 * 1024 * 1024; // 10MB
const ALLOWED_FILE_TYPES = [
  'application/pdf',
  'image/jpeg',
  'image/png',
  'image/gif',
  'image/webp',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain',
  'text/csv'
];

export const DocumentUploadModal: React.FC<DocumentUploadModalProps> = ({
  claimId,
  onClose,
  onUploadComplete
}) => {
  const { user } = useAuth();
  const [files, setFiles] = useState<FileWithMetadata[]>([]);
  const [dragActive, setDragActive] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<Record<string, number>>({});
  const [errors, setErrors] = useState<string[]>([]);
  const [currentStep, setCurrentStep] = useState<'upload' | 'metadata'>('upload');
  const [newTag, setNewTag] = useState('');

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="h-8 w-8 text-blue-500" />;
    if (fileType.startsWith('audio/')) return <FileAudio className="h-8 w-8 text-green-500" />;
    if (fileType.startsWith('video/')) return <FileVideo className="h-8 w-8 text-purple-500" />;
    if (fileType === 'application/pdf') return <FileText className="h-8 w-8 text-red-500" />;
    if (fileType.includes('zip') || fileType.includes('archive')) return <Archive className="h-8 w-8 text-orange-500" />;
    return <File className="h-8 w-8 text-gray-500" />;
  };

  const validateFile = (file: File): string | null => {
    if (file.size > MAX_FILE_SIZE) {
      return `File "${file.name}" exceeds 10MB limit`;
    }
    if (!ALLOWED_FILE_TYPES.includes(file.type)) {
      return `File type "${file.type}" is not allowed`;
    }
    return null;
  };

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === "dragenter" || e.type === "dragover") {
      setDragActive(true);
    } else if (e.type === "dragleave") {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFiles(Array.from(e.dataTransfer.files));
    }
  }, []);

  const handleFiles = (fileList: File[]) => {
    const newErrors: string[] = [];
    const validFiles: FileWithMetadata[] = [];

    fileList.forEach(file => {
      const error = validateFile(file);
      if (error) {
        newErrors.push(error);
      } else {
        const fileWithMetadata: FileWithMetadata = Object.assign(file, {
          id: Math.random().toString(36).substr(2, 9),
          preview: file.type.startsWith('image/') ? URL.createObjectURL(file) : undefined,
          category: '',
          subcategory: '',
          description: '',
          tags: [],
          isConfidential: false,
          permissions: 'internal' as const
        });
        validFiles.push(fileWithMetadata);
      }
    });

    setErrors(newErrors);
    setFiles(prev => [...prev, ...validFiles]);
  };

  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(file => file.id !== fileId));
    setUploadProgress(prev => {
      const updated = { ...prev };
      delete updated[fileId];
      return updated;
    });
  };

  const updateFileMetadata = (fileId: string, field: string, value: any) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId ? { ...file, [field]: value } : file
    ));
  };

  const addTag = (fileId: string) => {
    if (!newTag.trim()) return;
    
    setFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, tags: [...(file.tags || []), newTag.trim()] }
        : file
    ));
    setNewTag('');
  };

  const removeTag = (fileId: string, tagIndex: number) => {
    setFiles(prev => prev.map(file => 
      file.id === fileId 
        ? { ...file, tags: file.tags?.filter((_, index) => index !== tagIndex) }
        : file
    ));
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const canProceedToMetadata = () => {
    return files.length > 0 && files.every(file => file.category);
  };

  const canUpload = () => {
    return files.length > 0 && files.every(file => 
      file.category && file.subcategory
    );
  };

  const uploadFiles = async () => {
    if (!canUpload()) {
      setErrors(['Please complete all required metadata fields']);
      return;
    }

    if (!user) {
      setErrors(['User not authenticated. Please log in again.']);
      return;
    }

    setUploading(true);
    setErrors([]);

    try {
      for (const file of files) {
        // Update progress
        setUploadProgress(prev => ({ ...prev, [file.id]: 0 }));

        // Generate unique filename
        const fileExt = file.name.split('.').pop();
        const fileName = `${claimId}/${Date.now()}-${Math.random().toString(36).substr(2, 9)}.${fileExt}`;

        // Upload to Supabase Storage
        const { data: uploadData, error: uploadError } = await supabase.storage
          .from('documents')
          .upload(fileName, file, {
            cacheControl: '3600',
            upsert: false
          });

        if (uploadError) throw uploadError;

        // Update progress
        setUploadProgress(prev => ({ ...prev, [file.id]: 50 }));

        // Get public URL
        const { data: { publicUrl } } = supabase.storage
          .from('documents')
          .getPublicUrl(fileName);

        // Save document record to database
        const { error: dbError } = await supabase
          .from('claim_documents')
          .insert({
            claim_id: claimId,
            file_name: fileName,
            original_name: file.name,
            category: file.category,
            subcategory: file.subcategory,
            file_size: file.size,
            file_type: file.type,
            file_url: publicUrl,
            description: file.description,
            tags: file.tags || [],
            is_confidential: file.isConfidential || false,
            expiry_date: file.expiryDate || null,
            permissions: file.permissions || 'internal',
            status: 'pending_review',
            version: 1,
            uploaded_by: user.id
          });

        if (dbError) throw dbError;

        // Complete progress
        setUploadProgress(prev => ({ ...prev, [file.id]: 100 }));
      }

      // Add activity log
      await supabase
        .from('claim_activities')
        .insert({
          claim_id: claimId,
          agent_id: user.id,
          activity_type: 'document_upload',
          title: 'Documents uploaded',
          description: `${files.length} document(s) uploaded with metadata`
        });

      onUploadComplete();

    } catch (error) {
      console.error('Upload error:', error);
      setErrors(['Failed to upload files. Please try again.']);
    } finally {
      setUploading(false);
    }
  };

  const getSubcategories = (category: string) => {
    return DOCUMENT_CATEGORIES.find(cat => cat.value === category)?.subcategories || [];
  };

  return (
    <Dialog open={true} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Upload className="h-5 w-5" />
            Upload Documents
          </DialogTitle>
          <DialogDescription>
            Upload and organize documents with detailed metadata for better management.
          </DialogDescription>
        </DialogHeader>

        {/* Step Indicator */}
        <div className="flex items-center gap-4 mb-6">
          <div className={`flex items-center gap-2 ${currentStep === 'upload' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'upload' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              1
            </div>
            <span>Upload Files</span>
          </div>
          <div className="flex-1 h-px bg-gray-200" />
          <div className={`flex items-center gap-2 ${currentStep === 'metadata' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center ${currentStep === 'metadata' ? 'bg-blue-600 text-white' : 'bg-gray-200'}`}>
              2
            </div>
            <span>Add Metadata</span>
          </div>
        </div>

        {currentStep === 'upload' && (
          <div className="space-y-6">
            {/* Upload Area */}
            <div
              className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                dragActive 
                  ? 'border-blue-500 bg-blue-50' 
                  : 'border-gray-300 hover:border-gray-400'
              }`}
              onDragEnter={handleDrag}
              onDragLeave={handleDrag}
              onDragOver={handleDrag}
              onDrop={handleDrop}
            >
              <Upload className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Drop files here or click to browse
              </h3>
              <p className="text-sm text-gray-600 mb-4">
                Supports: PDF, Word, Excel, Images, Text files (Max 10MB each)
              </p>
              <Input
                type="file"
                multiple
                accept={ALLOWED_FILE_TYPES.join(',')}
                onChange={(e) => {
                  if (e.target.files) {
                    handleFiles(Array.from(e.target.files));
                  }
                }}
                className="hidden"
                id="file-upload"
              />
              <label htmlFor="file-upload">
                <Button variant="outline" className="cursor-pointer" asChild>
                  <span>Choose Files</span>
                </Button>
              </label>
            </div>

            {/* Error Messages */}
            {errors.length > 0 && (
              <div className="space-y-2">
                {errors.map((error, index) => (
                  <div key={index} className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    {error}
                  </div>
                ))}
              </div>
            )}

            {/* File List */}
            {files.length > 0 && (
              <div className="space-y-3">
                <h4 className="font-medium">Selected Files ({files.length})</h4>
                <div className="space-y-3 max-h-60 overflow-y-auto">
                  {files.map((file) => (
                    <div key={file.id} className="flex items-center gap-3 p-3 border rounded-lg">
                      {getFileIcon(file.type)}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium truncate">{file.name}</p>
                        <p className="text-xs text-gray-500">{formatFileSize(file.size)}</p>
                        {/* Basic Category Selection */}
                        <div className="mt-2">
                          <Select 
                            value={file.category} 
                            onValueChange={(value) => updateFileMetadata(file.id, 'category', value)}
                          >
                            <SelectTrigger className="h-8 text-xs">
                              <SelectValue placeholder="Select category" />
                            </SelectTrigger>
                            <SelectContent>
                              {DOCUMENT_CATEGORIES.map(category => (
                                <SelectItem key={category.value} value={category.value}>
                                  {category.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => removeFile(file.id)}
                        disabled={uploading}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={onClose}>
                Cancel
              </Button>
              <Button 
                onClick={() => setCurrentStep('metadata')} 
                disabled={!canProceedToMetadata()}
              >
                Next: Add Metadata
              </Button>
            </div>
          </div>
        )}

        {currentStep === 'metadata' && (
          <div className="space-y-6">
            <div className="text-sm text-gray-600 mb-4">
              Complete the metadata for each document to improve organization and searchability.
            </div>

            <div className="space-y-6 max-h-96 overflow-y-auto">
              {files.map((file) => (
                <div key={file.id} className="border rounded-lg p-4 space-y-4">
                  <div className="flex items-center gap-3">
                    {getFileIcon(file.type)}
                    <div>
                      <h4 className="font-medium">{file.name}</h4>
                      <p className="text-sm text-gray-500">{formatFileSize(file.size)}</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {/* Category */}
                    <div>
                      <Label>Category *</Label>
                      <Select 
                        value={file.category} 
                        onValueChange={(value) => updateFileMetadata(file.id, 'category', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          {DOCUMENT_CATEGORIES.map(category => (
                            <SelectItem key={category.value} value={category.value}>
                              {category.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Subcategory */}
                    <div>
                      <Label>Subcategory *</Label>
                      <Select 
                        value={file.subcategory} 
                        onValueChange={(value) => updateFileMetadata(file.id, 'subcategory', value)}
                        disabled={!file.category}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select subcategory" />
                        </SelectTrigger>
                        <SelectContent>
                          {getSubcategories(file.category || '').map(subcat => (
                            <SelectItem key={subcat} value={subcat}>
                              {subcat.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>

                    {/* Description */}
                    <div className="md:col-span-2">
                      <Label>Description</Label>
                      <Textarea
                        placeholder="Brief description of the document..."
                        value={file.description || ''}
                        onChange={(e) => updateFileMetadata(file.id, 'description', e.target.value)}
                        className="mt-1"
                      />
                    </div>

                    {/* Tags */}
                    <div className="md:col-span-2">
                      <Label>Tags</Label>
                      <div className="space-y-2">
                        <div className="flex gap-2">
                          <Input
                            placeholder="Add tag..."
                            value={newTag}
                            onChange={(e) => setNewTag(e.target.value)}
                            onKeyPress={(e) => {
                              if (e.key === 'Enter') {
                                e.preventDefault();
                                addTag(file.id);
                              }
                            }}
                          />
                          <Button
                            type="button"
                            size="sm"
                            onClick={() => addTag(file.id)}
                            disabled={!newTag.trim()}
                          >
                            <Plus className="h-4 w-4" />
                          </Button>
                        </div>
                        {file.tags && file.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1">
                            {file.tags.map((tag, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {tag}
                                <button
                                  onClick={() => removeTag(file.id, index)}
                                  className="ml-1 hover:text-red-500"
                                >
                                  <X className="h-3 w-3" />
                                </button>
                              </Badge>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Settings */}
                    <div className="md:col-span-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div className="flex items-center space-x-2">
                        <Switch
                          id={`confidential-${file.id}`}
                          checked={file.isConfidential || false}
                          onCheckedChange={(checked) => updateFileMetadata(file.id, 'isConfidential', checked)}
                        />
                        <Label htmlFor={`confidential-${file.id}`}>Confidential</Label>
                      </div>

                      <div>
                        <Label>Permissions</Label>
                        <Select 
                          value={file.permissions} 
                          onValueChange={(value) => updateFileMetadata(file.id, 'permissions', value)}
                        >
                          <SelectTrigger>
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="internal">Internal Only</SelectItem>
                            <SelectItem value="shareable">Shareable</SelectItem>
                            <SelectItem value="public">Public</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>

                      <div>
                        <Label>Expiry Date</Label>
                        <Input
                          type="date"
                          value={file.expiryDate || ''}
                          onChange={(e) => updateFileMetadata(file.id, 'expiryDate', e.target.value)}
                          className="mt-1"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>

            {/* Error Messages */}
            {errors.length > 0 && (
              <div className="space-y-2">
                {errors.map((error, index) => (
                  <div key={index} className="flex items-center gap-2 text-red-600 text-sm">
                    <AlertCircle className="h-4 w-4" />
                    {error}
                  </div>
                ))}
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between">
              <Button variant="outline" onClick={() => setCurrentStep('upload')}>
                Back
              </Button>
              <Button 
                onClick={uploadFiles} 
                disabled={!canUpload() || uploading}
              >
                {uploading ? 'Uploading...' : `Upload ${files.length} Documents`}
              </Button>
            </div>

            {/* Upload Progress */}
            {uploading && (
              <div className="space-y-2">
                {files.map((file) => (
                  <div key={file.id} className="flex items-center gap-3">
                    <span className="text-sm truncate flex-1">{file.name}</span>
                    <div className="w-32 h-2 bg-gray-200 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-500 transition-all duration-300"
                        style={{ width: `${uploadProgress[file.id] || 0}%` }}
                      />
                    </div>
                    {uploadProgress[file.id] === 100 && (
                      <CheckCircle className="h-4 w-4 text-green-500" />
                    )}
                  </div>
                ))}
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}; 