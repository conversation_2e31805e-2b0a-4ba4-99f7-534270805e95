<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Fixes Test Page</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 20px; 
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        .button:hover {
            background: #2563eb;
        }
        .success { color: #059669; }
        .error { color: #dc2626; }
        .warning { color: #d97706; }
        .log {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
            margin-left: 10px;
        }
        .status.pass { background: #dcfce7; color: #166534; }
        .status.fail { background: #fef2f2; color: #991b1b; }
        .status.warn { background: #fefce8; color: #a16207; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 UI Fixes Test Page</h1>
        <p>This page helps test and apply fixes for the UI issues identified in the system health check.</p>
        
        <div class="test-section">
            <h2>🎯 Quick Actions</h2>
            <button class="button" onclick="runDiagnostics()">🔍 Run Diagnostics</button>
            <button class="button" onclick="applyFixes()">🔧 Apply Fixes</button>
            <button class="button" onclick="openMainApp()">🚀 Open Main App</button>
            <button class="button" onclick="clearStorage()">🗑️ Clear Storage</button>
        </div>
        
        <div class="test-section">
            <h2>📊 Current Status</h2>
            <div id="status-display">
                <p>Click "Run Diagnostics" to check current status...</p>
            </div>
        </div>
        
        <div class="test-section">
            <h2>📝 Console Log</h2>
            <div id="console-log" class="log">
                Console output will appear here...
            </div>
        </div>
        
        <div class="test-section">
            <h2>🔐 Authentication Test</h2>
            <button class="button" onclick="testAuth()">Test Authentication</button>
            <button class="button" onclick="createDemoUser()">Create Demo User</button>
            <button class="button" onclick="loginAsDemo()">Login as Demo</button>
            <div id="auth-status"></div>
        </div>
    </div>

    <script>
        // Capture console output
        const originalLog = console.log;
        const originalError = console.error;
        const originalWarn = console.warn;
        
        function logToPage(message, type = 'log') {
            const logDiv = document.getElementById('console-log');
            const timestamp = new Date().toLocaleTimeString();
            const color = type === 'error' ? '#ef4444' : type === 'warn' ? '#f59e0b' : '#10b981';
            logDiv.innerHTML += `<div style="color: ${color}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            logToPage(args.join(' '), 'log');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            logToPage(args.join(' '), 'error');
        };
        
        console.warn = function(...args) {
            originalWarn.apply(console, args);
            logToPage(args.join(' '), 'warn');
        };
        
        // Test functions
        function runDiagnostics() {
            console.log('🔍 Running diagnostics...');
            
            const checks = {
                'React Root': document.getElementById('root') ? 'pass' : 'fail',
                'React Content': document.getElementById('root')?.children.length > 0 ? 'pass' : 'fail',
                'Form Elements': document.querySelectorAll('input, textarea, select').length > 0 ? 'pass' : 'fail',
                'Navigation': document.querySelectorAll('nav, aside, [class*="sidebar"]').length > 0 ? 'pass' : 'fail',
                'Buttons': document.querySelectorAll('button').length > 0 ? 'pass' : 'fail',
                'Authentication': localStorage.getItem('arwa_user') ? 'pass' : 'fail'
            };
            
            let statusHtml = '<h3>Diagnostic Results:</h3>';
            for (const [check, status] of Object.entries(checks)) {
                statusHtml += `<div>${check} <span class="status ${status}">${status.toUpperCase()}</span></div>`;
            }
            
            document.getElementById('status-display').innerHTML = statusHtml;
            console.log('✅ Diagnostics completed');
        }
        
        function applyFixes() {
            console.log('🔧 Applying fixes...');
            
            // Load and execute the comprehensive fix script
            const script = document.createElement('script');
            script.src = '/comprehensive-ui-fix.js';
            script.onload = () => {
                console.log('✅ Fix script loaded');
                if (window.runComprehensiveFix) {
                    window.runComprehensiveFix();
                }
            };
            script.onerror = () => {
                console.error('❌ Failed to load fix script');
                // Apply fixes manually
                createDemoUser();
            };
            document.head.appendChild(script);
        }
        
        function openMainApp() {
            console.log('🚀 Opening main app...');
            window.open('http://localhost:3005', '_blank');
        }
        
        function clearStorage() {
            console.log('🗑️ Clearing storage...');
            localStorage.clear();
            sessionStorage.clear();
            console.log('✅ Storage cleared');
            runDiagnostics();
        }
        
        function testAuth() {
            console.log('🔐 Testing authentication...');
            const user = localStorage.getItem('arwa_user');
            const authDiv = document.getElementById('auth-status');
            
            if (user) {
                try {
                    const userData = JSON.parse(user);
                    authDiv.innerHTML = `<div class="success">✅ Authenticated as: ${userData.name} (${userData.role})</div>`;
                    console.log('✅ User is authenticated');
                } catch (e) {
                    authDiv.innerHTML = `<div class="error">❌ Invalid user data in storage</div>`;
                    console.error('❌ Invalid user data');
                }
            } else {
                authDiv.innerHTML = `<div class="warning">⚠️ No user found in storage</div>`;
                console.warn('⚠️ No authentication found');
            }
        }
        
        function createDemoUser() {
            console.log('👤 Creating demo user...');
            
            const demoUser = {
                id: 'demo-admin-001',
                email: '<EMAIL>',
                name: 'Demo Admin',
                role: 'admin',
                phone: '******-0123',
                active: true,
                companyName: 'Demo Company',
                plan: 'professional',
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                lastLoginAt: new Date().toISOString(),
                permissions: [
                    'claims:view_all', 'claims:update_all', 'claims:assign', 'claims:delete', 'claims:export',
                    'claimants:view', 'claimants:update', 'claimants:create', 'claimants:delete', 'claimants:export',
                    'activities:create', 'activities:view_all', 'activities:export',
                    'documents:view', 'documents:upload', 'documents:generate', 'documents:approve', 'documents:delete',
                    'batch:import', 'batch:export', 'batch:validate',
                    'analytics:view_all', 'analytics:export',
                    'workflow:create', 'workflow:manage', 'workflow:execute',
                    'users:view', 'users:create', 'users:update', 'users:delete',
                    'system:configure', 'system:backup', 'system:audit'
                ]
            };
            
            localStorage.setItem('arwa_user', JSON.stringify(demoUser));
            console.log('✅ Demo user created');
            testAuth();
        }
        
        function loginAsDemo() {
            console.log('🔑 Logging in as demo user...');
            createDemoUser();
            setTimeout(() => {
                console.log('🔄 Refreshing main app...');
                window.location.href = 'http://localhost:3005';
            }, 1000);
        }
        
        // Auto-run diagnostics on page load
        window.addEventListener('load', () => {
            console.log('📄 Test page loaded');
            setTimeout(runDiagnostics, 500);
        });
    </script>
</body>
</html>
