import { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>er, Card<PERSON><PERSON>le, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Shield, 
  AlertTriangle, 
  CheckCircle, 
  Clock, 
  FileText, 
  DollarSign,
  Gavel,
  MapPin,
  TrendingUp,
  AlertCircle,
  Users,
  Calendar,
  Download,
  Eye,
  Settings
} from 'lucide-react';
import { StateComplianceRule, ComplianceAlert, StateLicenseStatus } from '@/types/compliance';
import { usePricingLogic } from '@/hooks/usePricingLogic';

interface StateComplianceEngineProps {
  onUpgrade?: () => void;
}

export default function StateComplianceEngine({ onUpgrade }: StateComplianceEngineProps) {
  const { currentPlan, subscription } = usePricingLogic();
  const [activeTab, setActiveTab] = useState('overview');
  const [complianceAlerts, setComplianceAlerts] = useState<ComplianceAlert[]>([]);
  const [licenseStatuses, setLicenseStatuses] = useState<StateLicenseStatus[]>([]);
  const [complianceScore, setComplianceScore] = useState(92);

  // Mock data - would come from API in real implementation
  useEffect(() => {
    const mockAlerts: ComplianceAlert[] = [
      {
        id: '1',
        user_id: 'user123',
        claim_id: 'claim456',
        state_code: 'CA',
        alert_type: 'deadline_approaching',
        severity: 'medium',
        title: 'California Filing Deadline Approaching',
        message: 'Notice of Asset Recovery filing due in 5 days for claim #456',
        details: 'California requires notice filing within 30 days of first contact. Current claim is approaching deadline.',
        action_required: true,
        suggested_actions: [
          'Complete Notice of Asset Recovery form',
          'Submit filing fee payment',
          'Upload required documentation'
        ],
        deadline: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
        resolution_status: 'open',
        created_at: new Date(),
        related_rules: ['CA-FILING-001', 'CA-NOTICE-002']
      },
      {
        id: '2',
        user_id: 'user123',
        state_code: 'TX',
        alert_type: 'rule_change',
        severity: 'high',
        title: 'Texas Fee Cap Update',
        message: 'Texas has updated maximum fee percentages effective January 1st',
        details: 'Maximum contingency fee reduced from 30% to 25% for claims over $10,000.',
        action_required: true,
        suggested_actions: [
          'Review active Texas claims',
          'Update fee agreements',
          'Notify affected claimants'
        ],
        resolution_status: 'open',
        created_at: new Date(),
        related_rules: ['TX-FEE-001']
      }
    ];

    const mockLicenses: StateLicenseStatus[] = [
      {
        user_id: 'user123',
        state_code: 'CA',
        license_number: 'CA-AR-12345',
        license_type: 'Asset Recovery License',
        status: 'active',
        issue_date: new Date('2023-01-15'),
        expiration_date: new Date('2024-01-15'),
        renewal_date: new Date('2023-12-15'),
        issuing_authority: 'California Department of Financial Protection',
        license_fees_paid: true,
        continuing_education_current: true,
        bond_status: 'current',
        bond_amount: 50000,
        bond_expiration: new Date('2024-01-15'),
        violations: [],
        last_verified: new Date()
      },
      {
        user_id: 'user123',
        state_code: 'TX',
        license_type: 'Asset Recovery License',
        status: 'active',
        issue_date: new Date('2023-03-01'),
        expiration_date: new Date('2024-03-01'),
        issuing_authority: 'Texas Department of Banking',
        license_fees_paid: true,
        continuing_education_current: true,
        bond_status: 'not_required',
        violations: [],
        last_verified: new Date()
      }
    ];

    setComplianceAlerts(mockAlerts);
    setLicenseStatuses(mockLicenses);
  }, []);

  const hasAccess = currentPlan && ['bronze', 'silver', 'gold', 'topaz', 'ruby', 'diamond'].includes(currentPlan.id);
  const hasAdvancedCompliance = currentPlan && ['gold', 'diamond'].includes(currentPlan.id);
  const hasTeamCompliance = currentPlan && ['topaz', 'ruby', 'diamond'].includes(currentPlan.id);

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      default: return 'bg-blue-500';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'pending': return 'bg-yellow-500';
      case 'expired': return 'bg-red-500';
      case 'suspended': return 'bg-orange-500';
      default: return 'bg-gray-500';
    }
  };

  if (!hasAccess) {
    return (
      <Card className="p-6">
        <div className="text-center space-y-4">
          <Shield className="h-16 w-16 mx-auto text-gray-400" />
          <h3 className="text-xl font-semibold">State Compliance Engine</h3>
          <p className="text-gray-600 max-w-md mx-auto">
            Comprehensive state-specific compliance management, automated rule checking, and regulatory updates.
          </p>
          <div className="bg-gradient-to-r from-blue-50 to-indigo-50 p-4 rounded-lg">
            <p className="text-sm text-gray-700 mb-3">
              <strong>Included in Bronze and above:</strong>
            </p>
            <ul className="text-sm text-gray-600 space-y-1 text-left max-w-md mx-auto">
              <li>• State-specific fee cap enforcement</li>
              <li>• Automated filing requirement tracking</li>
              <li>• Compliance deadline management</li>
              <li>• Regulatory update notifications</li>
              <li>• State document templates</li>
            </ul>
          </div>
          <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
            Upgrade to Access Compliance Engine
          </Button>
        </div>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Compliance Overview Dashboard */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Shield className="h-6 w-6 text-blue-600" />
              <CardTitle>State Compliance Dashboard</CardTitle>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-right">
                <div className="text-sm text-gray-500">Compliance Score</div>
                <div className="text-2xl font-bold text-green-600">{complianceScore}%</div>
              </div>
              <div className="h-12 w-12 rounded-full bg-green-100 flex items-center justify-center">
                <CheckCircle className="h-6 w-6 text-green-600" />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <MapPin className="h-5 w-5 text-blue-600" />
                <div>
                  <div className="text-sm text-gray-500">Licensed States</div>
                  <div className="text-xl font-semibold">{subscription?.active_state_licenses.length || 0}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                <div>
                  <div className="text-sm text-gray-500">Active Alerts</div>
                  <div className="text-xl font-semibold">{complianceAlerts.length}</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-yellow-600" />
                <div>
                  <div className="text-sm text-gray-500">Upcoming Deadlines</div>
                  <div className="text-xl font-semibold">3</div>
                </div>
              </div>
            </Card>
            <Card className="p-4">
              <div className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-green-600" />
                <div>
                  <div className="text-sm text-gray-500">Compliance Trend</div>
                  <div className="text-xl font-semibold text-green-600">+2.3%</div>
                </div>
              </div>
            </Card>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="overview">Overview</TabsTrigger>
              <TabsTrigger value="alerts">Alerts</TabsTrigger>
              <TabsTrigger value="licenses">Licenses</TabsTrigger>
              <TabsTrigger value="rules">Rules</TabsTrigger>
              <TabsTrigger value="reports">Reports</TabsTrigger>
            </TabsList>

            <TabsContent value="overview" className="space-y-4">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Recent Compliance Activity</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-3">
                    <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <CheckCircle className="h-4 w-4 text-green-600" />
                        <span className="text-sm">California filing completed</span>
                      </div>
                      <span className="text-xs text-gray-500">2 hours ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-yellow-600" />
                        <span className="text-sm">Texas bond renewal due</span>
                      </div>
                      <span className="text-xs text-gray-500">1 day ago</span>
                    </div>
                    <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <FileText className="h-4 w-4 text-blue-600" />
                        <span className="text-sm">New Florida regulations published</span>
                      </div>
                      <span className="text-xs text-gray-500">3 days ago</span>
                    </div>
                  </CardContent>
                </Card>

                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">State Performance Summary</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {subscription?.active_state_licenses.slice(0, 3).map((stateCode) => (
                      <div key={stateCode} className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-xs font-semibold text-blue-700">{stateCode}</span>
                          </div>
                          <span className="font-medium">{stateCode === 'CA' ? 'California' : stateCode === 'TX' ? 'Texas' : 'Florida'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Progress value={Math.floor(Math.random() * 20) + 80} className="w-20" />
                          <span className="text-sm text-gray-600">{Math.floor(Math.random() * 20) + 80}%</span>
                        </div>
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="alerts" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Compliance Alerts</h3>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4 mr-2" />
                  Configure Alerts
                </Button>
              </div>
              <div className="space-y-3">
                {complianceAlerts.map((alert) => (
                  <Card key={alert.id} className="p-4">
                    <div className="flex items-start gap-4">
                      <div className={`w-2 h-12 rounded-full ${getSeverityColor(alert.severity)}`} />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-semibold">{alert.title}</h4>
                          <div className="flex items-center gap-2">
                            <Badge variant={alert.severity === 'high' ? 'destructive' : 'secondary'}>
                              {alert.severity}
                            </Badge>
                            <Badge variant="outline">{alert.state_code}</Badge>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600 mb-3">{alert.message}</p>
                        {alert.deadline && (
                          <div className="flex items-center gap-2 text-sm text-orange-600 mb-3">
                            <Clock className="h-4 w-4" />
                            <span>Due: {alert.deadline.toLocaleDateString()}</span>
                          </div>
                        )}
                        {alert.suggested_actions.length > 0 && (
                          <div className="space-y-2">
                            <div className="text-sm font-medium">Suggested Actions:</div>
                            <ul className="text-sm text-gray-600 space-y-1">
                              {alert.suggested_actions.map((action, index) => (
                                <li key={index} className="flex items-center gap-2">
                                  <div className="w-1.5 h-1.5 bg-gray-400 rounded-full" />
                                  {action}
                                </li>
                              ))}
                            </ul>
                          </div>
                        )}
                        <div className="flex items-center gap-2 mt-4">
                          <Button size="sm">Resolve</Button>
                          <Button size="sm" variant="outline">View Details</Button>
                          <Button size="sm" variant="ghost">Snooze</Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="licenses" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">License Management</h3>
                <Button size="sm" variant="outline">
                  <MapPin className="h-4 w-4 mr-2" />
                  Add State License
                </Button>
              </div>
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                {licenseStatuses.map((license) => (
                  <Card key={license.state_code} className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="flex items-center gap-2">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="font-semibold text-blue-700">{license.state_code}</span>
                        </div>
                        <div>
                          <h4 className="font-semibold">{license.state_code === 'CA' ? 'California' : 'Texas'}</h4>
                          <p className="text-sm text-gray-600">{license.license_type}</p>
                        </div>
                      </div>
                      <Badge className={getStatusColor(license.status)}>
                        {license.status}
                      </Badge>
                    </div>
                    
                    <div className="space-y-3 text-sm">
                      {license.license_number && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">License #:</span>
                          <span className="font-medium">{license.license_number}</span>
                        </div>
                      )}
                      <div className="flex justify-between">
                        <span className="text-gray-600">Expires:</span>
                        <span className="font-medium">{license.expiration_date?.toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Fees Paid:</span>
                        <span className={license.license_fees_paid ? 'text-green-600' : 'text-red-600'}>
                          {license.license_fees_paid ? 'Yes' : 'No'}
                        </span>
                      </div>
                      {license.bond_status !== 'not_required' && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">Bond Status:</span>
                          <span className={license.bond_status === 'current' ? 'text-green-600' : 'text-red-600'}>
                            {license.bond_status}
                          </span>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center gap-2 mt-4">
                      <Button size="sm" variant="outline">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button size="sm" variant="outline">
                        <Download className="h-4 w-4 mr-1" />
                        Export
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="rules" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Compliance Rules</h3>
                {!hasAdvancedCompliance && (
                  <Badge variant="outline" className="text-orange-600">
                    Advanced rules require Gold+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <h4 className="font-semibold">Fee Caps</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Automatic enforcement of state-specific maximum fee percentages
                  </p>
                  <div className="text-xs text-gray-500">
                    {subscription?.active_state_licenses.length || 0} states configured
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <FileText className="h-5 w-5 text-blue-600" />
                    <h4 className="font-semibold">Filing Requirements</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    Automated tracking of filing deadlines and requirements
                  </p>
                  <div className="text-xs text-gray-500">
                    23 active filing requirements
                  </div>
                </Card>

                <Card className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <Gavel className="h-5 w-5 text-purple-600" />
                    <h4 className="font-semibold">Legal Compliance</h4>
                  </div>
                  <p className="text-sm text-gray-600 mb-3">
                    State-specific legal requirements and documentation standards
                  </p>
                  <div className="text-xs text-gray-500">
                    156 compliance rules active
                  </div>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="reports" className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Compliance Reports</h3>
                {!hasTeamCompliance && (
                  <Badge variant="outline" className="text-orange-600">
                    Advanced reporting requires Topaz+ plan
                  </Badge>
                )}
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Available Reports</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">Monthly Compliance Summary</span>
                      <Button size="sm" variant="outline">Generate</Button>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">State License Status Report</span>
                      <Button size="sm" variant="outline">Generate</Button>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <span className="text-sm">Compliance Violations Report</span>
                      <Button size="sm" variant="outline">Generate</Button>
                    </div>
                  </div>
                </Card>

                <Card className="p-4">
                  <h4 className="font-semibold mb-3">Scheduled Reports</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between p-2 bg-blue-50 rounded">
                      <div>
                        <div className="text-sm font-medium">Weekly Alert Summary</div>
                        <div className="text-xs text-gray-600">Every Monday at 9 AM</div>
                      </div>
                      <Button size="sm" variant="ghost">Edit</Button>
                    </div>
                    <div className="flex items-center justify-between p-2 bg-green-50 rounded">
                      <div>
                        <div className="text-sm font-medium">Monthly Compliance Score</div>
                        <div className="text-xs text-gray-600">1st of each month</div>
                      </div>
                      <Button size="sm" variant="ghost">Edit</Button>
                    </div>
                  </div>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Upgrade Prompt for Limited Features */}
      {!hasAdvancedCompliance && (
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="font-semibold text-gray-900">Unlock Advanced Compliance Features</h3>
                  <p className="text-sm text-gray-600">
                    Upgrade to Gold plan for AI-powered compliance analysis, all-state coverage, and predictive risk assessment.
                  </p>
                </div>
              </div>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Gold
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
} 