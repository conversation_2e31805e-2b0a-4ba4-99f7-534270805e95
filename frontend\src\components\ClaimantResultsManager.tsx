import React, { useState, useEffect } from 'react';
import { ClaimantResult, ClaimantResultCard } from './ClaimantResultCard';
import { analyticsEngine } from '@/services/analyticsEngine';
import { notificationService } from '@/services/notificationService';
import { securityManager } from '@/services/securityManager';

interface ClaimantResultsManagerProps {
  results: ClaimantResult[];
  searchId: string;
  onResultsUpdate?: (updatedResults: ClaimantResult[]) => void;
}

export const ClaimantResultsManager: React.FC<ClaimantResultsManagerProps> = ({
  results: initialResults,
  searchId,
  onResultsUpdate
}) => {
  const [results, setResults] = useState<ClaimantResult[]>(initialResults);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [sortBy, setSortBy] = useState<'confidence' | 'quality' | 'lastActivity'>('confidence');
  const [showValidatedOnly, setShowValidatedOnly] = useState(false);

  // Update results when props change
  useEffect(() => {
    setResults(initialResults);
  }, [initialResults]);

  // Handle validation of a claimant
  const handleValidateClaimant = async (resultId: string, method: string, notes?: string) => {
    try {
      const updatedResults = results.map(result => {
        if (result.discoveryId === resultId) {
          const updatedResult = {
            ...result,
            validationStatus: 'in_progress' as const,
            contactAttempts: [
              ...(result.contactAttempts || []),
              {
                method,
                timestamp: new Date(),
                status: 'sent' as const,
                notes: notes || `Validation attempt via ${method}`
              }
            ]
          };

          // Track validation attempt in analytics
          analyticsEngine.recordSearchEvent({
            userId: 'current-user', // Would get from auth context
            searchType: 'validation',
            state: result.addresses?.[0]?.state || 'unknown',
            success: true,
            propertiesFound: 0,
            responseTime: 0,
            estimatedValue: 0,
            timestamp: new Date()
          });

          // Log security event
          securityManager.logUserAction(
            'current-user',
            'validate_claimant',
            'claimant_result',
            true,
            '127.0.0.1', // Would get real IP
            navigator.userAgent,
            { 
              resultId, 
              method, 
              confidence: result.confidence,
              qualityScore: result.qualityScore 
            }
          );

          return updatedResult;
        }
        return result;
      });

      setResults(updatedResults);
      onResultsUpdate?.(updatedResults);

      // Send notification about validation attempt
      await notificationService.sendNotification(
        'validation_initiated',
        'current-user',
        {
          claimantName: results.find(r => r.discoveryId === resultId)?.primaryName,
          method,
          timestamp: new Date().toISOString()
        }
      );

    } catch (error) {
      console.error('Failed to validate claimant:', error);
    }
  };

  // Handle marking a claimant as invalid
  const handleMarkInvalid = async (resultId: string, reason: string) => {
    try {
      const updatedResults = results.map(result => {
        if (result.discoveryId === resultId) {
          return {
            ...result,
            validationStatus: 'invalid' as const,
            contactAttempts: [
              ...(result.contactAttempts || []),
              {
                method: 'manual_review',
                timestamp: new Date(),
                status: 'failed' as const,
                notes: reason
              }
            ]
          };
        }
        return result;
      });

      setResults(updatedResults);
      onResultsUpdate?.(updatedResults);

      // Log security event
      securityManager.logUserAction(
        'current-user',
        'mark_invalid_claimant',
        'claimant_result',
        true,
        '127.0.0.1',
        navigator.userAgent,
        { resultId, reason }
      );

    } catch (error) {
      console.error('Failed to mark claimant invalid:', error);
    }
  };

  // Handle contact attempts
  const handleContactAttempt = async (resultId: string, method: string, contact: string) => {
    try {
      const updatedResults = results.map(result => {
        if (result.discoveryId === resultId) {
          return {
            ...result,
            contactAttempts: [
              ...(result.contactAttempts || []),
              {
                method,
                timestamp: new Date(),
                status: 'sent' as const,
                notes: `Contact attempted via ${method}: ${contact}`
              }
            ]
          };
        }
        return result;
      });

      setResults(updatedResults);
      onResultsUpdate?.(updatedResults);

      // Initiate actual contact based on method
      await initiateContact(method, contact, resultId);

    } catch (error) {
      console.error('Failed to record contact attempt:', error);
    }
  };

  // Initiate actual contact
  const initiateContact = async (method: string, contact: string, resultId: string) => {
    const result = results.find(r => r.discoveryId === resultId);
    if (!result) return;

    switch (method) {
      case 'email':
        // Generate and send email
        await notificationService.sendNotification(
          'claimant_contact_email',
          'system',
          {
            recipientEmail: contact,
            claimantName: result.primaryName,
            searchId,
            contactMethod: 'email'
          }
        );
        break;

      case 'phone':
        // Log phone call attempt (would integrate with phone system)
        console.log(`Phone call initiated to: ${contact}`);
        break;

      case 'mail':
        // Generate mailing list entry
        console.log(`Physical mail queued for: ${contact}`);
        break;

      case 'social':
        // Open social media link
        window.open(contact, '_blank');
        break;

      default:
        console.log(`Contact initiated via ${method}: ${contact}`);
    }
  };

  // Filter and sort results
  const getFilteredAndSortedResults = () => {
    let filtered = results;

    // Filter by status
    if (filterStatus !== 'all') {
      filtered = filtered.filter(result => result.validationStatus === filterStatus);
    }

    // Filter validated only
    if (showValidatedOnly) {
      filtered = filtered.filter(result => result.validationStatus === 'validated');
    }

    // Sort results
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'confidence':
          return b.confidence - a.confidence;
        case 'quality':
          return b.qualityScore - a.qualityScore;
        case 'lastActivity':
          if (!a.lastActivity && !b.lastActivity) return 0;
          if (!a.lastActivity) return 1;
          if (!b.lastActivity) return -1;
          return new Date(b.lastActivity).getTime() - new Date(a.lastActivity).getTime();
        default:
          return 0;
      }
    });

    return filtered;
  };

  const filteredResults = getFilteredAndSortedResults();

  // Calculate summary statistics
  const stats = {
    total: results.length,
    pending: results.filter(r => r.validationStatus === 'pending').length,
    inProgress: results.filter(r => r.validationStatus === 'in_progress').length,
    validated: results.filter(r => r.validationStatus === 'validated').length,
    invalid: results.filter(r => r.validationStatus === 'invalid').length,
    avgConfidence: results.reduce((sum, r) => sum + r.confidence, 0) / results.length,
    avgQuality: results.reduce((sum, r) => sum + r.qualityScore, 0) / results.length
  };

  return (
    <div className="space-y-6">
      {/* Header with Summary Stats */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-2xl font-bold text-gray-900">Discovery Results</h2>
          <div className="text-sm text-gray-500">Search ID: {searchId}</div>
        </div>

        {/* Summary Statistics */}
        <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-4 mb-6">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.total}</div>
            <div className="text-xs text-gray-500">Total Found</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">{stats.pending}</div>
            <div className="text-xs text-gray-500">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">{stats.inProgress}</div>
            <div className="text-xs text-gray-500">In Progress</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">{stats.validated}</div>
            <div className="text-xs text-gray-500">Validated</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-red-600">{stats.invalid}</div>
            <div className="text-xs text-gray-500">Invalid</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-600">{Math.round(stats.avgConfidence * 100)}%</div>
            <div className="text-xs text-gray-500">Avg Confidence</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-indigo-600">{Math.round(stats.avgQuality * 100)}</div>
            <div className="text-xs text-gray-500">Avg Quality</div>
          </div>
        </div>

        {/* Filters and Controls */}
        <div className="flex flex-wrap gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Filter by Status</label>
            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Results</option>
              <option value="pending">Pending</option>
              <option value="in_progress">In Progress</option>
              <option value="validated">Validated</option>
              <option value="invalid">Invalid</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Sort by</label>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'confidence' | 'quality' | 'lastActivity')}
              className="px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="confidence">Confidence Score</option>
              <option value="quality">Quality Score</option>
              <option value="lastActivity">Last Activity</option>
            </select>
          </div>

          <div className="flex items-end">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={showValidatedOnly}
                onChange={(e) => setShowValidatedOnly(e.target.checked)}
                className="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200"
              />
              <span className="text-sm text-gray-700">Show validated only</span>
            </label>
          </div>
        </div>
      </div>

      {/* Results List */}
      <div className="space-y-4">
        {filteredResults.length === 0 ? (
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8 text-center">
            <div className="text-gray-500 text-lg mb-2">No results match your filters</div>
            <div className="text-gray-400 text-sm">
              Try adjusting your filter criteria or search parameters
            </div>
          </div>
        ) : (
          filteredResults.map((result) => (
            <ClaimantResultCard
              key={result.discoveryId}
              result={result}
              onValidate={handleValidateClaimant}
              onMarkInvalid={handleMarkInvalid}
              onContactAttempt={handleContactAttempt}
            />
          ))
        )}
      </div>

      {/* Bulk Actions (if multiple results) */}
      {results.length > 1 && (
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Bulk Actions</h3>
          <div className="flex flex-wrap gap-3">
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors">
              Export All Results
            </button>
            <button className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
              Validate High Confidence
            </button>
            <button className="px-4 py-2 bg-yellow-600 text-white rounded-md hover:bg-yellow-700 transition-colors">
              Generate Summary Report
            </button>
            <button className="px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors">
              Schedule Follow-ups
            </button>
          </div>
        </div>
      )}
    </div>
  );
}; 