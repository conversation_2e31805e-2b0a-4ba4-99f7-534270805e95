import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { AlertCircle, ArrowLeft, FileText, User, Phone, Mail, Upload } from 'lucide-react';
import { ClaimDetailViewProps, ClaimFormData, ClaimDocument } from '../../types';
import { useClaim, useClaimMutations } from '../../hooks';
import { ClaimHeader, ClaimInfo, ActivityTimeline } from '../';
import { QuickActions } from '../QuickActions/QuickActions';
import { EmailTemplates } from '../EmailTemplates/EmailTemplates';
import { EmailDashboard } from '../EmailDashboard/EmailDashboard';
import { formatDate } from '@/utils/formatting';
import DocumentUpload from '@/components/claims/DocumentUpload';
import DocumentViewer from '@/components/claims/DocumentViewer';
import DocumentModal from '@/components/claims/DocumentModal';

export const ClaimDetailView: React.FC<ClaimDetailViewProps> = ({ 
  claimId, 
  onBack 
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [editedClaim, setEditedClaim] = useState<ClaimFormData>({});
  const [showUploadModal, setShowUploadModal] = useState(false);
  const [showDocumentModal, setShowDocumentModal] = useState(false);
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [selectedDocumentIndex, setSelectedDocumentIndex] = useState(0);

  const { claim, activities, documents, loading, error, refreshClaim } = useClaim(claimId);
  const { isUpdating, isAddingNote, updateClaim, addNote } = useClaimMutations(refreshClaim);

  const handleSave = async () => {
    if (!claim) return;
    
    try {
      await updateClaim(claimId, editedClaim);
      setIsEditing(false);
    } catch (error) {
      console.error('Failed to save claim:', error);
    }
  };

  const handleStartEdit = () => {
    setEditedClaim(claim || {});
    setIsEditing(true);
  };

  const handleCancelEdit = () => {
    setEditedClaim({});
    setIsEditing(false);
  };

  const handleUploadComplete = () => {
    setShowUploadModal(false);
    refreshClaim();
  };

  const handleAddNote = async (note: string) => {
    await addNote(claimId, note);
  };

  // New function for quick activities
  const handleAddActivity = async (title: string, description: string, activityType: string) => {
    await addNote(claimId, `${title}: ${description}`);
  };

  // New function for quick status updates
  const handleUpdateStatus = async (status: string) => {
    if (!claim) return;
    
    try {
      await updateClaim(claimId, { ...claim, status });
      // Add activity log for status change
      await addNote(claimId, `Status updated to: ${status.replace('_', ' ')}`);
    } catch (error) {
      console.error('Failed to update status:', error);
    }
  };

  // New function for email sending
  const handleSendEmail = async (subject: string, body: string) => {
    try {
      // Log the email activity
      await addNote(claimId, `Email sent: ${subject}`);
      setShowEmailModal(false);
    } catch (error) {
      console.error('Failed to log email activity:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error || !claim) {
    return (
      <Card className="border-red-200">
        <CardContent className="pt-6">
          <div className="text-center text-red-600">
            <AlertCircle className="h-8 w-8 mx-auto mb-2" />
            <p className="font-semibold">Error Loading Claim</p>
            <p className="text-sm">{error || 'Claim not found'}</p>
            <Button variant="outline" onClick={onBack} className="mt-4">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <ClaimHeader
        claim={claim}
        isEditing={isEditing}
        isUpdating={isUpdating}
        onBack={onBack}
        onStartEdit={handleStartEdit}
        onSave={handleSave}
        onCancelEdit={handleCancelEdit}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Content */}
        <div className="lg:col-span-2 space-y-6">
          <ClaimInfo
            claim={claim}
            isEditing={isEditing}
            editedClaim={editedClaim}
            onUpdate={setEditedClaim}
          />

          <ActivityTimeline
            activities={activities}
            isAddingNote={isAddingNote}
            onAddNote={handleAddNote}
            onAddActivity={handleAddActivity}
          />

          <EmailDashboard
            claim={claim}
            onComposeNew={() => setShowEmailModal(true)}
          />
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Quick Actions - NEW */}
          <QuickActions
            claim={claim}
            onAddActivity={handleAddActivity}
            onUpdateStatus={handleUpdateStatus}
            onOpenEmailTemplates={() => setShowEmailModal(true)}
          />

          {/* Quick Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Quick Stats</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Created</span>
                <span className="text-sm font-medium">{formatDate(claim.created_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last Updated</span>
                <span className="text-sm font-medium">{formatDate(claim.updated_at)}</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Complexity Score</span>
                <span className="text-sm font-medium">{claim.complexity_score}/10</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Commission Rate</span>
                <span className="text-sm font-medium">{(claim.commission_rate * 100).toFixed(1)}%</span>
              </div>
            </CardContent>
          </Card>

          {/* Documents */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Documents
                </span>
                <Button size="sm" onClick={() => setShowUploadModal(true)}>
                  <Upload className="h-4 w-4 mr-2" />
                  Upload
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent>
              {documents.length === 0 ? (
                <p className="text-gray-500 text-center py-4">No documents uploaded</p>
              ) : (
                <div className="space-y-2">
                  {documents.map((doc, index) => (
                    <DocumentViewer 
                      key={doc.id} 
                      document={doc} 
                      onView={() => {
                        setSelectedDocumentIndex(index);
                        setShowDocumentModal(true);
                      }}
                    />
                  ))}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                Contact Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-600">Last Contact</label>
                <p className="text-sm">{claim.last_contact_date ? formatDate(claim.last_contact_date) : 'No contact recorded'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-600">Next Follow-up</label>
                <p className="text-sm">{claim.next_followup_date ? formatDate(claim.next_followup_date) : 'Not scheduled'}</p>
              </div>
              {/* Note: Quick call/email actions are now in QuickActions component */}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Modals */}
      {showUploadModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <DocumentUpload
            claimId={claimId}
            onUploadComplete={handleUploadComplete}
            onClose={() => setShowUploadModal(false)}
          />
        </div>
      )}

      {showDocumentModal && (
        <DocumentModal
          documents={documents}
          initialDocumentIndex={selectedDocumentIndex}
          isOpen={showDocumentModal}
          onClose={() => setShowDocumentModal(false)}
        />
      )}

      {showEmailModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <EmailTemplates
            claim={claim}
            onSendEmail={handleSendEmail}
            onClose={() => setShowEmailModal(false)}
          />
        </div>
      )}
    </div>
  );
}; 