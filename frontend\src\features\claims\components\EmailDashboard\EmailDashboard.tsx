import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Mail, 
  Eye, 
  Clock, 
  CheckCircle, 
  XCircle, 
  Reply, 
  ExternalLink,
  MoreHorizontal,
  Calendar,
  Send,
  AlertTriangle
} from 'lucide-react';
import { emailService, EmailRecord, EmailThread } from '@/lib/emailService';
import { Claim } from '../../types';

interface EmailDashboardProps {
  claim: Claim;
  onComposeNew?: () => void;
}

export const EmailDashboard: React.FC<EmailDashboardProps> = ({
  claim,
  onComposeNew
}) => {
  const [emails, setEmails] = useState<EmailRecord[]>([]);
  const [threads, setThreads] = useState<EmailThread[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmail, setSelectedEmail] = useState<EmailRecord | null>(null);

  useEffect(() => {
    loadEmailData();
  }, [claim.id]);

  const loadEmailData = async () => {
    try {
      setLoading(true);
      const [emailsData, threadsData] = await Promise.all([
        emailService.getEmailsForClaim(claim.id),
        emailService.getEmailThreadsForClaim(claim.id)
      ]);
      setEmails(emailsData);
      setThreads(threadsData);
    } catch (error) {
      console.error('Failed to load email data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'sent':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'delivered':
        return <CheckCircle className="h-4 w-4 text-blue-600" />;
      case 'failed':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'queued':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Mail className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'sent':
        return 'bg-green-100 text-green-800';
      case 'delivered':
        return 'bg-blue-100 text-blue-800';
      case 'failed':
        return 'bg-red-100 text-red-800';
      case 'queued':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getEngagementRate = (email: EmailRecord) => {
    if (email.status !== 'sent' && email.status !== 'delivered') return null;
    return email.openCount > 0 ? 'Opened' : 'Not opened';
  };

  const needsFollowUp = (email: EmailRecord) => {
    if (!email.followUpDate || email.followUpCompleted) return false;
    return new Date(email.followUpDate) <= new Date();
  };

  const emailsNeedingFollowUp = emails.filter(needsFollowUp);

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center h-48">
          <div className="text-center">
            <Clock className="h-8 w-8 animate-spin mx-auto mb-2" />
            <p>Loading email history...</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Email Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Send className="h-4 w-4 text-blue-600" />
              <div>
                <p className="text-2xl font-bold">{emails.length}</p>
                <p className="text-sm text-gray-600">Total Emails</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="h-4 w-4 text-green-600" />
              <div>
                <p className="text-2xl font-bold">
                  {emails.filter(e => e.openCount > 0).length}
                </p>
                <p className="text-sm text-gray-600">Opened</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Reply className="h-4 w-4 text-purple-600" />
              <div>
                <p className="text-2xl font-bold">
                  {emails.filter(e => e.direction === 'inbound').length}
                </p>
                <p className="text-sm text-gray-600">Replies</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <AlertTriangle className="h-4 w-4 text-orange-600" />
              <div>
                <p className="text-2xl font-bold">{emailsNeedingFollowUp.length}</p>
                <p className="text-sm text-gray-600">Need Follow-up</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Follow-up Alerts */}
      {emailsNeedingFollowUp.length > 0 && (
        <Card className="border-orange-200 bg-orange-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-orange-800">
              <AlertTriangle className="h-5 w-5" />
              Follow-up Required
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {emailsNeedingFollowUp.map((email) => (
                <div key={email.id} className="flex items-center justify-between p-2 bg-white rounded border">
                  <div>
                    <p className="font-medium">{email.subject}</p>
                    <p className="text-sm text-gray-600">
                      Follow-up due: {email.followUpDate && formatDate(new Date(email.followUpDate))}
                    </p>
                  </div>
                  <Button size="sm" onClick={onComposeNew}>
                    <Reply className="h-4 w-4 mr-2" />
                    Follow Up
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Email Threads */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Mail className="h-5 w-5" />
              Email History ({emails.length})
            </CardTitle>
            <Button onClick={onComposeNew}>
              <Send className="h-4 w-4 mr-2" />
              Compose New
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {emails.length === 0 ? (
            <div className="text-center py-8">
              <Mail className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No emails sent yet</p>
              <Button className="mt-4" onClick={onComposeNew}>
                Send First Email
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {emails.map((email) => (
                <div
                  key={email.id}
                  className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        {getStatusIcon(email.status)}
                        <h3 className="font-medium">{email.subject}</h3>
                        <Badge className={getStatusColor(email.status)}>
                          {email.status}
                        </Badge>
                        {email.emailType && (
                          <Badge variant="outline">
                            {email.emailType.replace('_', ' ')}
                          </Badge>
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-600 space-y-1">
                        <p>
                          <strong>To:</strong> {email.toEmails.join(', ')}
                        </p>
                        <p>
                          <strong>Sent:</strong> {email.sentAt ? formatDate(email.sentAt) : 'Not sent'}
                        </p>
                        {email.openCount > 0 && (
                          <p>
                            <strong>Opens:</strong> {email.openCount} 
                            {email.openedAt && ` (first: ${formatDate(email.openedAt)})`}
                          </p>
                        )}
                        {email.followUpDate && !email.followUpCompleted && (
                          <p className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            <strong>Follow-up:</strong> {formatDate(new Date(email.followUpDate))}
                          </p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      {email.openCount > 0 && (
                        <Badge variant="secondary" className="bg-green-100 text-green-800">
                          <Eye className="h-3 w-3 mr-1" />
                          Opened
                        </Badge>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => setSelectedEmail(selectedEmail?.id === email.id ? null : email)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Email Preview */}
                  {selectedEmail?.id === email.id && (
                    <div className="mt-4 pt-4 border-t">
                      <div className="bg-gray-50 p-3 rounded max-h-64 overflow-y-auto">
                        <pre className="text-sm whitespace-pre-wrap text-gray-700">
                          {email.bodyText || email.bodyHtml || 'No content'}
                        </pre>
                      </div>
                      {email.error && (
                        <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-sm">
                          <strong>Error:</strong> {email.error}
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}; 