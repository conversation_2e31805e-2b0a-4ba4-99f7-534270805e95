import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Load environment variables from .env file
config();

// Read environment variables
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

console.log('🔍 Environment check:');
console.log('VITE_SUPABASE_URL:', supabaseUrl ? '✅ Found' : '❌ Missing');
console.log('VITE_SUPABASE_ANON_KEY:', supabaseKey ? '✅ Found' : '❌ Missing');

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Please ensure your .env file contains:');
  console.log('VITE_SUPABASE_URL=your_supabase_url');
  console.log('VITE_SUPABASE_ANON_KEY=your_supabase_anon_key');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function setupEmailTables() {
  console.log('🚀 Setting up email tracking tables...');
  
  try {
    // Test Supabase connection first
    console.log('🔗 Testing Supabase connection...');
    const { data, error } = await supabase.from('claims').select('id').limit(1);
    
    if (error) {
      console.error('❌ Supabase connection failed:', error.message);
      return;
    }
    
    console.log('✅ Supabase connection successful');
    
    // Read the SQL schema file
    const schemaPath = path.join('database', 'email-system-schema.sql');
    
    if (!fs.existsSync(schemaPath)) {
      console.error('❌ Schema file not found:', schemaPath);
      return;
    }
    
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    console.log('📋 Email System Schema Summary:');
    console.log('   • email_templates - Reusable email templates');
    console.log('   • email_accounts - Email sending configurations');
    console.log('   • email_threads - Email conversation grouping');
    console.log('   • emails - Individual email records');
    console.log('   • email_events - Email tracking events');
    console.log('   • email_automation_rules - Automated email rules');
    console.log('   • email_unsubscribes - Opt-out tracking');
    
    console.log('\n⚠️  Manual Setup Required:');
    console.log('Due to security restrictions, please manually execute the schema in your Supabase dashboard:');
    console.log('1. Go to your Supabase project dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the contents of database/email-system-schema.sql');
    console.log('4. Execute the SQL to create the email tracking tables');
    
    console.log('\n✅ Email service is ready to use in development mode (with mock sending)');
    
  } catch (error) {
    console.error('❌ Failed to setup email tables:', error);
  }
}

// Run the setup
setupEmailTables(); 