import React, { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Bell, 
  X, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  AlertCircle,
  ChevronLeft,
  ChevronRight,
  Settings
} from 'lucide-react';

interface NotificationAlert {
  id: string;
  type: 'urgent' | 'warning' | 'info' | 'success';
  title: string;
  message: string;
  timestamp: Date;
  actionRequired?: boolean;
  actionUrl?: string;
  dismissible?: boolean;
}

interface AlertNotificationBarProps {
  className?: string;
}

// Mock notification data
const generateMockAlerts = (): NotificationAlert[] => [
  {
    id: '1',
    type: 'urgent',
    title: 'High Priority Claims Require Attention',
    message: '5 high-value claims ($250K+) need immediate contact. Compliance deadline in 2 days.',
    timestamp: new Date(Date.now() - 10 * 60 * 1000),
    actionRequired: true,
    actionUrl: '/claims/high-priority',
    dismissible: false
  },
  {
    id: '2',
    type: 'warning',
    title: 'System Maintenance Scheduled',
    message: 'Planned downtime Sunday 2:00 AM - 4:00 AM EST for system updates.',
    timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
    actionRequired: false,
    dismissible: true
  },
  {
    id: '3',
    type: 'info',
    title: 'Weekly Report Available',
    message: 'Your performance summary for Week 47 is ready for review.',
    timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
    actionRequired: false,
    actionUrl: '/reports/weekly',
    dismissible: true
  },
  {
    id: '4',
    type: 'success',
    title: 'Compliance Target Achieved',
    message: 'Congratulations! You\'ve exceeded your monthly contact goals by 15%.',
    timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
    actionRequired: false,
    dismissible: true
  }
];

export const AlertNotificationBar: React.FC<AlertNotificationBarProps> = ({ className }) => {
  const [alerts, setAlerts] = useState<NotificationAlert[]>(generateMockAlerts());
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isExpanded, setIsExpanded] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
    if (currentIndex >= alerts.length - 1) {
      setCurrentIndex(Math.max(0, alerts.length - 2));
    }
  };

  const nextAlert = () => {
    setCurrentIndex((prev) => (prev + 1) % alerts.length);
  };

  const previousAlert = () => {
    setCurrentIndex((prev) => (prev - 1 + alerts.length) % alerts.length);
  };

  // Auto-rotate alerts every 10 seconds
  useEffect(() => {
    if (alerts.length > 1 && !isExpanded) {
      const interval = setInterval(nextAlert, 10000);
      return () => clearInterval(interval);
    }
  }, [alerts.length, isExpanded]);

  const getAlertIcon = (type: NotificationAlert['type']) => {
    switch (type) {
      case 'urgent':
        return <AlertTriangle className="h-4 w-4" />;
      case 'warning':
        return <AlertCircle className="h-4 w-4" />;
      case 'info':
        return <Info className="h-4 w-4" />;
      case 'success':
        return <CheckCircle className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const getAlertColors = (type: NotificationAlert['type']) => {
    switch (type) {
      case 'urgent':
        return {
          bg: 'bg-red-50 border-red-200',
          text: 'text-red-800',
          icon: 'text-red-600',
          badge: 'bg-red-100 text-red-800 border-red-200'
        };
      case 'warning':
        return {
          bg: 'bg-yellow-50 border-yellow-200',
          text: 'text-yellow-800',
          icon: 'text-yellow-600',
          badge: 'bg-yellow-100 text-yellow-800 border-yellow-200'
        };
      case 'info':
        return {
          bg: 'bg-blue-50 border-blue-200',
          text: 'text-blue-800',
          icon: 'text-blue-600',
          badge: 'bg-blue-100 text-blue-800 border-blue-200'
        };
      case 'success':
        return {
          bg: 'bg-green-50 border-green-200',
          text: 'text-green-800',
          icon: 'text-green-600',
          badge: 'bg-green-100 text-green-800 border-green-200'
        };
      default:
        return {
          bg: 'bg-gray-50 border-gray-200',
          text: 'text-gray-800',
          icon: 'text-gray-600',
          badge: 'bg-gray-100 text-gray-800 border-gray-200'
        };
    }
  };

  const formatTimestamp = (timestamp: Date): string => {
    const now = new Date();
    const diff = now.getTime() - timestamp.getTime();
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    
    if (minutes < 60) {
      return `${minutes}m ago`;
    } else if (hours < 24) {
      return `${hours}h ago`;
    } else {
      return timestamp.toLocaleDateString();
    }
  };

  if (alerts.length === 0) {
    return (
      <Card className={`${className} bg-green-50 border-green-200`}>
        <CardContent className="flex items-center justify-center py-3">
          <div className="flex items-center gap-2 text-green-700">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">All caught up! No pending alerts.</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const currentAlert = alerts[currentIndex];
  const colors = getAlertColors(currentAlert.type);

  return (
    <Card className={`${className} ${colors.bg} border`}>
      <CardContent className="py-3">
        {/* Compact View */}
        {!isExpanded && (
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3 min-w-0 flex-1">
              <div className={`${colors.icon} flex-shrink-0`}>
                {getAlertIcon(currentAlert.type)}
              </div>
              
              <div className="min-w-0 flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className={`text-sm font-medium ${colors.text} truncate`}>
                    {currentAlert.title}
                  </h4>
                  {currentAlert.actionRequired && (
                    <Badge variant="outline" className={`text-xs ${colors.badge} flex-shrink-0`}>
                      Action Required
                    </Badge>
                  )}
                </div>
                <p className={`text-xs ${colors.text} opacity-80 truncate`}>
                  {currentAlert.message}
                </p>
              </div>
            </div>

            <div className="flex items-center gap-1 flex-shrink-0 ml-4">
              {alerts.length > 1 && (
                <>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={previousAlert}
                    className={`h-6 w-6 p-0 ${colors.text} hover:bg-white/50`}
                  >
                    <ChevronLeft className="h-3 w-3" />
                  </Button>
                  <span className={`text-xs ${colors.text} opacity-60 px-2`}>
                    {currentIndex + 1}/{alerts.length}
                  </span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={nextAlert}
                    className={`h-6 w-6 p-0 ${colors.text} hover:bg-white/50`}
                  >
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </>
              )}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsExpanded(true)}
                className={`h-6 w-6 p-0 ${colors.text} hover:bg-white/50 ml-2`}
              >
                <Bell className="h-3 w-3" />
              </Button>
            </div>
          </div>
        )}

        {/* Expanded View */}
        {isExpanded && (
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h3 className={`text-lg font-semibold ${colors.text} flex items-center gap-2`}>
                <Bell className="h-4 w-4" />
                Notifications ({alerts.length})
              </h3>
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSettings(!showSettings)}
                  className={`h-7 ${colors.text} hover:bg-white/50`}
                >
                  <Settings className="h-3 w-3" />
                </Button>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setIsExpanded(false)}
                  className={`h-7 ${colors.text} hover:bg-white/50`}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            </div>

            <div className="space-y-2 max-h-64 overflow-y-auto">
              {alerts.map((alert, index) => {
                const alertColors = getAlertColors(alert.type);
                return (
                  <div
                    key={alert.id}
                    className={`p-3 rounded-lg border ${index === currentIndex ? 'bg-white/80' : 'bg-white/40'} 
                      ${alertColors.bg.replace('50', '100')} ${alertColors.bg.replace('border-', 'border-').replace('200', '300')}`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex items-start gap-2 min-w-0 flex-1">
                        <div className={`${alertColors.icon} mt-0.5 flex-shrink-0`}>
                          {getAlertIcon(alert.type)}
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="flex items-center gap-2 mb-1">
                            <h5 className={`text-sm font-medium ${alertColors.text}`}>
                              {alert.title}
                            </h5>
                            {alert.actionRequired && (
                              <Badge variant="outline" className={`text-xs ${alertColors.badge}`}>
                                Action Required
                              </Badge>
                            )}
                          </div>
                          <p className={`text-xs ${alertColors.text} opacity-80 mb-2`}>
                            {alert.message}
                          </p>
                          <div className="flex items-center gap-3">
                            <span className={`text-xs ${alertColors.text} opacity-60`}>
                              {formatTimestamp(alert.timestamp)}
                            </span>
                            {alert.actionUrl && (
                              <Button
                                size="sm"
                                variant="outline"
                                className={`h-6 text-xs ${alertColors.badge} hover:bg-white/50`}
                              >
                                View Details
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                      {alert.dismissible && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => dismissAlert(alert.id)}
                          className={`h-6 w-6 p-0 ${alertColors.text} hover:bg-white/50 flex-shrink-0 ml-2`}
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>

            {showSettings && (
              <div className="pt-3 border-t border-white/50">
                <div className="grid grid-cols-2 gap-2 text-xs">
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className={colors.text}>Desktop notifications</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className={colors.text}>High priority alerts</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" defaultChecked className="rounded" />
                    <span className={colors.text}>System updates</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input type="checkbox" className="rounded" />
                    <span className={colors.text}>Email digest</span>
                  </label>
                </div>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 