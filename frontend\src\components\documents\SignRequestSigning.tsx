import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { 
  FileText, 
  Send, 
  Users, 
  Clock,
  CheckCircle,
  AlertTriangle,
  Download,
  Eye,
  RefreshCw,
  Plus,
  X,
  Gift
} from 'lucide-react'
import { signRequestService, type SignRequestCreate, type SignRequestResponse } from '@/services/signRequestService'
import { useAuth } from '@/contexts/AuthContext'
import { toast } from '@/components/ui/use-toast'

interface SignRequestSigningProps {
  claimId?: string
  onDocumentSigned?: (signRequestId: string) => void
}

export const SignRequestSigning: React.FC<SignRequestSigningProps> = ({ 
  claimId, 
  onDocumentSigned 
}) => {
  const { user } = useAuth()
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  
  // Document creation state
  const [documentFile, setDocumentFile] = useState<File | null>(null)
  const [subject, setSubject] = useState('Asset Recovery Agreement')
  const [message, setMessage] = useState('Please review and sign this document.')
  const [signers, setSigners] = useState([
    { email: '', first_name: '', last_name: '', needs_to_sign: true }
  ])
  
  // Active sign requests
  const [signRequests, setSignRequests] = useState<SignRequestResponse[]>([])

  useEffect(() => {
    loadSignRequests()
  }, [claimId])

  const loadSignRequests = async () => {
    // In a real implementation, this would load from your database
    // For now, we'll load from localStorage
    try {
      const saved = localStorage.getItem(`signrequests_${claimId || 'general'}`)
      if (saved) {
        setSignRequests(JSON.parse(saved))
      }
    } catch (error) {
      console.error('Error loading sign requests:', error)
    }
  }

  const saveSignRequests = (requests: SignRequestResponse[]) => {
    localStorage.setItem(`signrequests_${claimId || 'general'}`, JSON.stringify(requests))
    setSignRequests(requests)
  }

  const addSigner = () => {
    setSigners([...signers, { email: '', first_name: '', last_name: '', needs_to_sign: true }])
  }

  const removeSigner = (index: number) => {
    if (signers.length > 1) {
      setSigners(signers.filter((_, i) => i !== index))
    }
  }

  const updateSigner = (index: number, field: string, value: string) => {
    const updated = [...signers]
    updated[index] = { ...updated[index], [field]: value }
    setSigners(updated)
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Validate file type
      const allowedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']
      if (!allowedTypes.includes(file.type)) {
        setError('Please select a PDF or Word document')
        return
      }
      
      // Validate file size (10MB limit)
      if (file.size > 10 * 1024 * 1024) {
        setError('File size must be less than 10MB')
        return
      }
      
      setDocumentFile(file)
      setError(null)
    }
  }

  const createSignRequest = async () => {
    if (!documentFile) {
      setError('Please select a document to sign')
      return
    }

    // Validate signers
    const validSigners = signers.filter(s => s.email && s.first_name && s.last_name)
    if (validSigners.length === 0) {
      setError('Please add at least one signer')
      return
    }

    setLoading(true)
    setError(null)

    try {
      // Convert file to base64
      const base64 = await signRequestService.fileToBase64(documentFile)
      
      const signRequestData: SignRequestCreate = {
        from_email: user?.email || '<EMAIL>',
        subject,
        message,
        send_immediately: true,
        signers: validSigners.map((signer, index) => ({
          ...signer,
          order: index + 1
        })),
        documents: [{
          name: documentFile.name,
          file: base64
        }]
      }

      const { data, error } = await signRequestService.createSignRequest(signRequestData)
      
      if (error) {
        setError(`Failed to create sign request: ${error.message || 'Unknown error'}`)
        return
      }

      if (data) {
        // Add to our local list
        const updatedRequests = [...signRequests, data]
        saveSignRequests(updatedRequests)
        
        // Reset form
        setDocumentFile(null)
        setSubject('Asset Recovery Agreement')
        setMessage('Please review and sign this document.')
        setSigners([{ email: '', first_name: '', last_name: '', needs_to_sign: true }])
        
        toast({
          title: "Document Sent for Signature",
          description: `Sign request created successfully: ${data.uuid}`,
        })

        if (onDocumentSigned) {
          onDocumentSigned(data.uuid)
        }
      }
    } catch (error) {
      setError('Failed to create sign request')
      console.error('Sign request creation error:', error)
    } finally {
      setLoading(false)
    }
  }

  const refreshSignRequest = async (uuid: string) => {
    try {
      const { data, error } = await signRequestService.getSignRequestStatus(uuid)
      
      if (error) {
        toast({
          title: "Error",
          description: "Failed to refresh sign request status",
          variant: "destructive"
        })
        return
      }

      if (data) {
        const updatedRequests = signRequests.map(sr => 
          sr.uuid === uuid ? data : sr
        )
        saveSignRequests(updatedRequests)
        
        toast({
          title: "Status Updated",
          description: `Document status: ${signRequestService.getStatusDisplayName(data.status)}`,
        })
      }
    } catch (error) {
      console.error('Error refreshing sign request:', error)
    }
  }

  const downloadSignedDocument = async (uuid: string) => {
    try {
      const { data, error } = await signRequestService.downloadSignedDocument(uuid)
      
      if (error) {
        toast({
          title: "Download Failed",
          description: "Document is not yet signed or download failed",
          variant: "destructive"
        })
        return
      }

      if (data) {
        // Create download link
        const blob = new Blob([data], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `signed-document-${uuid}.pdf`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
        
        toast({
          title: "Download Started",
          description: "Signed document download has started",
        })
      }
    } catch (error) {
      console.error('Error downloading document:', error)
    }
  }

  const getStatusColor = (status: string) => {
    if (signRequestService.isCompleted(status)) return 'bg-green-100 text-green-700'
    if (signRequestService.needsAction(status)) return 'bg-yellow-100 text-yellow-700'
    if (['ca', 'de', 'ec', 'es', 'xp'].includes(status)) return 'bg-red-100 text-red-700'
    return 'bg-gray-100 text-gray-700'
  }

  const getStatusIcon = (status: string) => {
    if (signRequestService.isCompleted(status)) return <CheckCircle className="h-4 w-4" />
    if (signRequestService.needsAction(status)) return <Clock className="h-4 w-4" />
    if (['ca', 'de', 'ec', 'es', 'xp'].includes(status)) return <AlertTriangle className="h-4 w-4" />
    return <Clock className="h-4 w-4" />
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Document Signing</h2>
          <p className="text-gray-600">Send documents for electronic signature</p>
        </div>
        <Badge variant="secondary" className="flex items-center space-x-1">
          <Gift className="h-3 w-3" />
          <span>10 Free/Month</span>
        </Badge>
      </div>

      {error && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Tabs defaultValue="create" className="space-y-6">
        <TabsList>
          <TabsTrigger value="create">Create New</TabsTrigger>
          <TabsTrigger value="active">Active Requests ({signRequests.length})</TabsTrigger>
        </TabsList>

        <TabsContent value="create" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <FileText className="h-5 w-5" />
                <span>Create Sign Request</span>
              </CardTitle>
              <CardDescription>
                Upload a document and send it for electronic signature
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Document *</label>
                <input
                  type="file"
                  accept=".pdf,.doc,.docx"
                  onChange={handleFileChange}
                  className="w-full p-2 border rounded-lg"
                />
                <p className="text-xs text-gray-500">
                  Supported formats: PDF, DOC, DOCX (max 10MB)
                </p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label className="text-sm font-medium">Subject *</label>
                  <Input
                    value={subject}
                    onChange={(e) => setSubject(e.target.value)}
                    placeholder="Document subject"
                  />
                </div>
                <div className="space-y-2">
                  <label className="text-sm font-medium">Message</label>
                  <Input
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Message to signers"
                  />
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Signers *</label>
                  <Button variant="outline" size="sm" onClick={addSigner}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Signer
                  </Button>
                </div>
                
                {signers.map((signer, index) => (
                  <div key={index} className="flex items-center space-x-2 p-3 border rounded-lg">
                    <div className="flex-1 grid grid-cols-3 gap-2">
                      <Input
                        placeholder="First name"
                        value={signer.first_name}
                        onChange={(e) => updateSigner(index, 'first_name', e.target.value)}
                      />
                      <Input
                        placeholder="Last name"
                        value={signer.last_name}
                        onChange={(e) => updateSigner(index, 'last_name', e.target.value)}
                      />
                      <Input
                        placeholder="Email address"
                        type="email"
                        value={signer.email}
                        onChange={(e) => updateSigner(index, 'email', e.target.value)}
                      />
                    </div>
                    {signers.length > 1 && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => removeSigner(index)}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    )}
                  </div>
                ))}
              </div>

              <Button 
                onClick={createSignRequest}
                disabled={loading || !documentFile}
                className="w-full"
                size="lg"
              >
                {loading ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Send className="mr-2 h-4 w-4" />
                )}
                {loading ? 'Creating Sign Request...' : 'Send for Signature'}
              </Button>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="active" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Active Sign Requests</span>
              </CardTitle>
              <CardDescription>
                Track the status of your sent documents
              </CardDescription>
            </CardHeader>
            <CardContent>
              {signRequests.length > 0 ? (
                <div className="space-y-3">
                  {signRequests.map((request) => (
                    <div key={request.uuid} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                          <FileText className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium">{request.subject}</div>
                          <div className="text-sm text-gray-500">
                            {request.signers.length} signer(s) • Created {new Date(request.created).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <Badge className={getStatusColor(request.status)}>
                          {getStatusIcon(request.status)}
                          <span className="ml-1">{signRequestService.getStatusDisplayName(request.status)}</span>
                        </Badge>
                        <div className="flex space-x-2">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => refreshSignRequest(request.uuid)}
                          >
                            <RefreshCw className="h-4 w-4" />
                          </Button>
                          {signRequestService.isCompleted(request.status) && (
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => downloadSignedDocument(request.uuid)}
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No Active Sign Requests</h3>
                  <p className="text-gray-500">
                    Create your first sign request to get started with electronic signatures
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
