import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  AlertCircle, 
  Clock, 
  CheckCircle, 
  ArrowRight, 
  Phone, 
  Mail, 
  Eye, 
  AlertTriangle,
  Calendar,
  User,
  Filter,
  Plus
} from 'lucide-react';
import { PriorityAction } from '@/types/dashboard';

interface PriorityActionsPanelProps {
  className?: string;
}

// Mock priority actions data
const generateMockActions = (): PriorityAction[] => [
  {
    id: '1',
    type: 'contact',
    title: 'Contact High-Value Claimant',
    description: '<PERSON> - $125K insurance settlement claim requires immediate contact',
    priority: 'urgent',
    dueDate: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    estimatedTime: 15,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-001',
    completed: false
  },
  {
    id: '2',
    type: 'review',
    title: 'Review Documentation',
    description: '<PERSON> claim documentation needs verification before proceeding',
    priority: 'high',
    dueDate: new Date(Date.now() + 4 * 60 * 60 * 1000), // 4 hours from now
    estimatedTime: 30,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-002',
    completed: false
  },
  {
    id: '3',
    type: 'follow-up',
    title: 'Follow-up Call Scheduled',
    description: 'Second attempt to reach Emily Rodriguez - voicemail left 3 days ago',
    priority: 'high',
    dueDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    estimatedTime: 10,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-003',
    completed: false
  },
  {
    id: '4',
    type: 'approve',
    title: 'Approve Settlement Terms',
    description: 'Robert Chen case - final settlement terms awaiting your approval',
    priority: 'medium',
    dueDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // 2 days from now
    estimatedTime: 20,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-004',
    completed: false
  },
  {
    id: '5',
    type: 'escalate',
    title: 'Escalate Complex Case',
    description: 'Williams Estate case requires legal review - documentation incomplete',
    priority: 'medium',
    dueDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days from now
    estimatedTime: 45,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-005',
    completed: false
  },
  {
    id: '6',
    type: 'contact',
    title: 'Contact Verified Successfully',
    description: 'Amanda Foster - successfully contacted and documentation sent',
    priority: 'low',
    dueDate: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    estimatedTime: 15,
    assignedTo: 'current-user',
    claimId: 'CLM-2024-006',
    completed: true
  }
];

export const PriorityActionsPanel: React.FC<PriorityActionsPanelProps> = ({ className }) => {
  const [actions, setActions] = useState<PriorityAction[]>(generateMockActions());
  const [filter, setFilter] = useState<'all' | 'pending' | 'overdue' | 'completed'>('pending');
  const [showCompleted, setShowCompleted] = useState(false);

  const toggleActionCompletion = (actionId: string) => {
    setActions(prev => prev.map(action => 
      action.id === actionId 
        ? { ...action, completed: !action.completed }
        : action
    ));
  };

  const filteredActions = actions.filter(action => {
    if (filter === 'completed') return action.completed;
    if (filter === 'pending') return !action.completed;
    if (filter === 'overdue') return !action.completed && action.dueDate < new Date();
    return showCompleted || !action.completed;
  });

  const getActionIcon = (type: PriorityAction['type']) => {
    switch (type) {
      case 'contact':
        return <Phone className="h-4 w-4" />;
      case 'review':
        return <Eye className="h-4 w-4" />;
      case 'approve':
        return <CheckCircle className="h-4 w-4" />;
      case 'escalate':
        return <AlertTriangle className="h-4 w-4" />;
      case 'follow-up':
        return <Mail className="h-4 w-4" />;
      default:
        return <Clock className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: PriorityAction['priority']) => {
    switch (priority) {
      case 'urgent':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'high':
        return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low':
        return 'bg-green-100 text-green-800 border-green-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const formatDueDate = (dueDate: Date): { text: string; isOverdue: boolean; isToday: boolean } => {
    const now = new Date();
    const diffMs = dueDate.getTime() - now.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    
    const isOverdue = diffMs < 0;
    const isToday = diffDays === 0 && diffHours >= 0;
    
    if (isOverdue) {
      const hoursOverdue = Math.abs(diffHours);
      if (hoursOverdue < 24) {
        return { text: `${hoursOverdue}h overdue`, isOverdue: true, isToday: false };
      } else {
        return { text: `${Math.abs(diffDays)}d overdue`, isOverdue: true, isToday: false };
      }
    } else if (isToday) {
      if (diffHours < 1) {
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        return { text: `${diffMinutes}m left`, isOverdue: false, isToday: true };
      } else {
        return { text: `${diffHours}h left`, isOverdue: false, isToday: true };
      }
    } else if (diffDays === 1) {
      return { text: 'Tomorrow', isOverdue: false, isToday: false };
    } else {
      return { text: `${diffDays}d left`, isOverdue: false, isToday: false };
    }
  };

  const urgentActions = filteredActions.filter(a => a.priority === 'urgent' && !a.completed);
  const overdueActions = filteredActions.filter(a => !a.completed && a.dueDate < new Date());

  return (
    <Card className={className}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <AlertCircle className="h-4 w-4" />
            Priority Actions
            {urgentActions.length > 0 && (
              <Badge variant="destructive" className="text-xs">
                {urgentActions.length} urgent
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setFilter(filter === 'all' ? 'pending' : 'all')}
              className="h-6 text-xs"
            >
              <Filter className="h-3 w-3 mr-1" />
              {filter}
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 text-xs"
            >
              <Plus className="h-3 w-3" />
            </Button>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Filter Buttons */}
        <div className="flex gap-1">
          {['pending', 'overdue', 'completed', 'all'].map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setFilter(filterType as typeof filter)}
              className="h-6 text-xs px-2"
            >
              {filterType === 'overdue' && overdueActions.length > 0 && (
                <span className="bg-red-500 text-white text-xs rounded-full px-1 mr-1">
                  {overdueActions.length}
                </span>
              )}
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
            </Button>
          ))}
        </div>

        {/* Actions List */}
        <div className="space-y-2 max-h-96 overflow-y-auto">
          {filteredActions.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <CheckCircle className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm">No {filter} actions</p>
              {filter === 'pending' && (
                <p className="text-xs text-gray-400">Great job staying on top of your tasks!</p>
              )}
            </div>
          ) : (
            filteredActions.map((action) => {
              const timeInfo = formatDueDate(action.dueDate);
              
              return (
                <div
                  key={action.id}
                  className={`p-3 rounded-lg border transition-all ${
                    action.completed 
                      ? 'bg-gray-50 border-gray-200 opacity-60' 
                      : timeInfo.isOverdue 
                        ? 'bg-red-50 border-red-200' 
                        : 'bg-white border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <Checkbox
                      checked={action.completed}
                      onCheckedChange={() => toggleActionCompletion(action.id)}
                      className="mt-1"
                    />
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2 mb-1">
                        <div className={`${timeInfo.isOverdue ? 'text-red-600' : 'text-gray-500'}`}>
                          {getActionIcon(action.type)}
                        </div>
                        <h4 className={`text-sm font-medium ${action.completed ? 'line-through text-gray-500' : 'text-gray-900'}`}>
                          {action.title}
                        </h4>
                        <Badge 
                          variant="outline" 
                          className={`text-xs ${getPriorityColor(action.priority)}`}
                        >
                          {action.priority}
                        </Badge>
                      </div>
                      
                      <p className={`text-xs mb-2 ${action.completed ? 'text-gray-400' : 'text-gray-600'}`}>
                        {action.description}
                      </p>
                      
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3 text-xs">
                          <div className={`flex items-center gap-1 ${
                            timeInfo.isOverdue ? 'text-red-600' : 
                            timeInfo.isToday ? 'text-orange-600' : 'text-gray-500'
                          }`}>
                            <Calendar className="h-3 w-3" />
                            {timeInfo.text}
                          </div>
                          <div className="flex items-center gap-1 text-gray-500">
                            <Clock className="h-3 w-3" />
                            {action.estimatedTime}m
                          </div>
                          {action.claimId && (
                            <div className="flex items-center gap-1 text-gray-500">
                              <User className="h-3 w-3" />
                              {action.claimId}
                            </div>
                          )}
                        </div>
                        
                        {!action.completed && (
                          <Button
                            size="sm"
                            variant="ghost"
                            className="h-6 px-2 text-xs"
                          >
                            Start
                            <ArrowRight className="h-3 w-3 ml-1" />
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })
          )}
        </div>

        {/* Summary Stats */}
        {filteredActions.length > 0 && (
          <div className="pt-3 border-t border-gray-100">
            <div className="grid grid-cols-3 gap-4 text-center">
              <div>
                <div className="text-lg font-semibold text-gray-900">
                  {actions.filter(a => !a.completed).length}
                </div>
                <div className="text-xs text-gray-500">Pending</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-red-600">
                  {overdueActions.length}
                </div>
                <div className="text-xs text-gray-500">Overdue</div>
              </div>
              <div>
                <div className="text-lg font-semibold text-green-600">
                  {actions.filter(a => a.completed).length}
                </div>
                <div className="text-xs text-gray-500">Completed</div>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}; 