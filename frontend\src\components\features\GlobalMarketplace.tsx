import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Store, 
  Search, 
  Download,
  Star,
  Globe,
  Code,
  FileText,
  Users,
  TrendingUp,
  Shield,
  Zap,
  Settings,
  Package,
  Briefcase,
  GraduationCap,
  ExternalLink,
  Filter,
  SortAsc,
  Heart,
  MessageCircle,
  DollarSign,
  Clock,
  Award,
  Building
} from 'lucide-react';
import { 
  MarketplaceCapabilities, 
  ApplicationMarketplace,
  GlobalExpansionFeatures 
} from '@/types/global-platform';

interface GlobalMarketplaceProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const GlobalMarketplaceComponent: React.FC<GlobalMarketplaceProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('apps');
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  
  const [marketplaceMetrics, setMarketplaceMetrics] = useState({
    total_apps: 2847,
    active_developers: 1256,
    monthly_downloads: 45678,
    revenue_generated: 890000,
    global_regions: 15,
    languages_supported: 12
  });

  const appCategories = [
    { name: 'All', count: 2847, id: 'all' },
    { name: 'Integrations', count: 845, id: 'integrations' },
    { name: 'Automation', count: 623, id: 'automation' },
    { name: 'Analytics', count: 489, id: 'analytics' },
    { name: 'Communication', count: 356, id: 'communication' },
    { name: 'Security', count: 298, id: 'security' },
    { name: 'Workflow', count: 236, id: 'workflow' }
  ];

  const featuredApps = [
    {
      name: 'HubSpot CRM Sync',
      developer: 'HubSpot Inc.',
      category: 'Integrations',
      rating: 4.8,
      reviews: 1245,
      downloads: 25678,
      price: 'Free',
      description: 'Seamlessly sync claim data with HubSpot CRM for complete customer lifecycle management.',
      features: ['Real-time sync', 'Custom field mapping', 'Automated workflows'],
      verified: true,
      trending: true
    },
    {
      name: 'AI Document Analyzer',
      developer: 'DataFlow Solutions',
      category: 'Automation',
      rating: 4.9,
      reviews: 892,
      downloads: 18934,
      price: '$49/month',
      description: 'Advanced AI-powered document analysis and classification for faster claim processing.',
      features: ['OCR processing', 'Smart classification', 'Fraud detection'],
      verified: true,
      trending: false
    },
    {
      name: 'Multi-Language Reports',
      developer: 'Global Reports Ltd',
      category: 'Analytics',
      rating: 4.7,
      reviews: 567,
      downloads: 12456,
      price: '$29/month',
      description: 'Generate professional reports in 25+ languages with automated translations.',
      features: ['25+ languages', 'Custom templates', 'Automated translation'],
      verified: true,
      trending: true
    },
    {
      name: 'SMS Notification Hub',
      developer: 'CommReach',
      category: 'Communication',
      rating: 4.6,
      reviews: 723,
      downloads: 15234,
      price: '$19/month',
      description: 'Advanced SMS notifications with global carrier support and delivery tracking.',
      features: ['Global coverage', 'Delivery tracking', 'Template library'],
      verified: false,
      trending: false
    }
  ];

  const templateMarketplace = [
    {
      name: 'Legal Engagement Letters',
      type: 'Document Template',
      creator: 'LegalPro Templates',
      downloads: 8934,
      rating: 4.9,
      price: '$15',
      regions: ['US', 'CA', 'UK', 'AU'],
      description: 'Comprehensive legal engagement letter templates for asset recovery services.'
    },
    {
      name: 'Automated Workflow Pack',
      type: 'Workflow Template',
      creator: 'FlowMaster',
      downloads: 6745,
      rating: 4.7,
      price: '$25',
      regions: ['Global'],
      description: 'Pre-built automation workflows for common asset recovery processes.'
    },
    {
      name: 'Executive Dashboard',
      type: 'Report Template',
      creator: 'DataViz Pro',
      downloads: 5432,
      rating: 4.8,
      price: '$35',
      regions: ['Global'],
      description: 'Professional executive dashboard template with key performance metrics.'
    }
  ];

  const serviceMarketplace = [
    {
      name: 'Implementation Consulting',
      provider: 'AssetPro Consultants',
      type: 'Professional Services',
      rating: 4.9,
      projects_completed: 234,
      price: '$150/hour',
      expertise: ['System Setup', 'Process Optimization', 'Training'],
      regions: ['North America', 'Europe'],
      certified: true
    },
    {
      name: 'Managed Support Services',
      provider: 'GlobalTech Support',
      type: 'Managed Services',
      rating: 4.8,
      projects_completed: 567,
      price: '$2,500/month',
      expertise: ['24/7 Support', 'System Monitoring', 'User Training'],
      regions: ['Global'],
      certified: true
    },
    {
      name: 'Custom Integration Development',
      provider: 'DevBridge Solutions',
      type: 'Development Services',
      rating: 4.7,
      projects_completed: 189,
      price: 'Quote-based',
      expertise: ['API Development', 'Custom Integrations', 'System Migration'],
      regions: ['Global'],
      certified: false
    }
  ];

  const globalRegions = [
    { region: 'North America', apps: 2145, developers: 678, revenue: 450000 },
    { region: 'Europe', apps: 1876, developers: 456, revenue: 290000 },
    { region: 'Asia Pacific', apps: 1234, developers: 234, revenue: 120000 },
    { region: 'Latin America', apps: 567, developers: 123, revenue: 25000 },
    { region: 'Middle East & Africa', apps: 345, developers: 89, revenue: 15000 }
  ];

  const renderAppMarketplace = () => (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search apps, integrations, and add-ons..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" size="sm">
            <Filter className="h-4 w-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" size="sm">
            <SortAsc className="h-4 w-4 mr-2" />
            Sort
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-7 gap-2">
        {appCategories.map((category) => (
          <Button
            key={category.id}
            variant={selectedCategory === category.id ? "default" : "outline"}
            size="sm"
            onClick={() => setSelectedCategory(category.id)}
            className="justify-start"
          >
            {category.name}
            <Badge variant="secondary" className="ml-2">
              {category.count}
            </Badge>
          </Button>
        ))}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-6">
        {featuredApps.map((app, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                    <Package className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h3 className="font-semibold">{app.name}</h3>
                      {app.verified && <Shield className="h-4 w-4 text-green-600" />}
                      {app.trending && <TrendingUp className="h-4 w-4 text-orange-600" />}
                    </div>
                    <p className="text-sm text-gray-600">by {app.developer}</p>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{app.price}</div>
                  <Badge variant="outline">{app.category}</Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">{app.description}</p>
              
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="font-medium">{app.rating}</span>
                  <span className="text-gray-500">({app.reviews} reviews)</span>
                </div>
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-500">{app.downloads.toLocaleString()} downloads</span>
                </div>
              </div>

              <div className="space-y-2">
                <div className="text-sm font-medium">Key Features:</div>
                <div className="flex flex-wrap gap-1">
                  {app.features.map((feature, featureIndex) => (
                    <Badge key={featureIndex} variant="secondary" className="text-xs">
                      {feature}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button className="flex-1" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Install
                </Button>
                <Button variant="outline" size="sm">
                  <ExternalLink className="h-4 w-4" />
                </Button>
                <Button variant="outline" size="sm">
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderTemplateMarketplace = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {templateMarketplace.map((template, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold">{template.name}</h3>
                  <p className="text-sm text-gray-600">by {template.creator}</p>
                  <Badge variant="outline" className="mt-1">{template.type}</Badge>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-green-600">{template.price}</div>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-gray-600">{template.description}</p>
              
              <div className="flex items-center gap-4 text-sm">
                <div className="flex items-center gap-1">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="font-medium">{template.rating}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Download className="h-4 w-4 text-gray-500" />
                  <span className="text-gray-500">{template.downloads.toLocaleString()}</span>
                </div>
              </div>

              <div>
                <div className="text-sm font-medium mb-2">Available Regions:</div>
                <div className="flex flex-wrap gap-1">
                  {template.regions.map((region, regionIndex) => (
                    <Badge key={regionIndex} variant="secondary" className="text-xs">
                      {region}
                    </Badge>
                  ))}
                </div>
              </div>

              <div className="flex gap-2">
                <Button className="flex-1" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Download
                </Button>
                <Button variant="outline" size="sm">
                  Preview
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderServiceMarketplace = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-1 gap-6">
        {serviceMarketplace.map((service, index) => (
          <Card key={index} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start gap-4">
                  <div className="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                    <Briefcase className="h-6 w-6 text-purple-600" />
                  </div>
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-semibold text-lg">{service.name}</h3>
                      {service.certified && <Award className="h-4 w-4 text-gold-600" />}
                    </div>
                    <p className="text-gray-600 mb-2">by {service.provider}</p>
                    <Badge variant="outline">{service.type}</Badge>
                    
                    <div className="flex items-center gap-4 mt-3 text-sm">
                      <div className="flex items-center gap-1">
                        <Star className="h-4 w-4 text-yellow-500 fill-current" />
                        <span className="font-medium">{service.rating}</span>
                      </div>
                      <div>
                        <span className="text-gray-600">{service.projects_completed} projects completed</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="font-semibold text-lg text-green-600">{service.price}</div>
                </div>
              </div>

              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <div className="text-sm font-medium mb-2">Expertise Areas:</div>
                  <div className="flex flex-wrap gap-1">
                    {service.expertise.map((skill, skillIndex) => (
                      <Badge key={skillIndex} variant="secondary" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </div>
                <div>
                  <div className="text-sm font-medium mb-2">Service Regions:</div>
                  <div className="flex flex-wrap gap-1">
                    {service.regions.map((region, regionIndex) => (
                      <Badge key={regionIndex} variant="outline" className="text-xs">
                        {region}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>

              <div className="flex gap-2 mt-4">
                <Button className="flex-1">
                  Contact Provider
                </Button>
                <Button variant="outline">
                  View Portfolio
                </Button>
                <Button variant="outline" size="sm">
                  <MessageCircle className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );

  const renderGlobalOverview = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Total Apps</p>
                <p className="text-2xl font-bold text-blue-600">{marketplaceMetrics.total_apps.toLocaleString()}</p>
              </div>
              <Store className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              +12% this month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Developers</p>
                <p className="text-2xl font-bold text-purple-600">{marketplaceMetrics.active_developers.toLocaleString()}</p>
              </div>
              <Code className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              +8% this month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Monthly Downloads</p>
                <p className="text-2xl font-bold text-green-600">{marketplaceMetrics.monthly_downloads.toLocaleString()}</p>
              </div>
              <Download className="h-8 w-8 text-green-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              +25% this month
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Revenue Generated</p>
                <p className="text-2xl font-bold text-orange-600">
                  ${(marketplaceMetrics.revenue_generated / 1000).toFixed(0)}K
                </p>
              </div>
              <DollarSign className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 text-xs text-green-600">
              +18% this month
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Globe className="h-5 w-5" />
              Global Distribution
            </CardTitle>
            <CardDescription>
              Marketplace presence across regions
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {globalRegions.map((region, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="font-medium">{region.region}</span>
                  <div className="text-right text-sm">
                    <div>{region.apps} apps</div>
                    <div className="text-gray-500">{region.developers} developers</div>
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Progress value={(region.revenue / 450000) * 100} className="flex-1 h-2" />
                  <span className="text-sm font-medium text-green-600">
                    ${(region.revenue / 1000).toFixed(0)}K
                  </span>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Developer Ecosystem
            </CardTitle>
            <CardDescription>
              Partner and developer engagement metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {[
              { metric: 'App Approval Rate', value: 78, color: 'bg-green-500' },
              { metric: 'Developer Satisfaction', value: 92, color: 'bg-blue-500' },
              { metric: 'Time to Market (avg)', value: 14, color: 'bg-purple-500', unit: ' days' },
              { metric: 'Revenue Share Rate', value: 70, color: 'bg-orange-500', unit: '%' }
            ].map((metric, index) => (
              <div key={index} className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">{metric.metric}</span>
                  <span className="text-sm font-medium">
                    {metric.value}{metric.unit || '%'}
                  </span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className={`h-2 rounded-full ${metric.color}`}
                    style={{ width: `${metric.unit === ' days' ? (metric.value / 30) * 100 : metric.value}%` }}
                  ></div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Global Marketplace</h2>
          <p className="text-gray-600">
            Discover apps, templates, and services from our global developer ecosystem
          </p>
        </div>
        <div className="flex items-center gap-4">
          <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
            {marketplaceMetrics.global_regions} Regions
          </Badge>
          <Badge variant="outline" className="px-3 py-1">
            {marketplaceMetrics.languages_supported} Languages
          </Badge>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="apps">Apps & Integrations</TabsTrigger>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="services">Services</TabsTrigger>
          <TabsTrigger value="overview">Global Overview</TabsTrigger>
        </TabsList>

        <TabsContent value="apps" className="space-y-6">
          {renderAppMarketplace()}
        </TabsContent>

        <TabsContent value="templates" className="space-y-6">
          {renderTemplateMarketplace()}
        </TabsContent>

        <TabsContent value="services" className="space-y-6">
          {renderServiceMarketplace()}
        </TabsContent>

        <TabsContent value="overview" className="space-y-6">
          {renderGlobalOverview()}
        </TabsContent>
      </Tabs>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Globe className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Unlock Global Marketplace</h3>
              <p className="text-gray-600 mb-4">
                Upgrade to Diamond for access to premium marketplace features, developer tools, and global expansion capabilities
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}; 