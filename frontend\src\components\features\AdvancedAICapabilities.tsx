import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { 
  Brain, 
  TrendingUp, 
  Shield, 
  Eye, 
  Zap,
  BarChart3,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
  Users,
  DollarSign,
  Activity,
  Cpu,
  Database,
  Settings,
  ArrowUp,
  ArrowDown
} from 'lucide-react';
import { 
  AdvancedAICapabilities, 
  PredictiveAnalytics,
  MLModelManagement,
  FraudDetectionCapabilities 
} from '@/types/enterprise';

interface AdvancedAICapabilitiesProps {
  userPlan: 'platinum' | 'diamond';
  onUpgrade?: () => void;
}

export const AdvancedAICapabilitiesComponent: React.FC<AdvancedAICapabilitiesProps> = ({ 
  userPlan, 
  onUpgrade 
}) => {
  const [activeTab, setActiveTab] = useState('predictive');
  const [modelMetrics, setModelMetrics] = useState({
    prediction_accuracy: 94.2,
    fraud_detection_rate: 97.8,
    processing_speed: 1247,
    active_models: 12,
    data_processed_today: 45672
  });

  const aiCapabilities = {
    platinum: [
      'Basic Predictive Analytics',
      'Standard ML Models',
      'Fraud Detection',
      'Risk Scoring',
      'Trend Analysis',
      'Basic NLP Processing'
    ],
    diamond: [
      'Advanced Predictive Analytics',
      'Custom ML Model Training',
      'Real-time Fraud Detection',
      'Advanced Risk Assessment',
      'Computer Vision',
      'Full NLP Suite',
      'Decision Support AI',
      'Automated Model Management',
      'Custom AI Workflows'
    ]
  };

  const mockPredictions = [
    {
      type: 'Claim Success Probability',
      value: 87.3,
      confidence: 94.2,
      factors: ['Historical Data', 'Asset Type', 'Time Frame', 'Location'],
      trend: 'up'
    },
    {
      type: 'Fraud Risk Assessment',
      value: 12.1,
      confidence: 96.8,
      factors: ['Document Analysis', 'Pattern Recognition', 'Behavioral Signals'],
      trend: 'down'
    },
    {
      type: 'Recovery Time Estimate',
      value: 28,
      confidence: 89.5,
      factors: ['Jurisdiction', 'Asset Type', 'Claim Complexity', 'Historical Performance'],
      trend: 'stable'
    }
  ];

  const renderPredictiveAnalytics = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Prediction Accuracy</p>
                <p className="text-2xl font-bold text-green-600">{modelMetrics.prediction_accuracy}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
            <Progress value={modelMetrics.prediction_accuracy} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Active Models</p>
                <p className="text-2xl font-bold text-blue-600">{modelMetrics.active_models}</p>
              </div>
              <Brain className="h-8 w-8 text-blue-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">6 Training, 6 Production</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Processing Speed</p>
                <p className="text-2xl font-bold text-purple-600">{modelMetrics.processing_speed.toLocaleString()}/s</p>
              </div>
              <Zap className="h-8 w-8 text-purple-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">Records per second</div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-gray-600">Data Processed</p>
                <p className="text-2xl font-bold text-orange-600">{modelMetrics.data_processed_today.toLocaleString()}</p>
              </div>
              <Database className="h-8 w-8 text-orange-600" />
            </div>
            <div className="mt-2 text-xs text-gray-500">Today</div>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-5 w-5" />
              Active Predictions
            </CardTitle>
            <CardDescription>
              Real-time AI predictions and assessments
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {mockPredictions.map((prediction, index) => (
              <div key={index} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-center justify-between">
                  <h4 className="font-medium">{prediction.type}</h4>
                  <div className="flex items-center gap-2">
                    {prediction.trend === 'up' && <ArrowUp className="h-4 w-4 text-green-600" />}
                    {prediction.trend === 'down' && <ArrowDown className="h-4 w-4 text-red-600" />}
                    {prediction.trend === 'stable' && <Activity className="h-4 w-4 text-blue-600" />}
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <p className="text-sm text-gray-600">Value</p>
                    <p className="text-lg font-semibold">
                      {prediction.type.includes('Probability') ? `${prediction.value}%` : 
                       prediction.type.includes('Risk') ? `${prediction.value}%` : 
                       `${prediction.value} days`}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Confidence</p>
                    <p className="text-lg font-semibold text-green-600">{prediction.confidence}%</p>
                  </div>
                </div>
                <div>
                  <p className="text-sm text-gray-600 mb-2">Key Factors</p>
                  <div className="flex flex-wrap gap-1">
                    {prediction.factors.map((factor, i) => (
                      <Badge key={i} variant="outline" className="text-xs">
                        {factor}
                      </Badge>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Model Performance
            </CardTitle>
            <CardDescription>
              AI model accuracy and performance metrics
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Claim Success Prediction</span>
                <span className="text-sm font-medium">94.2%</span>
              </div>
              <Progress value={94.2} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Fraud Detection Model</span>
                <span className="text-sm font-medium">97.8%</span>
              </div>
              <Progress value={97.8} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Risk Assessment AI</span>
                <span className="text-sm font-medium">91.6%</span>
              </div>
              <Progress value={91.6} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Document Analysis</span>
                <span className="text-sm font-medium">96.1%</span>
              </div>
              <Progress value={96.1} className="h-2" />
            </div>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">NLP Sentiment Analysis</span>
                <span className="text-sm font-medium">89.3%</span>
              </div>
              <Progress value={89.3} className="h-2" />
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderMLManagement = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Cpu className="h-5 w-5" />
              Model Deployment
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {['Blue-Green', 'Canary', 'Rolling', 'A/B Testing'].map((strategy) => (
                <div key={strategy} className="flex items-center justify-between">
                  <span className="text-sm">{strategy}</span>
                  <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                    {userPlan === 'diamond' ? "Available" : "Diamond"}
                  </Badge>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5" />
              Model Monitoring
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Performance Tracking</span>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Drift Detection</span>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Auto Retraining</span>
                <Badge variant={userPlan === 'diamond' ? "default" : "secondary"}>
                  {userPlan === 'diamond' ? "Active" : "Diamond"}
                </Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Rollback Capability</span>
                <CheckCircle className="h-4 w-4 text-green-600" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Eye className="h-5 w-5" />
              Model Health
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm">Production Models</span>
                <Badge variant="default" className="bg-green-600">6 Healthy</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Training Models</span>
                <Badge variant="secondary">6 In Progress</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Alerts</span>
                <Badge variant="outline">0 Active</Badge>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm">Last Health Check</span>
                <span className="text-xs text-gray-500">2 min ago</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Model Performance Dashboard</CardTitle>
          <CardDescription>
            Real-time monitoring of all AI models in production
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[
              { name: 'Claim Success Predictor v2.1', accuracy: 94.2, status: 'Healthy', requests: 1247 },
              { name: 'Fraud Detection Engine v1.8', accuracy: 97.8, status: 'Healthy', requests: 892 },
              { name: 'Risk Assessment Model v3.0', accuracy: 91.6, status: 'Warning', requests: 534 },
              { name: 'Document Classifier v1.3', accuracy: 96.1, status: 'Healthy', requests: 723 },
              { name: 'NLP Sentiment Analyzer v2.0', accuracy: 89.3, status: 'Healthy', requests: 445 }
            ].map((model, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium">{model.name}</h4>
                  <Badge variant={
                    model.status === 'Healthy' ? 'default' :
                    model.status === 'Warning' ? 'outline' : 'destructive'
                  }>
                    {model.status}
                  </Badge>
                </div>
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Accuracy: </span>
                    <span className="font-medium">{model.accuracy}%</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Requests/hour: </span>
                    <span className="font-medium">{model.requests}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Status: </span>
                    <span className="font-medium">{model.status}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );

  const renderCapabilityComparison = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-purple-600" />
              Platinum AI Features
            </CardTitle>
            <CardDescription>
              Professional AI capabilities for enhanced operations
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {aiCapabilities.platinum.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-purple-600 rounded-full"></div>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Brain className="h-5 w-5 text-blue-600" />
              Diamond AI Features
            </CardTitle>
            <CardDescription>
              Enterprise-grade AI with custom capabilities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {aiCapabilities.diamond.map((feature, index) => (
                <div key={index} className="flex items-center gap-2">
                  <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                  <span className="text-sm">{feature}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>AI Performance Impact</CardTitle>
          <CardDescription>
            Measurable business impact from AI implementation
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">+34%</div>
              <div className="text-sm text-gray-600">Claim Success Rate</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">-67%</div>
              <div className="text-sm text-gray-600">Fraud Incidents</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-600">-45%</div>
              <div className="text-sm text-gray-600">Processing Time</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-orange-600">+28%</div>
              <div className="text-sm text-gray-600">Recovery Amount</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {userPlan === 'platinum' && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center">
              <Brain className="h-12 w-12 text-blue-600 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">Upgrade to Diamond AI</h3>
              <p className="text-gray-600 mb-4">
                Unlock custom AI model training, advanced automation, and enterprise-grade ML capabilities
              </p>
              <Button onClick={onUpgrade} className="bg-blue-600 hover:bg-blue-700">
                Upgrade to Diamond Plan
              </Button>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Advanced AI Capabilities</h2>
          <p className="text-gray-600">
            Enterprise-grade artificial intelligence for superior asset recovery
          </p>
        </div>
        <Badge variant={userPlan === 'diamond' ? "default" : "secondary"} className="px-3 py-1">
          {userPlan === 'diamond' ? 'Diamond AI Active' : 'Platinum AI'}
        </Badge>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="predictive">Predictive Analytics</TabsTrigger>
          <TabsTrigger value="models">ML Management</TabsTrigger>
          <TabsTrigger value="capabilities">Capabilities</TabsTrigger>
        </TabsList>

        <TabsContent value="predictive" className="space-y-6">
          {renderPredictiveAnalytics()}
        </TabsContent>

        <TabsContent value="models" className="space-y-6">
          {renderMLManagement()}
        </TabsContent>

        <TabsContent value="capabilities" className="space-y-6">
          {renderCapabilityComparison()}
        </TabsContent>
      </Tabs>
    </div>
  );
}; 