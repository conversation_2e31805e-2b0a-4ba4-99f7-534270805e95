// Report modal component for displaying processing results

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { 
  FileText, X, CheckCircle, XCircle, AlertCircle, 
  Users, MapPin, Clock, Shield 
} from 'lucide-react'
import { BatchFile } from '@/types/batchImport'
import { formatNumber, formatDate } from '@/utils/formatters'

interface ReportModalProps {
  show: boolean
  file: BatchFile | null
  onClose: () => void
}

export function ReportModal({ show, file, onClose }: ReportModalProps) {
  if (!show || !file) {
    return null
  }

  const getStatusIcon = (status: BatchFile['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'verified':
        return <Shield className="h-5 w-5 text-blue-600" />
      default:
        return <Clock className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusColor = (status: BatchFile['status']) => {
    switch (status) {
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'error':
        return 'bg-red-100 text-red-800'
      case 'verified':
        return 'bg-blue-100 text-blue-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <Card className="w-full max-w-4xl mx-4 max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              {getStatusIcon(file.status)}
              <div>
                <CardTitle>Processing Report</CardTitle>
                <CardDescription>{file.name}</CardDescription>
              </div>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* File Overview */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <FileText className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">File Size</p>
                <p className="font-medium">{(file.size / (1024 * 1024)).toFixed(1)} MB</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <MapPin className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">State</p>
                <p className="font-medium">{file.state || 'N/A'}</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Clock className="h-8 w-8 text-gray-600 mx-auto mb-2" />
                <p className="text-sm text-gray-600">Uploaded</p>
                <p className="font-medium">{formatDate(file.uploadedAt)}</p>
              </div>
              <div className="text-center p-3 bg-gray-50 rounded-lg">
                <Badge className={getStatusColor(file.status)} variant="secondary">
                  {file.status.replace('_', ' ')}
                </Badge>
              </div>
            </div>

            {/* Record Statistics */}
            {file.recordCount !== undefined && (
              <div>
                <h3 className="text-lg font-medium mb-3">Record Statistics</h3>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <p className="text-sm text-blue-600 font-medium">Total Records</p>
                    <p className="text-2xl font-bold text-blue-900">
                      {formatNumber(file.recordCount)}
                    </p>
                  </div>
                  {file.validRecords !== undefined && (
                    <div className="bg-green-50 p-4 rounded-lg">
                      <p className="text-sm text-green-600 font-medium">Valid Records</p>
                      <p className="text-2xl font-bold text-green-900">
                        {formatNumber(file.validRecords)}
                      </p>
                    </div>
                  )}
                  {file.errorRecords !== undefined && (
                    <div className="bg-red-50 p-4 rounded-lg">
                      <p className="text-sm text-red-600 font-medium">Error Records</p>
                      <p className="text-2xl font-bold text-red-900">
                        {formatNumber(file.errorRecords)}
                      </p>
                    </div>
                  )}
                  {file.duplicateRecords !== undefined && (
                    <div className="bg-orange-50 p-4 rounded-lg">
                      <p className="text-sm text-orange-600 font-medium">Duplicates</p>
                      <p className="text-2xl font-bold text-orange-900">
                        {formatNumber(file.duplicateRecords)}
                      </p>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Detected Headers */}
            {file.detectedHeaders && file.detectedHeaders.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-3">Detected Headers</h3>
                <div className="flex flex-wrap gap-2">
                  {file.detectedHeaders.map((header, index) => (
                    <Badge key={index} variant="outline">
                      {header}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Error Details */}
            {file.errorDetails && file.errorDetails.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                  <AlertCircle className="h-5 w-5 text-red-600" />
                  Error Details (First 10)
                </h3>
                <div className="bg-red-50 border border-red-200 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div className="space-y-2">
                    {file.errorDetails.slice(0, 10).map((error, index) => (
                      <div key={index} className="text-sm">
                        <span className="font-medium text-red-800">Row {error.row}:</span>
                        <span className="text-red-700 ml-2">{error.error}</span>
                      </div>
                    ))}
                    {file.errorDetails.length > 10 && (
                      <p className="text-sm text-red-600 mt-2">
                        ...and {file.errorDetails.length - 10} more errors
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Duplicate Details */}
            {file.duplicates && file.duplicates.length > 0 && (
              <div>
                <h3 className="text-lg font-medium mb-3 flex items-center gap-2">
                  <Users className="h-5 w-5 text-orange-600" />
                  Duplicate Details (First 5)
                </h3>
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 max-h-64 overflow-y-auto">
                  <div className="space-y-3">
                    {file.duplicates.slice(0, 5).map((duplicate, index) => (
                      <div key={index} className="text-sm">
                        <div className="font-medium text-orange-800">
                          Rows {duplicate.originalRow} and {duplicate.duplicateRow} 
                          ({duplicate.similarity}% similarity)
                        </div>
                        <div className="text-orange-700 ml-2">
                          Matching fields: {duplicate.matchingFields.join(', ')}
                        </div>
                      </div>
                    ))}
                    {file.duplicates.length > 5 && (
                      <p className="text-sm text-orange-600 mt-2">
                        ...and {file.duplicates.length - 5} more duplicates
                      </p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {file.errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <h3 className="text-lg font-medium mb-2 text-red-800">Error Message</h3>
                <p className="text-red-700">{file.errorMessage}</p>
              </div>
            )}

            {/* Storage Information */}
            {file.batchId && (
              <div className="bg-gray-50 rounded-lg p-4">
                <h3 className="text-lg font-medium mb-2">Storage Information</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Batch ID:</span>
                    <span className="ml-2 font-mono">{file.batchId}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Storage Method:</span>
                    <span className="ml-2">{file.storageMethod || 'localStorage'}</span>
                  </div>
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 