import React, { useState, useRef, useCallback } from 'react';
import { 
  Upload, File, CheckCircle, AlertTriangle, X, Play, Pause, 
  RotateCcw, Download, Eye, Settings, ArrowRight, FileText,
  Database, Clock, TrendingUp, AlertCircle, RefreshCw
} from 'lucide-react';
import { AdministratorState, CSVUploadSession, ColumnMapping } from '../types';

interface CSVUploadManagerProps {
  adminState: AdministratorState;
}

export const CSVUploadManager: React.FC<CSVUploadManagerProps> = ({ adminState }) => {
  const [uploadSessions, setUploadSessions] = useState<CSVUploadSession[]>([]);
  const [activeSession, setActiveSession] = useState<CSVUploadSession | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [showMappingModal, setShowMappingModal] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Mock upload sessions for demonstration
  React.useEffect(() => {
    const mockSessions: CSVUploadSession[] = [
      {
        sessionId: 'session-001',
        fileName: 'georgia_properties_Q4_2024.csv',
        fileSize: 287654321, // ~288MB
        uploadProgress: 100,
        processingStatus: 'processing',
        recordCount: 1847293,
        processedRecords: 1234567,
        errorCount: 234,
        warnings: ['Some phone numbers need formatting', 'Missing ZIP+4 for 15% of addresses'],
        errors: [],
        startTime: new Date(Date.now() - 3600000),
        estimatedCompletion: new Date(Date.now() + 900000),
        processingRate: 1847,
        dataMapping: [],
        qualityScore: 87.3,
        preview: {
          headers: ['Property_ID', 'Owner_Name', 'Address', 'City', 'State', 'ZIP', 'Phone', 'Email', 'Value'],
          sampleRows: [
            ['GA001234', 'John Smith', '123 Main St', 'Atlanta', 'GA', '30309', '************', '<EMAIL>', '250000'],
            ['GA001235', 'Jane Doe', '456 Oak Ave', 'Savannah', 'GA', '31401', '************', '<EMAIL>', '185000']
          ],
          columnTypes: {},
          columnStats: {}
        }
      },
      {
        sessionId: 'session-002',
        fileName: 'florida_leads_batch_47.csv',
        fileSize: 156789123,
        uploadProgress: 100,
        processingStatus: 'completed',
        recordCount: 987654,
        processedRecords: 987654,
        errorCount: 0,
        warnings: [],
        errors: [],
        startTime: new Date(Date.now() - 7200000),
        completionTime: new Date(Date.now() - 3600000),
        processingRate: 2341,
        dataMapping: [],
        qualityScore: 96.7,
        preview: {
          headers: ['Lead_ID', 'First_Name', 'Last_Name', 'Property_Address', 'Phone', 'Email', 'Estimated_Value'],
          sampleRows: [
            ['FL098765', 'Robert', 'Johnson', '789 Beach Blvd', '************', '<EMAIL>', '425000'],
            ['FL098766', 'Maria', 'Garcia', '321 Sunset Dr', '************', '<EMAIL>', '375000']
          ],
          columnTypes: {},
          columnStats: {}
        }
      }
    ];
    setUploadSessions(mockSessions);
  }, []);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    handleFileUpload(files);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    handleFileUpload(files);
  }, []);

  const handleFileUpload = (files: File[]) => {
    files.forEach(file => {
      if (file.name.endsWith('.csv')) {
        const newSession: CSVUploadSession = {
          sessionId: `session-${Date.now()}`,
          fileName: file.name,
          fileSize: file.size,
          uploadProgress: 0,
          processingStatus: 'uploading',
          recordCount: 0,
          processedRecords: 0,
          errorCount: 0,
          warnings: [],
          errors: [],
          startTime: new Date(),
          processingRate: 0,
          dataMapping: [],
          qualityScore: 0,
          preview: {
            headers: [],
            sampleRows: [],
            columnTypes: {},
            columnStats: {}
          }
        };
        
        setUploadSessions(prev => [newSession, ...prev]);
        
        // Simulate upload progress
        const progressInterval = setInterval(() => {
          setUploadSessions(prev => prev.map(session => {
            if (session.sessionId === newSession.sessionId && session.uploadProgress < 100) {
              const newProgress = Math.min(session.uploadProgress + Math.random() * 10, 100);
              if (newProgress === 100) {
                clearInterval(progressInterval);
                return { 
                  ...session, 
                  uploadProgress: 100, 
                  processingStatus: 'mapping' as const 
                };
              }
              return { ...session, uploadProgress: newProgress };
            }
            return session;
          }));
        }, 500);
      }
    });
  };

  const formatFileSize = (bytes: number) => {
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    if (bytes === 0) return '0 Bytes';
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round((bytes / Math.pow(1024, i)) * 100) / 100 + ' ' + sizes[i];
  };

  const formatDuration = (ms: number) => {
    const minutes = Math.floor(ms / 60000);
    const seconds = Math.floor((ms % 60000) / 1000);
    return `${minutes}m ${seconds}s`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'uploading': return 'bg-blue-100 text-blue-800';
      case 'mapping': return 'bg-yellow-100 text-yellow-800';
      case 'processing': return 'bg-orange-100 text-orange-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Upload Area */}
      <div 
        className={`border-2 border-dashed rounded-xl p-8 text-center transition-colors ${
          isDragOver 
            ? 'border-blue-500 bg-blue-50' 
            : 'border-gray-300 bg-gray-50 hover:border-gray-400'
        }`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
      >
        <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Upload CSV Files
        </h3>
        <p className="text-gray-600 mb-4">
          Drag and drop your CSV files here, or click to browse. 
          Support for files up to 300MB and 2M+ records.
        </p>
        <button
          onClick={() => fileInputRef.current?.click()}
          className="bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors"
        >
          Select Files
        </button>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept=".csv"
          onChange={handleFileSelect}
          className="hidden"
        />
        <div className="mt-4 text-sm text-gray-500">
          Supports CSV files • Max 300MB per file • Automatic data mapping
        </div>
      </div>

      {/* Upload Sessions */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">Upload Sessions</h3>
            <div className="flex items-center space-x-4">
              <div className="text-sm text-gray-600">
                {uploadSessions.length} sessions
              </div>
              <button className="text-blue-600 hover:text-blue-700 text-sm font-medium">
                View All
              </button>
            </div>
          </div>
        </div>

        <div className="divide-y divide-gray-200">
          {uploadSessions.map(session => (
            <div key={session.sessionId} className="p-6">
              <div className="flex items-start justify-between">
                <div className="flex items-start space-x-4">
                  <div className="flex-shrink-0">
                    <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                      <FileText className="w-5 h-5 text-blue-600" />
                    </div>
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-3">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {session.fileName}
                      </h4>
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(session.processingStatus)}`}>
                        {session.processingStatus.charAt(0).toUpperCase() + session.processingStatus.slice(1)}
                      </span>
                    </div>
                    
                    <div className="mt-1 flex items-center space-x-4 text-sm text-gray-500">
                      <span>{formatFileSize(session.fileSize)}</span>
                      <span>•</span>
                      <span>{session.recordCount.toLocaleString()} records</span>
                      <span>•</span>
                      <span>Started {new Date(session.startTime).toLocaleTimeString()}</span>
                    </div>

                    {/* Progress Bar */}
                    {session.processingStatus !== 'completed' && (
                      <div className="mt-3">
                        <div className="flex justify-between text-xs text-gray-600 mb-1">
                          <span>
                            {session.processingStatus === 'uploading' ? 'Uploading' :
                             session.processingStatus === 'mapping' ? 'Analyzing structure' :
                             'Processing records'}
                          </span>
                          <span>
                            {session.processingStatus === 'processing' ? 
                              `${session.processedRecords.toLocaleString()} / ${session.recordCount.toLocaleString()}` :
                              `${Math.round(session.uploadProgress)}%`
                            }
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className={`h-2 rounded-full transition-all duration-300 ${
                              session.processingStatus === 'uploading' ? 'bg-blue-500' :
                              session.processingStatus === 'mapping' ? 'bg-yellow-500' :
                              'bg-orange-500'
                            }`}
                            style={{ 
                              width: session.processingStatus === 'processing' ? 
                                `${(session.processedRecords / session.recordCount) * 100}%` :
                                `${session.uploadProgress}%`
                            }}
                          ></div>
                        </div>
                      </div>
                    )}

                    {/* Processing Stats */}
                    {session.processingStatus === 'processing' && (
                      <div className="mt-3 grid grid-cols-3 gap-4">
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {session.processingRate.toLocaleString()}/min
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-gray-600">
                            Quality: {session.qualityScore}%
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="w-4 h-4 text-yellow-500" />
                          <span className="text-sm text-gray-600">
                            {session.errorCount} errors
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Completion Stats */}
                    {session.processingStatus === 'completed' && (
                      <div className="mt-3 grid grid-cols-4 gap-4">
                        <div className="flex items-center space-x-2">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span className="text-sm text-green-600">
                            {session.processedRecords.toLocaleString()} processed
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <TrendingUp className="w-4 h-4 text-blue-500" />
                          <span className="text-sm text-gray-600">
                            Quality: {session.qualityScore}%
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="w-4 h-4 text-gray-400" />
                          <span className="text-sm text-gray-600">
                            {session.completionTime && formatDuration(
                              session.completionTime.getTime() - session.startTime.getTime()
                            )}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Database className="w-4 h-4 text-purple-500" />
                          <span className="text-sm text-gray-600">
                            Ready for assignment
                          </span>
                        </div>
                      </div>
                    )}

                    {/* Warnings */}
                    {session.warnings.length > 0 && (
                      <div className="mt-3 p-3 bg-yellow-50 rounded-lg">
                        <div className="flex items-start space-x-2">
                          <AlertTriangle className="w-4 h-4 text-yellow-600 mt-0.5" />
                          <div className="flex-1">
                            <p className="text-sm font-medium text-yellow-800">Warnings</p>
                            <ul className="mt-1 text-sm text-yellow-700 space-y-1">
                              {session.warnings.map((warning, index) => (
                                <li key={index}>• {warning}</li>
                              ))}
                            </ul>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>

                {/* Actions */}
                <div className="flex items-center space-x-3">
                  {session.processingStatus === 'mapping' && (
                    <button
                      onClick={() => {
                        setActiveSession(session);
                        setShowMappingModal(true);
                      }}
                      className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm font-medium"
                    >
                      Configure Mapping
                    </button>
                  )}
                  
                  {session.processingStatus === 'processing' && (
                    <button className="text-orange-600 hover:text-orange-700 p-2 rounded-lg hover:bg-orange-50">
                      <Pause className="w-4 h-4" />
                    </button>
                  )}
                  
                  {session.processingStatus === 'completed' && (
                    <>
                      <button className="text-blue-600 hover:text-blue-700 p-2 rounded-lg hover:bg-blue-50">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-700 p-2 rounded-lg hover:bg-green-50">
                        <Download className="w-4 h-4" />
                      </button>
                    </>
                  )}
                  
                  <button className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-50">
                    <X className="w-4 h-4" />
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Upload className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Uploads</p>
              <p className="text-2xl font-bold text-gray-900">
                {uploadSessions.length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Database className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Records Processed</p>
              <p className="text-2xl font-bold text-gray-900">
                {uploadSessions.reduce((sum, s) => sum + s.processedRecords, 0).toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Quality Score</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(uploadSessions.reduce((sum, s) => sum + s.qualityScore, 0) / uploadSessions.length)}%
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Processing Rate</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(uploadSessions.reduce((sum, s) => sum + s.processingRate, 0) / uploadSessions.length).toLocaleString()}/min
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Column Mapping Modal */}
      {showMappingModal && activeSession && (
        <ColumnMappingModal 
          session={activeSession}
          onClose={() => setShowMappingModal(false)}
          onSave={(mappings) => {
            // Handle saving mappings
            setShowMappingModal(false);
            setActiveSession(null);
          }}
        />
      )}
    </div>
  );
};

// Column Mapping Modal Component
const ColumnMappingModal: React.FC<{
  session: CSVUploadSession;
  onClose: () => void;
  onSave: (mappings: ColumnMapping[]) => void;
}> = ({ session, onClose, onSave }) => {
  const [mappings, setMappings] = useState<ColumnMapping[]>([]);

  React.useEffect(() => {
    // Initialize with suggested mappings
    const suggestedMappings: ColumnMapping[] = session.preview.headers.map(header => ({
      sourceColumn: header,
      targetField: getSuggestedField(header),
      dataType: getSuggestedType(header),
      isRequired: isRequiredField(header),
      transformationRules: [],
      validationRules: [],
      mappingConfidence: calculateConfidence(header),
      sampleValues: session.preview.sampleRows.map(row => 
        row[session.preview.headers.indexOf(header)]
      ).slice(0, 5)
    }));
    setMappings(suggestedMappings);
  }, [session]);

  const getSuggestedField = (header: string): string => {
    const lower = header.toLowerCase();
    if (lower.includes('name')) return 'full_name';
    if (lower.includes('first')) return 'first_name';
    if (lower.includes('last')) return 'last_name';
    if (lower.includes('email')) return 'email';
    if (lower.includes('phone')) return 'phone';
    if (lower.includes('address')) return 'address';
    if (lower.includes('city')) return 'city';
    if (lower.includes('state')) return 'state';
    if (lower.includes('zip')) return 'zip_code';
    if (lower.includes('value') || lower.includes('amount')) return 'property_value';
    return 'custom_field';
  };

  const getSuggestedType = (header: string): string => {
    const lower = header.toLowerCase();
    if (lower.includes('email')) return 'email';
    if (lower.includes('phone')) return 'phone';
    if (lower.includes('value') || lower.includes('amount')) return 'number';
    if (lower.includes('date')) return 'date';
    return 'string';
  };

  const isRequiredField = (header: string): boolean => {
    const lower = header.toLowerCase();
    return lower.includes('name') || lower.includes('address') || lower.includes('phone');
  };

  const calculateConfidence = (header: string): number => {
    // Simple confidence calculation based on header name similarity
    return Math.floor(Math.random() * 30) + 70; // 70-100%
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-900">Column Mapping</h3>
              <p className="text-sm text-gray-600">{session.fileName}</p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-180px)]">
          <div className="space-y-4">
            {mappings.map((mapping, index) => (
              <div key={index} className="border border-gray-200 rounded-lg p-4">
                <div className="grid grid-cols-4 gap-4 items-center">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Source Column
                    </label>
                    <div className="text-sm text-gray-900 font-mono bg-gray-50 px-3 py-2 rounded">
                      {mapping.sourceColumn}
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Target Field
                    </label>
                    <select 
                      value={mapping.targetField}
                      onChange={(e) => {
                        const newMappings = [...mappings];
                        newMappings[index].targetField = e.target.value;
                        setMappings(newMappings);
                      }}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="full_name">Full Name</option>
                      <option value="first_name">First Name</option>
                      <option value="last_name">Last Name</option>
                      <option value="email">Email</option>
                      <option value="phone">Phone</option>
                      <option value="address">Address</option>
                      <option value="city">City</option>
                      <option value="state">State</option>
                      <option value="zip_code">ZIP Code</option>
                      <option value="property_value">Property Value</option>
                      <option value="custom_field">Custom Field</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Data Type
                    </label>
                    <select 
                      value={mapping.dataType}
                      onChange={(e) => {
                        const newMappings = [...mappings];
                        newMappings[index].dataType = e.target.value as any;
                        setMappings(newMappings);
                      }}
                      className="w-full border border-gray-300 rounded-lg px-3 py-2 text-sm"
                    >
                      <option value="string">Text</option>
                      <option value="number">Number</option>
                      <option value="email">Email</option>
                      <option value="phone">Phone</option>
                      <option value="date">Date</option>
                      <option value="address">Address</option>
                    </select>
                  </div>

                  <div className="flex items-center space-x-3">
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={mapping.isRequired}
                        onChange={(e) => {
                          const newMappings = [...mappings];
                          newMappings[index].isRequired = e.target.checked;
                          setMappings(newMappings);
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm text-gray-700">Required</span>
                    </label>
                    <div className="text-xs text-gray-500">
                      {mapping.mappingConfidence}% confidence
                    </div>
                  </div>
                </div>

                {/* Sample Values */}
                <div className="mt-3 pt-3 border-t border-gray-100">
                  <label className="block text-xs font-medium text-gray-700 mb-1">
                    Sample Values
                  </label>
                  <div className="flex space-x-2">
                    {mapping.sampleValues.slice(0, 5).map((value, idx) => (
                      <span key={idx} className="inline-block bg-gray-100 text-xs px-2 py-1 rounded">
                        {value}
                      </span>
                    ))}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-600">
              {mappings.length} columns mapped • 
              {mappings.filter(m => m.isRequired).length} required fields
            </div>
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
              >
                Cancel
              </button>
              <button
                onClick={() => onSave(mappings)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Start Processing
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
