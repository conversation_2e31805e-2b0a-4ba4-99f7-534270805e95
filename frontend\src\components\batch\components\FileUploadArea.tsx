// File upload area component with drag and drop functionality

import React from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Upload, FileText, AlertCircle } from 'lucide-react'

interface FileUploadAreaProps {
  selectedState: string
  onFileSelect: (files: FileList) => void
  fileInputRef: React.RefObject<HTMLInputElement>
  generateSampleCSV: () => void
  isUploading: boolean
}

export function FileUploadArea({
  selectedState,
  onFileSelect,
  fileInputRef,
  generateSampleCSV,
  isUploading
}: FileUploadAreaProps) {
  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault()
    const files = e.dataTransfer.files
    if (files.length > 0) {
      onFileSelect(files)
    }
  }

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault()
  }

  const handleFileInputClick = () => {
    fileInputRef.current?.click()
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-4 w-4" />
          File Upload
        </CardTitle>
        <CardDescription>
          Upload CSV files for batch processing. Supports files up to 400MB.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div
          className={`border-2 border-dashed rounded-lg p-8 text-center space-y-4 transition-colors ${
            selectedState 
              ? 'border-blue-300 hover:border-blue-400 hover:bg-blue-50' 
              : 'border-gray-300 bg-gray-50'
          }`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
        >
          {!selectedState ? (
            <>
              <AlertCircle className="h-12 w-12 text-gray-400 mx-auto" />
              <div>
                <p className="text-gray-600">Please select a state first</p>
              </div>
            </>
          ) : isUploading ? (
            <>
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto" />
              <div>
                <p className="text-blue-600 font-medium">Uploading files...</p>
                <p className="text-sm text-gray-500">Please wait while we process your files</p>
              </div>
            </>
          ) : (
            <>
              <Upload className="h-12 w-12 text-blue-500 mx-auto" />
              <div>
                <p className="text-lg font-medium text-gray-700">
                  Drop CSV files here or click to browse
                </p>
                <p className="text-sm text-gray-500">
                  Supports multiple files up to 400MB each
                </p>
              </div>
              <div className="flex justify-center gap-4">
                <Button onClick={handleFileInputClick} className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  Browse Files
                </Button>
                <Button variant="outline" onClick={generateSampleCSV}>
                  Download Sample CSV
                </Button>
              </div>
            </>
          )}
        </div>
        
        <div className="mt-4 text-sm text-gray-600">
          <p><strong>Supported formats:</strong> CSV files (.csv)</p>
          <p><strong>File size limit:</strong> 400MB per file</p>
          <p><strong>Record limit:</strong> Unlimited (chunked processing for large files)</p>
        </div>
      </CardContent>
    </Card>
  )
} 