{"name": "mnemonist", "version": "0.40.0", "description": "Curated collection of data structures for the JavaScript/TypeScript.", "scripts": {"lint": "eslint --cache --ext .js,.mjs ./*.js ./*.mjs ./utils ./test", "lint:fix": "eslint --cache --fix --ext .js,.mjs ./*.js ./*.mjs ./utils ./test", "prepublishOnly": "npm run lint && npm test && npm run test:exports", "test": "mocha", "test:exports": "cd test/exports && npm i && npm run test"}, "main": "./index.js", "module": "./index.mjs", "types": "./index.d.ts", "exports": {".": {"types": "./index.d.ts", "require": "./index.js", "import": "./index.mjs"}, "./*": {"types": "./*.d.ts", "require": "./*.js"}, "./*.js": {"types": "./*.d.ts", "require": "./*.js"}}, "files": ["sort", "utils", "*.d.ts", "*.js", "*.mjs"], "repository": {"type": "git", "url": "git+https://github.com/yomguithereal/mnemonist.git"}, "keywords": ["bag", "bimap", "bit array", "bit set", "bit vector", "bitset", "bk tree", "burkhard-keller tree", "cache", "circular buffer", "counter", "data structures", "default map", "deque", "disjoint set", "<PERSON><PERSON><PERSON><PERSON> heap", "fuzzy map", "hashed array tree", "heap", "interval tree", "inverted index", "kd tree", "linked list", "lru", "lru cache", "multimap", "multiset", "passjoin", "queue", "sparse map", "sparse set", "stack", "structures", "suffix tree", "symspell", "trie", "union find", "vantage point tree", "vector", "vp tree"], "author": {"name": "<PERSON>", "url": "http://github.com/Yomguithereal"}, "license": "MIT", "bugs": {"url": "https://github.com/yomguithereal/mnemonist/issues"}, "homepage": "https://github.com/yomguithereal/mnemonist#readme", "dependencies": {"obliterator": "^2.0.4"}, "devDependencies": {"@yomguithereal/eslint-config": "^4.4.0", "asciitree": "^1.0.2", "damerau-levenshtein": "^1.0.8", "eslint": "^8.2.0", "leven": "^3.1.0", "lodash": "^4.17.21", "matcha": "^0.7.0", "mocha": "^9.1.3", "pandemonium": "^2.0.0", "seedrandom": "^3.0.5", "static-kdtree": "^1.0.2"}, "eslintConfig": {"extends": "@yomguithereal/eslint-config", "parserOptions": {"ecmaVersion": 6, "ecmaFeatures": {"forOf": true}, "sourceType": "module"}, "rules": {"no-new": 0}}}