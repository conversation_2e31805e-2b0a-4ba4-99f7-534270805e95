import React, { useState, useEffect } from 'react';
import { 
  Users, UserPlus, Search, Filter, MoreVertical, Edit, Trash2, 
  Shield, Eye, Mail, Phone, MapPin, Calendar, Crown, Star,
  TrendingUp, Activity, CheckCircle, AlertTriangle, Clock,
  UserCheck, Settings, Download, Upload, RefreshCw, X
} from 'lucide-react';
import { AdministratorState, UserAccount } from '../types';

interface UserManagementProps {
  adminState: AdministratorState;
}

export const UserManagement: React.FC<UserManagementProps> = ({ adminState }) => {
  const [users, setUsers] = useState<UserAccount[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserAccount[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeFilter, setActiveFilter] = useState<string>('all');
  const [showUserModal, setShowUserModal] = useState(false);
  const [editingUser, setEditingUser] = useState<UserAccount | null>(null);
  const [showBulkActions, setShowBulkActions] = useState(false);

  // Mock user data
  useEffect(() => {
    const mockUsers: UserAccount[] = [
      {
        id: 'usr-001',
        email: '<EMAIL>',
        created_at: '2024-01-15T00:00:00Z',
        updated_at: '2024-12-01T00:00:00Z',
        profile: {
          firstName: 'Sarah',
          lastName: 'Johnson',
          email: '<EMAIL>',
          phone: '******-555-0123',
          avatar: '/avatars/sarah.jpg',
          department: 'Operations',
          jobTitle: 'Senior Asset Recovery Agent',
          location: 'Atlanta, GA',
          timezone: 'America/New_York',
          language: 'en-US',
          startDate: new Date('2024-01-15'),
          manager: 'usr-002',
          directReports: ['usr-004', 'usr-005'],
          skills: ['Negotiation', 'Legal Research', 'Client Relations'],
          certifications: ['Certified Recovery Specialist', 'TCPA Certified'],
          emergencyContact: {
            name: 'Michael Johnson',
            relationship: 'Spouse',
            phone: '******-555-0124'
          }
        },
        permissions: {
          role: 'senior_agent',
          permissions: [
            { resource: 'leads', actions: ['read', 'write', 'assign'], scope: 'team' },
            { resource: 'reports', actions: ['read', 'create'], scope: 'team' },
            { resource: 'team', actions: ['read', 'manage'], scope: 'team' }
          ],
          restrictions: [],
          customRules: [],
          effectiveDate: new Date('2024-01-15')
        },
        subscription: {
          plan: 'professional',
          features: ['advanced_search', 'team_management', 'analytics'],
          limits: {
            searchCredits: 1000,
            storageGB: 50,
            apiCalls: 10000,
            activeLeads: 500,
            teamMembers: 5,
            customFields: 20
          },
          usage: {
            searchCreditsUsed: 743,
            storageUsedGB: 32.5,
            apiCallsUsed: 7234,
            activeLeadsCount: 347,
            teamMembersCount: 3,
            lastResetDate: new Date('2024-12-01')
          },
          billing: {
            billingCycle: 'monthly',
            nextBillingDate: new Date('2025-01-01'),
            paymentMethod: 'Credit Card',
            billingAddress: {
              street: '123 Main St',
              city: 'Atlanta',
              state: 'GA',
              zipCode: '30309',
              country: 'USA'
            },
            invoiceHistory: []
          }
        },
        activity: {
          lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000),
          loginCount: 156,
          sessionDuration: 4.2,
          featuresUsed: [
            { feature: 'lead_search', usageCount: 89, lastUsed: new Date(), averageSessionTime: 12.5 },
            { feature: 'contact_management', usageCount: 67, lastUsed: new Date(), averageSessionTime: 8.3 }
          ],
          recentActions: [],
          performanceMetrics: {
            productivityScore: 94,
            engagementLevel: 'high',
            featureAdoptionRate: 87,
            helpDeskTickets: 2
          }
        },
        security: {
          mfaEnabled: true,
          mfaMethod: 'app',
          passwordLastChanged: new Date('2024-11-15'),
          securityQuestions: [],
          trustedDevices: [],
          securityAlerts: [],
          ipRestrictions: []
        },
        preferences: {
          notifications: {
            email: true,
            sms: false,
            push: true,
            inApp: true,
            frequency: 'real_time',
            types: ['lead_assignments', 'team_updates'],
            quietHours: {
              enabled: true,
              startTime: '18:00',
              endTime: '08:00',
              timezone: 'America/New_York'
            }
          },
          dashboard: {
            layout: 'grid',
            widgets: [],
            theme: 'light',
            refreshRate: 30,
            defaultView: 'leads'
          },
          communication: {
            preferredChannels: ['email', 'slack'],
            responseTimeExpectation: 2,
            language: 'en-US',
            timezone: 'America/New_York',
            workingHours: []
          },
          privacy: {
            profileVisibility: 'organization',
            activityTracking: true,
            locationSharing: false,
            dataCollection: true,
            marketingCommunications: false
          },
          accessibility: {
            screenReader: false,
            highContrast: false,
            largeText: false,
            keyboardNavigation: true,
            motionReduction: false
          }
        },
        performance: {
          overallRating: 4.7,
          goals: [],
          achievements: [],
          feedback: [],
          developmentPlan: {
            id: 'dev-001',
            objectives: [],
            timeline: '6 months',
            status: 'in_progress',
            progress: 65,
            budget: 5000
          },
          performanceHistory: []
        }
      },
      {
        id: 'usr-002',
        email: '<EMAIL>',
        created_at: '2023-11-20T00:00:00Z',
        updated_at: '2024-12-01T00:00:00Z',
        profile: {
          firstName: 'Michael',
          lastName: 'Chen',
          email: '<EMAIL>',
          phone: '******-555-0156',
          department: 'Operations',
          jobTitle: 'Team Lead - Southeast Region',
          location: 'Atlanta, GA',
          timezone: 'America/New_York',
          language: 'en-US',
          startDate: new Date('2023-11-20'),
          directReports: ['usr-001', 'usr-003'],
          skills: ['Team Management', 'Strategic Planning', 'Compliance'],
          certifications: ['Team Leadership Certification', 'Compliance Officer']
        },
        permissions: {
          role: 'team_manager',
          permissions: [
            { resource: 'leads', actions: ['read', 'write', 'assign', 'reassign'], scope: 'organization' },
            { resource: 'reports', actions: ['read', 'create', 'manage'], scope: 'organization' },
            { resource: 'team', actions: ['read', 'manage', 'evaluate'], scope: 'organization' }
          ],
          restrictions: [],
          customRules: [],
          effectiveDate: new Date('2023-11-20')
        },
        subscription: {
          plan: 'enterprise',
          features: ['advanced_search', 'team_management', 'analytics', 'api_access'],
          limits: {
            searchCredits: 2500,
            storageGB: 100,
            apiCalls: 25000,
            activeLeads: 1000,
            teamMembers: 15,
            customFields: 50
          },
          usage: {
            searchCreditsUsed: 1234,
            storageUsedGB: 67.8,
            apiCallsUsed: 15432,
            activeLeadsCount: 689,
            teamMembersCount: 8,
            lastResetDate: new Date('2024-12-01')
          },
          billing: {
            billingCycle: 'annual',
            nextBillingDate: new Date('2025-11-20'),
            paymentMethod: 'Corporate Card',
            billingAddress: {
              street: '456 Corporate Blvd',
              city: 'Atlanta',
              state: 'GA',
              zipCode: '30308',
              country: 'USA'
            },
            invoiceHistory: []
          }
        },
        activity: {
          lastLogin: new Date(Date.now() - 30 * 60 * 1000),
          loginCount: 289,
          sessionDuration: 6.7,
          featuresUsed: [
            { feature: 'team_analytics', usageCount: 145, lastUsed: new Date(), averageSessionTime: 18.2 },
            { feature: 'lead_assignment', usageCount: 234, lastUsed: new Date(), averageSessionTime: 5.7 }
          ],
          recentActions: [],
          performanceMetrics: {
            productivityScore: 96,
            engagementLevel: 'high',
            featureAdoptionRate: 93,
            helpDeskTickets: 1
          }
        },
        security: {
          mfaEnabled: true,
          mfaMethod: 'app',
          passwordLastChanged: new Date('2024-10-20'),
          securityQuestions: [],
          trustedDevices: [],
          securityAlerts: [],
          ipRestrictions: []
        },
        preferences: {
          notifications: {
            email: true,
            sms: true,
            push: true,
            inApp: true,
            frequency: 'real_time',
            types: ['system_alerts', 'team_performance', 'escalations'],
            quietHours: {
              enabled: false,
              startTime: '18:00',
              endTime: '08:00',
              timezone: 'America/New_York'
            }
          },
          dashboard: {
            layout: 'executive',
            widgets: [],
            theme: 'dark',
            refreshRate: 15,
            defaultView: 'team_dashboard'
          },
          communication: {
            preferredChannels: ['email', 'phone', 'slack'],
            responseTimeExpectation: 1,
            language: 'en-US',
            timezone: 'America/New_York',
            workingHours: []
          },
          privacy: {
            profileVisibility: 'public',
            activityTracking: true,
            locationSharing: true,
            dataCollection: true,
            marketingCommunications: true
          },
          accessibility: {
            screenReader: false,
            highContrast: false,
            largeText: true,
            keyboardNavigation: true,
            motionReduction: false
          }
        },
        performance: {
          overallRating: 4.9,
          goals: [],
          achievements: [],
          feedback: [],
          developmentPlan: {
            id: 'dev-002',
            objectives: [],
            timeline: '12 months',
            status: 'approved',
            progress: 23,
            budget: 8000
          },
          performanceHistory: []
        }
      }
    ];

    setUsers(mockUsers);
    setFilteredUsers(mockUsers);
  }, []);

  // Filter and search functionality
  useEffect(() => {
    let filtered = users;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user => 
        user.profile.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.profile.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.profile.department.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (activeFilter !== 'all') {
      filtered = filtered.filter(user => user.permissions.role === activeFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, activeFilter]);

  const handleBulkAction = (action: string) => {
    console.log(`Bulk action: ${action} on users:`, selectedUsers);
    setSelectedUsers([]);
    setShowBulkActions(false);
  };

  const formatLastLogin = (date: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffHours < 24) return `${diffHours}h ago`;
    return `${diffDays}d ago`;
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'administrator': return 'bg-red-100 text-red-800';
      case 'team_manager': return 'bg-purple-100 text-purple-800';
      case 'senior_agent': return 'bg-blue-100 text-blue-800';
      case 'junior_agent': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (user: UserAccount) => {
    const lastLogin = new Date(user.activity.lastLogin);
    const hoursAgo = (Date.now() - lastLogin.getTime()) / (1000 * 60 * 60);
    
    if (hoursAgo < 1) return 'bg-green-500';
    if (hoursAgo < 24) return 'bg-yellow-500';
    return 'bg-gray-400';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold text-gray-900">User Management</h2>
          <p className="text-gray-600">Manage user accounts, permissions, and team structure</p>
        </div>
        <div className="flex space-x-3">
          <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
            <Download className="w-4 h-4" />
            <span>Export</span>
          </button>
          <button className="bg-white border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 flex items-center space-x-2">
            <Upload className="w-4 h-4" />
            <span>Import</span>
          </button>
          <button 
            onClick={() => setShowUserModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 flex items-center space-x-2"
          >
            <UserPlus className="w-4 h-4" />
            <span>Add User</span>
          </button>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
              <Users className="w-5 h-5 text-blue-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Total Users</p>
              <p className="text-2xl font-bold text-gray-900">{users.length}</p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
              <Activity className="w-5 h-5 text-green-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Active Now</p>
              <p className="text-2xl font-bold text-gray-900">
                {users.filter(u => {
                  const hoursAgo = (Date.now() - new Date(u.activity.lastLogin).getTime()) / (1000 * 60 * 60);
                  return hoursAgo < 1;
                }).length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center">
              <Crown className="w-5 h-5 text-purple-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Team Managers</p>
              <p className="text-2xl font-bold text-gray-900">
                {users.filter(u => u.permissions.role === 'team_manager').length}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-yellow-100 rounded-lg flex items-center justify-center">
              <TrendingUp className="w-5 h-5 text-yellow-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Performance</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(users.reduce((sum, u) => sum + u.performance.overallRating, 0) / users.length * 10) / 10}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center">
              <Clock className="w-5 h-5 text-orange-600" />
            </div>
            <div>
              <p className="text-sm text-gray-600">Avg Session</p>
              <p className="text-2xl font-bold text-gray-900">
                {Math.round(users.reduce((sum, u) => sum + u.activity.sessionDuration, 0) / users.length * 10) / 10}h
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center space-x-4 flex-1">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <input
                type="text"
                placeholder="Search users..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
              />
            </div>

            <div className="flex items-center space-x-2">
              <Filter className="w-4 h-4 text-gray-400" />
              <select 
                value={activeFilter}
                onChange={(e) => setActiveFilter(e.target.value)}
                className="border border-gray-300 rounded-lg px-3 py-2 text-sm"
              >
                <option value="all">All Roles</option>
                <option value="administrator">Administrators</option>
                <option value="team_manager">Team Managers</option>
                <option value="senior_agent">Senior Agents</option>
                <option value="junior_agent">Junior Agents</option>
              </select>
            </div>
          </div>

          {selectedUsers.length > 0 && (
            <div className="flex items-center space-x-3">
              <span className="text-sm text-gray-600">{selectedUsers.length} selected</span>
              <div className="relative">
                <button
                  onClick={() => setShowBulkActions(!showBulkActions)}
                  className="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 text-sm"
                >
                  Bulk Actions
                </button>
                {showBulkActions && (
                  <div className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-10">
                    <button
                      onClick={() => handleBulkAction('deactivate')}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Deactivate Users
                    </button>
                    <button
                      onClick={() => handleBulkAction('change_role')}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Change Role
                    </button>
                    <button
                      onClick={() => handleBulkAction('reset_password')}
                      className="w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      Reset Passwords
                    </button>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Users Table */}
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <input
                    type="checkbox"
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedUsers(filteredUsers.map(u => u.id));
                      } else {
                        setSelectedUsers([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  User
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Role & Department
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status & Activity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Performance
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Subscription
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {filteredUsers.map(user => (
                <tr key={user.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <input
                      type="checkbox"
                      checked={selectedUsers.includes(user.id)}
                      onChange={(e) => {
                        if (e.target.checked) {
                          setSelectedUsers([...selectedUsers, user.id]);
                        } else {
                          setSelectedUsers(selectedUsers.filter(id => id !== user.id));
                        }
                      }}
                      className="rounded border-gray-300"
                    />
                  </td>
                  
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-3">
                      <div className="relative">
                        {user.profile.avatar ? (
                          <img 
                            src={user.profile.avatar} 
                            alt={user.profile.firstName}
                            className="w-10 h-10 rounded-full"
                          />
                        ) : (
                          <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                            <span className="text-sm font-medium text-gray-700">
                              {user.profile.firstName[0]}{user.profile.lastName[0]}
                            </span>
                          </div>
                        )}
                        <div 
                          className={`absolute -bottom-1 -right-1 w-3 h-3 ${getStatusColor(user)} rounded-full border-2 border-white`}
                        ></div>
                      </div>
                      <div>
                        <p className="text-sm font-medium text-gray-900">
                          {user.profile.firstName} {user.profile.lastName}
                        </p>
                        <p className="text-sm text-gray-500">{user.email}</p>
                      </div>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getRoleColor(user.permissions.role)}`}>
                        {user.permissions.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <p className="text-sm text-gray-500">{user.profile.department}</p>
                      <p className="text-xs text-gray-400">{user.profile.jobTitle}</p>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <div className={`w-2 h-2 ${getStatusColor(user)} rounded-full`}></div>
                        <span className="text-sm text-gray-900">
                          {formatLastLogin(new Date(user.activity.lastLogin))}
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        {user.activity.loginCount} logins
                      </p>
                      <p className="text-xs text-gray-500">
                        {user.activity.sessionDuration}h avg session
                      </p>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <div className="flex items-center space-x-2">
                        <Star className="w-4 h-4 text-yellow-500" />
                        <span className="text-sm font-medium text-gray-900">
                          {user.performance.overallRating}/5.0
                        </span>
                      </div>
                      <p className="text-xs text-gray-500">
                        Productivity: {user.activity.performanceMetrics.productivityScore}%
                      </p>
                      <p className="text-xs text-gray-500">
                        Engagement: {user.activity.performanceMetrics.engagementLevel}
                      </p>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="space-y-1">
                      <p className="text-sm font-medium text-gray-900">
                        {user.subscription.plan.charAt(0).toUpperCase() + user.subscription.plan.slice(1)}
                      </p>
                      <p className="text-xs text-gray-500">
                        {user.subscription.usage.searchCreditsUsed}/{user.subscription.limits.searchCredits} searches
                      </p>
                      <p className="text-xs text-gray-500">
                        {user.subscription.usage.storageUsedGB}GB/{user.subscription.limits.storageGB}GB storage
                      </p>
                    </div>
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={() => {
                          setEditingUser(user);
                          setShowUserModal(true);
                        }}
                        className="text-blue-600 hover:text-blue-700 p-1 rounded"
                      >
                        <Edit className="w-4 h-4" />
                      </button>
                      <button className="text-green-600 hover:text-green-700 p-1 rounded">
                        <Eye className="w-4 h-4" />
                      </button>
                      <button className="text-gray-600 hover:text-gray-700 p-1 rounded">
                        <Shield className="w-4 h-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-700 p-1 rounded">
                        <Trash2 className="w-4 h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* User Modal */}
      {showUserModal && (
        <UserModal
          user={editingUser}
          onClose={() => {
            setShowUserModal(false);
            setEditingUser(null);
          }}
          onSave={(userData) => {
            // Handle user creation/update
            setShowUserModal(false);
            setEditingUser(null);
          }}
        />
      )}
    </div>
  );
};

// User Modal Component
const UserModal: React.FC<{
  user?: UserAccount | null;
  onClose: () => void;
  onSave: (userData: any) => void;
}> = ({ user, onClose, onSave }) => {
  const [activeTab, setActiveTab] = useState('profile');
  const [formData, setFormData] = useState({
    firstName: user?.profile.firstName || '',
    lastName: user?.profile.lastName || '',
    email: user?.email || '',
    phone: user?.profile.phone || '',
    department: user?.profile.department || '',
    jobTitle: user?.profile.jobTitle || '',
    role: user?.permissions.role || 'junior_agent',
    manager: user?.profile.manager || '',
    location: user?.profile.location || ''
  });

  const handleSave = () => {
    onSave(formData);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold text-gray-900">
              {user ? 'Edit User' : 'Create New User'}
            </h3>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 p-2 rounded-lg hover:bg-gray-50"
            >
              <X className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8 px-6">
            {[
              { id: 'profile', label: 'Profile', icon: Users },
              { id: 'permissions', label: 'Permissions', icon: Shield },
              { id: 'subscription', label: 'Subscription', icon: Star },
              { id: 'preferences', label: 'Preferences', icon: Settings }
            ].map(tab => {
              const Icon = tab.icon;
              const isActive = activeTab === tab.id;
              
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-2 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    isActive
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.label}</span>
                </button>
              );
            })}
          </nav>
        </div>

        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {activeTab === 'profile' && (
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    First Name
                  </label>
                  <input
                    type="text"
                    value={formData.firstName}
                    onChange={(e) => setFormData({...formData, firstName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Last Name
                  </label>
                  <input
                    type="text"
                    value={formData.lastName}
                    onChange={(e) => setFormData({...formData, lastName: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email
                  </label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({...formData, email: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Phone
                  </label>
                  <input
                    type="tel"
                    value={formData.phone}
                    onChange={(e) => setFormData({...formData, phone: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Department
                  </label>
                  <select
                    value={formData.department}
                    onChange={(e) => setFormData({...formData, department: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  >
                    <option value="">Select Department</option>
                    <option value="Operations">Operations</option>
                    <option value="Sales">Sales</option>
                    <option value="Legal">Legal</option>
                    <option value="Technology">Technology</option>
                    <option value="Administration">Administration</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Job Title
                  </label>
                  <input
                    type="text"
                    value={formData.jobTitle}
                    onChange={(e) => setFormData({...formData, jobTitle: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Role
                  </label>
                  <select
                    value={formData.role}
                    onChange={(e) => setFormData({...formData, role: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  >
                    <option value="junior_agent">Junior Agent</option>
                    <option value="senior_agent">Senior Agent</option>
                    <option value="team_manager">Team Manager</option>
                    <option value="administrator">Administrator</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location
                  </label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData({...formData, location: e.target.value})}
                    className="w-full border border-gray-300 rounded-lg px-3 py-2"
                  />
                </div>
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="space-y-6">
              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <p className="text-sm text-yellow-800">
                  Permission configuration will be available in the full implementation.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'subscription' && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <p className="text-sm text-blue-800">
                  Subscription management will be available in the full implementation.
                </p>
              </div>
            </div>
          )}

          {activeTab === 'preferences' && (
            <div className="space-y-6">
              <div className="bg-green-50 border border-green-200 rounded-lg p-4">
                <p className="text-sm text-green-800">
                  User preferences configuration will be available in the full implementation.
                </p>
              </div>
            </div>
          )}
        </div>

        <div className="p-6 border-t border-gray-200 bg-gray-50">
          <div className="flex items-center justify-end space-x-3">
            <button
              onClick={onClose}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50"
            >
              Cancel
            </button>
            <button
              onClick={handleSave}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              {user ? 'Update User' : 'Create User'}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};