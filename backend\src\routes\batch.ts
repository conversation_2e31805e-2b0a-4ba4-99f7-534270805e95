import { FastifyInstance } from 'fastify'
import { createReadStream, createWriteStream, promises as fs } from 'fs'
import { pipeline } from 'stream/promises'
import { Transform } from 'stream'
import path from 'path'
import csv from 'csv-parser'
import { v4 as uuidv4 } from 'uuid'
import multipart from '@fastify/multipart'

// Types
interface BatchJob {
  id: string
  filename: string
  originalName: string
  size: number
  status: 'uploading' | 'uploaded' | 'processing' | 'validating' | 'importing' | 'completed' | 'error'
  progress: number
  recordCount: number
  validRecords: number
  errorRecords: number
  errors: ValidationError[]
  createdAt: string
  updatedAt: string
  userId: string
  state?: string
  agency?: string
}

interface ValidationError {
  row: number
  field: string
  value: string
  error: string
}

interface FieldMapping {
  csvField: string
  systemField: string
  dataType: 'string' | 'number' | 'date' | 'email' | 'phone'
}

// Constants
const MAX_FILE_SIZE = 400 * 1024 * 1024 // 400MB
const MAX_RECORDS = 2000000 // 2 million records
const CHUNK_SIZE = 10000 // Process 10k records at a time
const UPLOAD_DIR = path.join(process.cwd(), 'uploads')
const TEMP_DIR = path.join(process.cwd(), 'temp')

// In-memory storage for demo (use database in production)
const batchJobs = new Map<string, BatchJob>()

// Ensure upload directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(UPLOAD_DIR, { recursive: true })
    await fs.mkdir(TEMP_DIR, { recursive: true })
  } catch (error) {
    console.error('Failed to create directories:', error)
  }
}

// Validation functions
function validateEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

function validatePhone(phone: string): boolean {
  const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
  return phoneRegex.test(phone)
}

function validateDate(date: string): boolean {
  const parsedDate = new Date(date)
  return !isNaN(parsedDate.getTime())
}

function validateNumber(value: string): boolean {
  return !isNaN(parseFloat(value)) && isFinite(parseFloat(value))
}

// CSV processing with streaming
async function processCSVFile(
  filePath: string, 
  batchJob: BatchJob, 
  fieldMappings: FieldMapping[]
): Promise<void> {
  return new Promise((resolve, reject) => {
    const errors: ValidationError[] = []
    let recordCount = 0
    let validRecords = 0
    let errorRecords = 0
    let processedChunks = 0

    const validationTransform = new Transform({
      objectMode: true,
      transform(chunk: any, encoding, callback) {
        recordCount++
        
        // Update progress every CHUNK_SIZE records
        if (recordCount % CHUNK_SIZE === 0) {
          processedChunks++
          const progress = Math.min(Math.round((recordCount / MAX_RECORDS) * 100), 100)
          
          batchJob.progress = progress
          batchJob.recordCount = recordCount
          batchJob.updatedAt = new Date().toISOString()
          
          // Emit progress update (in real app, use WebSocket or SSE)
          console.log(`Processing progress: ${progress}% (${recordCount} records)`)
        }

        // Validate record
        const recordErrors = validateRecord(chunk, fieldMappings, recordCount)
        
        if (recordErrors.length > 0) {
          errorRecords++
          errors.push(...recordErrors)
        } else {
          validRecords++
        }

        // Transform data according to field mappings
        const transformedRecord = transformRecord(chunk, fieldMappings)
        
        callback(null, transformedRecord)
      }
    })

    const readStream = createReadStream(filePath)
    
    pipeline(
      readStream,
      csv(),
      validationTransform
    )
    .then(() => {
      batchJob.status = 'completed'
      batchJob.progress = 100
      batchJob.recordCount = recordCount
      batchJob.validRecords = validRecords
      batchJob.errorRecords = errorRecords
      batchJob.errors = errors.slice(0, 1000) // Limit errors to first 1000
      batchJob.updatedAt = new Date().toISOString()
      
      resolve()
    })
    .catch((error) => {
      batchJob.status = 'error'
      batchJob.updatedAt = new Date().toISOString()
      reject(error)
    })
  })
}

function validateRecord(record: any, fieldMappings: FieldMapping[], rowNumber: number): ValidationError[] {
  const errors: ValidationError[] = []
  
  for (const mapping of fieldMappings) {
    if (!mapping.systemField) continue // Skip unmapped fields
    
    const value = record[mapping.csvField]
    
    // Check required fields
    if (!value || value.trim() === '') {
      // Check if this is a required system field
      const requiredFields = ['claimNumber', 'claimantFirstName', 'claimantLastName', 'state', 'amountReported']
      if (requiredFields.includes(mapping.systemField)) {
        errors.push({
          row: rowNumber,
          field: mapping.csvField,
          value: value || '',
          error: `Required field is empty`
        })
      }
      continue
    }
    
    // Validate data types
    switch (mapping.dataType) {
      case 'email':
        if (!validateEmail(value)) {
          errors.push({
            row: rowNumber,
            field: mapping.csvField,
            value,
            error: 'Invalid email format'
          })
        }
        break
        
      case 'phone':
        if (!validatePhone(value)) {
          errors.push({
            row: rowNumber,
            field: mapping.csvField,
            value,
            error: 'Invalid phone format'
          })
        }
        break
        
      case 'date':
        if (!validateDate(value)) {
          errors.push({
            row: rowNumber,
            field: mapping.csvField,
            value,
            error: 'Invalid date format'
          })
        }
        break
        
      case 'number':
        if (!validateNumber(value)) {
          errors.push({
            row: rowNumber,
            field: mapping.csvField,
            value,
            error: 'Invalid number format'
          })
        }
        break
    }
  }
  
  return errors
}

function transformRecord(record: any, fieldMappings: FieldMapping[]): any {
  const transformed: any = {}
  
  for (const mapping of fieldMappings) {
    if (!mapping.systemField) continue
    
    let value = record[mapping.csvField]
    
    // Transform based on data type
    switch (mapping.dataType) {
      case 'number':
        value = parseFloat(value) || 0
        break
      case 'date':
        value = new Date(value).toISOString()
        break
      default:
        value = String(value || '').trim()
    }
    
    transformed[mapping.systemField] = value
  }
  
  return transformed
}

export default async function batchRoutes(fastify: FastifyInstance) {
  // Register multipart plugin for file uploads
  await fastify.register(multipart, {
    limits: {
      fileSize: MAX_FILE_SIZE,
      files: 10
    }
  })
  
  // Ensure directories exist
  await ensureDirectories()

  // Upload CSV file
  fastify.post('/upload', async (request, reply) => {
    try {
      const data = await request.file()
      
      if (!data) {
        return reply.code(400).send({ error: 'No file uploaded' })
      }

      // Validate file type
      if (!data.filename.toLowerCase().endsWith('.csv')) {
        return reply.code(400).send({ error: 'Only CSV files are allowed' })
      }

      // Generate unique filename
      const jobId = uuidv4()
      const filename = `${jobId}_${data.filename}`
      const filePath = path.join(UPLOAD_DIR, filename)

      // Create batch job
      const batchJob: BatchJob = {
        id: jobId,
        filename,
        originalName: data.filename,
        size: 0,
        status: 'uploading',
        progress: 0,
        recordCount: 0,
        validRecords: 0,
        errorRecords: 0,
        errors: [],
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: 'demo-user' // In real app, get from JWT token
      }

      batchJobs.set(jobId, batchJob)

      // Stream file to disk with progress tracking
      const writeStream = createWriteStream(filePath)
      let uploadedBytes = 0

      data.file.on('data', (chunk) => {
        uploadedBytes += chunk.length
        const progress = Math.round((uploadedBytes / MAX_FILE_SIZE) * 100)
        
        batchJob.size = uploadedBytes
        batchJob.progress = Math.min(progress, 100)
        batchJob.updatedAt = new Date().toISOString()
      })

      await pipeline(data.file, writeStream)

      // Update job status
      batchJob.status = 'uploaded'
      batchJob.progress = 100
      batchJob.size = uploadedBytes
      batchJob.updatedAt = new Date().toISOString()

      // Quick validation of file size and record count
      const stats = await fs.stat(filePath)
      if (stats.size > MAX_FILE_SIZE) {
        batchJob.status = 'error'
        await fs.unlink(filePath) // Clean up
        return reply.code(400).send({ error: 'File size exceeds 400MB limit' })
      }

      // Count records (quick estimate)
      const fileContent = await fs.readFile(filePath, 'utf8')
      const lineCount = fileContent.split('\n').filter(line => line.trim()).length - 1 // -1 for header
      
      if (lineCount > MAX_RECORDS) {
        batchJob.status = 'error'
        await fs.unlink(filePath) // Clean up
        return reply.code(400).send({ 
          error: `File contains ${lineCount.toLocaleString()} records. Maximum allowed is ${MAX_RECORDS.toLocaleString()}.` 
        })
      }

      batchJob.recordCount = lineCount

      reply.send({
        jobId,
        filename: data.filename,
        size: stats.size,
        recordCount: lineCount,
        status: batchJob.status
      })

    } catch (error) {
      console.error('Upload error:', error)
      reply.code(500).send({ error: 'Upload failed' })
    }
  })

  // Get batch job status
  fastify.get('/jobs/:jobId', async (request, reply) => {
    const { jobId } = request.params as { jobId: string }
    const job = batchJobs.get(jobId)
    
    if (!job) {
      return reply.code(404).send({ error: 'Job not found' })
    }
    
    reply.send(job)
  })

  // List all batch jobs
  fastify.get('/jobs', async (request, reply) => {
    const jobs = Array.from(batchJobs.values())
      .sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())
    
    reply.send(jobs)
  })

  // Start processing with field mappings
  fastify.post('/jobs/:jobId/process', async (request, reply) => {
    const { jobId } = request.params as { jobId: string }
    const { fieldMappings } = request.body as { fieldMappings: FieldMapping[] }
    
    const job = batchJobs.get(jobId)
    if (!job) {
      return reply.code(404).send({ error: 'Job not found' })
    }

    if (job.status !== 'uploaded') {
      return reply.code(400).send({ error: 'Job is not ready for processing' })
    }

    // Validate field mappings
    const requiredFields = ['claimNumber', 'claimantFirstName', 'claimantLastName', 'state', 'amountReported']
    const mappedSystemFields = fieldMappings
      .filter(m => m.systemField)
      .map(m => m.systemField)

    const missingRequired = requiredFields.filter(f => !mappedSystemFields.includes(f))
    if (missingRequired.length > 0) {
      return reply.code(400).send({ 
        error: `Missing required field mappings: ${missingRequired.join(', ')}` 
      })
    }

    // Start processing asynchronously
    job.status = 'processing'
    job.progress = 0
    job.updatedAt = new Date().toISOString()

    const filePath = path.join(UPLOAD_DIR, job.filename)
    
    // Process in background
    processCSVFile(filePath, job, fieldMappings)
      .catch((error) => {
        console.error('Processing error:', error)
        job.status = 'error'
        job.updatedAt = new Date().toISOString()
      })

    reply.send({ message: 'Processing started', jobId })
  })

  // Download processed data
  fastify.get('/jobs/:jobId/download', async (request, reply) => {
    const { jobId } = request.params as { jobId: string }
    const job = batchJobs.get(jobId)
    
    if (!job) {
      return reply.code(404).send({ error: 'Job not found' })
    }

    if (job.status !== 'completed') {
      return reply.code(400).send({ error: 'Job is not completed' })
    }

    // In a real implementation, you would generate and return the processed file
    reply.send({
      message: 'Download would start here',
      jobId,
      recordCount: job.recordCount,
      validRecords: job.validRecords,
      errorRecords: job.errorRecords
    })
  })

  // Get validation errors
  fastify.get('/jobs/:jobId/errors', async (request, reply) => {
    const { jobId } = request.params as { jobId: string }
    const job = batchJobs.get(jobId)
    
    if (!job) {
      return reply.code(404).send({ error: 'Job not found' })
    }

    reply.send({
      jobId,
      errorCount: job.errorRecords,
      errors: job.errors
    })
  })

  // Delete batch job
  fastify.delete('/jobs/:jobId', async (request, reply) => {
    const { jobId } = request.params as { jobId: string }
    const job = batchJobs.get(jobId)
    
    if (!job) {
      return reply.code(404).send({ error: 'Job not found' })
    }

    // Clean up files
    try {
      const filePath = path.join(UPLOAD_DIR, job.filename)
      await fs.unlink(filePath)
    } catch (error) {
      console.error('Failed to delete file:', error)
    }

    batchJobs.delete(jobId)
    reply.send({ message: 'Job deleted successfully' })
  })

  // Get CSV template
  fastify.get('/template', async (request, reply) => {
    const template = [
      'claim_number,first_name,last_name,email,phone,address1,city,state,zip_code,amount_reported,date_reported,agency,description',
      'CLM-001,John,Doe,<EMAIL>,555-0123,123 Main St,Anytown,CA,12345,15000.00,2024-01-15,State Controller,Unclaimed wages',
      'CLM-002,Jane,Smith,<EMAIL>,555-0124,456 Oak Ave,Somewhere,TX,67890,25000.00,2024-01-10,Treasury Department,Unclaimed property'
    ].join('\n')

    reply
      .header('Content-Type', 'text/csv')
      .header('Content-Disposition', 'attachment; filename="batch_import_template.csv"')
      .send(template)
  })
} 