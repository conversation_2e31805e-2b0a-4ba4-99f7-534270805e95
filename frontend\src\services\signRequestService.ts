// SignRequest Integration Service for AssetHunterPro
// Handles electronic signature workflows and document management
// SignRequest offers 10 free documents per month - perfect for getting started!

export interface SignRequestConfig {
  apiToken: string
  baseUrl: string
  webhookUrl?: string
}

export interface SignRequestDocument {
  name: string
  file?: string // Base64 encoded file content
  file_from_url?: string // URL to file
  external_id?: string
}

export interface SignRequestSigner {
  email: string
  first_name: string
  last_name: string
  order?: number
  needs_to_sign?: boolean
  verify_phone_number?: string
  verify_bank_account?: string
  language?: string
  force_language?: boolean
  emailed?: boolean
  verify_email?: boolean
  declined?: boolean
  signed?: boolean
  downloaded?: boolean
  signed_on?: string
  needs_to_sign_on?: string
  approve_only?: boolean
  notify_only?: boolean
  in_person?: boolean
  order_index?: number
  language_code?: string
  redirect_url?: string
  redirect_url_declined?: string
  after_document?: string
  integrations?: any[]
  password?: string
}

export interface SignRequestCreate {
  from_email?: string
  message?: string
  subject?: string
  who?: 'o' | 'm' // 'o' = only me, 'm' = me and others
  send_immediately?: boolean
  template?: string
  prefill_tags?: any[]
  integrations?: any[]
  file_from_url?: string
  events_callback_url?: string
  required_attachments?: any[]
  disable_attachments?: boolean
  disable_text_signatures?: boolean
  disable_date?: boolean
  disable_emails?: boolean
  disable_upload_signatures?: boolean
  disable_blockchain_proof?: boolean
  text_message_verification_locked?: boolean
  subject_locked?: boolean
  message_locked?: boolean
  redirect_url?: string
  redirect_url_declined?: string
  required_attachments_message?: string
  disable_text?: boolean
  disable_date_signed?: boolean
  disable_attachments_signer?: boolean
  disable_reassign?: boolean
  signing_redirect_url?: string
  signing_redirect_url_declined?: string
  embedded_signing_enabled?: boolean
  auto_delete_days?: number
  auto_expire_days?: number
  password?: string
  signers: SignRequestSigner[]
  documents: SignRequestDocument[]
}

export interface SignRequestResponse {
  uuid: string
  url: string
  document: string
  status: 'co' | 'se' | 'vi' | 'si' | 'do' | 'ca' | 'de' | 'ec' | 'es' | 'xp' // completed, sent, viewed, signed, downloaded, cancelled, declined, error_converting, error_sending, expired
  created: string
  modified: string
  subject: string
  message: string
  from_email: string
  from_email_name: string
  is_being_prepared: boolean
  prepare_url?: string
  signing_log: string
  security_hash: string
  attachments: any[]
  auto_delete_days?: number
  auto_expire_days?: number
  required_attachments: any[]
  disable_attachments: boolean
  disable_text_signatures: boolean
  disable_date: boolean
  disable_emails: boolean
  disable_upload_signatures: boolean
  disable_blockchain_proof: boolean
  text_message_verification_locked: boolean
  subject_locked: boolean
  message_locked: boolean
  redirect_url?: string
  redirect_url_declined?: string
  required_attachments_message: string
  disable_text: boolean
  disable_date_signed: boolean
  disable_attachments_signer: boolean
  disable_reassign: boolean
  signing_redirect_url?: string
  signing_redirect_url_declined?: string
  embedded_signing_enabled: boolean
  in_person: boolean
  signers: SignRequestSignerResponse[]
  events: any[]
}

export interface SignRequestSignerResponse {
  uuid: string
  email: string
  first_name: string
  last_name: string
  needs_to_sign: boolean
  approve_only: boolean
  notify_only: boolean
  in_person: boolean
  order: number
  language: string
  force_language: boolean
  emailed: boolean
  verify_phone_number?: string
  verify_bank_account?: string
  declined: boolean
  signed: boolean
  downloaded: boolean
  signed_on?: string
  needs_to_sign_on?: string
  approve_only_on?: string
  notify_only_on?: string
  viewed_on?: string
  emailed_on?: string
  reminded_on?: string
  declined_on?: string
  cancelled_on?: string
  order_index: number
  language_code: string
  redirect_url?: string
  redirect_url_declined?: string
  after_document: string
  integrations: any[]
  password?: string
  embed_url_user_id?: string
  inputs: any[]
  use_stamp_for_approve_only: boolean
  embed_url?: string
  attachments: any[]
  redirect_url_error?: string
  signing_url?: string
  error?: string
  security_hash: string
}

export class SignRequestService {
  private static instance: SignRequestService
  private config: SignRequestConfig | null = null

  static getInstance(): SignRequestService {
    if (!SignRequestService.instance) {
      SignRequestService.instance = new SignRequestService()
    }
    return SignRequestService.instance
  }

  /**
   * Initialize SignRequest service with configuration
   */
  initialize(config: SignRequestConfig) {
    this.config = {
      ...config,
      baseUrl: config.baseUrl || 'https://signrequest.com/api/v1'
    }
    console.log('📝 SignRequest service initialized')
  }

  /**
   * Create and send document for signature
   */
  async createSignRequest(request: SignRequestCreate): Promise<{ data: SignRequestResponse | null; error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      const response = await fetch(`${this.config.baseUrl}/signrequests/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.config.apiToken}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(request)
      })

      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(`SignRequest API error: ${JSON.stringify(errorData)}`)
      }

      const data = await response.json()
      
      console.log('✅ SignRequest created:', data.uuid)
      return { data, error: null }
    } catch (error) {
      console.error('❌ SignRequest creation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get sign request status and details
   */
  async getSignRequestStatus(uuid: string): Promise<{ data: SignRequestResponse | null; error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      const response = await fetch(`${this.config.baseUrl}/signrequests/${uuid}/`, {
        headers: {
          'Authorization': `Token ${this.config.apiToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`SignRequest API error: ${response.statusText}`)
      }

      const data = await response.json()
      return { data, error: null }
    } catch (error) {
      console.error('❌ SignRequest status check failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Cancel a sign request
   */
  async cancelSignRequest(uuid: string): Promise<{ error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      const response = await fetch(`${this.config.baseUrl}/signrequests/${uuid}/cancel_signrequest/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.config.apiToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`SignRequest API error: ${response.statusText}`)
      }

      console.log('✅ SignRequest cancelled:', uuid)
      return { error: null }
    } catch (error) {
      console.error('❌ SignRequest cancellation failed:', error)
      return { error }
    }
  }

  /**
   * Resend sign request to signers
   */
  async resendSignRequest(uuid: string): Promise<{ error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      const response = await fetch(`${this.config.baseUrl}/signrequests/${uuid}/resend_signrequest_email/`, {
        method: 'POST',
        headers: {
          'Authorization': `Token ${this.config.apiToken}`,
          'Content-Type': 'application/json'
        }
      })

      if (!response.ok) {
        throw new Error(`SignRequest API error: ${response.statusText}`)
      }

      console.log('✅ SignRequest resent:', uuid)
      return { error: null }
    } catch (error) {
      console.error('❌ SignRequest resend failed:', error)
      return { error }
    }
  }

  /**
   * Download signed document
   */
  async downloadSignedDocument(uuid: string): Promise<{ data: Uint8Array | null; error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      // First get the sign request to find the document URL
      const { data: signRequest, error: statusError } = await this.getSignRequestStatus(uuid)
      if (statusError || !signRequest) {
        throw new Error('Could not get sign request status')
      }

      if (signRequest.status !== 'si' && signRequest.status !== 'co') {
        throw new Error('Document is not yet signed')
      }

      // Download the signed document
      const response = await fetch(signRequest.document, {
        headers: {
          'Authorization': `Token ${this.config.apiToken}`
        }
      })

      if (!response.ok) {
        throw new Error(`SignRequest download error: ${response.statusText}`)
      }

      const data = await response.arrayBuffer()
      return { data: new Uint8Array(data), error: null }
    } catch (error) {
      console.error('❌ SignRequest document download failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Get signing URL for embedded signing
   */
  async getEmbeddedSigningUrl(uuid: string, signerEmail: string): Promise<{ data: { url: string } | null; error: any }> {
    try {
      if (!this.config) {
        throw new Error('SignRequest service not configured')
      }

      const { data: signRequest, error: statusError } = await this.getSignRequestStatus(uuid)
      if (statusError || !signRequest) {
        throw new Error('Could not get sign request status')
      }

      const signer = signRequest.signers.find(s => s.email === signerEmail)
      if (!signer) {
        throw new Error('Signer not found')
      }

      if (!signer.embed_url) {
        throw new Error('Embedded signing not enabled for this signer')
      }

      return { 
        data: { url: signer.embed_url }, 
        error: null 
      }
    } catch (error) {
      console.error('❌ SignRequest embedded URL generation failed:', error)
      return { data: null, error }
    }
  }

  /**
   * Helper method to convert file to base64
   */
  fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = () => {
        const result = reader.result as string
        // Remove data:mime/type;base64, prefix
        const base64 = result.split(',')[1]
        resolve(base64)
      }
      reader.onerror = error => reject(error)
    })
  }

  /**
   * Helper method to get status display name
   */
  getStatusDisplayName(status: string): string {
    const statusMap: Record<string, string> = {
      'co': 'Completed',
      'se': 'Sent',
      'vi': 'Viewed',
      'si': 'Signed',
      'do': 'Downloaded',
      'ca': 'Cancelled',
      'de': 'Declined',
      'ec': 'Error Converting',
      'es': 'Error Sending',
      'xp': 'Expired'
    }
    return statusMap[status] || status
  }

  /**
   * Helper method to check if document is completed
   */
  isCompleted(status: string): boolean {
    return ['co', 'si', 'do'].includes(status)
  }

  /**
   * Helper method to check if document needs action
   */
  needsAction(status: string): boolean {
    return ['se', 'vi'].includes(status)
  }
}

export const signRequestService = SignRequestService.getInstance()
