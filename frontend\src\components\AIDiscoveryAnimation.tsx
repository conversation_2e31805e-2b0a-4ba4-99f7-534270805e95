import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Search, CheckCircle, Clock, Database, Globe, 
  Users, Building, FileText, Phone, Mail, MapPin,
  Brain, Zap, Target, TrendingUp, Loader2, Scale
} from 'lucide-react';

interface SearchStep {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  duration: number; // milliseconds
  status: 'pending' | 'searching' | 'completed' | 'found' | 'no_results';
  results?: SearchResult[];
}

interface SearchResult {
  type: 'phone' | 'email' | 'address' | 'social' | 'business' | 'family';
  value: string;
  confidence: number;
  source: string;
}

interface DiscoveryAnimationProps {
  claimantName: string;
  onComplete: (results: any) => void;
  isActive: boolean;
}

export const AIDiscoveryAnimation: React.FC<DiscoveryAnimationProps> = ({
  claimantName,
  onComplete,
  isActive
}) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [progress, setProgress] = useState(0);
  const [searchSteps, setSearchSteps] = useState<SearchStep[]>([]);
  const [overallResults, setOverallResults] = useState<SearchResult[]>([]);
  const [isComplete, setIsComplete] = useState(false);

  // Define search steps with realistic timing
  const initializeSearchSteps = (): SearchStep[] => [
    {
      id: 'preprocessing',
      name: 'AI Analysis',
      description: 'Analyzing name patterns and building search strategy',
      icon: <Brain className="w-5 h-5" />,
      duration: 1500,
      status: 'pending'
    },
    {
      id: 'property_records',
      name: 'Property Records',
      description: 'Searching county assessor and property ownership databases',
      icon: <Building className="w-5 h-5" />,
      duration: 2000,
      status: 'pending'
    },
    {
      id: 'voter_registration',
      name: 'Voter Registration',
      description: 'Checking voter databases for address updates',
      icon: <FileText className="w-5 h-5" />,
      duration: 1800,
      status: 'pending'
    },
    {
      id: 'professional_licenses',
      name: 'Professional Licenses',
      description: 'Scanning state licensing boards and certifications',
      icon: <Badge className="w-5 h-5" />,
      duration: 2200,
      status: 'pending'
    },
    {
      id: 'court_records',
      name: 'Court Records',
      description: 'Analyzing marriage, divorce, and civil court filings',
      icon: <Scale className="w-5 h-5" />,
      duration: 2500,
      status: 'pending'
    },
    {
      id: 'social_media',
      name: 'Social Networks',
      description: 'Searching LinkedIn, Facebook, and professional networks',
      icon: <Users className="w-5 h-5" />,
      duration: 3000,
      status: 'pending'
    },
    {
      id: 'business_records',
      name: 'Business Registrations',
      description: 'Checking Secretary of State and business databases',
      icon: <Database className="w-5 h-5" />,
      duration: 1700,
      status: 'pending'
    },
    {
      id: 'people_search',
      name: 'People Search Engines',
      description: 'Scanning TruePeopleSearch, WhitePages, and directory sites',
      icon: <Globe className="w-5 h-5" />,
      duration: 2800,
      status: 'pending'
    },
    {
      id: 'family_tree',
      name: 'Family Connections',
      description: 'Mapping family relationships and genealogy records',
      icon: <Users className="w-5 h-5" />,
      duration: 2300,
      status: 'pending'
    },
    {
      id: 'ai_analysis',
      name: 'AI Consolidation',
      description: 'Analyzing results and calculating confidence scores',
      icon: <Target className="w-5 h-5" />,
      duration: 2000,
      status: 'pending'
    }
  ];

  // Initialize when animation starts
  useEffect(() => {
    if (isActive && searchSteps.length === 0) {
      setSearchSteps(initializeSearchSteps());
      setCurrentStep(0);
      setProgress(0);
      setOverallResults([]);
      setIsComplete(false);
    }
  }, [isActive]);

  // Run search animation
  useEffect(() => {
    if (!isActive || searchSteps.length === 0) return;

    const runSearchSequence = async () => {
      for (let i = 0; i < searchSteps.length; i++) {
        // Update current step to searching
        setCurrentStep(i);
        setSearchSteps(prev => 
          prev.map((step, idx) => 
            idx === i ? { ...step, status: 'searching' } : step
          )
        );

        // Simulate search duration
        await new Promise(resolve => setTimeout(resolve, searchSteps[i].duration));

        // Generate mock results for this step
        const stepResults = generateMockResults(searchSteps[i], claimantName);
        
        // Update step status and add results
        setSearchSteps(prev => 
          prev.map((step, idx) => 
            idx === i ? { 
              ...step, 
              status: stepResults.length > 0 ? 'found' : 'completed',
              results: stepResults 
            } : step
          )
        );

        // Add to overall results
        if (stepResults.length > 0) {
          setOverallResults(prev => [...prev, ...stepResults]);
        }

        // Update progress
        setProgress(((i + 1) / searchSteps.length) * 100);

        // Small delay between steps
        await new Promise(resolve => setTimeout(resolve, 300));
      }

      // Mark as complete
      setIsComplete(true);
      
      // Call completion callback with results
      setTimeout(() => {
        onComplete({
          totalResults: overallResults,
          searchSteps: searchSteps,
          confidence: calculateOverallConfidence(overallResults)
        });
      }, 1000);
    };

    runSearchSequence();
  }, [isActive, searchSteps.length]);

  // Generate realistic mock results
  const generateMockResults = (step: SearchStep, name: string): SearchResult[] => {
    const results: SearchResult[] = [];
    
    switch (step.id) {
      case 'property_records':
        if (Math.random() > 0.3) {
          results.push({
            type: 'address',
            value: generateRealisticAddress(),
            confidence: 0.8 + Math.random() * 0.15,
            source: 'County Property Records'
          });
        }
        break;
        
      case 'voter_registration':
        if (Math.random() > 0.4) {
          results.push({
            type: 'address',
            value: generateRealisticAddress(),
            confidence: 0.7 + Math.random() * 0.2,
            source: 'Voter Registration Database'
          });
        }
        break;
        
      case 'professional_licenses':
        if (Math.random() > 0.6) {
          results.push({
            type: 'business',
            value: `${getProfession()} License - Active`,
            confidence: 0.85 + Math.random() * 0.1,
            source: 'State Licensing Board'
          });
        }
        break;
        
      case 'social_media':
        if (Math.random() > 0.4) {
          results.push({
            type: 'social',
            value: `LinkedIn: ${name} - ${getProfession()}`,
            confidence: 0.6 + Math.random() * 0.25,
            source: 'LinkedIn Public Profile'
          });
        }
        if (Math.random() > 0.6) {
          results.push({
            type: 'social',
            value: `Facebook: ${name.split(' ')[0]} ${name.split(' ').pop()}`,
            confidence: 0.5 + Math.random() * 0.3,
            source: 'Facebook Public Profile'
          });
        }
        break;
        
      case 'people_search':
        if (Math.random() > 0.3) {
          results.push({
            type: 'phone',
            value: generatePhoneNumber(),
            confidence: 0.6 + Math.random() * 0.25,
            source: 'TruePeopleSearch'
          });
        }
        if (Math.random() > 0.5) {
          results.push({
            type: 'email',
            value: generateEmail(name),
            confidence: 0.5 + Math.random() * 0.3,
            source: 'People Search Directory'
          });
        }
        break;
        
      case 'family_tree':
        if (Math.random() > 0.5) {
          results.push({
            type: 'family',
            value: `Relative: ${generateRelativeName(name)}`,
            confidence: 0.4 + Math.random() * 0.4,
            source: 'FamilyTreeNow'
          });
        }
        break;
    }
    
    return results;
  };

  // Helper functions for generating realistic data
  const generateRealisticAddress = (): string => {
    const streetNumbers = Math.floor(Math.random() * 9999) + 1;
    const streets = ['Oak St', 'Main Ave', 'First Blvd', 'Park Dr', 'Elm Way', 'Cedar Ln'];
    const cities = ['Bakersfield', 'Fresno', 'Sacramento', 'San Jose', 'Los Angeles'];
    const street = streets[Math.floor(Math.random() * streets.length)];
    const city = cities[Math.floor(Math.random() * cities.length)];
    return `${streetNumbers} ${street}, ${city} CA`;
  };

  const generatePhoneNumber = (): string => {
    const areaCodes = ['559', '661', '415', '510', '925'];
    const areaCode = areaCodes[Math.floor(Math.random() * areaCodes.length)];
    const number = Math.floor(Math.random() * 9000000) + 1000000;
    return `(${areaCode}) ${number.toString().slice(0, 3)}-${number.toString().slice(3)}`;
  };

  const generateEmail = (name: string): string => {
    const parts = name.toLowerCase().split(' ').filter(p => p.length > 0);
    const domains = ['gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com'];
    const domain = domains[Math.floor(Math.random() * domains.length)];
    
    if (parts.length >= 2) {
      const formats = [
        `${parts[0]}.${parts[parts.length - 1]}@${domain}`,
        `${parts[0]}${parts[parts.length - 1]}@${domain}`,
        `${parts[0].charAt(0)}${parts[parts.length - 1]}@${domain}`
      ];
      return formats[Math.floor(Math.random() * formats.length)];
    }
    
    return `${parts[0]}@${domain}`;
  };

  const getProfession = (): string => {
    const professions = ['Registered Nurse', 'Teacher', 'Engineer', 'Manager', 'Consultant', 'Analyst'];
    return professions[Math.floor(Math.random() * professions.length)];
  };

  const generateRelativeName = (name: string): string => {
    const lastName = name.split(' ').pop();
    const firstNames = ['Michael', 'Sarah', 'David', 'Linda', 'Robert', 'Maria', 'James', 'Lisa'];
    const firstName = firstNames[Math.floor(Math.random() * firstNames.length)];
    const relationships = ['Sister', 'Brother', 'Mother', 'Father', 'Daughter', 'Son'];
    const relationship = relationships[Math.floor(Math.random() * relationships.length)];
    return `${firstName} ${lastName} (${relationship})`;
  };

  const calculateOverallConfidence = (results: SearchResult[]): number => {
    if (results.length === 0) return 0;
    const avgConfidence = results.reduce((sum, r) => sum + r.confidence, 0) / results.length;
    const diversityBonus = Math.min(results.length * 0.05, 0.2);
    return Math.min(avgConfidence + diversityBonus, 0.95);
  };

  const getStepIcon = (step: SearchStep) => {
    switch (step.status) {
      case 'completed':
      case 'found':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'searching':
        return <Loader2 className="w-5 h-5 text-blue-500 animate-spin" />;
      default:
        return <Clock className="w-5 h-5 text-gray-400" />;
    }
  };

  const getStepStatusColor = (step: SearchStep) => {
    switch (step.status) {
      case 'found':
        return 'border-l-green-500 bg-green-50';
      case 'completed':
        return 'border-l-blue-500 bg-blue-50';
      case 'searching':
        return 'border-l-yellow-500 bg-yellow-50';
      default:
        return 'border-l-gray-300 bg-gray-50';
    }
  };

  if (!isActive) return null;

  return (
    <div className="space-y-4">
      {/* Header with Progress */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Zap className="w-5 h-5 text-blue-500" />
            <span>🤖 AI Discovery in Progress</span>
          </CardTitle>
          <CardDescription>
            Searching {searchSteps.length} intelligence sources for {claimantName}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Progress</span>
              <span>{Math.round(progress)}%</span>
            </div>
            <Progress value={progress} className="w-full" />
            <div className="flex justify-between text-xs text-muted-foreground">
              <span>Step {currentStep + 1} of {searchSteps.length}</span>
              <span>{overallResults.length} results found</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Live Search Steps */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">Live Search Feed</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3 max-h-96 overflow-y-auto">
            {searchSteps.map((step, index) => (
              <div
                key={step.id}
                className={`p-3 border-l-4 rounded-r transition-all duration-300 ${getStepStatusColor(step)}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    {getStepIcon(step)}
                    <div>
                      <div className="font-medium text-sm">{step.name}</div>
                      <div className="text-xs text-muted-foreground">{step.description}</div>
                    </div>
                  </div>
                  
                  {step.results && step.results.length > 0 && (
                    <Badge variant="default" className="bg-green-500">
                      {step.results.length} found
                    </Badge>
                  )}
                </div>
                
                {/* Show results as they come in */}
                {step.results && step.results.length > 0 && (
                  <div className="mt-2 space-y-1">
                    {step.results.map((result, idx) => (
                      <div key={idx} className="flex items-center justify-between text-xs bg-white p-2 rounded">
                        <div className="flex items-center space-x-2">
                          {result.type === 'phone' && <Phone className="w-3 h-3" />}
                          {result.type === 'email' && <Mail className="w-3 h-3" />}
                          {result.type === 'address' && <MapPin className="w-3 h-3" />}
                          {result.type === 'social' && <Users className="w-3 h-3" />}
                          {result.type === 'business' && <Building className="w-3 h-3" />}
                          {result.type === 'family' && <Users className="w-3 h-3" />}
                          <span className="font-mono">{result.value}</span>
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {Math.round(result.confidence * 100)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Real-time Results Counter */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <TrendingUp className="w-5 h-5 text-green-500" />
              <span className="font-medium">Results Found</span>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold">{overallResults.length}</div>
              <div className="text-xs text-muted-foreground">potential contacts</div>
            </div>
          </div>
          
          {isComplete && (
            <div className="mt-4 p-3 bg-green-50 border border-green-200 rounded">
              <div className="flex items-center space-x-2">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <span className="font-medium text-green-700">Discovery Complete!</span>
              </div>
              <p className="text-sm text-green-600 mt-1">
                Found {overallResults.length} potential contacts with {Math.round(calculateOverallConfidence(overallResults) * 100)}% overall confidence
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

// Main component that integrates the animation
export const AnimatedAIDiscovery: React.FC<{
  claimData: any;
  onDiscoveryComplete: (results: any) => void;
}> = ({ claimData, onDiscoveryComplete }) => {
  const [isDiscovering, setIsDiscovering] = useState(false);
  const [showResults, setShowResults] = useState(false);
  const [discoveryResults, setDiscoveryResults] = useState<{
    totalResults: SearchResult[];
    searchSteps: SearchStep[];
    confidence: number;
  } | null>(null);

  const handleStartDiscovery = () => {
    setIsDiscovering(true);
    setShowResults(false);
  };

  const handleDiscoveryComplete = (results: any) => {
    setDiscoveryResults(results);
    setIsDiscovering(false);
    setShowResults(true);
    onDiscoveryComplete(results);
  };

  if (showResults && discoveryResults) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <CheckCircle className="w-5 h-5 text-green-500" />
            <span>Discovery Results</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="text-center">
              <div className="text-3xl font-bold">{discoveryResults.totalResults.length}</div>
              <div className="text-sm text-muted-foreground">contacts discovered</div>
              <div className="text-lg font-semibold text-green-600">
                {Math.round(discoveryResults.confidence * 100)}% confidence
              </div>
            </div>
            
            <Button 
              onClick={() => setShowResults(false)} 
              variant="outline" 
              className="w-full"
            >
              View Detailed Results
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      {!isDiscovering && (
        <Button 
          onClick={handleStartDiscovery}
          className="w-full"
          size="lg"
        >
          <Search className="w-4 h-4 mr-2" />
          🤖 Start AI Discovery
        </Button>
      )}
      
      <AIDiscoveryAnimation
        claimantName={claimData.claimantName}
        onComplete={handleDiscoveryComplete}
        isActive={isDiscovering}
      />
    </div>
  );
};

export default AnimatedAIDiscovery; 